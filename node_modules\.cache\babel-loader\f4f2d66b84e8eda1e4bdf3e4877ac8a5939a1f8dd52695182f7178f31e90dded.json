{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Expenses\\\\ExpensesList.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Tooltip, TablePagination } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Receipt as ReceiptIcon } from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExpenseDialog = ({\n  open,\n  onClose,\n  expense,\n  onSave,\n  vehicles\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: ''\n  });\n  const expenseTypes = ['Mantenimiento', 'Reparación', 'Seguro', 'ITV', 'Impuestos', 'Neumáticos', 'Aceite', 'Filtros', 'Frenos', 'Batería', 'Otros'];\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0] // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: ''\n      });\n    }\n  }, [expense, vehicles, open]);\n  const handleChange = field => event => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value\n    });\n  };\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && formData.coste && formData.descripcion;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: expense ? 'Editar Gasto' : 'Nuevo Gasto'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: 2,\n        pt: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Veh\\xEDculo\",\n          select: true,\n          value: formData.vehiculo_id,\n          onChange: handleChange('vehiculo_id'),\n          fullWidth: true,\n          required: true,\n          children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: vehicle.id,\n            children: vehicle.nombre\n          }, vehicle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Fecha\",\n            type: \"date\",\n            value: formData.fecha,\n            onChange: handleChange('fecha'),\n            fullWidth: true,\n            required: true,\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Kilometraje actual\",\n            type: \"number\",\n            value: formData.kilometros_actuales,\n            onChange: handleChange('kilometros_actuales'),\n            fullWidth: true,\n            inputProps: {\n              min: 0\n            },\n            helperText: \"Opcional\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Tipo de gasto\",\n            select: true,\n            value: formData.tipo_gasto,\n            onChange: handleChange('tipo_gasto'),\n            fullWidth: true,\n            required: true,\n            children: expenseTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: type,\n              children: type\n            }, type, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Coste (\\u20AC)\",\n            type: \"number\",\n            value: formData.coste,\n            onChange: handleChange('coste'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Descripci\\xF3n\",\n          value: formData.descripcion,\n          onChange: handleChange('descripcion'),\n          fullWidth: true,\n          required: true,\n          multiline: true,\n          rows: 2,\n          placeholder: \"Describe el gasto realizado...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Proveedor/Taller\",\n          value: formData.proveedor,\n          onChange: handleChange('proveedor'),\n          fullWidth: true,\n          placeholder: \"Nombre del taller, tienda, etc.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: !isValid,\n        children: expense ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpenseDialog, \"hAHDk6I+hYE0GG43vBPHqnsZ1HE=\");\n_c = ExpenseDialog;\nconst ExpensesList = () => {\n  _s2();\n  const {\n    vehicles,\n    expenses,\n    loadExpenses,\n    addExpense\n  } = useApp();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n\n  // Check if we should show the form based on URL\n  useEffect(() => {\n    setDialogOpen(location.pathname.endsWith('/new'));\n  }, [location]);\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    navigate('/expenses');\n  };\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    navigate('/expenses/new');\n  };\n  const handleEditExpense = expense => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n  const handleDeleteExpense = expense => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n  const handleSaveExpense = async expenseData => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\n      } else {\n        await addExpense(expenseData);\n        // Recargar la lista\n        loadExpenses(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving expense:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n  const getExpenseTypeColor = type => {\n    const colors = {\n      'Mantenimiento': 'primary',\n      'Reparación': 'error',\n      'Seguro': 'info',\n      'ITV': 'warning',\n      'Impuestos': 'secondary',\n      'Neumáticos': 'success'\n    };\n    return colors[type] || 'default';\n  };\n\n  // Calcular estadísticas rápidas\n  const totalExpenses = expenses.length;\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\n\n  // Agrupar por tipo\n  const expensesByType = expenses.reduce((acc, expense) => {\n    const type = expense.tipo_gasto || 'Otros';\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\n    return acc;\n  }, {});\n  const topExpenseType = Object.entries(expensesByType).sort(([, a], [, b]) => b - a)[0];\n\n  // Paginación\n  const paginatedExpenses = expenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        fontWeight: \"bold\",\n        children: \"Gastos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddExpense,\n        disabled: vehicles.length === 0,\n        children: \"Nuevo Gasto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary\",\n            children: totalExpenses\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Total Gastos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"error.main\",\n            children: formatCurrency(totalCost)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Coste Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"warning.main\",\n            children: formatCurrency(avgExpense)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Gasto Promedio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"info.main\",\n            children: topExpenseType ? topExpenseType[0] : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Tipo M\\xE1s Frecuente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), expenses.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          py: 6,\n          children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n            sx: {\n              fontSize: 64,\n              color: 'text.secondary',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"textSecondary\",\n            gutterBottom: true,\n            children: \"No hay gastos registrados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            mb: 3,\n            children: \"Comienza registrando gastos de mantenimiento, reparaciones, seguros, etc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 28\n            }, this),\n            onClick: handleAddExpense,\n            disabled: vehicles.length === 0,\n            children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Gasto'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Fecha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Coste\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Proveedor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Km\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Acciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: paginatedExpenses.map(expense => {\n              var _expense$descripcion;\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                hover: true,\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatDate(expense.fecha)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: expense.vehiculo_nombre\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: expense.tipo_gasto,\n                    color: getExpenseTypeColor(expense.tipo_gasto),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: ((_expense$descripcion = expense.descripcion) === null || _expense$descripcion === void 0 ? void 0 : _expense$descripcion.length) > 50 ? `${expense.descripcion.substring(0, 50)}...` : expense.descripcion\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: formatCurrency(expense.coste)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: expense.proveedor || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: expense.kilometros_actuales ? formatNumber(expense.kilometros_actuales) : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Editar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleEditExpense(expense),\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 424,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Eliminar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteExpense(expense),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n        component: \"div\",\n        count: expenses.length,\n        page: page,\n        onPageChange: (event, newPage) => setPage(newPage),\n        rowsPerPage: rowsPerPage,\n        onRowsPerPageChange: event => {\n          setRowsPerPage(parseInt(event.target.value, 10));\n          setPage(0);\n        },\n        rowsPerPageOptions: [10, 25, 50, 100],\n        labelRowsPerPage: \"Filas por p\\xE1gina:\",\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} de ${count}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ExpenseDialog, {\n      open: dialogOpen,\n      onClose: handleCloseDialog,\n      expense: selectedExpense,\n      onSave: handleSaveExpense,\n      vehicles: vehicles\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 309,\n    columnNumber: 5\n  }, this);\n};\n_s2(ExpensesList, \"n4x+SHdiBWCuxTsBX+ztmPC6Se8=\", false, function () {\n  return [useApp, useLocation, useNavigate];\n});\n_c2 = ExpensesList;\nvar _c, _c2;\n$RefreshReg$(_c, \"ExpenseDialog\");\n$RefreshReg$(_c2, \"ExpensesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON><PERSON>", "TablePagination", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Receipt", "ReceiptIcon", "useApp", "format", "es", "useLocation", "useNavigate", "jsxDEV", "_jsxDEV", "ExpenseDialog", "open", "onClose", "expense", "onSave", "vehicles", "_s", "formData", "setFormData", "vehiculo_id", "fecha", "Date", "toISOString", "split", "kilometros_actuales", "tipo_gasto", "coste", "descripcion", "proveedor", "expenseTypes", "length", "id", "handleChange", "field", "event", "target", "value", "handleSubmit", "dataToSave", "parseFloat", "parseInt", "categoria", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexDirection", "gap", "pt", "label", "select", "onChange", "required", "map", "vehicle", "nombre", "type", "InputLabelProps", "shrink", "inputProps", "min", "helperText", "step", "multiline", "rows", "placeholder", "onClick", "variant", "disabled", "_c", "ExpensesList", "_s2", "expenses", "loadExpenses", "addExpense", "location", "navigate", "dialogOpen", "setDialogOpen", "selectedExpense", "setSelectedExpense", "page", "setPage", "rowsPerPage", "setRowsPerPage", "pathname", "endsWith", "handleCloseDialog", "handleAddExpense", "handleEditExpense", "handleDeleteExpense", "console", "log", "handleSaveExpense", "expenseData", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "locale", "formatNumber", "num", "getExpenseTypeColor", "colors", "totalExpenses", "totalCost", "reduce", "sum", "avgExpense", "expensesByType", "acc", "topExpenseType", "Object", "entries", "sort", "a", "b", "paginatedExpenses", "slice", "justifyContent", "alignItems", "mb", "component", "fontWeight", "startIcon", "sx", "flex", "textAlign", "color", "py", "fontSize", "gutterBottom", "align", "_expense$descripcion", "hover", "vehiculo_nombre", "size", "substring", "title", "count", "onPageChange", "newPage", "onRowsPerPageChange", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c2", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Expenses/ExpensesList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Tooltip,\n  TablePagination,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Receipt as ReceiptIcon,\n} from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { useLocation, useNavigate } from 'react-router-dom';\n\nconst ExpenseDialog = ({ open, onClose, expense, onSave, vehicles }) => {\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: '',\n  });\n\n  const expenseTypes = [\n    'Mantenimiento',\n    'Reparación',\n    'Seguro',\n    'ITV',\n    'Impuestos',\n    'Neumáticos',\n    'Aceite',\n    'Filtros',\n    'Frenos',\n    'Batería',\n    'Otros'\n  ];\n\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0], // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: '',\n      });\n    }\n  }, [expense, vehicles, open]);\n\n  const handleChange = (field) => (event) => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value,\n    });\n  };\n\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto, // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && \n                  formData.coste && formData.descripcion;\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        {expense ? 'Editar Gasto' : 'Nuevo Gasto'}\n      </DialogTitle>\n      <DialogContent>\n        <Box display=\"flex\" flexDirection=\"column\" gap={2} pt={1}>\n          <TextField\n            label=\"Vehículo\"\n            select\n            value={formData.vehiculo_id}\n            onChange={handleChange('vehiculo_id')}\n            fullWidth\n            required\n          >\n            {vehicles.map((vehicle) => (\n              <MenuItem key={vehicle.id} value={vehicle.id}>\n                {vehicle.nombre}\n              </MenuItem>\n            ))}\n          </TextField>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Fecha\"\n              type=\"date\"\n              value={formData.fecha}\n              onChange={handleChange('fecha')}\n              fullWidth\n              required\n              InputLabelProps={{ shrink: true }}\n            />\n            <TextField\n              label=\"Kilometraje actual\"\n              type=\"number\"\n              value={formData.kilometros_actuales}\n              onChange={handleChange('kilometros_actuales')}\n              fullWidth\n              inputProps={{ min: 0 }}\n              helperText=\"Opcional\"\n            />\n          </Box>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Tipo de gasto\"\n              select\n              value={formData.tipo_gasto}\n              onChange={handleChange('tipo_gasto')}\n              fullWidth\n              required\n            >\n              {expenseTypes.map((type) => (\n                <MenuItem key={type} value={type}>\n                  {type}\n                </MenuItem>\n              ))}\n            </TextField>\n            <TextField\n              label=\"Coste (€)\"\n              type=\"number\"\n              value={formData.coste}\n              onChange={handleChange('coste')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n          </Box>\n\n          <TextField\n            label=\"Descripción\"\n            value={formData.descripcion}\n            onChange={handleChange('descripcion')}\n            fullWidth\n            required\n            multiline\n            rows={2}\n            placeholder=\"Describe el gasto realizado...\"\n          />\n\n          <TextField\n            label=\"Proveedor/Taller\"\n            value={formData.proveedor}\n            onChange={handleChange('proveedor')}\n            fullWidth\n            placeholder=\"Nombre del taller, tienda, etc.\"\n          />\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose}>Cancelar</Button>\n        <Button \n          onClick={handleSubmit} \n          variant=\"contained\"\n          disabled={!isValid}\n        >\n          {expense ? 'Actualizar' : 'Guardar'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nconst ExpensesList = () => {\n  const { vehicles, expenses, loadExpenses, addExpense } = useApp();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  \n  // Check if we should show the form based on URL\n  useEffect(() => {\n    setDialogOpen(location.pathname.endsWith('/new'));\n  }, [location]);\n  \n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    navigate('/expenses');\n  };\n\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    navigate('/expenses/new');\n  };\n\n  const handleEditExpense = (expense) => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n\n  const handleDeleteExpense = (expense) => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n\n  const handleSaveExpense = async (expenseData) => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\n      } else {\n        await addExpense(expenseData);\n        // Recargar la lista\n        loadExpenses(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving expense:', error);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  const formatDate = (dateString) => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\n    } catch {\n      return dateString;\n    }\n  };\n\n  const formatNumber = (num) => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  const getExpenseTypeColor = (type) => {\n    const colors = {\n      'Mantenimiento': 'primary',\n      'Reparación': 'error',\n      'Seguro': 'info',\n      'ITV': 'warning',\n      'Impuestos': 'secondary',\n      'Neumáticos': 'success',\n    };\n    return colors[type] || 'default';\n  };\n\n  // Calcular estadísticas rápidas\n  const totalExpenses = expenses.length;\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\n  \n  // Agrupar por tipo\n  const expensesByType = expenses.reduce((acc, expense) => {\n    const type = expense.tipo_gasto || 'Otros';\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\n    return acc;\n  }, {});\n  \n  const topExpenseType = Object.entries(expensesByType)\n    .sort(([,a], [,b]) => b - a)[0];\n\n  // Paginación\n  const paginatedExpenses = expenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n\n  return (\n    <Box>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n        <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\">\n          Gastos\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleAddExpense}\n          disabled={vehicles.length === 0}\n        >\n          Nuevo Gasto\n        </Button>\n      </Box>\n\n      {/* Estadísticas rápidas */}\n      <Box display=\"flex\" gap={2} mb={3}>\n        <Card sx={{ flex: 1 }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"primary\">{totalExpenses}</Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Total Gastos</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: 1 }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"error.main\">{formatCurrency(totalCost)}</Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Coste Total</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: 1 }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"warning.main\">{formatCurrency(avgExpense)}</Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Gasto Promedio</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: 1 }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h6\" color=\"info.main\">\n              {topExpenseType ? topExpenseType[0] : 'N/A'}\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Tipo Más Frecuente</Typography>\n          </CardContent>\n        </Card>\n      </Box>\n\n      {expenses.length === 0 ? (\n        <Card>\n          <CardContent>\n            <Box textAlign=\"center\" py={6}>\n              <ReceiptIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"textSecondary\" gutterBottom>\n                No hay gastos registrados\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\" mb={3}>\n                Comienza registrando gastos de mantenimiento, reparaciones, seguros, etc.\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={handleAddExpense}\n                disabled={vehicles.length === 0}\n              >\n                {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Gasto'}\n              </Button>\n            </Box>\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <TableContainer component={Paper}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Fecha</TableCell>\n                  <TableCell>Vehículo</TableCell>\n                  <TableCell>Tipo</TableCell>\n                  <TableCell>Descripción</TableCell>\n                  <TableCell align=\"right\">Coste</TableCell>\n                  <TableCell>Proveedor</TableCell>\n                  <TableCell align=\"right\">Km</TableCell>\n                  <TableCell align=\"center\">Acciones</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {paginatedExpenses.map((expense) => (\n                  <TableRow key={expense.id} hover>\n                    <TableCell>{formatDate(expense.fecha)}</TableCell>\n                    <TableCell>{expense.vehiculo_nombre}</TableCell>\n                    <TableCell>\n                      <Chip \n                        label={expense.tipo_gasto} \n                        color={getExpenseTypeColor(expense.tipo_gasto)}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">\n                        {expense.descripcion?.length > 50 \n                          ? `${expense.descripcion.substring(0, 50)}...`\n                          : expense.descripcion\n                        }\n                      </Typography>\n                    </TableCell>\n                    <TableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\n                        {formatCurrency(expense.coste)}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{expense.proveedor || '-'}</TableCell>\n                    <TableCell align=\"right\">\n                      {expense.kilometros_actuales ? formatNumber(expense.kilometros_actuales) : '-'}\n                    </TableCell>\n                    <TableCell align=\"center\">\n                      <Tooltip title=\"Editar\">\n                        <IconButton size=\"small\" onClick={() => handleEditExpense(expense)}>\n                          <EditIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Eliminar\">\n                        <IconButton size=\"small\" color=\"error\" onClick={() => handleDeleteExpense(expense)}>\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n          \n          <TablePagination\n            component=\"div\"\n            count={expenses.length}\n            page={page}\n            onPageChange={(event, newPage) => setPage(newPage)}\n            rowsPerPage={rowsPerPage}\n            onRowsPerPageChange={(event) => {\n              setRowsPerPage(parseInt(event.target.value, 10));\n              setPage(0);\n            }}\n            rowsPerPageOptions={[10, 25, 50, 100]}\n            labelRowsPerPage=\"Filas por página:\"\n            labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}\n          />\n        </Card>\n      )}\n\n      <ExpenseDialog\n        open={dialogOpen}\n        onClose={handleCloseDialog}\n        expense={selectedExpense}\n        onSave={handleSaveExpense}\n        vehicles={vehicles}\n      />\n    </Box>\n  );\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,eAAe,QACV,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC;IACvCgD,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,mBAAmB,EAAE,EAAE;IACvBC,UAAU,EAAE,eAAe;IAC3BC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,CACnB,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,CACR;EAEDzD,SAAS,CAAC,MAAM;IACd,IAAIyC,OAAO,EAAE;MACXK,WAAW,CAAC;QACV,GAAGL,OAAO;QACVO,KAAK,EAAEP,OAAO,CAACO,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE;MACtC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,WAAW,CAAC;QACVC,WAAW,EAAEJ,QAAQ,CAACe,MAAM,GAAG,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAACgB,EAAE,GAAG,EAAE;QACtDX,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7CC,mBAAmB,EAAE,EAAE;QACvBC,UAAU,EAAE,eAAe;QAC3BC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACf,OAAO,EAAEE,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EAE7B,MAAMqB,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzChB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgB,KAAK,GAAGC,KAAK,CAACC,MAAM,CAACC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjB,GAAGrB,QAAQ;MACXS,KAAK,EAAEa,UAAU,CAACtB,QAAQ,CAACS,KAAK,CAAC;MACjCF,mBAAmB,EAAEgB,QAAQ,CAACvB,QAAQ,CAACO,mBAAmB,CAAC,IAAI,CAAC;MAChEiB,SAAS,EAAExB,QAAQ,CAACQ,UAAU,CAAE;IAClC,CAAC;IACDX,MAAM,CAACwB,UAAU,CAAC;IAClB1B,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM8B,OAAO,GAAGzB,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACQ,UAAU,IAC7DR,QAAQ,CAACS,KAAK,IAAIT,QAAQ,CAACU,WAAW;EAEtD,oBACElB,OAAA,CAACtB,MAAM;IAACwB,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC+B,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DpC,OAAA,CAACrB,WAAW;MAAAyD,QAAA,EACThC,OAAO,GAAG,cAAc,GAAG;IAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACdxC,OAAA,CAACpB,aAAa;MAAAwD,QAAA,eACZpC,OAAA,CAACpC,GAAG;QAAC6E,OAAO,EAAC,MAAM;QAACC,aAAa,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAR,QAAA,gBACvDpC,OAAA,CAAClB,SAAS;UACR+D,KAAK,EAAC,aAAU;UAChBC,MAAM;UACNnB,KAAK,EAAEnB,QAAQ,CAACE,WAAY;UAC5BqC,QAAQ,EAAExB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTa,QAAQ;UAAAZ,QAAA,EAEP9B,QAAQ,CAAC2C,GAAG,CAAEC,OAAO,iBACpBlD,OAAA,CAACjB,QAAQ;YAAkB4C,KAAK,EAAEuB,OAAO,CAAC5B,EAAG;YAAAc,QAAA,EAC1Cc,OAAO,CAACC;UAAM,GADFD,OAAO,CAAC5B,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZxC,OAAA,CAACpC,GAAG;UAAC6E,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAAAP,QAAA,gBACzBpC,OAAA,CAAClB,SAAS;YACR+D,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,MAAM;YACXzB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;YACtBoC,QAAQ,EAAExB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTa,QAAQ;YACRK,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFxC,OAAA,CAAClB,SAAS;YACR+D,KAAK,EAAC,oBAAoB;YAC1BO,IAAI,EAAC,QAAQ;YACbzB,KAAK,EAAEnB,QAAQ,CAACO,mBAAoB;YACpCgC,QAAQ,EAAExB,YAAY,CAAC,qBAAqB,CAAE;YAC9CY,SAAS;YACToB,UAAU,EAAE;cAAEC,GAAG,EAAE;YAAE,CAAE;YACvBC,UAAU,EAAC;UAAU;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxC,OAAA,CAACpC,GAAG;UAAC6E,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAAAP,QAAA,gBACzBpC,OAAA,CAAClB,SAAS;YACR+D,KAAK,EAAC,eAAe;YACrBC,MAAM;YACNnB,KAAK,EAAEnB,QAAQ,CAACQ,UAAW;YAC3B+B,QAAQ,EAAExB,YAAY,CAAC,YAAY,CAAE;YACrCY,SAAS;YACTa,QAAQ;YAAAZ,QAAA,EAEPhB,YAAY,CAAC6B,GAAG,CAAEG,IAAI,iBACrBpD,OAAA,CAACjB,QAAQ;cAAY4C,KAAK,EAAEyB,IAAK;cAAAhB,QAAA,EAC9BgB;YAAI,GADQA,IAAI;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZxC,OAAA,CAAClB,SAAS;YACR+D,KAAK,EAAC,gBAAW;YACjBO,IAAI,EAAC,QAAQ;YACbzB,KAAK,EAAEnB,QAAQ,CAACS,KAAM;YACtB8B,QAAQ,EAAExB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTa,QAAQ;YACRO,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEE,IAAI,EAAE;YAAK;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxC,OAAA,CAAClB,SAAS;UACR+D,KAAK,EAAC,gBAAa;UACnBlB,KAAK,EAAEnB,QAAQ,CAACU,WAAY;UAC5B6B,QAAQ,EAAExB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTa,QAAQ;UACRW,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,WAAW,EAAC;QAAgC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEFxC,OAAA,CAAClB,SAAS;UACR+D,KAAK,EAAC,kBAAkB;UACxBlB,KAAK,EAAEnB,QAAQ,CAACW,SAAU;UAC1B4B,QAAQ,EAAExB,YAAY,CAAC,WAAW,CAAE;UACpCY,SAAS;UACT0B,WAAW,EAAC;QAAiC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChBxC,OAAA,CAACnB,aAAa;MAAAuD,QAAA,gBACZpC,OAAA,CAAChC,MAAM;QAAC8F,OAAO,EAAE3D,OAAQ;QAAAiC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3CxC,OAAA,CAAChC,MAAM;QACL8F,OAAO,EAAElC,YAAa;QACtBmC,OAAO,EAAC,WAAW;QACnBC,QAAQ,EAAE,CAAC/B,OAAQ;QAAAG,QAAA,EAElBhC,OAAO,GAAG,YAAY,GAAG;MAAS;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACjC,EAAA,CAtKIN,aAAa;AAAAgE,EAAA,GAAbhE,aAAa;AAwKnB,MAAMiE,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAE7D,QAAQ;IAAE8D,QAAQ;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAG5E,MAAM,CAAC,CAAC;EACjE,MAAM6E,QAAQ,GAAG1E,WAAW,CAAC,CAAC;EAC9B,MAAM2E,QAAQ,GAAG1E,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2E,UAAU,EAAEC,aAAa,CAAC,GAAGhH,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiH,eAAe,EAAEC,kBAAkB,CAAC,GAAGlH,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmH,IAAI,EAAEC,OAAO,CAAC,GAAGpH,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACqH,WAAW,EAAEC,cAAc,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd+G,aAAa,CAACH,QAAQ,CAACU,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;EACnD,CAAC,EAAE,CAACX,QAAQ,CAAC,CAAC;EAEd,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;IAC9BT,aAAa,CAAC,KAAK,CAAC;IACpBF,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAED7G,SAAS,CAAC,MAAM;IACd;IACA0G,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7BR,kBAAkB,CAAC,IAAI,CAAC;IACxBJ,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAMa,iBAAiB,GAAIjF,OAAO,IAAK;IACrCwE,kBAAkB,CAACxE,OAAO,CAAC;IAC3BsE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMY,mBAAmB,GAAIlF,OAAO,IAAK;IACvC;IACAmF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEpF,OAAO,CAAC;EACzC,CAAC;EAED,MAAMqF,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C,IAAI;MACF,IAAIf,eAAe,EAAE;QACnB;QACAY,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,WAAW,CAAC;MAC7C,CAAC,MAAM;QACL,MAAMpB,UAAU,CAACoB,WAAW,CAAC;QAC7B;QACArB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACtG,MAAM,CAACkG,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOxG,MAAM,CAAC,IAAIiB,IAAI,CAACuF,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEC,MAAM,EAAExG;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAOuG,UAAU;IACnB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,GAAG,IAAK;IAC5B,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACpG,MAAM,CAAC2G,GAAG,IAAI,CAAC,CAAC;EACxD,CAAC;EAED,MAAMC,mBAAmB,GAAInD,IAAI,IAAK;IACpC,MAAMoD,MAAM,GAAG;MACb,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE,OAAO;MACrB,QAAQ,EAAE,MAAM;MAChB,KAAK,EAAE,SAAS;MAChB,WAAW,EAAE,WAAW;MACxB,YAAY,EAAE;IAChB,CAAC;IACD,OAAOA,MAAM,CAACpD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;;EAED;EACA,MAAMqD,aAAa,GAAGrC,QAAQ,CAAC/C,MAAM;EACrC,MAAMqF,SAAS,GAAGtC,QAAQ,CAACuC,MAAM,CAAC,CAACC,GAAG,EAAExG,OAAO,KAAKwG,GAAG,IAAIxG,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAM4F,UAAU,GAAGJ,aAAa,GAAG,CAAC,GAAGC,SAAS,GAAGD,aAAa,GAAG,CAAC;;EAEpE;EACA,MAAMK,cAAc,GAAG1C,QAAQ,CAACuC,MAAM,CAAC,CAACI,GAAG,EAAE3G,OAAO,KAAK;IACvD,MAAMgD,IAAI,GAAGhD,OAAO,CAACY,UAAU,IAAI,OAAO;IAC1C+F,GAAG,CAAC3D,IAAI,CAAC,GAAG,CAAC2D,GAAG,CAAC3D,IAAI,CAAC,IAAI,CAAC,KAAKhD,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC;IACnD,OAAO8F,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO,CAACJ,cAAc,CAAC,CAClDK,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC;EACA,MAAME,iBAAiB,GAAGlD,QAAQ,CAACmD,KAAK,CAAC1C,IAAI,GAAGE,WAAW,EAAEF,IAAI,GAAGE,WAAW,GAAGA,WAAW,CAAC;EAE9F,oBACE/E,OAAA,CAACpC,GAAG;IAAAwE,QAAA,gBACFpC,OAAA,CAACpC,GAAG;MAAC6E,OAAO,EAAC,MAAM;MAAC+E,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAtF,QAAA,gBAC3EpC,OAAA,CAACnC,UAAU;QAACkG,OAAO,EAAC,IAAI;QAAC4D,SAAS,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAAAxF,QAAA,EAAC;MAE1D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxC,OAAA,CAAChC,MAAM;QACL+F,OAAO,EAAC,WAAW;QACnB8D,SAAS,eAAE7H,OAAA,CAACb,OAAO;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBsB,OAAO,EAAEsB,gBAAiB;QAC1BpB,QAAQ,EAAE1D,QAAQ,CAACe,MAAM,KAAK,CAAE;QAAAe,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNxC,OAAA,CAACpC,GAAG;MAAC6E,OAAO,EAAC,MAAM;MAACE,GAAG,EAAE,CAAE;MAAC+E,EAAE,EAAE,CAAE;MAAAtF,QAAA,gBAChCpC,OAAA,CAAClC,IAAI;QAACgK,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAA3F,QAAA,eACpBpC,OAAA,CAACjC,WAAW;UAAC+J,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA5F,QAAA,gBACvCpC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,IAAI;YAACkE,KAAK,EAAC,SAAS;YAAA7F,QAAA,EAAEqE;UAAa;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrExC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,OAAO;YAACkE,KAAK,EAAC,eAAe;YAAA7F,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPxC,OAAA,CAAClC,IAAI;QAACgK,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAA3F,QAAA,eACpBpC,OAAA,CAACjC,WAAW;UAAC+J,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA5F,QAAA,gBACvCpC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,IAAI;YAACkE,KAAK,EAAC,YAAY;YAAA7F,QAAA,EAAEwD,cAAc,CAACc,SAAS;UAAC;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACpFxC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,OAAO;YAACkE,KAAK,EAAC,eAAe;YAAA7F,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPxC,OAAA,CAAClC,IAAI;QAACgK,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAA3F,QAAA,eACpBpC,OAAA,CAACjC,WAAW;UAAC+J,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA5F,QAAA,gBACvCpC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,IAAI;YAACkE,KAAK,EAAC,cAAc;YAAA7F,QAAA,EAAEwD,cAAc,CAACiB,UAAU;UAAC;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACvFxC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,OAAO;YAACkE,KAAK,EAAC,eAAe;YAAA7F,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPxC,OAAA,CAAClC,IAAI;QAACgK,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAA3F,QAAA,eACpBpC,OAAA,CAACjC,WAAW;UAAC+J,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA5F,QAAA,gBACvCpC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,IAAI;YAACkE,KAAK,EAAC,WAAW;YAAA7F,QAAA,EACvC4E,cAAc,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAG;UAAK;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACbxC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,OAAO;YAACkE,KAAK,EAAC,eAAe;YAAA7F,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEL4B,QAAQ,CAAC/C,MAAM,KAAK,CAAC,gBACpBrB,OAAA,CAAClC,IAAI;MAAAsE,QAAA,eACHpC,OAAA,CAACjC,WAAW;QAAAqE,QAAA,eACVpC,OAAA,CAACpC,GAAG;UAACoK,SAAS,EAAC,QAAQ;UAACE,EAAE,EAAE,CAAE;UAAA9F,QAAA,gBAC5BpC,OAAA,CAACP,WAAW;YAACqI,EAAE,EAAE;cAAEK,QAAQ,EAAE,EAAE;cAAEF,KAAK,EAAE,gBAAgB;cAAEP,EAAE,EAAE;YAAE;UAAE;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrExC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,IAAI;YAACkE,KAAK,EAAC,eAAe;YAACG,YAAY;YAAAhG,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxC,OAAA,CAACnC,UAAU;YAACkG,OAAO,EAAC,OAAO;YAACkE,KAAK,EAAC,eAAe;YAACP,EAAE,EAAE,CAAE;YAAAtF,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxC,OAAA,CAAChC,MAAM;YACL+F,OAAO,EAAC,WAAW;YACnB8D,SAAS,eAAE7H,OAAA,CAACb,OAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBsB,OAAO,EAAEsB,gBAAiB;YAC1BpB,QAAQ,EAAE1D,QAAQ,CAACe,MAAM,KAAK,CAAE;YAAAe,QAAA,EAE/B9B,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;UAAwB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPxC,OAAA,CAAClC,IAAI;MAAAsE,QAAA,gBACHpC,OAAA,CAAC5B,cAAc;QAACuJ,SAAS,EAAEpJ,KAAM;QAAA6D,QAAA,eAC/BpC,OAAA,CAAC/B,KAAK;UAAAmE,QAAA,gBACJpC,OAAA,CAAC3B,SAAS;YAAA+D,QAAA,eACRpC,OAAA,CAAC1B,QAAQ;cAAA8D,QAAA,gBACPpC,OAAA,CAAC7B,SAAS;gBAAAiE,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BxC,OAAA,CAAC7B,SAAS;gBAAAiE,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BxC,OAAA,CAAC7B,SAAS;gBAAAiE,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3BxC,OAAA,CAAC7B,SAAS;gBAAAiE,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCxC,OAAA,CAAC7B,SAAS;gBAACkK,KAAK,EAAC,OAAO;gBAAAjG,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1CxC,OAAA,CAAC7B,SAAS;gBAAAiE,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCxC,OAAA,CAAC7B,SAAS;gBAACkK,KAAK,EAAC,OAAO;gBAAAjG,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACvCxC,OAAA,CAAC7B,SAAS;gBAACkK,KAAK,EAAC,QAAQ;gBAAAjG,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZxC,OAAA,CAAC9B,SAAS;YAAAkE,QAAA,EACPkF,iBAAiB,CAACrE,GAAG,CAAE7C,OAAO;cAAA,IAAAkI,oBAAA;cAAA,oBAC7BtI,OAAA,CAAC1B,QAAQ;gBAAkBiK,KAAK;gBAAAnG,QAAA,gBAC9BpC,OAAA,CAAC7B,SAAS;kBAAAiE,QAAA,EAAE8D,UAAU,CAAC9F,OAAO,CAACO,KAAK;gBAAC;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDxC,OAAA,CAAC7B,SAAS;kBAAAiE,QAAA,EAAEhC,OAAO,CAACoI;gBAAe;kBAAAnG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDxC,OAAA,CAAC7B,SAAS;kBAAAiE,QAAA,eACRpC,OAAA,CAACxB,IAAI;oBACHqE,KAAK,EAAEzC,OAAO,CAACY,UAAW;oBAC1BiH,KAAK,EAAE1B,mBAAmB,CAACnG,OAAO,CAACY,UAAU,CAAE;oBAC/CyH,IAAI,EAAC;kBAAO;oBAAApG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZxC,OAAA,CAAC7B,SAAS;kBAAAiE,QAAA,eACRpC,OAAA,CAACnC,UAAU;oBAACkG,OAAO,EAAC,OAAO;oBAAA3B,QAAA,EACxB,EAAAkG,oBAAA,GAAAlI,OAAO,CAACc,WAAW,cAAAoH,oBAAA,uBAAnBA,oBAAA,CAAqBjH,MAAM,IAAG,EAAE,GAC7B,GAAGjB,OAAO,CAACc,WAAW,CAACwH,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC5CtI,OAAO,CAACc;kBAAW;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZxC,OAAA,CAAC7B,SAAS;kBAACkK,KAAK,EAAC,OAAO;kBAAAjG,QAAA,eACtBpC,OAAA,CAACnC,UAAU;oBAACkG,OAAO,EAAC,OAAO;oBAAC6D,UAAU,EAAC,MAAM;oBAAAxF,QAAA,EAC1CwD,cAAc,CAACxF,OAAO,CAACa,KAAK;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZxC,OAAA,CAAC7B,SAAS;kBAAAiE,QAAA,EAAEhC,OAAO,CAACe,SAAS,IAAI;gBAAG;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDxC,OAAA,CAAC7B,SAAS;kBAACkK,KAAK,EAAC,OAAO;kBAAAjG,QAAA,EACrBhC,OAAO,CAACW,mBAAmB,GAAGsF,YAAY,CAACjG,OAAO,CAACW,mBAAmB,CAAC,GAAG;gBAAG;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACZxC,OAAA,CAAC7B,SAAS;kBAACkK,KAAK,EAAC,QAAQ;kBAAAjG,QAAA,gBACvBpC,OAAA,CAAChB,OAAO;oBAAC2J,KAAK,EAAC,QAAQ;oBAAAvG,QAAA,eACrBpC,OAAA,CAACvB,UAAU;sBAACgK,IAAI,EAAC,OAAO;sBAAC3E,OAAO,EAAEA,CAAA,KAAMuB,iBAAiB,CAACjF,OAAO,CAAE;sBAAAgC,QAAA,eACjEpC,OAAA,CAACX,QAAQ;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVxC,OAAA,CAAChB,OAAO;oBAAC2J,KAAK,EAAC,UAAU;oBAAAvG,QAAA,eACvBpC,OAAA,CAACvB,UAAU;sBAACgK,IAAI,EAAC,OAAO;sBAACR,KAAK,EAAC,OAAO;sBAACnE,OAAO,EAAEA,CAAA,KAAMwB,mBAAmB,CAAClF,OAAO,CAAE;sBAAAgC,QAAA,eACjFpC,OAAA,CAACT,UAAU;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,GAtCCpC,OAAO,CAACkB,EAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCf,CAAC;YAAA,CACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEjBxC,OAAA,CAACf,eAAe;QACd0I,SAAS,EAAC,KAAK;QACfiB,KAAK,EAAExE,QAAQ,CAAC/C,MAAO;QACvBwD,IAAI,EAAEA,IAAK;QACXgE,YAAY,EAAEA,CAACpH,KAAK,EAAEqH,OAAO,KAAKhE,OAAO,CAACgE,OAAO,CAAE;QACnD/D,WAAW,EAAEA,WAAY;QACzBgE,mBAAmB,EAAGtH,KAAK,IAAK;UAC9BuD,cAAc,CAACjD,QAAQ,CAACN,KAAK,CAACC,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;UAChDmD,OAAO,CAAC,CAAC,CAAC;QACZ,CAAE;QACFkE,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QACtCC,gBAAgB,EAAC,sBAAmB;QACpCC,kBAAkB,EAAEA,CAAC;UAAEC,IAAI;UAAEC,EAAE;UAAER;QAAM,CAAC,KAAK,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK;MAAG;QAAAvG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAEDxC,OAAA,CAACC,aAAa;MACZC,IAAI,EAAEuE,UAAW;MACjBtE,OAAO,EAAEgF,iBAAkB;MAC3B/E,OAAO,EAAEuE,eAAgB;MACzBtE,MAAM,EAAEoF,iBAAkB;MAC1BnF,QAAQ,EAAEA;IAAS;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC2B,GAAA,CApQID,YAAY;EAAA,QACyCxE,MAAM,EAC9CG,WAAW,EACXC,WAAW;AAAA;AAAAuJ,GAAA,GAHxBnF,YAAY;AAAA,IAAAD,EAAA,EAAAoF,GAAA;AAAAC,YAAA,CAAArF,EAAA;AAAAqF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}