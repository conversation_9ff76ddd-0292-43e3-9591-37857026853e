{"ast": null, "code": "\"use strict\";\n'use client';\n\n// TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- process.env never changes, dependency arrays are intentionally ignored\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useControlled;\nvar React = _interopRequireWildcard(require(\"react\"));\nfunction useControlled(props) {\n  const {\n    controlled,\n    default: defaultProp,\n    name,\n    state = 'value'\n  } = props;\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled ${state} state of ${name} to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${name} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      // Object.is() is not equivalent to the === operator.\n      // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is for more details.\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([`MUI: A component is changing the default ${state} state of an uncontrolled ${name} after being initialized. ` + `To suppress this warning opt to use a controlled ${name}.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n\n  // TODO: provide overloads for the useControlled function to account for the case where either\n  // controlled or default is not undefiend.\n  // In that case the return type should be [T, React.Dispatch<React.SetStateAction<T>>]\n  // otherwise it should be [T | undefined, React.Dispatch<React.SetStateAction<T | undefined>>]\n  return [value, setValueIfUncontrolled];\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "Object", "defineProperty", "exports", "value", "useControlled", "React", "props", "controlled", "defaultProp", "name", "state", "current", "isControlled", "useRef", "undefined", "valueState", "setValue", "useState", "process", "env", "NODE_ENV", "useEffect", "console", "error", "join", "defaultValue", "is", "JSON", "stringify", "setValueIfUncontrolled", "useCallback", "newValue"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/useControlled/useControlled.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\n// TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- process.env never changes, dependency arrays are intentionally ignored\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useControlled;\nvar React = _interopRequireWildcard(require(\"react\"));\nfunction useControlled(props) {\n  const {\n    controlled,\n    default: defaultProp,\n    name,\n    state = 'value'\n  } = props;\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled ${state} state of ${name} to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${name} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      // Object.is() is not equivalent to the === operator.\n      // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is for more details.\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([`MUI: A component is changing the default ${state} state of an uncontrolled ${name} after being initialized. ` + `To suppress this warning opt to use a controlled ${name}.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n\n  // TODO: provide overloads for the useControlled function to account for the case where either\n  // controlled or default is not undefiend.\n  // In that case the return type should be [T, React.Dispatch<React.SetStateAction<T>>]\n  // otherwise it should be [T | undefined, React.Dispatch<React.SetStateAction<T | undefined>>]\n  return [value, setValueIfUncontrolled];\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ;AACA;AACA,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAGK,aAAa;AAC/B,IAAIC,KAAK,GAAGR,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,SAASM,aAAaA,CAACE,KAAK,EAAE;EAC5B,MAAM;IACJC,UAAU;IACVR,OAAO,EAAES,WAAW;IACpBC,IAAI;IACJC,KAAK,GAAG;EACV,CAAC,GAAGJ,KAAK;EACT;EACA,MAAM;IACJK,OAAO,EAAEC;EACX,CAAC,GAAGP,KAAK,CAACQ,MAAM,CAACN,UAAU,KAAKO,SAAS,CAAC;EAC1C,MAAM,CAACC,UAAU,EAAEC,QAAQ,CAAC,GAAGX,KAAK,CAACY,QAAQ,CAACT,WAAW,CAAC;EAC1D,MAAML,KAAK,GAAGS,YAAY,GAAGL,UAAU,GAAGQ,UAAU;EACpD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCf,KAAK,CAACgB,SAAS,CAAC,MAAM;MACpB,IAAIT,YAAY,MAAML,UAAU,KAAKO,SAAS,CAAC,EAAE;QAC/CQ,OAAO,CAACC,KAAK,CAAC,CAAC,oCAAoCX,YAAY,GAAG,EAAE,GAAG,IAAI,cAAcF,KAAK,aAAaD,IAAI,UAAUG,YAAY,GAAG,IAAI,GAAG,EAAE,aAAa,EAAE,6EAA6E,EAAE,qDAAqDH,IAAI,GAAG,GAAG,4CAA4C,EAAE,4HAA4H,EAAE,sDAAsD,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/hB;IACF,CAAC,EAAE,CAACd,KAAK,EAAED,IAAI,EAAEF,UAAU,CAAC,CAAC;IAC7B,MAAM;MACJI,OAAO,EAAEc;IACX,CAAC,GAAGpB,KAAK,CAACQ,MAAM,CAACL,WAAW,CAAC;IAC7BH,KAAK,CAACgB,SAAS,CAAC,MAAM;MACpB;MACA;MACA,IAAI,CAACT,YAAY,IAAI,CAACZ,MAAM,CAAC0B,EAAE,CAACD,YAAY,EAAEjB,WAAW,CAAC,EAAE;QAC1Dc,OAAO,CAACC,KAAK,CAAC,CAAC,4CAA4Cb,KAAK,6BAA6BD,IAAI,4BAA4B,GAAG,oDAAoDA,IAAI,GAAG,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1M;IACF,CAAC,EAAE,CAACG,IAAI,CAACC,SAAS,CAACpB,WAAW,CAAC,CAAC,CAAC;EACnC;EACA,MAAMqB,sBAAsB,GAAGxB,KAAK,CAACyB,WAAW,CAACC,QAAQ,IAAI;IAC3D,IAAI,CAACnB,YAAY,EAAE;MACjBI,QAAQ,CAACe,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA,OAAO,CAAC5B,KAAK,EAAE0B,sBAAsB,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}