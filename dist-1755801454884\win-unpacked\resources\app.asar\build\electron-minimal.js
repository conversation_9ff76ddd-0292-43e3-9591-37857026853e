const { app, BrowserWindow } = require('electron');
const path = require('path');

// Variables globales
let mainWindow;

// Detectar si estamos en desarrollo
const isDev = !app.isPackaged;

console.log('Iniciando aplicación...');
console.log('isDev:', isDev);
console.log('app.isPackaged:', app.isPackaged);
console.log('process.execPath:', process.execPath);
console.log('__dirname:', __dirname);

// Función para crear la ventana principal
function createWindow() {
  console.log('Creando ventana principal...');
  
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false
    },
    show: false
  });

  // Cargar una página HTML básica
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Repostajes Manager - Prueba</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 50px;
                background: #f5f5f5;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                text-align: center;
            }
            .success {
                color: #4CAF50;
                font-size: 24px;
                margin-bottom: 20px;
            }
            .info {
                color: #666;
                margin: 10px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="success">✅ ¡Aplicación Funcionando!</h1>
            <p class="info">Esta es una versión de prueba mínima de Repostajes Manager</p>
            <p class="info">Si ves este mensaje, Electron está funcionando correctamente</p>
            <p class="info">Versión: 1.0.0</p>
            <p class="info">Modo: ${isDev ? 'Desarrollo' : 'Producción'}</p>
            <hr>
            <p><strong>Próximos pasos:</strong></p>
            <p>1. Confirmar que esta versión funciona</p>
            <p>2. Agregar gradualmente las funcionalidades</p>
            <p>3. Integrar el backend y la base de datos</p>
        </div>
    </body>
    </html>
  `;

  // Crear un archivo HTML temporal
  const fs = require('fs');
  const tempHtmlPath = path.join(__dirname, 'temp-test.html');
  
  try {
    fs.writeFileSync(tempHtmlPath, htmlContent);
    console.log('Archivo HTML temporal creado:', tempHtmlPath);
    
    mainWindow.loadFile(tempHtmlPath);
    
    // Limpiar el archivo temporal después de cargar
    setTimeout(() => {
      try {
        fs.unlinkSync(tempHtmlPath);
        console.log('Archivo temporal eliminado');
      } catch (e) {
        console.log('No se pudo eliminar el archivo temporal:', e.message);
      }
    }, 5000);
    
  } catch (error) {
    console.error('Error creando archivo HTML:', error);
    // Fallback: cargar contenido directamente
    mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);
  }

  // Mostrar ventana cuando esté lista
  mainWindow.once('ready-to-show', () => {
    console.log('Ventana lista, mostrando...');
    mainWindow.show();
  });

  // Abrir DevTools en desarrollo
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    console.log('Ventana cerrada');
    mainWindow = null;
  });

  // Log de eventos de la ventana
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Contenido cargado exitosamente');
  });

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Error cargando contenido:', errorCode, errorDescription);
  });
}

// Inicialización de la aplicación
app.whenReady().then(() => {
  console.log('App ready, creando ventana...');
  createWindow();

  app.on('activate', () => {
    console.log('App activated');
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Salir cuando todas las ventanas estén cerradas
app.on('window-all-closed', () => {
  console.log('Todas las ventanas cerradas');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  console.log('App cerrándose...');
});

// Capturar errores no manejados
process.on('uncaughtException', (error) => {
  console.error('Error no capturado:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Promesa rechazada no manejada:', reason);
});

console.log('Archivo electron-minimal.js cargado completamente');
