{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Dashboard\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, Button, Chip, List, ListItem, ListItemText, ListItemIcon, Divider, IconButton, Tooltip } from '@mui/material';\nimport { DirectionsCar as VehicleIcon, LocalGasStation as RefuelIcon, Receipt as ExpenseIcon, TrendingUp as TrendingUpIcon, Add as AddIcon, Speed as SpeedIcon, Euro as EuroIcon, Timeline as TimelineIcon, Notifications as NotificationIcon,\n// Added\nWarning as WarningIcon,\n// Added\nSchedule as ScheduleIcon,\n// Added\nBuild as MaintenanceIcon,\n// Added\nSecurity as InsuranceIcon,\n// Added\nAssignment as InspectionIcon,\n// Added\nLocalGasStation as FuelIcon // Added\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useApp } from '../../context/AppContext';\nimport { format, subMonths, differenceInDays } from 'date-fns'; // Added differenceInDays\nimport { es } from 'date-fns/locale';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as ChartTooltip, ResponsiveContainer } from 'recharts';\n\n// Componente para tarjetas de estadísticas\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatCard = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  color = 'primary',\n  onClick\n}) => /*#__PURE__*/_jsxDEV(Card, {\n  sx: {\n    height: '100%',\n    cursor: onClick ? 'pointer' : 'default',\n    transition: 'transform 0.2s, box-shadow 0.2s',\n    '&:hover': onClick ? {\n      transform: 'translateY(-2px)',\n      boxShadow: 4\n    } : {}\n  },\n  onClick: onClick,\n  children: /*#__PURE__*/_jsxDEV(CardContent, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          color: \"textSecondary\",\n          gutterBottom: true,\n          variant: \"body2\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"div\",\n          color: color,\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: subtitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          backgroundColor: `${color}.light`,\n          borderRadius: '50%',\n          p: 1.5,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 51,\n  columnNumber: 3\n}, this);\n_c = StatCard;\nconst Dashboard = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    vehicles,\n    refuels,\n    expenses,\n    loadStatistics,\n    statistics\n  } = useApp();\n  const [dashboardStats, setDashboardStats] = useState(null);\n  const [immediateReminders, setImmediateReminders] = useState([]); // New state for immediate reminders\n\n  useEffect(() => {\n    loadStatistics();\n    loadImmediateReminders(); // Call new function to load immediate reminders\n  }, [refuels]); // Added refuels as dependency for loadImmediateReminders\n\n  // Function to load and filter immediate reminders\n  const loadImmediateReminders = async () => {\n    try {\n      if (window.electronAPI) {\n        const allReminders = await window.electronAPI.getReminders();\n        const now = new Date();\n        const active = [];\n        allReminders.forEach(reminder => {\n          if (!reminder.activo) return;\n          let isActive = false;\n          let daysUntil = null;\n          let kmUntil = null;\n\n          // Check by date\n          if (reminder.fecha_vencimiento) {\n            const dueDate = new Date(reminder.fecha_vencimiento);\n            daysUntil = differenceInDays(dueDate, now);\n            if (daysUntil <= 30) {\n              // Alert 30 days before\n              isActive = true;\n            }\n          }\n\n          // Check by mileage\n          if (reminder.kilometraje_vencimiento && reminder.vehiculo_id) {\n            const vehicleRefuels = refuels.filter(r => r.vehiculo_id === reminder.vehiculo_id);\n            if (vehicleRefuels.length > 0) {\n              const currentKm = Math.max(...vehicleRefuels.map(r => r.kilometros_actuales));\n              kmUntil = reminder.kilometraje_vencimiento - currentKm;\n              if (kmUntil <= 1000) {\n                // Alert 1000 km before\n                isActive = true;\n              }\n            }\n          }\n          if (isActive) {\n            active.push({\n              ...reminder,\n              daysUntil,\n              kmUntil,\n              urgency: daysUntil !== null && daysUntil <= 7 || kmUntil !== null && kmUntil <= 500 ? 'high' : 'medium'\n            });\n          }\n        });\n\n        // Filter for high urgency reminders only for the dashboard\n        const highUrgency = active.filter(r => r.urgency === 'high');\n        setImmediateReminders(highUrgency.sort((a, b) => {\n          // Sort by daysUntil first, then kmUntil\n          if (a.daysUntil !== null && b.daysUntil !== null) {\n            return a.daysUntil - b.daysUntil;\n          }\n          if (a.kmUntil !== null && b.kmUntil !== null) {\n            return a.kmUntil - b.kmUntil;\n          }\n          return 0;\n        }));\n      } else {\n        // Mock data for development if electronAPI is not available\n        setImmediateReminders([{\n          id: 1,\n          vehiculo_id: 1,\n          titulo: 'Cambio de aceite',\n          descripcion: 'Cambio de aceite y filtro',\n          tipo: 'Mantenimiento',\n          fecha_vencimiento: format(new Date(), 'yyyy-MM-dd'),\n          // Today\n          kilometraje_vencimiento: 100,\n          es_recurrente: true,\n          intervalo_dias: 180,\n          intervalo_km: 10000,\n          activo: true,\n          vehiculo_nombre: 'Opel Corsa David',\n          daysUntil: 0,\n          kmUntil: 0,\n          urgency: 'high'\n        }, {\n          id: 2,\n          vehiculo_id: 1,\n          titulo: 'Revisión ITV',\n          descripcion: 'Próxima ITV',\n          tipo: 'ITV',\n          fecha_vencimiento: format(new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),\n          // 5 days from now\n          kilometraje_vencimiento: 250000,\n          es_recurrente: false,\n          activo: true,\n          vehiculo_nombre: 'Opel Corsa David',\n          daysUntil: 5,\n          kmUntil: 5000,\n          urgency: 'medium' // This won't show up with 'high' filter\n        }].filter(r => r.urgency === 'high')); // Filter mock data too\n      }\n    } catch (error) {\n      console.error('Error loading immediate reminders:', error);\n    }\n  };\n\n  // Calcular datos para gráfico de tendencia (últimos 6 meses)\n  const calculateTrendData = () => {\n    const sixMonthsAgo = subMonths(new Date(), 6);\n    const recentRefuels = refuels.filter(r => new Date(r.fecha) >= sixMonthsAgo);\n    const monthlyData = {};\n    recentRefuels.forEach(refuel => {\n      const month = format(new Date(refuel.fecha), 'MMM yyyy', {\n        locale: es\n      });\n      const monthKey = format(new Date(refuel.fecha), 'yyyy-MM');\n      if (!monthlyData[monthKey]) {\n        monthlyData[monthKey] = {\n          month,\n          cost: 0,\n          liters: 0,\n          monthKey\n        };\n      }\n      monthlyData[monthKey].cost += refuel.coste_total || 0;\n      monthlyData[monthKey].liters += refuel.litros || 0;\n    });\n    return Object.values(monthlyData).sort((a, b) => a.monthKey.localeCompare(b.monthKey));\n  };\n  const trendData = calculateTrendData();\n  useEffect(() => {\n    // Calcular estadísticas del dashboard\n    if (vehicles.length > 0 && refuels.length > 0) {\n      const totalRefuels = refuels.length;\n      const totalExpenses = expenses.length;\n      const totalFuelCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);\n      const totalExpenseCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n      const totalCost = totalFuelCost + totalExpenseCost;\n\n      // Calcular consumo promedio si hay datos suficientes\n      let avgConsumption = null;\n      if (refuels.length >= 2) {\n        const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n        const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales - sortedRefuels[0].kilometros_actuales;\n        const totalLiters = refuels.reduce((sum, refuel) => sum + (refuel.litros || 0), 0);\n        if (totalKm > 0) {\n          avgConsumption = (totalLiters / totalKm * 100).toFixed(1);\n        }\n      }\n      setDashboardStats({\n        totalRefuels,\n        totalExpenses,\n        totalFuelCost,\n        totalExpenseCost,\n        totalCost,\n        avgConsumption\n      });\n    }\n  }, [vehicles, refuels, expenses]);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd MMM yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n\n  // Helper functions for reminder icons and colors\n  const getTypeIcon = type => {\n    const typeConfig = {\n      'Mantenimiento': /*#__PURE__*/_jsxDEV(MaintenanceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 24\n      }, this),\n      'ITV': /*#__PURE__*/_jsxDEV(InspectionIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 14\n      }, this),\n      'Seguro': /*#__PURE__*/_jsxDEV(InsuranceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 17\n      }, this),\n      'Cambio_Aceite': /*#__PURE__*/_jsxDEV(FuelIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 24\n      }, this),\n      'Revision': /*#__PURE__*/_jsxDEV(ScheduleIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 19\n      }, this),\n      'Otros': /*#__PURE__*/_jsxDEV(NotificationIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 16\n      }, this)\n    };\n    return typeConfig[type] || /*#__PURE__*/_jsxDEV(NotificationIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 32\n    }, this);\n  };\n  const getUrgencyColor = urgency => {\n    switch (urgency) {\n      case 'high':\n        return 'error';\n      case 'medium':\n        return 'warning';\n      default:\n        return 'info';\n    }\n  };\n\n  // Obtener últimos repostajes y gastos\n  const recentRefuels = refuels.slice(0, 5);\n  const recentExpenses = expenses.slice(0, 5);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        fontWeight: \"bold\",\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefuelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate('/refuels/new'),\n          size: \"small\",\n          children: \"Nuevo Repostaje\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExpenseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate('/expenses/new'),\n          size: \"small\",\n          children: \"Nuevo Gasto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Veh\\xEDculos Activos\",\n          value: vehicles.length,\n          subtitle: \"Total registrados\",\n          icon: /*#__PURE__*/_jsxDEV(VehicleIcon, {\n            sx: {\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 19\n          }, this),\n          color: \"primary\",\n          onClick: () => navigate('/vehicles')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Repostajes\",\n          value: (dashboardStats === null || dashboardStats === void 0 ? void 0 : dashboardStats.totalRefuels) || 0,\n          subtitle: \"Registros totales\",\n          icon: /*#__PURE__*/_jsxDEV(RefuelIcon, {\n            sx: {\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 19\n          }, this),\n          color: \"success\",\n          onClick: () => navigate('/refuels')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Gasto Total\",\n          value: formatCurrency(dashboardStats === null || dashboardStats === void 0 ? void 0 : dashboardStats.totalCost),\n          subtitle: \"Combustible + gastos\",\n          icon: /*#__PURE__*/_jsxDEV(EuroIcon, {\n            sx: {\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 19\n          }, this),\n          color: \"warning\",\n          onClick: () => navigate('/expenses')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Consumo Promedio\",\n          value: dashboardStats !== null && dashboardStats !== void 0 && dashboardStats.avgConsumption ? `${dashboardStats.avgConsumption} L` : 'N/A',\n          subtitle: \"Por 100 km\",\n          icon: /*#__PURE__*/_jsxDEV(SpeedIcon, {\n            sx: {\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 19\n          }, this),\n          color: \"info\",\n          onClick: () => navigate('/statistics')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h2\",\n                children: \"Avisos Inmediatos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Ver todos los recordatorios\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => navigate('/settings') // Assuming reminders are under settings\n                  ,\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(NotificationIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), immediateReminders.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 3,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"No hay avisos inmediatos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 32\n                }, this),\n                onClick: () => navigate('/settings'),\n                size: \"small\",\n                children: \"Gestionar Recordatorios\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: immediateReminders.map((reminder, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                    children: getTypeIcon(reminder.tipo)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        component: \"span\",\n                        variant: \"body1\",\n                        children: reminder.titulo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: reminder.urgency === 'high' ? 'Urgente' : 'Próximo',\n                        color: getUrgencyColor(reminder.urgency),\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 425,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 29\n                    }, this),\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      component: \"div\",\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        component: \"span\",\n                        variant: \"body2\",\n                        color: \"textSecondary\",\n                        children: reminder.vehiculo_nombre\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 31\n                      }, this), reminder.daysUntil !== null && /*#__PURE__*/_jsxDEV(Typography, {\n                        component: \"div\",\n                        variant: \"caption\",\n                        color: \"textSecondary\",\n                        children: reminder.daysUntil <= 0 ? 'Vencido' : `${reminder.daysUntil} días restantes`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 438,\n                        columnNumber: 33\n                      }, this), reminder.kmUntil !== null && /*#__PURE__*/_jsxDEV(Typography, {\n                        component: \"div\",\n                        variant: \"caption\",\n                        color: \"textSecondary\",\n                        children: reminder.kmUntil <= 0 ? 'Kilometraje superado' : `${reminder.kmUntil} km restantes`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 443,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => navigate('/settings'),\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 452,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 23\n                }, this), index < immediateReminders.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 65\n                }, this)]\n              }, reminder.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h2\",\n                children: \"Mis Veh\\xEDculos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Agregar veh\\xEDculo\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => navigate('/vehicles'),\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), vehicles.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 3,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"No hay veh\\xEDculos registrados\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 32\n                }, this),\n                onClick: () => navigate('/vehicles'),\n                size: \"small\",\n                children: \"Agregar Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: vehicles.map((vehicle, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                    children: /*#__PURE__*/_jsxDEV(VehicleIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 504,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: vehicle.nombre,\n                    secondary: `${vehicle.marca} ${vehicle.modelo} - ${vehicle.matricula}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: vehicle.tipo_combustible || 'N/A',\n                    size: \"small\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 23\n                }, this), index < vehicles.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 55\n                }, this)]\n              }, vehicle.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h2\",\n                children: \"\\xDAltimos Repostajes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Ver todos\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => navigate('/refuels'),\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this), recentRefuels.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 3,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"No hay repostajes registrados\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 32\n                }, this),\n                onClick: () => navigate('/refuels'),\n                size: \"small\",\n                children: \"Registrar Repostaje\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: recentRefuels.map((refuel, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                    children: /*#__PURE__*/_jsxDEV(RefuelIcon, {\n                      color: \"success\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: `${refuel.litros}L - ${formatCurrency(refuel.coste_total)}`,\n                    secondary: `${formatDate(refuel.fecha)} - ${refuel.gasolinera || 'Sin especificar'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 23\n                }, this), index < recentRefuels.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 60\n                }, this)]\n              }, refuel.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h2\",\n                children: \"\\xDAltimos Gastos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Ver todos\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => navigate('/expenses'),\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this), recentExpenses.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 3,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"No hay gastos registrados\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 32\n                }, this),\n                onClick: () => navigate('/expenses'),\n                size: \"small\",\n                children: \"Registrar Gasto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: recentExpenses.map((expense, index) => {\n                var _expense$descripcion, _expense$descripcion2;\n                return /*#__PURE__*/_jsxDEV(React.Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(ExpenseIcon, {\n                        color: \"warning\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 619,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: `${expense.tipo_gasto} - ${formatCurrency(expense.coste)}`,\n                      secondary: `${formatDate(expense.fecha)} - ${(_expense$descripcion = expense.descripcion) === null || _expense$descripcion === void 0 ? void 0 : _expense$descripcion.substring(0, 30)}${((_expense$descripcion2 = expense.descripcion) === null || _expense$descripcion2 === void 0 ? void 0 : _expense$descripcion2.length) > 30 ? '...' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 23\n                  }, this), index < recentExpenses.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 61\n                  }, this)]\n                }, expense.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this), trendData.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Tendencia de Gastos (\\xDAltimos 6 meses)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          height: 250,\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: trendData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                yAxisId: \"left\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                yAxisId: \"right\",\n                orientation: \"right\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                formatter: (value, name) => [name === 'cost' ? formatCurrency(value) : `${value}L`, name === 'cost' ? 'Coste' : 'Litros']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"cost\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                name: \"Coste\",\n                yAxisId: \"left\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"liters\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                name: \"Litros\",\n                yAxisId: \"right\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"2KKYel09x3Bn8EdtZAOls1y7DsE=\", false, function () {\n  return [useNavigate, useApp];\n});\n_c2 = Dashboard;\nexport default Dashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"StatCard\");\n$RefreshReg$(_c2, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Chip", "List", "ListItem", "ListItemText", "ListItemIcon", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "DirectionsCar", "VehicleIcon", "LocalGasStation", "RefuelIcon", "Receipt", "ExpenseIcon", "TrendingUp", "TrendingUpIcon", "Add", "AddIcon", "Speed", "SpeedIcon", "Euro", "EuroIcon", "Timeline", "TimelineIcon", "Notifications", "NotificationIcon", "Warning", "WarningIcon", "Schedule", "ScheduleIcon", "Build", "MaintenanceIcon", "Security", "InsuranceIcon", "Assignment", "InspectionIcon", "FuelIcon", "useNavigate", "useApp", "format", "subMonths", "differenceInDays", "es", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "ChartTooltip", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "StatCard", "title", "value", "subtitle", "icon", "color", "onClick", "sx", "height", "cursor", "transition", "transform", "boxShadow", "children", "display", "alignItems", "justifyContent", "gutterBottom", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "backgroundColor", "borderRadius", "p", "_c", "Dashboard", "_s", "navigate", "vehicles", "refuels", "expenses", "loadStatistics", "statistics", "dashboardStats", "setDashboardStats", "immediateR<PERSON>inders", "setImmediateReminders", "loadImmediateReminders", "window", "electronAPI", "allReminders", "get<PERSON><PERSON><PERSON>s", "now", "Date", "active", "for<PERSON>ach", "reminder", "activo", "isActive", "daysUntil", "kmUntil", "fecha_vencimiento", "dueDate", "kilometraje_vencimiento", "vehiculo_id", "vehicleRefuels", "filter", "r", "length", "currentKm", "Math", "max", "map", "kilometros_actuales", "push", "urgency", "highUrgency", "sort", "a", "b", "id", "titulo", "descripcion", "tipo", "es_recurrente", "intervalo_dias", "intervalo_km", "vehiculo_nombre", "error", "console", "calculateTrendData", "sixMonthsAgo", "recentRefuels", "fecha", "monthlyData", "refuel", "month", "locale", "<PERSON><PERSON><PERSON>", "cost", "liters", "coste_total", "litros", "Object", "values", "localeCompare", "trendData", "totalRefuels", "totalExpenses", "totalFuelCost", "reduce", "sum", "totalExpenseCost", "expense", "coste", "totalCost", "avgConsumption", "sortedRefuels", "totalKm", "totalLiters", "toFixed", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "getTypeIcon", "type", "typeConfig", "getUrgencyColor", "slice", "recentExpenses", "mb", "fontWeight", "gap", "startIcon", "size", "container", "spacing", "item", "xs", "sm", "md", "textAlign", "py", "dense", "index", "Fragment", "primary", "label", "secondary", "ListItemSecondaryAction", "EditIcon", "vehicle", "nombre", "marca", "modelo", "matricula", "tipo_combustible", "gasolinera", "_expense$descripcion", "_expense$descripcion2", "tipo_gasto", "substring", "mt", "width", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "yAxisId", "orientation", "formatter", "name", "stroke", "strokeWidth", "_c2", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Dashboard/Dashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  Chip,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Divider,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  DirectionsCar as VehicleIcon,\n  LocalGasStation as RefuelIcon,\n  Receipt as ExpenseIcon,\n  TrendingUp as TrendingUpIcon,\n  Add as AddIcon,\n  Speed as SpeedIcon,\n  Euro as EuroIcon,\n  Timeline as TimelineIcon,\n  Notifications as NotificationIcon, // Added\n  Warning as WarningIcon, // Added\n  Schedule as ScheduleIcon, // Added\n  Build as MaintenanceIcon, // Added\n  Security as InsuranceIcon, // Added\n  Assignment as InspectionIcon, // Added\n  LocalGasStation as FuelIcon, // Added\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useApp } from '../../context/AppContext';\nimport { format, subMonths, differenceInDays } from 'date-fns'; // Added differenceInDays\nimport { es } from 'date-fns/locale';\nimport {\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip as ChartTooltip,\n  ResponsiveContainer,\n} from 'recharts';\n\n// Componente para tarjetas de estadísticas\nconst StatCard = ({ title, value, subtitle, icon, color = 'primary', onClick }) => (\n  <Card \n    sx={{ \n      height: '100%', \n      cursor: onClick ? 'pointer' : 'default',\n      transition: 'transform 0.2s, box-shadow 0.2s',\n      '&:hover': onClick ? {\n        transform: 'translateY(-2px)',\n        boxShadow: 4,\n      } : {},\n    }}\n    onClick={onClick}\n  >\n    <CardContent>\n      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n        <Box>\n          <Typography color=\"textSecondary\" gutterBottom variant=\"body2\">\n            {title}\n          </Typography>\n          <Typography variant=\"h4\" component=\"div\" color={color}>\n            {value}\n          </Typography>\n          {subtitle && (\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              {subtitle}\n            </Typography>\n          )}\n        </Box>\n        <Box\n          sx={{\n            backgroundColor: `${color}.light`,\n            borderRadius: '50%',\n            p: 1.5,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n          }}\n        >\n          {icon}\n        </Box>\n      </Box>\n    </CardContent>\n  </Card>\n);\n\nconst Dashboard = () => {\n  const navigate = useNavigate();\n  const { vehicles, refuels, expenses, loadStatistics, statistics } = useApp();\n  const [dashboardStats, setDashboardStats] = useState(null);\n  const [immediateReminders, setImmediateReminders] = useState([]); // New state for immediate reminders\n\n  useEffect(() => {\n    loadStatistics();\n    loadImmediateReminders(); // Call new function to load immediate reminders\n  }, [refuels]); // Added refuels as dependency for loadImmediateReminders\n\n  // Function to load and filter immediate reminders\n  const loadImmediateReminders = async () => {\n    try {\n      if (window.electronAPI) {\n        const allReminders = await window.electronAPI.getReminders();\n        const now = new Date();\n        const active = [];\n\n        allReminders.forEach(reminder => {\n          if (!reminder.activo) return;\n\n          let isActive = false;\n          let daysUntil = null;\n          let kmUntil = null;\n\n          // Check by date\n          if (reminder.fecha_vencimiento) {\n            const dueDate = new Date(reminder.fecha_vencimiento);\n            daysUntil = differenceInDays(dueDate, now);\n            if (daysUntil <= 30) { // Alert 30 days before\n              isActive = true;\n            }\n          }\n\n          // Check by mileage\n          if (reminder.kilometraje_vencimiento && reminder.vehiculo_id) {\n            const vehicleRefuels = refuels.filter(r => r.vehiculo_id === reminder.vehiculo_id);\n            if (vehicleRefuels.length > 0) {\n              const currentKm = Math.max(...vehicleRefuels.map(r => r.kilometros_actuales));\n              kmUntil = reminder.kilometraje_vencimiento - currentKm;\n              if (kmUntil <= 1000) { // Alert 1000 km before\n                isActive = true;\n              }\n            }\n          }\n\n          if (isActive) {\n            active.push({\n              ...reminder,\n              daysUntil,\n              kmUntil,\n              urgency: (daysUntil !== null && daysUntil <= 7) || (kmUntil !== null && kmUntil <= 500) ? 'high' : 'medium'\n            });\n          }\n        });\n\n        // Filter for high urgency reminders only for the dashboard\n        const highUrgency = active.filter(r => r.urgency === 'high');\n        setImmediateReminders(highUrgency.sort((a, b) => {\n          // Sort by daysUntil first, then kmUntil\n          if (a.daysUntil !== null && b.daysUntil !== null) {\n            return a.daysUntil - b.daysUntil;\n          }\n          if (a.kmUntil !== null && b.kmUntil !== null) {\n            return a.kmUntil - b.kmUntil;\n          }\n          return 0;\n        }));\n\n      } else {\n        // Mock data for development if electronAPI is not available\n        setImmediateReminders([\n          {\n            id: 1,\n            vehiculo_id: 1,\n            titulo: 'Cambio de aceite',\n            descripcion: 'Cambio de aceite y filtro',\n            tipo: 'Mantenimiento',\n            fecha_vencimiento: format(new Date(), 'yyyy-MM-dd'), // Today\n            kilometraje_vencimiento: 100,\n            es_recurrente: true,\n            intervalo_dias: 180,\n            intervalo_km: 10000,\n            activo: true,\n            vehiculo_nombre: 'Opel Corsa David',\n            daysUntil: 0,\n            kmUntil: 0,\n            urgency: 'high'\n          },\n          {\n            id: 2,\n            vehiculo_id: 1,\n            titulo: 'Revisión ITV',\n            descripcion: 'Próxima ITV',\n            tipo: 'ITV',\n            fecha_vencimiento: format(new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'), // 5 days from now\n            kilometraje_vencimiento: 250000,\n            es_recurrente: false,\n            activo: true,\n            vehiculo_nombre: 'Opel Corsa David',\n            daysUntil: 5,\n            kmUntil: 5000,\n            urgency: 'medium' // This won't show up with 'high' filter\n          }\n        ].filter(r => r.urgency === 'high')); // Filter mock data too\n      }\n    } catch (error) {\n      console.error('Error loading immediate reminders:', error);\n    }\n  };\n\n  // Calcular datos para gráfico de tendencia (últimos 6 meses)\n  const calculateTrendData = () => {\n    const sixMonthsAgo = subMonths(new Date(), 6);\n    const recentRefuels = refuels.filter(r => new Date(r.fecha) >= sixMonthsAgo);\n    \n    const monthlyData = {};\n    recentRefuels.forEach(refuel => {\n      const month = format(new Date(refuel.fecha), 'MMM yyyy', { locale: es });\n      const monthKey = format(new Date(refuel.fecha), 'yyyy-MM');\n      if (!monthlyData[monthKey]) {\n        monthlyData[monthKey] = { month, cost: 0, liters: 0, monthKey };\n      }\n      monthlyData[monthKey].cost += refuel.coste_total || 0;\n      monthlyData[monthKey].liters += refuel.litros || 0;\n    });\n\n    return Object.values(monthlyData).sort((a, b) => \n      a.monthKey.localeCompare(b.monthKey)\n    );\n  };\n\n  const trendData = calculateTrendData();\n\n  useEffect(() => {\n    // Calcular estadísticas del dashboard\n    if (vehicles.length > 0 && refuels.length > 0) {\n      const totalRefuels = refuels.length;\n      const totalExpenses = expenses.length;\n      const totalFuelCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);\n      const totalExpenseCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n      const totalCost = totalFuelCost + totalExpenseCost;\n\n      // Calcular consumo promedio si hay datos suficientes\n      let avgConsumption = null;\n      if (refuels.length >= 2) {\n        const sortedRefuels = [...refuels].sort((a, b) => \n          new Date(a.fecha) - new Date(b.fecha)\n        );\n        const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales - \n                       sortedRefuels[0].kilometros_actuales;\n        const totalLiters = refuels.reduce((sum, refuel) => sum + (refuel.litros || 0), 0);\n        \n        if (totalKm > 0) {\n          avgConsumption = (totalLiters / totalKm * 100).toFixed(1);\n        }\n      }\n\n      setDashboardStats({\n        totalRefuels,\n        totalExpenses,\n        totalFuelCost,\n        totalExpenseCost,\n        totalCost,\n        avgConsumption,\n      });\n    }\n  }, [vehicles, refuels, expenses]);\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  const formatDate = (dateString) => {\n    try {\n      return format(new Date(dateString), 'dd MMM yyyy', { locale: es });\n    } catch {\n      return dateString;\n    }\n  };\n\n  // Helper functions for reminder icons and colors\n  const getTypeIcon = (type) => {\n    const typeConfig = {\n      'Mantenimiento': <MaintenanceIcon />,\n      'ITV': <InspectionIcon />,\n      'Seguro': <InsuranceIcon />,\n      'Cambio_Aceite': <FuelIcon />,\n      'Revision': <ScheduleIcon />,\n      'Otros': <NotificationIcon />\n    };\n    return typeConfig[type] || <NotificationIcon />;\n  };\n\n  const getUrgencyColor = (urgency) => {\n    switch (urgency) {\n      case 'high': return 'error';\n      case 'medium': return 'warning';\n      default: return 'info';\n    }\n  };\n\n  // Obtener últimos repostajes y gastos\n  const recentRefuels = refuels.slice(0, 5);\n  const recentExpenses = expenses.slice(0, 5);\n\n  return (\n    <Box>\n      {/* Encabezado con acciones rápidas */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n        <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\">\n          Dashboard\n        </Typography>\n        <Box display=\"flex\" gap={1}>\n          <Button\n            variant=\"contained\"\n            startIcon={<RefuelIcon />}\n            onClick={() => navigate('/refuels/new')}\n            size=\"small\"\n          >\n            Nuevo Repostaje\n          </Button>\n          <Button\n            variant=\"outlined\"\n            startIcon={<ExpenseIcon />}\n            onClick={() => navigate('/expenses/new')}\n            size=\"small\"\n          >\n            Nuevo Gasto\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Tarjetas de estadísticas principales */}\n      <Grid container spacing={3} mb={4}>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Vehículos Activos\"\n            value={vehicles.length}\n            subtitle=\"Total registrados\"\n            icon={<VehicleIcon sx={{ color: 'white' }} />}\n            color=\"primary\"\n            onClick={() => navigate('/vehicles')}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Total Repostajes\"\n            value={dashboardStats?.totalRefuels || 0}\n            subtitle=\"Registros totales\"\n            icon={<RefuelIcon sx={{ color: 'white' }} />}\n            color=\"success\"\n            onClick={() => navigate('/refuels')}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Gasto Total\"\n            value={formatCurrency(dashboardStats?.totalCost)}\n            subtitle=\"Combustible + gastos\"\n            icon={<EuroIcon sx={{ color: 'white' }} />}\n            color=\"warning\"\n            onClick={() => navigate('/expenses')}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatCard\n            title=\"Consumo Promedio\"\n            value={dashboardStats?.avgConsumption ? `${dashboardStats.avgConsumption} L` : 'N/A'}\n            subtitle=\"Por 100 km\"\n            icon={<SpeedIcon sx={{ color: 'white' }} />}\n            color=\"info\"\n            onClick={() => navigate('/statistics')}\n          />\n        </Grid>\n      </Grid>\n\n      {/* Contenido principal */}\n      <Grid container spacing={3}>\n        {/* Immediate Reminders */}\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h6\" component=\"h2\">\n                  Avisos Inmediatos\n                </Typography>\n                <Tooltip title=\"Ver todos los recordatorios\">\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => navigate('/settings')} // Assuming reminders are under settings\n                    color=\"primary\"\n                  >\n                    <NotificationIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n\n              {immediateReminders.length === 0 ? (\n                <Box textAlign=\"center\" py={3}>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    No hay avisos inmediatos\n                  </Typography>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<AddIcon />}\n                    onClick={() => navigate('/settings')}\n                    size=\"small\"\n                  >\n                    Gestionar Recordatorios\n                  </Button>\n                </Box>\n              ) : (\n                <List dense>\n                  {immediateReminders.map((reminder, index) => (\n                    <React.Fragment key={reminder.id}>\n                      <ListItem>\n                        <ListItemIcon>\n                          {getTypeIcon(reminder.tipo)}\n                        </ListItemIcon>\n                        <ListItemText\n                          primary={\n                            <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                              <Typography component=\"span\" variant=\"body1\">\n                                {reminder.titulo}\n                              </Typography>\n                              <Chip\n                                label={reminder.urgency === 'high' ? 'Urgente' : 'Próximo'}\n                                color={getUrgencyColor(reminder.urgency)}\n                                size=\"small\"\n                              />\n                            </Box>\n                          }\n                          secondary={\n                            <Box component=\"div\">\n                              <Typography component=\"span\" variant=\"body2\" color=\"textSecondary\">\n                                {reminder.vehiculo_nombre}\n                              </Typography>\n                              {reminder.daysUntil !== null && (\n                                <Typography component=\"div\" variant=\"caption\" color=\"textSecondary\">\n                                  {reminder.daysUntil <= 0 ? 'Vencido' : `${reminder.daysUntil} días restantes`}\n                                </Typography>\n                              )}\n                              {reminder.kmUntil !== null && (\n                                <Typography component=\"div\" variant=\"caption\" color=\"textSecondary\">\n                                  {reminder.kmUntil <= 0 ? 'Kilometraje superado' : `${reminder.kmUntil} km restantes`}\n                                </Typography>\n                              )}\n                            </Box>\n                          }\n                        />\n                        <ListItemSecondaryAction>\n                          <IconButton size=\"small\" onClick={() => navigate('/settings')}>\n                            <EditIcon />\n                          </IconButton>\n                        </ListItemSecondaryAction>\n                      </ListItem>\n                      {index < immediateReminders.length - 1 && <Divider />}\n                    </React.Fragment>\n                  ))}\n                </List>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Vehículos */}\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h6\" component=\"h2\">\n                  Mis Vehículos\n                </Typography>\n                <Tooltip title=\"Agregar vehículo\">\n                  <IconButton \n                    size=\"small\" \n                    onClick={() => navigate('/vehicles')}\n                    color=\"primary\"\n                  >\n                    <AddIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n              \n              {vehicles.length === 0 ? (\n                <Box textAlign=\"center\" py={3}>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    No hay vehículos registrados\n                  </Typography>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<AddIcon />}\n                    onClick={() => navigate('/vehicles')}\n                    size=\"small\"\n                  >\n                    Agregar Vehículo\n                  </Button>\n                </Box>\n              ) : (\n                <List dense>\n                  {vehicles.map((vehicle, index) => (\n                    <React.Fragment key={vehicle.id}>\n                      <ListItem>\n                        <ListItemIcon>\n                          <VehicleIcon color=\"primary\" />\n                        </ListItemIcon>\n                        <ListItemText\n                          primary={vehicle.nombre}\n                          secondary={`${vehicle.marca} ${vehicle.modelo} - ${vehicle.matricula}`}\n                        />\n                        <Chip \n                          label={vehicle.tipo_combustible || 'N/A'} \n                          size=\"small\" \n                          variant=\"outlined\"\n                        />\n                      </ListItem>\n                      {index < vehicles.length - 1 && <Divider />}\n                    </React.Fragment>\n                  ))}\n                </List>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Últimos Repostajes */}\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h6\" component=\"h2\">\n                  Últimos Repostajes\n                </Typography>\n                <Tooltip title=\"Ver todos\">\n                  <IconButton \n                    size=\"small\" \n                    onClick={() => navigate('/refuels')}\n                    color=\"primary\"\n                  >\n                    <TimelineIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n              \n              {recentRefuels.length === 0 ? (\n                <Box textAlign=\"center\" py={3}>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    No hay repostajes registrados\n                  </Typography>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<AddIcon />}\n                    onClick={() => navigate('/refuels')}\n                    size=\"small\"\n                  >\n                    Registrar Repostaje\n                  </Button>\n                </Box>\n              ) : (\n                <List dense>\n                  {recentRefuels.map((refuel, index) => (\n                    <React.Fragment key={refuel.id}>\n                      <ListItem>\n                        <ListItemIcon>\n                          <RefuelIcon color=\"success\" />\n                        </ListItemIcon>\n                        <ListItemText\n                          primary={`${refuel.litros}L - ${formatCurrency(refuel.coste_total)}`}\n                          secondary={`${formatDate(refuel.fecha)} - ${refuel.gasolinera || 'Sin especificar'}`}\n                        />\n                      </ListItem>\n                      {index < recentRefuels.length - 1 && <Divider />}\n                    </React.Fragment>\n                  ))}\n                </List>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Últimos Gastos */}\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h6\" component=\"h2\">\n                  Últimos Gastos\n                </Typography>\n                <Tooltip title=\"Ver todos\">\n                  <IconButton \n                    size=\"small\" \n                    onClick={() => navigate('/expenses')}\n                    color=\"primary\"\n                  >\n                    <TimelineIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n              \n              {recentExpenses.length === 0 ? (\n                <Box textAlign=\"center\" py={3}>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    No hay gastos registrados\n                  </Typography>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<AddIcon />}\n                    onClick={() => navigate('/expenses')}\n                    size=\"small\"\n                  >\n                    Registrar Gasto\n                  </Button>\n                </Box>\n              ) : (\n                <List dense>\n                  {recentExpenses.map((expense, index) => (\n                    <React.Fragment key={expense.id}>\n                      <ListItem>\n                        <ListItemIcon>\n                          <ExpenseIcon color=\"warning\" />\n                        </ListItemIcon>\n                        <ListItemText\n                          primary={`${expense.tipo_gasto} - ${formatCurrency(expense.coste)}`}\n                          secondary={`${formatDate(expense.fecha)} - ${expense.descripcion?.substring(0, 30)}${expense.descripcion?.length > 30 ? '...' : ''}`}\n                        />\n                      </ListItem>\n                      {index < recentExpenses.length - 1 && <Divider />}\n                    </React.Fragment>\n                  ))}\n                </List>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Gráfico de tendencia */}\n      {trendData.length > 0 && (\n        <Card sx={{ mt: 4 }}>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Tendencia de Gastos (Últimos 6 meses)\n            </Typography>\n            <Box height={250}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart data={trendData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"month\" />\n                  <YAxis yAxisId=\"left\" />\n                  <YAxis yAxisId=\"right\" orientation=\"right\" />\n                  <ChartTooltip \n                    formatter={(value, name) => [\n                      name === 'cost' ? formatCurrency(value) : `${value}L`,\n                      name === 'cost' ? 'Coste' : 'Litros'\n                    ]}\n                  />\n                  <Line \n                    type=\"monotone\" \n                    dataKey=\"cost\" \n                    stroke=\"#8884d8\" \n                    strokeWidth={2}\n                    name=\"Coste\"\n                    yAxisId=\"left\"\n                  />\n                  <Line \n                    type=\"monotone\" \n                    dataKey=\"liters\" \n                    stroke=\"#82ca9d\" \n                    strokeWidth={2}\n                    name=\"Litros\"\n                    yAxisId=\"right\"\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </Box>\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,aAAa,IAAIC,WAAW,EAC5BC,eAAe,IAAIC,UAAU,EAC7BC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,GAAG,IAAIC,OAAO,EACdC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,aAAa,IAAIC,gBAAgB;AAAE;AACnCC,OAAO,IAAIC,WAAW;AAAE;AACxBC,QAAQ,IAAIC,YAAY;AAAE;AAC1BC,KAAK,IAAIC,eAAe;AAAE;AAC1BC,QAAQ,IAAIC,aAAa;AAAE;AAC3BC,UAAU,IAAIC,cAAc;AAAE;AAC9BzB,eAAe,IAAI0B,QAAQ,CAAE;AAAA,OACxB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,UAAU,CAAC,CAAC;AAChE,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbxC,OAAO,IAAIyC,YAAY,EACvBC,mBAAmB,QACd,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,IAAI;EAAEC,KAAK,GAAG,SAAS;EAAEC;AAAQ,CAAC,kBAC5EP,OAAA,CAACvD,IAAI;EACH+D,EAAE,EAAE;IACFC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEH,OAAO,GAAG,SAAS,GAAG,SAAS;IACvCI,UAAU,EAAE,iCAAiC;IAC7C,SAAS,EAAEJ,OAAO,GAAG;MACnBK,SAAS,EAAE,kBAAkB;MAC7BC,SAAS,EAAE;IACb,CAAC,GAAG,CAAC;EACP,CAAE;EACFN,OAAO,EAAEA,OAAQ;EAAAO,QAAA,eAEjBd,OAAA,CAACtD,WAAW;IAAAoE,QAAA,eACVd,OAAA,CAACzD,GAAG;MAACwE,OAAO,EAAC,MAAM;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAAAH,QAAA,gBACpEd,OAAA,CAACzD,GAAG;QAAAuE,QAAA,gBACFd,OAAA,CAACrD,UAAU;UAAC2D,KAAK,EAAC,eAAe;UAACY,YAAY;UAACC,OAAO,EAAC,OAAO;UAAAL,QAAA,EAC3DZ;QAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACbvB,OAAA,CAACrD,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACK,SAAS,EAAC,KAAK;UAAClB,KAAK,EAAEA,KAAM;UAAAQ,QAAA,EACnDX;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EACZnB,QAAQ,iBACPJ,OAAA,CAACrD,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACb,KAAK,EAAC,eAAe;UAAAQ,QAAA,EAC9CV;QAAQ;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNvB,OAAA,CAACzD,GAAG;QACFiE,EAAE,EAAE;UACFiB,eAAe,EAAE,GAAGnB,KAAK,QAAQ;UACjCoB,YAAY,EAAE,KAAK;UACnBC,CAAC,EAAE,GAAG;UACNZ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAH,QAAA,EAEDT;MAAI;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACP;AAACK,EAAA,GA3CI3B,QAAQ;AA6Cd,MAAM4B,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8C,QAAQ;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,cAAc;IAAEC;EAAW,CAAC,GAAGjD,MAAM,CAAC,CAAC;EAC5E,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGhG,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAElED,SAAS,CAAC,MAAM;IACd8F,cAAc,CAAC,CAAC;IAChBM,sBAAsB,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;EACA,MAAMQ,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,IAAIC,MAAM,CAACC,WAAW,EAAE;QACtB,MAAMC,YAAY,GAAG,MAAMF,MAAM,CAACC,WAAW,CAACE,YAAY,CAAC,CAAC;QAC5D,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,MAAM,GAAG,EAAE;QAEjBJ,YAAY,CAACK,OAAO,CAACC,QAAQ,IAAI;UAC/B,IAAI,CAACA,QAAQ,CAACC,MAAM,EAAE;UAEtB,IAAIC,QAAQ,GAAG,KAAK;UACpB,IAAIC,SAAS,GAAG,IAAI;UACpB,IAAIC,OAAO,GAAG,IAAI;;UAElB;UACA,IAAIJ,QAAQ,CAACK,iBAAiB,EAAE;YAC9B,MAAMC,OAAO,GAAG,IAAIT,IAAI,CAACG,QAAQ,CAACK,iBAAiB,CAAC;YACpDF,SAAS,GAAG/D,gBAAgB,CAACkE,OAAO,EAAEV,GAAG,CAAC;YAC1C,IAAIO,SAAS,IAAI,EAAE,EAAE;cAAE;cACrBD,QAAQ,GAAG,IAAI;YACjB;UACF;;UAEA;UACA,IAAIF,QAAQ,CAACO,uBAAuB,IAAIP,QAAQ,CAACQ,WAAW,EAAE;YAC5D,MAAMC,cAAc,GAAG1B,OAAO,CAAC2B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,WAAW,KAAKR,QAAQ,CAACQ,WAAW,CAAC;YAClF,IAAIC,cAAc,CAACG,MAAM,GAAG,CAAC,EAAE;cAC7B,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGN,cAAc,CAACO,GAAG,CAACL,CAAC,IAAIA,CAAC,CAACM,mBAAmB,CAAC,CAAC;cAC7Eb,OAAO,GAAGJ,QAAQ,CAACO,uBAAuB,GAAGM,SAAS;cACtD,IAAIT,OAAO,IAAI,IAAI,EAAE;gBAAE;gBACrBF,QAAQ,GAAG,IAAI;cACjB;YACF;UACF;UAEA,IAAIA,QAAQ,EAAE;YACZJ,MAAM,CAACoB,IAAI,CAAC;cACV,GAAGlB,QAAQ;cACXG,SAAS;cACTC,OAAO;cACPe,OAAO,EAAGhB,SAAS,KAAK,IAAI,IAAIA,SAAS,IAAI,CAAC,IAAMC,OAAO,KAAK,IAAI,IAAIA,OAAO,IAAI,GAAI,GAAG,MAAM,GAAG;YACrG,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;;QAEF;QACA,MAAMgB,WAAW,GAAGtB,MAAM,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACQ,OAAO,KAAK,MAAM,CAAC;QAC5D7B,qBAAqB,CAAC8B,WAAW,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UAC/C;UACA,IAAID,CAAC,CAACnB,SAAS,KAAK,IAAI,IAAIoB,CAAC,CAACpB,SAAS,KAAK,IAAI,EAAE;YAChD,OAAOmB,CAAC,CAACnB,SAAS,GAAGoB,CAAC,CAACpB,SAAS;UAClC;UACA,IAAImB,CAAC,CAAClB,OAAO,KAAK,IAAI,IAAImB,CAAC,CAACnB,OAAO,KAAK,IAAI,EAAE;YAC5C,OAAOkB,CAAC,CAAClB,OAAO,GAAGmB,CAAC,CAACnB,OAAO;UAC9B;UACA,OAAO,CAAC;QACV,CAAC,CAAC,CAAC;MAEL,CAAC,MAAM;QACL;QACAd,qBAAqB,CAAC,CACpB;UACEkC,EAAE,EAAE,CAAC;UACLhB,WAAW,EAAE,CAAC;UACdiB,MAAM,EAAE,kBAAkB;UAC1BC,WAAW,EAAE,2BAA2B;UACxCC,IAAI,EAAE,eAAe;UACrBtB,iBAAiB,EAAEnE,MAAM,CAAC,IAAI2D,IAAI,CAAC,CAAC,EAAE,YAAY,CAAC;UAAE;UACrDU,uBAAuB,EAAE,GAAG;UAC5BqB,aAAa,EAAE,IAAI;UACnBC,cAAc,EAAE,GAAG;UACnBC,YAAY,EAAE,KAAK;UACnB7B,MAAM,EAAE,IAAI;UACZ8B,eAAe,EAAE,kBAAkB;UACnC5B,SAAS,EAAE,CAAC;UACZC,OAAO,EAAE,CAAC;UACVe,OAAO,EAAE;QACX,CAAC,EACD;UACEK,EAAE,EAAE,CAAC;UACLhB,WAAW,EAAE,CAAC;UACdiB,MAAM,EAAE,cAAc;UACtBC,WAAW,EAAE,aAAa;UAC1BC,IAAI,EAAE,KAAK;UACXtB,iBAAiB,EAAEnE,MAAM,CAAC,IAAI2D,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,YAAY,CAAC;UAAE;UACzFW,uBAAuB,EAAE,MAAM;UAC/BqB,aAAa,EAAE,KAAK;UACpB3B,MAAM,EAAE,IAAI;UACZ8B,eAAe,EAAE,kBAAkB;UACnC5B,SAAS,EAAE,CAAC;UACZC,OAAO,EAAE,IAAI;UACbe,OAAO,EAAE,QAAQ,CAAC;QACpB,CAAC,CACF,CAACT,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACQ,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;MACxC;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D;EACF,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,YAAY,GAAGhG,SAAS,CAAC,IAAI0D,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,MAAMuC,aAAa,GAAGrD,OAAO,CAAC2B,MAAM,CAACC,CAAC,IAAI,IAAId,IAAI,CAACc,CAAC,CAAC0B,KAAK,CAAC,IAAIF,YAAY,CAAC;IAE5E,MAAMG,WAAW,GAAG,CAAC,CAAC;IACtBF,aAAa,CAACrC,OAAO,CAACwC,MAAM,IAAI;MAC9B,MAAMC,KAAK,GAAGtG,MAAM,CAAC,IAAI2D,IAAI,CAAC0C,MAAM,CAACF,KAAK,CAAC,EAAE,UAAU,EAAE;QAAEI,MAAM,EAAEpG;MAAG,CAAC,CAAC;MACxE,MAAMqG,QAAQ,GAAGxG,MAAM,CAAC,IAAI2D,IAAI,CAAC0C,MAAM,CAACF,KAAK,CAAC,EAAE,SAAS,CAAC;MAC1D,IAAI,CAACC,WAAW,CAACI,QAAQ,CAAC,EAAE;QAC1BJ,WAAW,CAACI,QAAQ,CAAC,GAAG;UAAEF,KAAK;UAAEG,IAAI,EAAE,CAAC;UAAEC,MAAM,EAAE,CAAC;UAAEF;QAAS,CAAC;MACjE;MACAJ,WAAW,CAACI,QAAQ,CAAC,CAACC,IAAI,IAAIJ,MAAM,CAACM,WAAW,IAAI,CAAC;MACrDP,WAAW,CAACI,QAAQ,CAAC,CAACE,MAAM,IAAIL,MAAM,CAACO,MAAM,IAAI,CAAC;IACpD,CAAC,CAAC;IAEF,OAAOC,MAAM,CAACC,MAAM,CAACV,WAAW,CAAC,CAACjB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC1CD,CAAC,CAACoB,QAAQ,CAACO,aAAa,CAAC1B,CAAC,CAACmB,QAAQ,CACrC,CAAC;EACH,CAAC;EAED,MAAMQ,SAAS,GAAGhB,kBAAkB,CAAC,CAAC;EAEtC/I,SAAS,CAAC,MAAM;IACd;IACA,IAAI2F,QAAQ,CAAC8B,MAAM,GAAG,CAAC,IAAI7B,OAAO,CAAC6B,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMuC,YAAY,GAAGpE,OAAO,CAAC6B,MAAM;MACnC,MAAMwC,aAAa,GAAGpE,QAAQ,CAAC4B,MAAM;MACrC,MAAMyC,aAAa,GAAGtE,OAAO,CAACuE,MAAM,CAAC,CAACC,GAAG,EAAEhB,MAAM,KAAKgB,GAAG,IAAIhB,MAAM,CAACM,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MACzF,MAAMW,gBAAgB,GAAGxE,QAAQ,CAACsE,MAAM,CAAC,CAACC,GAAG,EAAEE,OAAO,KAAKF,GAAG,IAAIE,OAAO,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MACzF,MAAMC,SAAS,GAAGN,aAAa,GAAGG,gBAAgB;;MAElD;MACA,IAAII,cAAc,GAAG,IAAI;MACzB,IAAI7E,OAAO,CAAC6B,MAAM,IAAI,CAAC,EAAE;QACvB,MAAMiD,aAAa,GAAG,CAAC,GAAG9E,OAAO,CAAC,CAACsC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC3C,IAAI1B,IAAI,CAACyB,CAAC,CAACe,KAAK,CAAC,GAAG,IAAIxC,IAAI,CAAC0B,CAAC,CAACc,KAAK,CACtC,CAAC;QACD,MAAMyB,OAAO,GAAGD,aAAa,CAACA,aAAa,CAACjD,MAAM,GAAG,CAAC,CAAC,CAACK,mBAAmB,GAC5D4C,aAAa,CAAC,CAAC,CAAC,CAAC5C,mBAAmB;QACnD,MAAM8C,WAAW,GAAGhF,OAAO,CAACuE,MAAM,CAAC,CAACC,GAAG,EAAEhB,MAAM,KAAKgB,GAAG,IAAIhB,MAAM,CAACO,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAElF,IAAIgB,OAAO,GAAG,CAAC,EAAE;UACfF,cAAc,GAAG,CAACG,WAAW,GAAGD,OAAO,GAAG,GAAG,EAAEE,OAAO,CAAC,CAAC,CAAC;QAC3D;MACF;MAEA5E,iBAAiB,CAAC;QAChB+D,YAAY;QACZC,aAAa;QACbC,aAAa;QACbG,gBAAgB;QAChBG,SAAS;QACTC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC9E,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,CAAC,CAAC;EAEjC,MAAMiF,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACpI,MAAM,CAACgI,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOtI,MAAM,CAAC,IAAI2D,IAAI,CAAC2E,UAAU,CAAC,EAAE,aAAa,EAAE;QAAE/B,MAAM,EAAEpG;MAAG,CAAC,CAAC;IACpE,CAAC,CAAC,MAAM;MACN,OAAOmI,UAAU;IACnB;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAIC,IAAI,IAAK;IAC5B,MAAMC,UAAU,GAAG;MACjB,eAAe,eAAE7H,OAAA,CAACpB,eAAe;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpC,KAAK,eAAEvB,OAAA,CAAChB,cAAc;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzB,QAAQ,eAAEvB,OAAA,CAAClB,aAAa;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC3B,eAAe,eAAEvB,OAAA,CAACf,QAAQ;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC7B,UAAU,eAAEvB,OAAA,CAACtB,YAAY;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC5B,OAAO,eAAEvB,OAAA,CAAC1B,gBAAgB;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC9B,CAAC;IACD,OAAOsG,UAAU,CAACD,IAAI,CAAC,iBAAI5H,OAAA,CAAC1B,gBAAgB;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjD,CAAC;EAED,MAAMuG,eAAe,GAAIzD,OAAO,IAAK;IACnC,QAAQA,OAAO;MACb,KAAK,MAAM;QAAE,OAAO,OAAO;MAC3B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;;EAED;EACA,MAAMiB,aAAa,GAAGrD,OAAO,CAAC8F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACzC,MAAMC,cAAc,GAAG9F,QAAQ,CAAC6F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAE3C,oBACE/H,OAAA,CAACzD,GAAG;IAAAuE,QAAA,gBAEFd,OAAA,CAACzD,GAAG;MAACwE,OAAO,EAAC,MAAM;MAACE,cAAc,EAAC,eAAe;MAACD,UAAU,EAAC,QAAQ;MAACiH,EAAE,EAAE,CAAE;MAAAnH,QAAA,gBAC3Ed,OAAA,CAACrD,UAAU;QAACwE,OAAO,EAAC,IAAI;QAACK,SAAS,EAAC,IAAI;QAAC0G,UAAU,EAAC,MAAM;QAAApH,QAAA,EAAC;MAE1D;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvB,OAAA,CAACzD,GAAG;QAACwE,OAAO,EAAC,MAAM;QAACoH,GAAG,EAAE,CAAE;QAAArH,QAAA,gBACzBd,OAAA,CAACpD,MAAM;UACLuE,OAAO,EAAC,WAAW;UACnBiH,SAAS,eAAEpI,OAAA,CAACxC,UAAU;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BhB,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,cAAc,CAAE;UACxCsG,IAAI,EAAC,OAAO;UAAAvH,QAAA,EACb;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvB,OAAA,CAACpD,MAAM;UACLuE,OAAO,EAAC,UAAU;UAClBiH,SAAS,eAAEpI,OAAA,CAACtC,WAAW;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BhB,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,eAAe,CAAE;UACzCsG,IAAI,EAAC,OAAO;UAAAvH,QAAA,EACb;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA,CAACxD,IAAI;MAAC8L,SAAS;MAACC,OAAO,EAAE,CAAE;MAACN,EAAE,EAAE,CAAE;MAAAnH,QAAA,gBAChCd,OAAA,CAACxD,IAAI;QAACgM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7H,QAAA,eAC9Bd,OAAA,CAACC,QAAQ;UACPC,KAAK,EAAC,sBAAmB;UACzBC,KAAK,EAAE6B,QAAQ,CAAC8B,MAAO;UACvB1D,QAAQ,EAAC,mBAAmB;UAC5BC,IAAI,eAAEL,OAAA,CAAC1C,WAAW;YAACkD,EAAE,EAAE;cAAEF,KAAK,EAAE;YAAQ;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9CjB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPvB,OAAA,CAACxD,IAAI;QAACgM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7H,QAAA,eAC9Bd,OAAA,CAACC,QAAQ;UACPC,KAAK,EAAC,kBAAkB;UACxBC,KAAK,EAAE,CAAAkC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgE,YAAY,KAAI,CAAE;UACzCjG,QAAQ,EAAC,mBAAmB;UAC5BC,IAAI,eAAEL,OAAA,CAACxC,UAAU;YAACgD,EAAE,EAAE;cAAEF,KAAK,EAAE;YAAQ;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7CjB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,UAAU;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPvB,OAAA,CAACxD,IAAI;QAACgM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7H,QAAA,eAC9Bd,OAAA,CAACC,QAAQ;UACPC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAEgH,cAAc,CAAC9E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEwE,SAAS,CAAE;UACjDzG,QAAQ,EAAC,sBAAsB;UAC/BC,IAAI,eAAEL,OAAA,CAAC9B,QAAQ;YAACsC,EAAE,EAAE;cAAEF,KAAK,EAAE;YAAQ;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3CjB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPvB,OAAA,CAACxD,IAAI;QAACgM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7H,QAAA,eAC9Bd,OAAA,CAACC,QAAQ;UACPC,KAAK,EAAC,kBAAkB;UACxBC,KAAK,EAAEkC,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEyE,cAAc,GAAG,GAAGzE,cAAc,CAACyE,cAAc,IAAI,GAAG,KAAM;UACrF1G,QAAQ,EAAC,YAAY;UACrBC,IAAI,eAAEL,OAAA,CAAChC,SAAS;YAACwC,EAAE,EAAE;cAAEF,KAAK,EAAE;YAAQ;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5CjB,KAAK,EAAC,MAAM;UACZC,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,aAAa;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvB,OAAA,CAACxD,IAAI;MAAC8L,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAzH,QAAA,gBAEzBd,OAAA,CAACxD,IAAI;QAACgM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7H,QAAA,eACvBd,OAAA,CAACvD,IAAI;UAAAqE,QAAA,eACHd,OAAA,CAACtD,WAAW;YAAAoE,QAAA,gBACVd,OAAA,CAACzD,GAAG;cAACwE,OAAO,EAAC,MAAM;cAACE,cAAc,EAAC,eAAe;cAACD,UAAU,EAAC,QAAQ;cAACiH,EAAE,EAAE,CAAE;cAAAnH,QAAA,gBAC3Ed,OAAA,CAACrD,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACK,SAAS,EAAC,IAAI;gBAAAV,QAAA,EAAC;cAExC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvB,OAAA,CAAC5C,OAAO;gBAAC8C,KAAK,EAAC,6BAA6B;gBAAAY,QAAA,eAC1Cd,OAAA,CAAC7C,UAAU;kBACTkL,IAAI,EAAC,OAAO;kBACZ9H,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW,CAAE,CAAC;kBAAA;kBACtCzB,KAAK,EAAC,SAAS;kBAAAQ,QAAA,eAEfd,OAAA,CAAC1B,gBAAgB;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,EAELgB,kBAAkB,CAACuB,MAAM,KAAK,CAAC,gBAC9B9D,OAAA,CAACzD,GAAG;cAACqM,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAA/H,QAAA,gBAC5Bd,OAAA,CAACrD,UAAU;gBAAC2D,KAAK,EAAC,eAAe;gBAACY,YAAY;gBAAAJ,QAAA,EAAC;cAE/C;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvB,OAAA,CAACpD,MAAM;gBACLuE,OAAO,EAAC,UAAU;gBAClBiH,SAAS,eAAEpI,OAAA,CAAClC,OAAO;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBhB,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW,CAAE;gBACrCsG,IAAI,EAAC,OAAO;gBAAAvH,QAAA,EACb;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENvB,OAAA,CAAClD,IAAI;cAACgM,KAAK;cAAAhI,QAAA,EACRyB,kBAAkB,CAAC2B,GAAG,CAAC,CAAChB,QAAQ,EAAE6F,KAAK,kBACtC/I,OAAA,CAAC5D,KAAK,CAAC4M,QAAQ;gBAAAlI,QAAA,gBACbd,OAAA,CAACjD,QAAQ;kBAAA+D,QAAA,gBACPd,OAAA,CAAC/C,YAAY;oBAAA6D,QAAA,EACV6G,WAAW,CAACzE,QAAQ,CAAC2B,IAAI;kBAAC;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACfvB,OAAA,CAAChD,YAAY;oBACXiM,OAAO,eACLjJ,OAAA,CAACzD,GAAG;sBAACwE,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACmH,GAAG,EAAE,CAAE;sBAAArH,QAAA,gBAC7Cd,OAAA,CAACrD,UAAU;wBAAC6E,SAAS,EAAC,MAAM;wBAACL,OAAO,EAAC,OAAO;wBAAAL,QAAA,EACzCoC,QAAQ,CAACyB;sBAAM;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACbvB,OAAA,CAACnD,IAAI;wBACHqM,KAAK,EAAEhG,QAAQ,CAACmB,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG,SAAU;wBAC3D/D,KAAK,EAAEwH,eAAe,CAAC5E,QAAQ,CAACmB,OAAO,CAAE;wBACzCgE,IAAI,EAAC;sBAAO;wBAAAjH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;oBACD4H,SAAS,eACPnJ,OAAA,CAACzD,GAAG;sBAACiF,SAAS,EAAC,KAAK;sBAAAV,QAAA,gBAClBd,OAAA,CAACrD,UAAU;wBAAC6E,SAAS,EAAC,MAAM;wBAACL,OAAO,EAAC,OAAO;wBAACb,KAAK,EAAC,eAAe;wBAAAQ,QAAA,EAC/DoC,QAAQ,CAAC+B;sBAAe;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CAAC,EACZ2B,QAAQ,CAACG,SAAS,KAAK,IAAI,iBAC1BrD,OAAA,CAACrD,UAAU;wBAAC6E,SAAS,EAAC,KAAK;wBAACL,OAAO,EAAC,SAAS;wBAACb,KAAK,EAAC,eAAe;wBAAAQ,QAAA,EAChEoC,QAAQ,CAACG,SAAS,IAAI,CAAC,GAAG,SAAS,GAAG,GAAGH,QAAQ,CAACG,SAAS;sBAAiB;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE,CACb,EACA2B,QAAQ,CAACI,OAAO,KAAK,IAAI,iBACxBtD,OAAA,CAACrD,UAAU;wBAAC6E,SAAS,EAAC,KAAK;wBAACL,OAAO,EAAC,SAAS;wBAACb,KAAK,EAAC,eAAe;wBAAAQ,QAAA,EAChEoC,QAAQ,CAACI,OAAO,IAAI,CAAC,GAAG,sBAAsB,GAAG,GAAGJ,QAAQ,CAACI,OAAO;sBAAe;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1E,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFvB,OAAA,CAACoJ,uBAAuB;oBAAAtI,QAAA,eACtBd,OAAA,CAAC7C,UAAU;sBAACkL,IAAI,EAAC,OAAO;sBAAC9H,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW,CAAE;sBAAAjB,QAAA,eAC5Dd,OAAA,CAACqJ,QAAQ;wBAAAjI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,EACVwH,KAAK,GAAGxG,kBAAkB,CAACuB,MAAM,GAAG,CAAC,iBAAI9D,OAAA,CAAC9C,OAAO;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GA1ClC2B,QAAQ,CAACwB,EAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2ChB,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvB,OAAA,CAACxD,IAAI;QAACgM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7H,QAAA,eACvBd,OAAA,CAACvD,IAAI;UAAAqE,QAAA,eACHd,OAAA,CAACtD,WAAW;YAAAoE,QAAA,gBACVd,OAAA,CAACzD,GAAG;cAACwE,OAAO,EAAC,MAAM;cAACE,cAAc,EAAC,eAAe;cAACD,UAAU,EAAC,QAAQ;cAACiH,EAAE,EAAE,CAAE;cAAAnH,QAAA,gBAC3Ed,OAAA,CAACrD,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACK,SAAS,EAAC,IAAI;gBAAAV,QAAA,EAAC;cAExC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvB,OAAA,CAAC5C,OAAO;gBAAC8C,KAAK,EAAC,qBAAkB;gBAAAY,QAAA,eAC/Bd,OAAA,CAAC7C,UAAU;kBACTkL,IAAI,EAAC,OAAO;kBACZ9H,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW,CAAE;kBACrCzB,KAAK,EAAC,SAAS;kBAAAQ,QAAA,eAEfd,OAAA,CAAClC,OAAO;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,EAELS,QAAQ,CAAC8B,MAAM,KAAK,CAAC,gBACpB9D,OAAA,CAACzD,GAAG;cAACqM,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAA/H,QAAA,gBAC5Bd,OAAA,CAACrD,UAAU;gBAAC2D,KAAK,EAAC,eAAe;gBAACY,YAAY;gBAAAJ,QAAA,EAAC;cAE/C;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvB,OAAA,CAACpD,MAAM;gBACLuE,OAAO,EAAC,UAAU;gBAClBiH,SAAS,eAAEpI,OAAA,CAAClC,OAAO;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBhB,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW,CAAE;gBACrCsG,IAAI,EAAC,OAAO;gBAAAvH,QAAA,EACb;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENvB,OAAA,CAAClD,IAAI;cAACgM,KAAK;cAAAhI,QAAA,EACRkB,QAAQ,CAACkC,GAAG,CAAC,CAACoF,OAAO,EAAEP,KAAK,kBAC3B/I,OAAA,CAAC5D,KAAK,CAAC4M,QAAQ;gBAAAlI,QAAA,gBACbd,OAAA,CAACjD,QAAQ;kBAAA+D,QAAA,gBACPd,OAAA,CAAC/C,YAAY;oBAAA6D,QAAA,eACXd,OAAA,CAAC1C,WAAW;sBAACgD,KAAK,EAAC;oBAAS;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACfvB,OAAA,CAAChD,YAAY;oBACXiM,OAAO,EAAEK,OAAO,CAACC,MAAO;oBACxBJ,SAAS,EAAE,GAAGG,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,MAAM,MAAMH,OAAO,CAACI,SAAS;kBAAG;oBAAAtI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACFvB,OAAA,CAACnD,IAAI;oBACHqM,KAAK,EAAEI,OAAO,CAACK,gBAAgB,IAAI,KAAM;oBACzCtB,IAAI,EAAC,OAAO;oBACZlH,OAAO,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,EACVwH,KAAK,GAAG/G,QAAQ,CAAC8B,MAAM,GAAG,CAAC,iBAAI9D,OAAA,CAAC9C,OAAO;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GAfxB+H,OAAO,CAAC5E,EAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBf,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvB,OAAA,CAACxD,IAAI;QAACgM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7H,QAAA,eACvBd,OAAA,CAACvD,IAAI;UAAAqE,QAAA,eACHd,OAAA,CAACtD,WAAW;YAAAoE,QAAA,gBACVd,OAAA,CAACzD,GAAG;cAACwE,OAAO,EAAC,MAAM;cAACE,cAAc,EAAC,eAAe;cAACD,UAAU,EAAC,QAAQ;cAACiH,EAAE,EAAE,CAAE;cAAAnH,QAAA,gBAC3Ed,OAAA,CAACrD,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACK,SAAS,EAAC,IAAI;gBAAAV,QAAA,EAAC;cAExC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvB,OAAA,CAAC5C,OAAO;gBAAC8C,KAAK,EAAC,WAAW;gBAAAY,QAAA,eACxBd,OAAA,CAAC7C,UAAU;kBACTkL,IAAI,EAAC,OAAO;kBACZ9H,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,UAAU,CAAE;kBACpCzB,KAAK,EAAC,SAAS;kBAAAQ,QAAA,eAEfd,OAAA,CAAC5B,YAAY;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,EAEL+D,aAAa,CAACxB,MAAM,KAAK,CAAC,gBACzB9D,OAAA,CAACzD,GAAG;cAACqM,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAA/H,QAAA,gBAC5Bd,OAAA,CAACrD,UAAU;gBAAC2D,KAAK,EAAC,eAAe;gBAACY,YAAY;gBAAAJ,QAAA,EAAC;cAE/C;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvB,OAAA,CAACpD,MAAM;gBACLuE,OAAO,EAAC,UAAU;gBAClBiH,SAAS,eAAEpI,OAAA,CAAClC,OAAO;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBhB,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,UAAU,CAAE;gBACpCsG,IAAI,EAAC,OAAO;gBAAAvH,QAAA,EACb;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENvB,OAAA,CAAClD,IAAI;cAACgM,KAAK;cAAAhI,QAAA,EACRwE,aAAa,CAACpB,GAAG,CAAC,CAACuB,MAAM,EAAEsD,KAAK,kBAC/B/I,OAAA,CAAC5D,KAAK,CAAC4M,QAAQ;gBAAAlI,QAAA,gBACbd,OAAA,CAACjD,QAAQ;kBAAA+D,QAAA,gBACPd,OAAA,CAAC/C,YAAY;oBAAA6D,QAAA,eACXd,OAAA,CAACxC,UAAU;sBAAC8C,KAAK,EAAC;oBAAS;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACfvB,OAAA,CAAChD,YAAY;oBACXiM,OAAO,EAAE,GAAGxD,MAAM,CAACO,MAAM,OAAOmB,cAAc,CAAC1B,MAAM,CAACM,WAAW,CAAC,EAAG;oBACrEoD,SAAS,EAAE,GAAG1B,UAAU,CAAChC,MAAM,CAACF,KAAK,CAAC,MAAME,MAAM,CAACmE,UAAU,IAAI,iBAAiB;kBAAG;oBAAAxI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,EACVwH,KAAK,GAAGzD,aAAa,CAACxB,MAAM,GAAG,CAAC,iBAAI9D,OAAA,CAAC9C,OAAO;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GAV7BkE,MAAM,CAACf,EAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWd,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvB,OAAA,CAACxD,IAAI;QAACgM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7H,QAAA,eACvBd,OAAA,CAACvD,IAAI;UAAAqE,QAAA,eACHd,OAAA,CAACtD,WAAW;YAAAoE,QAAA,gBACVd,OAAA,CAACzD,GAAG;cAACwE,OAAO,EAAC,MAAM;cAACE,cAAc,EAAC,eAAe;cAACD,UAAU,EAAC,QAAQ;cAACiH,EAAE,EAAE,CAAE;cAAAnH,QAAA,gBAC3Ed,OAAA,CAACrD,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACK,SAAS,EAAC,IAAI;gBAAAV,QAAA,EAAC;cAExC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvB,OAAA,CAAC5C,OAAO;gBAAC8C,KAAK,EAAC,WAAW;gBAAAY,QAAA,eACxBd,OAAA,CAAC7C,UAAU;kBACTkL,IAAI,EAAC,OAAO;kBACZ9H,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW,CAAE;kBACrCzB,KAAK,EAAC,SAAS;kBAAAQ,QAAA,eAEfd,OAAA,CAAC5B,YAAY;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,EAELyG,cAAc,CAAClE,MAAM,KAAK,CAAC,gBAC1B9D,OAAA,CAACzD,GAAG;cAACqM,SAAS,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAA/H,QAAA,gBAC5Bd,OAAA,CAACrD,UAAU;gBAAC2D,KAAK,EAAC,eAAe;gBAACY,YAAY;gBAAAJ,QAAA,EAAC;cAE/C;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvB,OAAA,CAACpD,MAAM;gBACLuE,OAAO,EAAC,UAAU;gBAClBiH,SAAS,eAAEpI,OAAA,CAAClC,OAAO;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBhB,OAAO,EAAEA,CAAA,KAAMwB,QAAQ,CAAC,WAAW,CAAE;gBACrCsG,IAAI,EAAC,OAAO;gBAAAvH,QAAA,EACb;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENvB,OAAA,CAAClD,IAAI;cAACgM,KAAK;cAAAhI,QAAA,EACRkH,cAAc,CAAC9D,GAAG,CAAC,CAACyC,OAAO,EAAEoC,KAAK;gBAAA,IAAAc,oBAAA,EAAAC,qBAAA;gBAAA,oBACjC9J,OAAA,CAAC5D,KAAK,CAAC4M,QAAQ;kBAAAlI,QAAA,gBACbd,OAAA,CAACjD,QAAQ;oBAAA+D,QAAA,gBACPd,OAAA,CAAC/C,YAAY;sBAAA6D,QAAA,eACXd,OAAA,CAACtC,WAAW;wBAAC4C,KAAK,EAAC;sBAAS;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACfvB,OAAA,CAAChD,YAAY;sBACXiM,OAAO,EAAE,GAAGtC,OAAO,CAACoD,UAAU,MAAM5C,cAAc,CAACR,OAAO,CAACC,KAAK,CAAC,EAAG;sBACpEuC,SAAS,EAAE,GAAG1B,UAAU,CAACd,OAAO,CAACpB,KAAK,CAAC,OAAAsE,oBAAA,GAAMlD,OAAO,CAAC/B,WAAW,cAAAiF,oBAAA,uBAAnBA,oBAAA,CAAqBG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAAF,qBAAA,GAAAnD,OAAO,CAAC/B,WAAW,cAAAkF,qBAAA,uBAAnBA,qBAAA,CAAqBhG,MAAM,IAAG,EAAE,GAAG,KAAK,GAAG,EAAE;oBAAG;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACM,CAAC,EACVwH,KAAK,GAAGf,cAAc,CAAClE,MAAM,GAAG,CAAC,iBAAI9D,OAAA,CAAC9C,OAAO;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA,GAV9BoF,OAAO,CAACjC,EAAE;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWf,CAAC;cAAA,CAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN6E,SAAS,CAACtC,MAAM,GAAG,CAAC,iBACnB9D,OAAA,CAACvD,IAAI;MAAC+D,EAAE,EAAE;QAAEyJ,EAAE,EAAE;MAAE,CAAE;MAAAnJ,QAAA,eAClBd,OAAA,CAACtD,WAAW;QAAAoE,QAAA,gBACVd,OAAA,CAACrD,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACD,YAAY;UAAAJ,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvB,OAAA,CAACzD,GAAG;UAACkE,MAAM,EAAE,GAAI;UAAAK,QAAA,eACfd,OAAA,CAACF,mBAAmB;YAACoK,KAAK,EAAC,MAAM;YAACzJ,MAAM,EAAC,MAAM;YAAAK,QAAA,eAC7Cd,OAAA,CAACR,SAAS;cAAC2K,IAAI,EAAE/D,SAAU;cAAAtF,QAAA,gBACzBd,OAAA,CAACJ,aAAa;gBAACwK,eAAe,EAAC;cAAK;gBAAAhJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCvB,OAAA,CAACN,KAAK;gBAAC2K,OAAO,EAAC;cAAO;gBAAAjJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBvB,OAAA,CAACL,KAAK;gBAAC2K,OAAO,EAAC;cAAM;gBAAAlJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxBvB,OAAA,CAACL,KAAK;gBAAC2K,OAAO,EAAC,OAAO;gBAACC,WAAW,EAAC;cAAO;gBAAAnJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CvB,OAAA,CAACH,YAAY;gBACX2K,SAAS,EAAEA,CAACrK,KAAK,EAAEsK,IAAI,KAAK,CAC1BA,IAAI,KAAK,MAAM,GAAGtD,cAAc,CAAChH,KAAK,CAAC,GAAG,GAAGA,KAAK,GAAG,EACrDsK,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,QAAQ;cACpC;gBAAArJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFvB,OAAA,CAACP,IAAI;gBACHmI,IAAI,EAAC,UAAU;gBACfyC,OAAO,EAAC,MAAM;gBACdK,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfF,IAAI,EAAC,OAAO;gBACZH,OAAO,EAAC;cAAM;gBAAAlJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACFvB,OAAA,CAACP,IAAI;gBACHmI,IAAI,EAAC,UAAU;gBACfyC,OAAO,EAAC,QAAQ;gBAChBK,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfF,IAAI,EAAC,QAAQ;gBACbH,OAAO,EAAC;cAAO;gBAAAlJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACO,EAAA,CAzkBID,SAAS;EAAA,QACI3C,WAAW,EACwCC,MAAM;AAAA;AAAAyL,GAAA,GAFtE/I,SAAS;AA2kBf,eAAeA,SAAS;AAAC,IAAAD,EAAA,EAAAgJ,GAAA;AAAAC,YAAA,CAAAjJ,EAAA;AAAAiJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}