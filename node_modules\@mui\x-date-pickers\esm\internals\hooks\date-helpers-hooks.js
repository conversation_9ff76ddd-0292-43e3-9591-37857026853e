import * as React from 'react';
import { getMeridiem, convertToMeridiem } from "../utils/time-utils.js";
import { usePickerAdapter } from "../../hooks/usePickerAdapter.js";
export function useNextMonthDisabled(month, {
  disableFuture,
  maxDate,
  timezone
}) {
  const adapter = usePickerAdapter();
  return React.useMemo(() => {
    const now = adapter.date(undefined, timezone);
    const lastEnabledMonth = adapter.startOfMonth(disableFuture && adapter.isBefore(now, maxDate) ? now : maxDate);
    return !adapter.isAfter(lastEnabledMonth, month);
  }, [disableFuture, maxDate, month, adapter, timezone]);
}
export function usePreviousMonthDisabled(month, {
  disablePast,
  minDate,
  timezone
}) {
  const adapter = usePickerAdapter();
  return React.useMemo(() => {
    const now = adapter.date(undefined, timezone);
    const firstEnabledMonth = adapter.startOfMonth(disablePast && adapter.isAfter(now, minDate) ? now : minDate);
    return !adapter.isBefore(firstEnabledMonth, month);
  }, [disablePast, minDate, month, adapter, timezone]);
}
export function useMeridiemMode(date, ampm, onChange, selectionState) {
  const adapter = usePickerAdapter();
  const cleanDate = React.useMemo(() => !adapter.isValid(date) ? null : date, [adapter, date]);
  const meridiemMode = getMeridiem(cleanDate, adapter);
  const handleMeridiemChange = React.useCallback(mode => {
    const timeWithMeridiem = cleanDate == null ? null : convertToMeridiem(cleanDate, mode, Boolean(ampm), adapter);
    onChange(timeWithMeridiem, selectionState ?? 'partial');
  }, [ampm, cleanDate, onChange, selectionState, adapter]);
  return {
    meridiemMode,
    handleMeridiemChange
  };
}