const express = require('express');
const cors = require('cors');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Obtener la ruta de la aplicación (portable)
const APP_PATH = process.env.APP_PATH || __dirname;
const DB_PATH = path.join(APP_PATH, 'repostaje.db');

console.log('Ruta de la aplicación:', APP_PATH);
console.log('Ruta de la base de datos:', DB_PATH);

// Inicializar base de datos
let db;

function initDatabase() {
  db = new sqlite3.Database(DB_PATH, (err) => {
    if (err) {
      console.error('Error al abrir la base de datos:', err.message);
    } else {
      console.log('Conectado a la base de datos SQLite en:', DB_PATH);
      createTables();
    }
  });
}

function createTables() {
  // Crear tabla de vehículos
  db.run(`CREATE TABLE IF NOT EXISTS vehiculos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nombre TEXT NOT NULL,
    marca TEXT,
    modelo TEXT,
    año INTEGER,
    tipo_combustible TEXT,
    capacidad_deposito REAL,
    kilometros_actuales INTEGER DEFAULT 0,
    fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // Crear tabla de repostajes
  db.run(`CREATE TABLE IF NOT EXISTS repostajes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehiculo_id INTEGER,
    fecha DATETIME NOT NULL,
    kilometros_actuales INTEGER NOT NULL,
    litros REAL NOT NULL,
    precio_litro REAL NOT NULL,
    coste_total REAL NOT NULL,
    gasolinera TEXT,
    notas TEXT,
    fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vehiculo_id) REFERENCES vehiculos (id)
  )`);

  // Crear tabla de gastos
  db.run(`CREATE TABLE IF NOT EXISTS gastos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehiculo_id INTEGER,
    fecha DATETIME NOT NULL,
    kilometros_actuales INTEGER,
    tipo_gasto TEXT NOT NULL,
    coste REAL NOT NULL,
    descripcion TEXT,
    proveedor TEXT,
    fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vehiculo_id) REFERENCES vehiculos (id)
  )`);

  console.log('Tablas creadas/verificadas correctamente');
}

// Rutas API

// Vehículos
app.get('/api/vehicles', (req, res) => {
  db.all('SELECT * FROM vehiculos ORDER BY nombre', (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json(rows);
  });
});

app.post('/api/vehicles', (req, res) => {
  const { nombre, marca, modelo, año, tipo_combustible, capacidad_deposito } = req.body;

  db.run(
    'INSERT INTO vehiculos (nombre, marca, modelo, año, tipo_combustible, capacidad_deposito) VALUES (?, ?, ?, ?, ?, ?)',
    [nombre, marca, modelo, año, tipo_combustible, capacidad_deposito],
    function (err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      res.json({ id: this.lastID });
    }
  );
});

// Repostajes
app.get('/api/refuels', (req, res) => {
  const query = `
    SELECT r.*, v.nombre as vehiculo_nombre 
    FROM repostajes r 
    LEFT JOIN vehiculos v ON r.vehiculo_id = v.id 
    ORDER BY r.fecha DESC
  `;

  db.all(query, (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json(rows);
  });
});

app.post('/api/refuels', (req, res) => {
  const { vehiculo_id, fecha, kilometros_actuales, litros, precio_litro, coste_total, gasolinera, notas } = req.body;

  db.run(
    'INSERT INTO repostajes (vehiculo_id, fecha, kilometros_actuales, litros, precio_litro, coste_total, gasolinera, notas) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
    [vehiculo_id, fecha, kilometros_actuales, litros, precio_litro, coste_total, gasolinera, notas],
    function (err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }

      // Actualizar kilometraje del vehículo
      db.run(
        'UPDATE vehiculos SET kilometros_actuales = ? WHERE id = ?',
        [kilometros_actuales, vehiculo_id]
      );

      res.json({ id: this.lastID });
    }
  );
});

// Gastos
app.get('/api/expenses', (req, res) => {
  const query = `
    SELECT g.*, v.nombre as vehiculo_nombre
    FROM gastos g
    LEFT JOIN vehiculos v ON g.vehiculo_id = v.id
    ORDER BY g.fecha DESC
  `;

  db.all(query, (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json(rows);
  });
});

app.post('/api/expenses', (req, res) => {
  const { vehiculo_id, fecha, kilometros_actuales, tipo_gasto, coste, descripcion, proveedor } = req.body;

  db.run(
    'INSERT INTO gastos (vehiculo_id, fecha, kilometros_actuales, tipo_gasto, coste, descripcion, proveedor) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [vehiculo_id, fecha, kilometros_actuales, tipo_gasto, coste, descripcion, proveedor],
    function (err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      res.json({ id: this.lastID });
    }
  );
});

// Estadísticas
app.get('/api/statistics', (req, res) => {
  const { vehicleId, startDate, endDate } = req.query;

  // Consulta base para estadísticas
  let vehicleFilter = vehicleId && vehicleId !== 'all' ? `WHERE v.id = ${vehicleId}` : '';
  let dateFilter = '';

  if (startDate && endDate) {
    dateFilter = vehicleFilter
      ? ` AND r.fecha BETWEEN '${startDate}' AND '${endDate}'`
      : ` WHERE r.fecha BETWEEN '${startDate}' AND '${endDate}'`;
  }

  const statsQuery = `
    SELECT
      COUNT(DISTINCT v.id) as total_vehicles,
      COUNT(r.id) as total_refuels,
      COUNT(g.id) as total_expenses,
      COALESCE(SUM(r.coste_total), 0) as total_fuel_cost,
      COALESCE(SUM(g.coste), 0) as total_expense_cost,
      COALESCE(AVG(r.precio_litro), 0) as avg_fuel_price,
      COALESCE(SUM(r.litros), 0) as total_liters
    FROM vehiculos v
    LEFT JOIN repostajes r ON v.id = r.vehiculo_id ${dateFilter}
    LEFT JOIN gastos g ON v.id = g.vehiculo_id
    ${vehicleFilter}
  `;

  db.get(statsQuery, (err, stats) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json(stats);
  });
});

// Información del sistema
app.get('/api/system/info', (req, res) => {
  const appPath = process.env.APP_PATH || __dirname;
  const dbPath = path.join(appPath, 'repostaje.db');

  const info = {
    version: '1.0.0',
    database_path: dbPath,
    app_path: appPath,
    node_version: process.version,
    platform: process.platform,
    arch: process.arch,
    uptime: process.uptime(),
    memory_usage: process.memoryUsage()
  };

  res.json(info);
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    database: db ? 'connected' : 'disconnected'
  });
});

// Inicializar base de datos y servidor
initDatabase();

app.listen(PORT, () => {
  console.log(`Servidor backend ejecutándose en puerto ${PORT}`);
});

// Manejar cierre graceful
process.on('SIGINT', () => {
  console.log('Cerrando servidor...');
  if (db) {
    db.close((err) => {
      if (err) {
        console.error(err.message);
      }
      console.log('Conexión a base de datos cerrada.');
    });
  }
  process.exit(0);
});
