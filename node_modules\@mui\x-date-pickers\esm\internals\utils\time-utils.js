import { areViewsEqual } from "./views.js";
export const EXPORTED_TIME_VIEWS = ['hours', 'minutes', 'seconds'];
export const TIME_VIEWS = ['hours', 'minutes', 'seconds', 'meridiem'];
export const isTimeView = view => EXPORTED_TIME_VIEWS.includes(view);
export const isInternalTimeView = view => TIME_VIEWS.includes(view);
export const getMeridiem = (date, adapter) => {
  if (!date) {
    return null;
  }
  return adapter.getHours(date) >= 12 ? 'pm' : 'am';
};
export const convertValueToMeridiem = (value, meridiem, ampm) => {
  if (ampm) {
    const currentMeridiem = value >= 12 ? 'pm' : 'am';
    if (currentMeridiem !== meridiem) {
      return meridiem === 'am' ? value - 12 : value + 12;
    }
  }
  return value;
};
export const convertToMeridiem = (time, meridiem, ampm, adapter) => {
  const newHoursAmount = convertValueToMeridiem(adapter.getHours(time), meridiem, ampm);
  return adapter.setHours(time, newHoursAmount);
};
export const getSecondsInDay = (date, adapter) => {
  return adapter.getHours(date) * 3600 + adapter.getMinutes(date) * 60 + adapter.getSeconds(date);
};
export const createIsAfterIgnoreDatePart = (disableIgnoringDatePartForTimeValidation, adapter) => (dateLeft, dateRight) => {
  if (disableIgnoringDatePartForTimeValidation) {
    return adapter.isAfter(dateLeft, dateRight);
  }
  return getSecondsInDay(dateLeft, adapter) > getSecondsInDay(dateRight, adapter);
};
export const resolveTimeFormat = (adapter, {
  format,
  views,
  ampm
}) => {
  if (format != null) {
    return format;
  }
  const formats = adapter.formats;
  if (areViewsEqual(views, ['hours'])) {
    return ampm ? `${formats.hours12h} ${formats.meridiem}` : formats.hours24h;
  }
  if (areViewsEqual(views, ['minutes'])) {
    return formats.minutes;
  }
  if (areViewsEqual(views, ['seconds'])) {
    return formats.seconds;
  }
  if (areViewsEqual(views, ['minutes', 'seconds'])) {
    return `${formats.minutes}:${formats.seconds}`;
  }
  if (areViewsEqual(views, ['hours', 'minutes', 'seconds'])) {
    return ampm ? `${formats.hours12h}:${formats.minutes}:${formats.seconds} ${formats.meridiem}` : `${formats.hours24h}:${formats.minutes}:${formats.seconds}`;
  }
  return ampm ? `${formats.hours12h}:${formats.minutes} ${formats.meridiem}` : `${formats.hours24h}:${formats.minutes}`;
};