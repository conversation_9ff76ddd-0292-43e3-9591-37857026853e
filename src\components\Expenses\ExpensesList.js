import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { styled, useTheme } from '@mui/material/styles';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { useApp } from '../../context/AppContext';

// Styled components
const StatCard = styled(Card)(({ theme }) => ({
  flex: '1 1 180px',
  minWidth: '180px',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

// Styled components for better visual hierarchy
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  padding: '14px 16px',
  '&:first-of-type': { paddingLeft: 24 },
  '&:last-child': { paddingRight: 24 },
  '&.MuiTableCell-head': {
    fontWeight: 600,
    color: theme.palette.text.primary,
    backgroundColor: theme.palette.background.paper,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.background.default,
  },
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    transition: 'background-color 0.2s',
  },
  '&:last-child td': {
    borderBottom: 0,
  },
}));

const PageHeader = styled(Box)(({ theme }) => ({
  background: theme.palette.background.paper,
  padding: theme.spacing(3, 4),
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  marginBottom: theme.spacing(3),
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const PrimaryButton = styled(Button)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 600,
  padding: '8px 20px',
  borderRadius: 8,
  boxShadow: 'none',
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: theme.shadows[2],
  },
  transition: 'all 0.2s ease',
}));

const ExpenseDialog = ({ open, onClose, expense, onSave, vehicles }) => {
  const [formData, setFormData] = useState({
    vehiculo_id: '',
    fecha: new Date().toISOString().split('T')[0],
    kilometros_actuales: '',
    tipo_gasto: 'Mantenimiento',
    coste: '',
    descripcion: '',
    proveedor: '',
  });

  const expenseTypes = [
    'Mantenimiento',
    'Reparación',
    'Seguro',
    'ITV',
    'Impuestos',
    'Neumáticos',
    'Aceite',
    'Filtros',
    'Frenos',
    'Batería',
    'Otros'
  ];

  useEffect(() => {
    if (expense) {
      setFormData({
        ...expense,
        fecha: expense.fecha.split('T')[0], // Formato para input date
      });
    } else {
      setFormData({
        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',
        fecha: new Date().toISOString().split('T')[0],
        kilometros_actuales: '',
        tipo_gasto: 'Mantenimiento',
        coste: '',
        descripcion: '',
        proveedor: '',
      });
    }
  }, [expense, vehicles, open]);

  const handleChange = (field) => (event) => {
    setFormData({
      ...formData,
      [field]: event.target.value,
    });
  };

  const handleSubmit = () => {
    const dataToSave = {
      ...formData,
      coste: parseFloat(formData.coste),
      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,
      categoria: formData.tipo_gasto, // Para compatibilidad
    };
    onSave(dataToSave);
    onClose();
  };

  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto &&
    formData.coste && formData.descripcion;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {expense ? 'Editar Gasto' : 'Nuevo Gasto'}
      </DialogTitle>
      <DialogContent>
        <Box display="flex" flexDirection="column" gap={2} pt={1}>
          <TextField
            label="Vehículo"
            select
            value={formData.vehiculo_id}
            onChange={handleChange('vehiculo_id')}
            fullWidth
            required
          >
            {vehicles.map((vehicle) => (
              <MenuItem key={vehicle.id} value={vehicle.id}>
                {vehicle.nombre}
              </MenuItem>
            ))}
          </TextField>

          <Box display="flex" gap={2}>
            <TextField
              label="Fecha"
              type="date"
              value={formData.fecha}
              onChange={handleChange('fecha')}
              fullWidth
              required
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              label="Kilometraje actual"
              type="number"
              value={formData.kilometros_actuales}
              onChange={handleChange('kilometros_actuales')}
              fullWidth
              inputProps={{ min: 0 }}
              helperText="Opcional"
            />
          </Box>

          <Box display="flex" gap={2}>
            <TextField
              label="Tipo de gasto"
              select
              value={formData.tipo_gasto}
              onChange={handleChange('tipo_gasto')}
              fullWidth
              required
            >
              {expenseTypes.map((type) => (
                <MenuItem key={type} value={type}>
                  {type}
                </MenuItem>
              ))}
            </TextField>
            <TextField
              label="Coste (€)"
              type="number"
              value={formData.coste}
              onChange={handleChange('coste')}
              fullWidth
              required
              inputProps={{ min: 0, step: 0.01 }}
            />
          </Box>

          <TextField
            label="Descripción"
            value={formData.descripcion}
            onChange={handleChange('descripcion')}
            fullWidth
            required
            multiline
            rows={2}
            placeholder="Describe el gasto realizado..."
          />

          <TextField
            label="Proveedor/Taller"
            value={formData.proveedor}
            onChange={handleChange('proveedor')}
            fullWidth
            placeholder="Nombre del taller, tienda, etc."
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancelar</Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={!isValid}
        >
          {expense ? 'Actualizar' : 'Guardar'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const ExpensesList = () => {
  const theme = useTheme();
  const { vehicles, expenses, loadExpenses, addExpense } = useApp();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Color mapping for different expense types
  const expenseTypeColors = {
    'Mantenimiento': 'primary.main',
    'Reparación': 'error.main',
    'Seguro': 'info.main',
    'ITV': 'secondary.main',
    'Impuestos': 'warning.main',
    'Neumáticos': 'success.main',
    'Aceite': 'info.dark',
    'Filtros': 'info.light',
    'Frenos': 'error.dark',
    'Batería': 'warning.dark',
    'Otros': 'text.secondary'
  };

  useEffect(() => {
    // Cargar todos los gastos (sin límite)
    loadExpenses(null, null);
  }, []);

  const handleAddExpense = () => {
    setSelectedExpense(null);
    setDialogOpen(true);
  };

  const handleEditExpense = (expense) => {
    setSelectedExpense(expense);
    setDialogOpen(true);
  };

  const handleDeleteExpense = (expense) => {
    // TODO: Implementar eliminación con confirmación
    console.log('Delete expense:', expense);
  };

  const handleSaveExpense = async (expenseData) => {
    try {
      if (selectedExpense) {
        // TODO: Implementar actualización
        console.log('Update expense:', expenseData);
      } else {
        await addExpense(expenseData);
        // Recargar la lista
        loadExpenses(null, null);
      }
    } catch (error) {
      console.error('Error saving expense:', error);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });
    } catch {
      return dateString;
    }
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('es-ES').format(num || 0);
  };

  const getExpenseTypeColor = (type) => {
    const colors = {
      'Mantenimiento': 'primary',
      'Reparación': 'error',
      'Seguro': 'info',
      'ITV': 'warning',
      'Impuestos': 'secondary',
      'Neumáticos': 'success',
    };
    return colors[type] || 'default';
  };

  // Calcular estadísticas rápidas
  const totalExpenses = expenses.length;
  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);
  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;

  // Agrupar por tipo
  const expensesByType = expenses.reduce((acc, expense) => {
    const type = expense.tipo_gasto || 'Otros';
    acc[type] = (acc[type] || 0) + (expense.coste || 0);
    return acc;
  }, {});

  const topExpenseType = Object.entries(expensesByType)
    .sort((a, b) => b[1] - a[1])
    .shift();

  // Pagination
  const paginatedExpenses = expenses.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
          Gastos
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddExpense}
          disabled={vehicles.length === 0}
          sx={{
            background: `linear-gradient(45deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          }}
        >
          {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Nuevo Gasto'}
        </Button>
      </Box>

      {/* Statistics Cards */}
      <Box display="flex" flexWrap="wrap" gap={2} mb={3}>
        <StatCard sx={{ borderLeftColor: 'primary.main' }}>
          <CardContent sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="primary.main" fontWeight="bold">
              {expenses.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">Total Gastos</Typography>
          </CardContent>
        </StatCard>
        <StatCard sx={{ borderLeftColor: 'success.main' }}>
          <CardContent sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="success.main" fontWeight="bold">
              {formatCurrency(totalCost)}
            </Typography>
            <Typography variant="body2" color="text.secondary">Importe Total</Typography>
          </CardContent>
        </StatCard>
        {topExpenseType && (
          <StatCard sx={{ borderLeftColor: 'info.main' }}>
            <CardContent sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="info.main" fontWeight="bold">
                {topExpenseType[0]}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {formatCurrency(topExpenseType[1])}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Categoría principal
              </Typography>
            </CardContent>
          </StatCard>
        )}
      </Box>

      {/* Legend */}
      {expenses.length > 0 && (
        <Card sx={{ mb: 3, bgcolor: 'background.paper' }}>
          <CardContent sx={{ p: 2 }}>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Símbolos y significados:
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <ReceiptIcon color="primary" fontSize="small" />
                <Typography variant="caption">Gasto con factura</Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={1}>
                <Chip label="Fijo" size="small" color="success" sx={{ height: 20, fontSize: '0.65rem' }} />
                <Typography variant="caption">Gasto recurrente</Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={1}>
                <Chip label="Alto" size="small" color="error" sx={{ height: 20, fontSize: '0.65rem' }} />
                <Typography variant="caption">Gasto superior a 500€</Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}

      {expenses.length === 0 ? (
        <Card sx={{
          bgcolor: 'background.default',
          boxShadow: '0 2px 12px rgba(0,0,0,0.05)',
          borderRadius: 2,
          overflow: 'hidden',
          textAlign: 'center',
          p: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 300,
          mb: 3
        }}>
          <ReceiptIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No hay gastos registrados
          </Typography>
          <Typography variant="body2" color="textSecondary" mb={3}>
            Comienza registrando tu primer gasto para hacer seguimiento de los costes.
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddExpense}
            disabled={vehicles.length === 0}
          >
            {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Agregar Gasto'}
          </Button>
        </Card>
      ) : (
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <StyledTableCell>Fecha</StyledTableCell>
                  <StyledTableCell>Concepto</StyledTableCell>
                  <StyledTableCell align="right">Importe</StyledTableCell>
                  <StyledTableCell>Categoría</StyledTableCell>
                  <StyledTableCell>Vehículo</StyledTableCell>
                  <StyledTableCell>Descripción</StyledTableCell>
                  <StyledTableCell align="center">Acciones</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedExpenses.map((expense, index) => (
                  <StyledTableRow key={expense.id || index}>
                    <StyledTableCell>
                      <Box display="flex" flexDirection="column">
                        <Typography variant="body2" fontWeight={600} color="text.primary">
                          {formatDate(expense.fecha)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(expense.fecha).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </Typography>
                      </Box>
                    </StyledTableCell>
                    <StyledTableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        {expense.tiene_factura && <ReceiptIcon color="primary" fontSize="small" />}
                        <Typography variant="body2" fontWeight={500}>
                          {expense.concepto}
                        </Typography>
                      </Box>
                    </StyledTableCell>
                    <StyledTableCell align="right">
                      <Typography
                        variant="body2"
                        fontWeight={600}
                        color={expense.coste > 500 ? 'error.main' : 'text.primary'}
                      >
                        {formatCurrency(expense.coste)}
                      </Typography>
                    </StyledTableCell>
                    <StyledTableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        {expense.recurrente && (
                          <Chip
                            label="Fijo"
                            size="small"
                            color="success"
                            sx={{ height: 20, fontSize: '0.65rem' }}
                          />
                        )}
                        {expense.coste > 500 && (
                          <Chip
                            label="Alto"
                            size="small"
                            color="error"
                            sx={{ height: 20, fontSize: '0.65rem' }}
                          />
                        )}
                        <Typography variant="body2">
                          {expense.categoria}
                        </Typography>
                      </Box>
                    </StyledTableCell>
                    <StyledTableCell>
                      <Chip
                        label={expense.vehiculo_nombre}
                        size="small"
                        variant="outlined"
                        sx={{
                          height: 24,
                          fontSize: '0.75rem',
                          borderColor: 'divider'
                        }}
                      />
                    </StyledTableCell>
                    <StyledTableCell>
                      <Tooltip title={expense.descripcion || 'Sin descripción'} arrow>
                        <Typography
                          variant="body2"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            maxWidth: '200px'
                          }}
                        >
                          {expense.descripcion || '-'}
                        </Typography>
                      </Tooltip>
                    </StyledTableCell>
                    <StyledTableCell align="right">
                      <Box display="flex" gap={1} justifyContent="flex-end">
                        <Tooltip title="Editar">
                          <IconButton
                            size="small"
                            onClick={() => handleEditExpense(expense)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Eliminar">
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteExpense(expense)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box display="flex" justifyContent="space-between" alignItems="center" p={2}>
            <Typography variant="body2" color="text.secondary">
              Mostrando {paginatedExpenses.length} de {expenses.length} gastos
            </Typography>
            <TablePagination
              rowsPerPageOptions={[10, 25, 50]}
              component="div"
              count={expenses.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={(e, newPage) => setPage(newPage)}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
              labelRowsPerPage="Filas por página:"
              labelDisplayedRows={({ from, to, count }) =>
                `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`
              }
            />
          </Box>
        </Card>
      )}

      <ExpenseDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        expense={selectedExpense}
        onSave={handleSaveExpense}
        vehicles={vehicles}
      />
    </Box>
  );
};

export default ExpensesList;