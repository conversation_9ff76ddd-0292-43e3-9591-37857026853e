{"ast": null, "code": "\"use strict\";\n'use client';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.usePickerTranslations = void 0;\nvar _usePickerAdapter = require(\"./usePickerAdapter\");\nconst usePickerTranslations = () => (0, _usePickerAdapter.useLocalizationContext)().localeText;\nexports.usePickerTranslations = usePickerTranslations;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "usePickerTranslations", "_usePickerAdapter", "require", "useLocalizationContext", "localeText"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/hooks/usePickerTranslations.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.usePickerTranslations = void 0;\nvar _usePickerAdapter = require(\"./usePickerAdapter\");\nconst usePickerTranslations = () => (0, _usePickerAdapter.useLocalizationContext)().localeText;\nexports.usePickerTranslations = usePickerTranslations;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,qBAAqB,GAAG,KAAK,CAAC;AACtC,IAAIC,iBAAiB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACrD,MAAMF,qBAAqB,GAAGA,CAAA,KAAM,CAAC,CAAC,EAAEC,iBAAiB,CAACE,sBAAsB,EAAE,CAAC,CAACC,UAAU;AAC9FN,OAAO,CAACE,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}