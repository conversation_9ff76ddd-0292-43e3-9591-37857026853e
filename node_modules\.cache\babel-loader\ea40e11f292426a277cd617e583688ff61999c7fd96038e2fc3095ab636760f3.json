{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Expenses\\\\ExpensesList.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Tooltip, TablePagination } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Receipt as ReceiptIcon } from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExpenseDialog = ({\n  open,\n  onClose,\n  expense,\n  onSave,\n  vehicles\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: ''\n  });\n  const expenseTypes = ['Mantenimiento', 'Reparación', 'Seguro', 'ITV', 'Impuestos', 'Neumáticos', 'Aceite', 'Filtros', 'Frenos', 'Batería', 'Otros'];\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0] // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: ''\n      });\n    }\n  }, [expense, vehicles, open]);\n  const handleChange = field => event => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value\n    });\n  };\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && formData.coste && formData.descripcion;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: expense ? 'Editar Gasto' : 'Nuevo Gasto'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: 2,\n        pt: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Veh\\xEDculo\",\n          select: true,\n          value: formData.vehiculo_id,\n          onChange: handleChange('vehiculo_id'),\n          fullWidth: true,\n          required: true,\n          children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: vehicle.id,\n            children: vehicle.nombre\n          }, vehicle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Fecha\",\n            type: \"date\",\n            value: formData.fecha,\n            onChange: handleChange('fecha'),\n            fullWidth: true,\n            required: true,\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Kilometraje actual\",\n            type: \"number\",\n            value: formData.kilometros_actuales,\n            onChange: handleChange('kilometros_actuales'),\n            fullWidth: true,\n            inputProps: {\n              min: 0\n            },\n            helperText: \"Opcional\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Tipo de gasto\",\n            select: true,\n            value: formData.tipo_gasto,\n            onChange: handleChange('tipo_gasto'),\n            fullWidth: true,\n            required: true,\n            children: expenseTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: type,\n              children: type\n            }, type, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Coste (\\u20AC)\",\n            type: \"number\",\n            value: formData.coste,\n            onChange: handleChange('coste'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Descripci\\xF3n\",\n          value: formData.descripcion,\n          onChange: handleChange('descripcion'),\n          fullWidth: true,\n          required: true,\n          multiline: true,\n          rows: 2,\n          placeholder: \"Describe el gasto realizado...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Proveedor/Taller\",\n          value: formData.proveedor,\n          onChange: handleChange('proveedor'),\n          fullWidth: true,\n          placeholder: \"Nombre del taller, tienda, etc.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: !isValid,\n        children: expense ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpenseDialog, \"hAHDk6I+hYE0GG43vBPHqnsZ1HE=\");\n_c = ExpenseDialog;\nconst ExpensesList = ({\n  showFormOnLoad = false\n}) => {\n  _s2();\n  const {\n    vehicles,\n    expenses,\n    loadExpenses,\n    addExpense\n  } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(showFormOnLoad);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    setDialogOpen(true);\n  };\n  const handleEditExpense = expense => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n  const handleDeleteExpense = expense => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n  const handleSaveExpense = async expenseData => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\n      } else {\n        await addExpense(expenseData);\n        // Recargar la lista\n        loadExpenses(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving expense:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n  const getExpenseTypeColor = type => {\n    const colors = {\n      'Mantenimiento': 'primary',\n      'Reparación': 'error',\n      'Seguro': 'info',\n      'ITV': 'warning',\n      'Impuestos': 'secondary',\n      'Neumáticos': 'success'\n    };\n    return colors[type] || 'default';\n  };\n\n  // Calcular estadísticas rápidas\n  const totalExpenses = expenses.length;\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\n\n  // Agrupar por tipo\n  const expensesByType = expenses.reduce((acc, expense) => {\n    const type = expense.tipo_gasto || 'Otros';\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\n    return acc;\n  }, {});\n  const topExpenseType = Object.entries(expensesByType).sort(([, a], [, b]) => b - a)[0];\n\n  // Paginación\n  const paginatedExpenses = expenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        fontWeight: \"bold\",\n        children: \"Gastos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddExpense,\n        disabled: vehicles.length === 0,\n        children: \"Nuevo Gasto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary\",\n            children: totalExpenses\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Total Gastos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"error.main\",\n            children: formatCurrency(totalCost)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Coste Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"warning.main\",\n            children: formatCurrency(avgExpense)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Gasto Promedio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"info.main\",\n            children: topExpenseType ? topExpenseType[0] : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Tipo M\\xE1s Frecuente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), expenses.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          py: 6,\n          children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n            sx: {\n              fontSize: 64,\n              color: 'text.secondary',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"textSecondary\",\n            gutterBottom: true,\n            children: \"No hay gastos registrados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            mb: 3,\n            children: \"Comienza registrando gastos de mantenimiento, reparaciones, seguros, etc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 28\n            }, this),\n            onClick: handleAddExpense,\n            disabled: vehicles.length === 0,\n            children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Gasto'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Fecha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Coste\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Proveedor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Km\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Acciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: paginatedExpenses.map(expense => {\n              var _expense$descripcion;\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                hover: true,\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatDate(expense.fecha)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: expense.vehiculo_nombre\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: expense.tipo_gasto,\n                    color: getExpenseTypeColor(expense.tipo_gasto),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: ((_expense$descripcion = expense.descripcion) === null || _expense$descripcion === void 0 ? void 0 : _expense$descripcion.length) > 50 ? `${expense.descripcion.substring(0, 50)}...` : expense.descripcion\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: formatCurrency(expense.coste)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: expense.proveedor || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: expense.kilometros_actuales ? formatNumber(expense.kilometros_actuales) : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Editar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleEditExpense(expense),\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 411,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Eliminar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteExpense(expense),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 416,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 21\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n        component: \"div\",\n        count: expenses.length,\n        page: page,\n        onPageChange: (event, newPage) => setPage(newPage),\n        rowsPerPage: rowsPerPage,\n        onRowsPerPageChange: event => {\n          setRowsPerPage(parseInt(event.target.value, 10));\n          setPage(0);\n        },\n        rowsPerPageOptions: [10, 25, 50, 100],\n        labelRowsPerPage: \"Filas por p\\xE1gina:\",\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} de ${count}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ExpenseDialog, {\n      open: dialogOpen,\n      onClose: () => setDialogOpen(false),\n      expense: selectedExpense,\n      onSave: handleSaveExpense,\n      vehicles: vehicles\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 296,\n    columnNumber: 5\n  }, this);\n};\n_s2(ExpensesList, \"6dVBsUrDgqhbsO0FBpTxkcX7t2I=\", false, function () {\n  return [useApp];\n});\n_c2 = ExpensesList;\nexport default ExpensesList;\nvar _c, _c2;\n$RefreshReg$(_c, \"ExpenseDialog\");\n$RefreshReg$(_c2, \"ExpensesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON><PERSON>", "TablePagination", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Receipt", "ReceiptIcon", "useApp", "format", "es", "jsxDEV", "_jsxDEV", "ExpenseDialog", "open", "onClose", "expense", "onSave", "vehicles", "_s", "formData", "setFormData", "vehiculo_id", "fecha", "Date", "toISOString", "split", "kilometros_actuales", "tipo_gasto", "coste", "descripcion", "proveedor", "expenseTypes", "length", "id", "handleChange", "field", "event", "target", "value", "handleSubmit", "dataToSave", "parseFloat", "parseInt", "categoria", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexDirection", "gap", "pt", "label", "select", "onChange", "required", "map", "vehicle", "nombre", "type", "InputLabelProps", "shrink", "inputProps", "min", "helperText", "step", "multiline", "rows", "placeholder", "onClick", "variant", "disabled", "_c", "ExpensesList", "showFormOnLoad", "_s2", "expenses", "loadExpenses", "addExpense", "dialogOpen", "setDialogOpen", "selectedExpense", "setSelectedExpense", "page", "setPage", "rowsPerPage", "setRowsPerPage", "handleAddExpense", "handleEditExpense", "handleDeleteExpense", "console", "log", "handleSaveExpense", "expenseData", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "locale", "formatNumber", "num", "getExpenseTypeColor", "colors", "totalExpenses", "totalCost", "reduce", "sum", "avgExpense", "expensesByType", "acc", "topExpenseType", "Object", "entries", "sort", "a", "b", "paginatedExpenses", "slice", "justifyContent", "alignItems", "mb", "component", "fontWeight", "startIcon", "sx", "flex", "textAlign", "color", "py", "fontSize", "gutterBottom", "align", "_expense$descripcion", "hover", "vehiculo_nombre", "size", "substring", "title", "count", "onPageChange", "newPage", "onRowsPerPageChange", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c2", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Expenses/ExpensesList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Tooltip,\n  TablePagination,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Receipt as ReceiptIcon,\n} from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\n\nconst ExpenseDialog = ({ open, onClose, expense, onSave, vehicles }) => {\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: '',\n  });\n\n  const expenseTypes = [\n    'Mantenimiento',\n    'Reparación',\n    'Seguro',\n    'ITV',\n    'Impuestos',\n    'Neumáticos',\n    'Aceite',\n    'Filtros',\n    'Frenos',\n    'Batería',\n    'Otros'\n  ];\n\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0], // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: '',\n      });\n    }\n  }, [expense, vehicles, open]);\n\n  const handleChange = (field) => (event) => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value,\n    });\n  };\n\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto, // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && \n                  formData.coste && formData.descripcion;\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        {expense ? 'Editar Gasto' : 'Nuevo Gasto'}\n      </DialogTitle>\n      <DialogContent>\n        <Box display=\"flex\" flexDirection=\"column\" gap={2} pt={1}>\n          <TextField\n            label=\"Vehículo\"\n            select\n            value={formData.vehiculo_id}\n            onChange={handleChange('vehiculo_id')}\n            fullWidth\n            required\n          >\n            {vehicles.map((vehicle) => (\n              <MenuItem key={vehicle.id} value={vehicle.id}>\n                {vehicle.nombre}\n              </MenuItem>\n            ))}\n          </TextField>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Fecha\"\n              type=\"date\"\n              value={formData.fecha}\n              onChange={handleChange('fecha')}\n              fullWidth\n              required\n              InputLabelProps={{ shrink: true }}\n            />\n            <TextField\n              label=\"Kilometraje actual\"\n              type=\"number\"\n              value={formData.kilometros_actuales}\n              onChange={handleChange('kilometros_actuales')}\n              fullWidth\n              inputProps={{ min: 0 }}\n              helperText=\"Opcional\"\n            />\n          </Box>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Tipo de gasto\"\n              select\n              value={formData.tipo_gasto}\n              onChange={handleChange('tipo_gasto')}\n              fullWidth\n              required\n            >\n              {expenseTypes.map((type) => (\n                <MenuItem key={type} value={type}>\n                  {type}\n                </MenuItem>\n              ))}\n            </TextField>\n            <TextField\n              label=\"Coste (€)\"\n              type=\"number\"\n              value={formData.coste}\n              onChange={handleChange('coste')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n          </Box>\n\n          <TextField\n            label=\"Descripción\"\n            value={formData.descripcion}\n            onChange={handleChange('descripcion')}\n            fullWidth\n            required\n            multiline\n            rows={2}\n            placeholder=\"Describe el gasto realizado...\"\n          />\n\n          <TextField\n            label=\"Proveedor/Taller\"\n            value={formData.proveedor}\n            onChange={handleChange('proveedor')}\n            fullWidth\n            placeholder=\"Nombre del taller, tienda, etc.\"\n          />\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose}>Cancelar</Button>\n        <Button \n          onClick={handleSubmit} \n          variant=\"contained\"\n          disabled={!isValid}\n        >\n          {expense ? 'Actualizar' : 'Guardar'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nconst ExpensesList = ({ showFormOnLoad = false }) => {\n  const { vehicles, expenses, loadExpenses, addExpense } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(showFormOnLoad);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    setDialogOpen(true);\n  };\n\n  const handleEditExpense = (expense) => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n\n  const handleDeleteExpense = (expense) => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n\n  const handleSaveExpense = async (expenseData) => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\n      } else {\n        await addExpense(expenseData);\n        // Recargar la lista\n        loadExpenses(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving expense:', error);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  const formatDate = (dateString) => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\n    } catch {\n      return dateString;\n    }\n  };\n\n  const formatNumber = (num) => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  const getExpenseTypeColor = (type) => {\n    const colors = {\n      'Mantenimiento': 'primary',\n      'Reparación': 'error',\n      'Seguro': 'info',\n      'ITV': 'warning',\n      'Impuestos': 'secondary',\n      'Neumáticos': 'success',\n    };\n    return colors[type] || 'default';\n  };\n\n  // Calcular estadísticas rápidas\n  const totalExpenses = expenses.length;\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\n  \n  // Agrupar por tipo\n  const expensesByType = expenses.reduce((acc, expense) => {\n    const type = expense.tipo_gasto || 'Otros';\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\n    return acc;\n  }, {});\n  \n  const topExpenseType = Object.entries(expensesByType)\n    .sort(([,a], [,b]) => b - a)[0];\n\n  // Paginación\n  const paginatedExpenses = expenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n\n  return (\n    <Box>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n        <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\">\n          Gastos\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleAddExpense}\n          disabled={vehicles.length === 0}\n        >\n          Nuevo Gasto\n        </Button>\n      </Box>\n\n      {/* Estadísticas rápidas */}\n      <Box display=\"flex\" gap={2} mb={3}>\n        <Card sx={{ flex: 1 }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"primary\">{totalExpenses}</Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Total Gastos</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: 1 }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"error.main\">{formatCurrency(totalCost)}</Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Coste Total</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: 1 }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"warning.main\">{formatCurrency(avgExpense)}</Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Gasto Promedio</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: 1 }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h6\" color=\"info.main\">\n              {topExpenseType ? topExpenseType[0] : 'N/A'}\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Tipo Más Frecuente</Typography>\n          </CardContent>\n        </Card>\n      </Box>\n\n      {expenses.length === 0 ? (\n        <Card>\n          <CardContent>\n            <Box textAlign=\"center\" py={6}>\n              <ReceiptIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"textSecondary\" gutterBottom>\n                No hay gastos registrados\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\" mb={3}>\n                Comienza registrando gastos de mantenimiento, reparaciones, seguros, etc.\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={handleAddExpense}\n                disabled={vehicles.length === 0}\n              >\n                {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Gasto'}\n              </Button>\n            </Box>\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <TableContainer component={Paper}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Fecha</TableCell>\n                  <TableCell>Vehículo</TableCell>\n                  <TableCell>Tipo</TableCell>\n                  <TableCell>Descripción</TableCell>\n                  <TableCell align=\"right\">Coste</TableCell>\n                  <TableCell>Proveedor</TableCell>\n                  <TableCell align=\"right\">Km</TableCell>\n                  <TableCell align=\"center\">Acciones</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {paginatedExpenses.map((expense) => (\n                  <TableRow key={expense.id} hover>\n                    <TableCell>{formatDate(expense.fecha)}</TableCell>\n                    <TableCell>{expense.vehiculo_nombre}</TableCell>\n                    <TableCell>\n                      <Chip \n                        label={expense.tipo_gasto} \n                        color={getExpenseTypeColor(expense.tipo_gasto)}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">\n                        {expense.descripcion?.length > 50 \n                          ? `${expense.descripcion.substring(0, 50)}...`\n                          : expense.descripcion\n                        }\n                      </Typography>\n                    </TableCell>\n                    <TableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\n                        {formatCurrency(expense.coste)}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{expense.proveedor || '-'}</TableCell>\n                    <TableCell align=\"right\">\n                      {expense.kilometros_actuales ? formatNumber(expense.kilometros_actuales) : '-'}\n                    </TableCell>\n                    <TableCell align=\"center\">\n                      <Tooltip title=\"Editar\">\r\n                        <IconButton size=\"small\" onClick={() => handleEditExpense(expense)}>\r\n                          <EditIcon />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                      <Tooltip title=\"Eliminar\">\r\n                        <IconButton size=\"small\" color=\"error\" onClick={() => handleDeleteExpense(expense)}>\r\n                          <DeleteIcon />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                    </TableCell>\r\n                  </TableRow>\r\n                ))}\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n          \r\n          <TablePagination\r\n            component=\"div\"\r\n            count={expenses.length}\r\n            page={page}\r\n            onPageChange={(event, newPage) => setPage(newPage)}\r\n            rowsPerPage={rowsPerPage}\r\n            onRowsPerPageChange={(event) => {\r\n              setRowsPerPage(parseInt(event.target.value, 10));\r\n              setPage(0);\r\n            }}\r\n            rowsPerPageOptions={[10, 25, 50, 100]}\r\n            labelRowsPerPage=\"Filas por página:\"\r\n            labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}\r\n          />\r\n        </Card>\r\n      )}\r\n\r\n      <ExpenseDialog\r\n        open={dialogOpen}\r\n        onClose={() => setDialogOpen(false)}\r\n        expense={selectedExpense}\r\n        onSave={handleSaveExpense}\r\n        vehicles={vehicles}\r\n      />\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ExpensesList;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,eAAe,QACV,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC;IACvC8C,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,mBAAmB,EAAE,EAAE;IACvBC,UAAU,EAAE,eAAe;IAC3BC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,CACnB,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,CACR;EAEDvD,SAAS,CAAC,MAAM;IACd,IAAIuC,OAAO,EAAE;MACXK,WAAW,CAAC;QACV,GAAGL,OAAO;QACVO,KAAK,EAAEP,OAAO,CAACO,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE;MACtC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,WAAW,CAAC;QACVC,WAAW,EAAEJ,QAAQ,CAACe,MAAM,GAAG,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAACgB,EAAE,GAAG,EAAE;QACtDX,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7CC,mBAAmB,EAAE,EAAE;QACvBC,UAAU,EAAE,eAAe;QAC3BC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACf,OAAO,EAAEE,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EAE7B,MAAMqB,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzChB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgB,KAAK,GAAGC,KAAK,CAACC,MAAM,CAACC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjB,GAAGrB,QAAQ;MACXS,KAAK,EAAEa,UAAU,CAACtB,QAAQ,CAACS,KAAK,CAAC;MACjCF,mBAAmB,EAAEgB,QAAQ,CAACvB,QAAQ,CAACO,mBAAmB,CAAC,IAAI,CAAC;MAChEiB,SAAS,EAAExB,QAAQ,CAACQ,UAAU,CAAE;IAClC,CAAC;IACDX,MAAM,CAACwB,UAAU,CAAC;IAClB1B,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM8B,OAAO,GAAGzB,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACQ,UAAU,IAC7DR,QAAQ,CAACS,KAAK,IAAIT,QAAQ,CAACU,WAAW;EAEtD,oBACElB,OAAA,CAACpB,MAAM;IAACsB,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC+B,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DpC,OAAA,CAACnB,WAAW;MAAAuD,QAAA,EACThC,OAAO,GAAG,cAAc,GAAG;IAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACdxC,OAAA,CAAClB,aAAa;MAAAsD,QAAA,eACZpC,OAAA,CAAClC,GAAG;QAAC2E,OAAO,EAAC,MAAM;QAACC,aAAa,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAR,QAAA,gBACvDpC,OAAA,CAAChB,SAAS;UACR6D,KAAK,EAAC,aAAU;UAChBC,MAAM;UACNnB,KAAK,EAAEnB,QAAQ,CAACE,WAAY;UAC5BqC,QAAQ,EAAExB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTa,QAAQ;UAAAZ,QAAA,EAEP9B,QAAQ,CAAC2C,GAAG,CAAEC,OAAO,iBACpBlD,OAAA,CAACf,QAAQ;YAAkB0C,KAAK,EAAEuB,OAAO,CAAC5B,EAAG;YAAAc,QAAA,EAC1Cc,OAAO,CAACC;UAAM,GADFD,OAAO,CAAC5B,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZxC,OAAA,CAAClC,GAAG;UAAC2E,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAAAP,QAAA,gBACzBpC,OAAA,CAAChB,SAAS;YACR6D,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,MAAM;YACXzB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;YACtBoC,QAAQ,EAAExB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTa,QAAQ;YACRK,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFxC,OAAA,CAAChB,SAAS;YACR6D,KAAK,EAAC,oBAAoB;YAC1BO,IAAI,EAAC,QAAQ;YACbzB,KAAK,EAAEnB,QAAQ,CAACO,mBAAoB;YACpCgC,QAAQ,EAAExB,YAAY,CAAC,qBAAqB,CAAE;YAC9CY,SAAS;YACToB,UAAU,EAAE;cAAEC,GAAG,EAAE;YAAE,CAAE;YACvBC,UAAU,EAAC;UAAU;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxC,OAAA,CAAClC,GAAG;UAAC2E,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAAAP,QAAA,gBACzBpC,OAAA,CAAChB,SAAS;YACR6D,KAAK,EAAC,eAAe;YACrBC,MAAM;YACNnB,KAAK,EAAEnB,QAAQ,CAACQ,UAAW;YAC3B+B,QAAQ,EAAExB,YAAY,CAAC,YAAY,CAAE;YACrCY,SAAS;YACTa,QAAQ;YAAAZ,QAAA,EAEPhB,YAAY,CAAC6B,GAAG,CAAEG,IAAI,iBACrBpD,OAAA,CAACf,QAAQ;cAAY0C,KAAK,EAAEyB,IAAK;cAAAhB,QAAA,EAC9BgB;YAAI,GADQA,IAAI;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZxC,OAAA,CAAChB,SAAS;YACR6D,KAAK,EAAC,gBAAW;YACjBO,IAAI,EAAC,QAAQ;YACbzB,KAAK,EAAEnB,QAAQ,CAACS,KAAM;YACtB8B,QAAQ,EAAExB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTa,QAAQ;YACRO,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEE,IAAI,EAAE;YAAK;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxC,OAAA,CAAChB,SAAS;UACR6D,KAAK,EAAC,gBAAa;UACnBlB,KAAK,EAAEnB,QAAQ,CAACU,WAAY;UAC5B6B,QAAQ,EAAExB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTa,QAAQ;UACRW,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,WAAW,EAAC;QAAgC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEFxC,OAAA,CAAChB,SAAS;UACR6D,KAAK,EAAC,kBAAkB;UACxBlB,KAAK,EAAEnB,QAAQ,CAACW,SAAU;UAC1B4B,QAAQ,EAAExB,YAAY,CAAC,WAAW,CAAE;UACpCY,SAAS;UACT0B,WAAW,EAAC;QAAiC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChBxC,OAAA,CAACjB,aAAa;MAAAqD,QAAA,gBACZpC,OAAA,CAAC9B,MAAM;QAAC4F,OAAO,EAAE3D,OAAQ;QAAAiC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3CxC,OAAA,CAAC9B,MAAM;QACL4F,OAAO,EAAElC,YAAa;QACtBmC,OAAO,EAAC,WAAW;QACnBC,QAAQ,EAAE,CAAC/B,OAAQ;QAAAG,QAAA,EAElBhC,OAAO,GAAG,YAAY,GAAG;MAAS;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACjC,EAAA,CAtKIN,aAAa;AAAAgE,EAAA,GAAbhE,aAAa;AAwKnB,MAAMiE,YAAY,GAAGA,CAAC;EAAEC,cAAc,GAAG;AAAM,CAAC,KAAK;EAAAC,GAAA;EACnD,MAAM;IAAE9D,QAAQ;IAAE+D,QAAQ;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAG3E,MAAM,CAAC,CAAC;EACjE,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7G,QAAQ,CAACuG,cAAc,CAAC;EAC5D,MAAM,CAACO,eAAe,EAAEC,kBAAkB,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgH,IAAI,EAAEC,OAAO,CAAC,GAAGjH,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACkH,WAAW,EAAEC,cAAc,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd;IACAyG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,gBAAgB,GAAGA,CAAA,KAAM;IAC7BL,kBAAkB,CAAC,IAAI,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMQ,iBAAiB,GAAI7E,OAAO,IAAK;IACrCuE,kBAAkB,CAACvE,OAAO,CAAC;IAC3BqE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMS,mBAAmB,GAAI9E,OAAO,IAAK;IACvC;IACA+E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEhF,OAAO,CAAC;EACzC,CAAC;EAED,MAAMiF,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C,IAAI;MACF,IAAIZ,eAAe,EAAE;QACnB;QACAS,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,WAAW,CAAC;MAC7C,CAAC,MAAM;QACL,MAAMf,UAAU,CAACe,WAAW,CAAC;QAC7B;QACAhB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAChG,MAAM,CAAC4F,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOlG,MAAM,CAAC,IAAIe,IAAI,CAACmF,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEC,MAAM,EAAElG;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAOiG,UAAU;IACnB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,GAAG,IAAK;IAC5B,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAAC9F,MAAM,CAACqG,GAAG,IAAI,CAAC,CAAC;EACxD,CAAC;EAED,MAAMC,mBAAmB,GAAI/C,IAAI,IAAK;IACpC,MAAMgD,MAAM,GAAG;MACb,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE,OAAO;MACrB,QAAQ,EAAE,MAAM;MAChB,KAAK,EAAE,SAAS;MAChB,WAAW,EAAE,WAAW;MACxB,YAAY,EAAE;IAChB,CAAC;IACD,OAAOA,MAAM,CAAChD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;;EAED;EACA,MAAMiD,aAAa,GAAGhC,QAAQ,CAAChD,MAAM;EACrC,MAAMiF,SAAS,GAAGjC,QAAQ,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAEpG,OAAO,KAAKoG,GAAG,IAAIpG,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAMwF,UAAU,GAAGJ,aAAa,GAAG,CAAC,GAAGC,SAAS,GAAGD,aAAa,GAAG,CAAC;;EAEpE;EACA,MAAMK,cAAc,GAAGrC,QAAQ,CAACkC,MAAM,CAAC,CAACI,GAAG,EAAEvG,OAAO,KAAK;IACvD,MAAMgD,IAAI,GAAGhD,OAAO,CAACY,UAAU,IAAI,OAAO;IAC1C2F,GAAG,CAACvD,IAAI,CAAC,GAAG,CAACuD,GAAG,CAACvD,IAAI,CAAC,IAAI,CAAC,KAAKhD,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC;IACnD,OAAO0F,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO,CAACJ,cAAc,CAAC,CAClDK,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC;EACA,MAAME,iBAAiB,GAAG7C,QAAQ,CAAC8C,KAAK,CAACvC,IAAI,GAAGE,WAAW,EAAEF,IAAI,GAAGE,WAAW,GAAGA,WAAW,CAAC;EAE9F,oBACE9E,OAAA,CAAClC,GAAG;IAAAsE,QAAA,gBACFpC,OAAA,CAAClC,GAAG;MAAC2E,OAAO,EAAC,MAAM;MAAC2E,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAlF,QAAA,gBAC3EpC,OAAA,CAACjC,UAAU;QAACgG,OAAO,EAAC,IAAI;QAACwD,SAAS,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAAApF,QAAA,EAAC;MAE1D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxC,OAAA,CAAC9B,MAAM;QACL6F,OAAO,EAAC,WAAW;QACnB0D,SAAS,eAAEzH,OAAA,CAACX,OAAO;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBsB,OAAO,EAAEkB,gBAAiB;QAC1BhB,QAAQ,EAAE1D,QAAQ,CAACe,MAAM,KAAK,CAAE;QAAAe,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNxC,OAAA,CAAClC,GAAG;MAAC2E,OAAO,EAAC,MAAM;MAACE,GAAG,EAAE,CAAE;MAAC2E,EAAE,EAAE,CAAE;MAAAlF,QAAA,gBAChCpC,OAAA,CAAChC,IAAI;QAAC0J,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAvF,QAAA,eACpBpC,OAAA,CAAC/B,WAAW;UAACyJ,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAxF,QAAA,gBACvCpC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,IAAI;YAAC8D,KAAK,EAAC,SAAS;YAAAzF,QAAA,EAAEiE;UAAa;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrExC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,OAAO;YAAC8D,KAAK,EAAC,eAAe;YAAAzF,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPxC,OAAA,CAAChC,IAAI;QAAC0J,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAvF,QAAA,eACpBpC,OAAA,CAAC/B,WAAW;UAACyJ,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAxF,QAAA,gBACvCpC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,IAAI;YAAC8D,KAAK,EAAC,YAAY;YAAAzF,QAAA,EAAEoD,cAAc,CAACc,SAAS;UAAC;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACpFxC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,OAAO;YAAC8D,KAAK,EAAC,eAAe;YAAAzF,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPxC,OAAA,CAAChC,IAAI;QAAC0J,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAvF,QAAA,eACpBpC,OAAA,CAAC/B,WAAW;UAACyJ,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAxF,QAAA,gBACvCpC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,IAAI;YAAC8D,KAAK,EAAC,cAAc;YAAAzF,QAAA,EAAEoD,cAAc,CAACiB,UAAU;UAAC;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACvFxC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,OAAO;YAAC8D,KAAK,EAAC,eAAe;YAAAzF,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPxC,OAAA,CAAChC,IAAI;QAAC0J,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAvF,QAAA,eACpBpC,OAAA,CAAC/B,WAAW;UAACyJ,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAxF,QAAA,gBACvCpC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,IAAI;YAAC8D,KAAK,EAAC,WAAW;YAAAzF,QAAA,EACvCwE,cAAc,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAG;UAAK;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACbxC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,OAAO;YAAC8D,KAAK,EAAC,eAAe;YAAAzF,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEL6B,QAAQ,CAAChD,MAAM,KAAK,CAAC,gBACpBrB,OAAA,CAAChC,IAAI;MAAAoE,QAAA,eACHpC,OAAA,CAAC/B,WAAW;QAAAmE,QAAA,eACVpC,OAAA,CAAClC,GAAG;UAAC8J,SAAS,EAAC,QAAQ;UAACE,EAAE,EAAE,CAAE;UAAA1F,QAAA,gBAC5BpC,OAAA,CAACL,WAAW;YAAC+H,EAAE,EAAE;cAAEK,QAAQ,EAAE,EAAE;cAAEF,KAAK,EAAE,gBAAgB;cAAEP,EAAE,EAAE;YAAE;UAAE;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrExC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,IAAI;YAAC8D,KAAK,EAAC,eAAe;YAACG,YAAY;YAAA5F,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxC,OAAA,CAACjC,UAAU;YAACgG,OAAO,EAAC,OAAO;YAAC8D,KAAK,EAAC,eAAe;YAACP,EAAE,EAAE,CAAE;YAAAlF,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxC,OAAA,CAAC9B,MAAM;YACL6F,OAAO,EAAC,WAAW;YACnB0D,SAAS,eAAEzH,OAAA,CAACX,OAAO;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBsB,OAAO,EAAEkB,gBAAiB;YAC1BhB,QAAQ,EAAE1D,QAAQ,CAACe,MAAM,KAAK,CAAE;YAAAe,QAAA,EAE/B9B,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;UAAwB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPxC,OAAA,CAAChC,IAAI;MAAAoE,QAAA,gBACHpC,OAAA,CAAC1B,cAAc;QAACiJ,SAAS,EAAE9I,KAAM;QAAA2D,QAAA,eAC/BpC,OAAA,CAAC7B,KAAK;UAAAiE,QAAA,gBACJpC,OAAA,CAACzB,SAAS;YAAA6D,QAAA,eACRpC,OAAA,CAACxB,QAAQ;cAAA4D,QAAA,gBACPpC,OAAA,CAAC3B,SAAS;gBAAA+D,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BxC,OAAA,CAAC3B,SAAS;gBAAA+D,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BxC,OAAA,CAAC3B,SAAS;gBAAA+D,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3BxC,OAAA,CAAC3B,SAAS;gBAAA+D,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCxC,OAAA,CAAC3B,SAAS;gBAAC4J,KAAK,EAAC,OAAO;gBAAA7F,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1CxC,OAAA,CAAC3B,SAAS;gBAAA+D,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCxC,OAAA,CAAC3B,SAAS;gBAAC4J,KAAK,EAAC,OAAO;gBAAA7F,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACvCxC,OAAA,CAAC3B,SAAS;gBAAC4J,KAAK,EAAC,QAAQ;gBAAA7F,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZxC,OAAA,CAAC5B,SAAS;YAAAgE,QAAA,EACP8E,iBAAiB,CAACjE,GAAG,CAAE7C,OAAO;cAAA,IAAA8H,oBAAA;cAAA,oBAC7BlI,OAAA,CAACxB,QAAQ;gBAAkB2J,KAAK;gBAAA/F,QAAA,gBAC9BpC,OAAA,CAAC3B,SAAS;kBAAA+D,QAAA,EAAE0D,UAAU,CAAC1F,OAAO,CAACO,KAAK;gBAAC;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDxC,OAAA,CAAC3B,SAAS;kBAAA+D,QAAA,EAAEhC,OAAO,CAACgI;gBAAe;kBAAA/F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDxC,OAAA,CAAC3B,SAAS;kBAAA+D,QAAA,eACRpC,OAAA,CAACtB,IAAI;oBACHmE,KAAK,EAAEzC,OAAO,CAACY,UAAW;oBAC1B6G,KAAK,EAAE1B,mBAAmB,CAAC/F,OAAO,CAACY,UAAU,CAAE;oBAC/CqH,IAAI,EAAC;kBAAO;oBAAAhG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZxC,OAAA,CAAC3B,SAAS;kBAAA+D,QAAA,eACRpC,OAAA,CAACjC,UAAU;oBAACgG,OAAO,EAAC,OAAO;oBAAA3B,QAAA,EACxB,EAAA8F,oBAAA,GAAA9H,OAAO,CAACc,WAAW,cAAAgH,oBAAA,uBAAnBA,oBAAA,CAAqB7G,MAAM,IAAG,EAAE,GAC7B,GAAGjB,OAAO,CAACc,WAAW,CAACoH,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC5ClI,OAAO,CAACc;kBAAW;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZxC,OAAA,CAAC3B,SAAS;kBAAC4J,KAAK,EAAC,OAAO;kBAAA7F,QAAA,eACtBpC,OAAA,CAACjC,UAAU;oBAACgG,OAAO,EAAC,OAAO;oBAACyD,UAAU,EAAC,MAAM;oBAAApF,QAAA,EAC1CoD,cAAc,CAACpF,OAAO,CAACa,KAAK;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZxC,OAAA,CAAC3B,SAAS;kBAAA+D,QAAA,EAAEhC,OAAO,CAACe,SAAS,IAAI;gBAAG;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDxC,OAAA,CAAC3B,SAAS;kBAAC4J,KAAK,EAAC,OAAO;kBAAA7F,QAAA,EACrBhC,OAAO,CAACW,mBAAmB,GAAGkF,YAAY,CAAC7F,OAAO,CAACW,mBAAmB,CAAC,GAAG;gBAAG;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACZxC,OAAA,CAAC3B,SAAS;kBAAC4J,KAAK,EAAC,QAAQ;kBAAA7F,QAAA,gBACvBpC,OAAA,CAACd,OAAO;oBAACqJ,KAAK,EAAC,QAAQ;oBAAAnG,QAAA,eACrBpC,OAAA,CAACrB,UAAU;sBAAC0J,IAAI,EAAC,OAAO;sBAACvE,OAAO,EAAEA,CAAA,KAAMmB,iBAAiB,CAAC7E,OAAO,CAAE;sBAAAgC,QAAA,eACjEpC,OAAA,CAACT,QAAQ;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVxC,OAAA,CAACd,OAAO;oBAACqJ,KAAK,EAAC,UAAU;oBAAAnG,QAAA,eACvBpC,OAAA,CAACrB,UAAU;sBAAC0J,IAAI,EAAC,OAAO;sBAACR,KAAK,EAAC,OAAO;sBAAC/D,OAAO,EAAEA,CAAA,KAAMoB,mBAAmB,CAAC9E,OAAO,CAAE;sBAAAgC,QAAA,eACjFpC,OAAA,CAACP,UAAU;wBAAA4C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,GAtCCpC,OAAO,CAACkB,EAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCf,CAAC;YAAA,CACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEjBxC,OAAA,CAACb,eAAe;QACdoI,SAAS,EAAC,KAAK;QACfiB,KAAK,EAAEnE,QAAQ,CAAChD,MAAO;QACvBuD,IAAI,EAAEA,IAAK;QACX6D,YAAY,EAAEA,CAAChH,KAAK,EAAEiH,OAAO,KAAK7D,OAAO,CAAC6D,OAAO,CAAE;QACnD5D,WAAW,EAAEA,WAAY;QACzB6D,mBAAmB,EAAGlH,KAAK,IAAK;UAC9BsD,cAAc,CAAChD,QAAQ,CAACN,KAAK,CAACC,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;UAChDkD,OAAO,CAAC,CAAC,CAAC;QACZ,CAAE;QACF+D,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QACtCC,gBAAgB,EAAC,sBAAmB;QACpCC,kBAAkB,EAAEA,CAAC;UAAEC,IAAI;UAAEC,EAAE;UAAER;QAAM,CAAC,KAAK,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK;MAAG;QAAAnG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAEDxC,OAAA,CAACC,aAAa;MACZC,IAAI,EAAEsE,UAAW;MACjBrE,OAAO,EAAEA,CAAA,KAAMsE,aAAa,CAAC,KAAK,CAAE;MACpCrE,OAAO,EAAEsE,eAAgB;MACzBrE,MAAM,EAAEgF,iBAAkB;MAC1B/E,QAAQ,EAAEA;IAAS;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC4B,GAAA,CAxPIF,YAAY;EAAA,QACyCtE,MAAM;AAAA;AAAAqJ,GAAA,GAD3D/E,YAAY;AA0PlB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAAgF,GAAA;AAAAC,YAAA,CAAAjF,EAAA;AAAAiF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}