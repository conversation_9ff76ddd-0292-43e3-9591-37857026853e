{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Expenses\\\\ExpensesList.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { styled } from '@mui/material/styles';\nimport { Box, Typography, Card, CardContent, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Paper, Chip, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Receipt as ReceiptIcon } from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\n\n// Styled components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatCard = styled(Card)(({\n  theme\n}) => ({\n  flex: '1 1 180px',\n  minWidth: '180px',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: theme.shadows[4]\n  }\n}));\n\n// Styled components for better visual hierarchy\n_c = StatCard;\nconst StyledTableCell = styled(TableCell)(({\n  theme\n}) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '14px 16px',\n  '&:first-of-type': {\n    paddingLeft: 24\n  },\n  '&:last-child': {\n    paddingRight: 24\n  },\n  '&.MuiTableCell-head': {\n    fontWeight: 600,\n    color: theme.palette.text.primary,\n    backgroundColor: theme.palette.background.paper\n  }\n}));\n_c2 = StyledTableCell;\nconst StyledTableRow = styled(TableRow)(({\n  theme\n}) => ({\n  '&:nth-of-type(odd)': {\n    backgroundColor: theme.palette.background.default\n  },\n  '&:hover': {\n    backgroundColor: theme.palette.action.hover,\n    transition: 'background-color 0.2s'\n  },\n  '&:last-child td': {\n    borderBottom: 0\n  }\n}));\n_c3 = StyledTableRow;\nconst PageHeader = styled(Box)(({\n  theme\n}) => ({\n  background: theme.palette.background.paper,\n  padding: theme.spacing(3, 4),\n  borderRadius: theme.shape.borderRadius,\n  boxShadow: theme.shadows[1],\n  marginBottom: theme.spacing(3),\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center'\n}));\nconst PrimaryButton = styled(Button)(({\n  theme\n}) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-1px)',\n    boxShadow: theme.shadows[2]\n  },\n  transition: 'all 0.2s ease'\n}));\nconst ExpenseDialog = ({\n  open,\n  onClose,\n  expense,\n  onSave,\n  vehicles\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: ''\n  });\n  const expenseTypes = ['Mantenimiento', 'Reparación', 'Seguro', 'ITV', 'Impuestos', 'Neumáticos', 'Aceite', 'Filtros', 'Frenos', 'Batería', 'Otros'];\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0] // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: ''\n      });\n    }\n  }, [expense, vehicles, open]);\n  const handleChange = field => event => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value\n    });\n  };\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && formData.coste && formData.descripcion;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: expense ? 'Editar Gasto' : 'Nuevo Gasto'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: 2,\n        pt: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Veh\\xEDculo\",\n          select: true,\n          value: formData.vehiculo_id,\n          onChange: handleChange('vehiculo_id'),\n          fullWidth: true,\n          required: true,\n          children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: vehicle.id,\n            children: vehicle.nombre\n          }, vehicle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Fecha\",\n            type: \"date\",\n            value: formData.fecha,\n            onChange: handleChange('fecha'),\n            fullWidth: true,\n            required: true,\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Kilometraje actual\",\n            type: \"number\",\n            value: formData.kilometros_actuales,\n            onChange: handleChange('kilometros_actuales'),\n            fullWidth: true,\n            inputProps: {\n              min: 0\n            },\n            helperText: \"Opcional\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Tipo de gasto\",\n            select: true,\n            value: formData.tipo_gasto,\n            onChange: handleChange('tipo_gasto'),\n            fullWidth: true,\n            required: true,\n            children: expenseTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: type,\n              children: type\n            }, type, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Coste (\\u20AC)\",\n            type: \"number\",\n            value: formData.coste,\n            onChange: handleChange('coste'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Descripci\\xF3n\",\n          value: formData.descripcion,\n          onChange: handleChange('descripcion'),\n          fullWidth: true,\n          required: true,\n          multiline: true,\n          rows: 2,\n          placeholder: \"Describe el gasto realizado...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Proveedor/Taller\",\n          value: formData.proveedor,\n          onChange: handleChange('proveedor'),\n          fullWidth: true,\n          placeholder: \"Nombre del taller, tienda, etc.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: !isValid,\n        children: expense ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpenseDialog, \"lm+lmS/97cPhGhSDs9YhThCmRDU=\");\n_c4 = ExpenseDialog;\nconst ExpensesList = () => {\n  _s2();\n  const theme = useTheme();\n  const {\n    vehicles,\n    expenses,\n    loadExpenses,\n    addExpense\n  } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n\n  // Color mapping for different expense types\n  const expenseTypeColors = {\n    'Mantenimiento': 'primary.main',\n    'Reparación': 'error.main',\n    'Seguro': 'info.main',\n    'ITV': 'secondary.main',\n    'Impuestos': 'warning.main',\n    'Neumáticos': 'success.main',\n    'Aceite': 'info.dark',\n    'Filtros': 'info.light',\n    'Frenos': 'error.dark',\n    'Batería': 'warning.dark',\n    'Otros': 'text.secondary'\n  };\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    setDialogOpen(true);\n  };\n  const handleEditExpense = expense => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n  const handleDeleteExpense = expense => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n  const handleSaveExpense = async expenseData => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\n      } else {\n        await addExpense(expenseData);\n        // Recargar la lista\n        loadExpenses(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving expense:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n  const getExpenseTypeColor = type => {\n    const colors = {\n      'Mantenimiento': 'primary',\n      'Reparación': 'error',\n      'Seguro': 'info',\n      'ITV': 'warning',\n      'Impuestos': 'secondary',\n      'Neumáticos': 'success'\n    };\n    return colors[type] || 'default';\n  };\n\n  // Calcular estadísticas rápidas\n  const totalExpenses = expenses.length;\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\n\n  // Agrupar por tipo\n  const expensesByType = expenses.reduce((acc, expense) => {\n    const type = expense.tipo_gasto || 'Otros';\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\n    return acc;\n  }, {});\n  const topExpenseType = Object.entries(expensesByType).sort((a, b) => b[1] - a[1]).shift();\n\n  // Pagination\n  const paginatedExpenses = expenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: {\n        xs: 2,\n        md: 3\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        fontWeight: \"bold\",\n        color: \"primary\",\n        children: \"Gastos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddExpense,\n        disabled: vehicles.length === 0,\n        sx: {\n          background: `linear-gradient(45deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`\n        },\n        children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Nuevo Gasto'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexWrap: \"wrap\",\n      gap: 2,\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'primary.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary.main\",\n            fontWeight: \"bold\",\n            children: expenses.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Total Gastos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'success.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"success.main\",\n            fontWeight: \"bold\",\n            children: formatCurrency(totalCost)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Importe Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), topExpenseType && /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'info.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"info.main\",\n            fontWeight: \"bold\",\n            children: topExpenseType[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: formatCurrency(topExpenseType[1])\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Categor\\xEDa principal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), expenses.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3,\n        bgcolor: 'background.paper'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          color: \"text.secondary\",\n          gutterBottom: true,\n          children: \"S\\xEDmbolos y significados:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          flexWrap: \"wrap\",\n          gap: 3,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n              color: \"primary\",\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Gasto con factura\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: \"Fijo\",\n              size: \"small\",\n              color: \"success\",\n              sx: {\n                height: 20,\n                fontSize: '0.65rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Gasto recurrente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: \"Alto\",\n              size: \"small\",\n              color: \"error\",\n              sx: {\n                height: 20,\n                fontSize: '0.65rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Gasto superior a 500\\u20AC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 9\n    }, this), expenses.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        bgcolor: 'background.default',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.05)',\n        borderRadius: 2,\n        overflow: 'hidden',\n        textAlign: 'center',\n        p: 4,\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: 300,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n        sx: {\n          fontSize: 64,\n          color: 'text.secondary',\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"textSecondary\",\n        gutterBottom: true,\n        children: \"No hay gastos registrados\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        mb: 3,\n        children: \"Comienza registrando tu primer gasto para hacer seguimiento de los costes.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 24\n        }, this),\n        onClick: handleAddExpense,\n        disabled: vehicles.length === 0,\n        children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Agregar Gasto'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Fecha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Concepto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Importe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Categor\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"center\",\n                children: \"Acciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: paginatedExpenses.map((expense, index) => /*#__PURE__*/_jsxDEV(StyledTableRow, {\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  flexDirection: \"column\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 600,\n                    color: \"text.primary\",\n                    children: formatDate(expense.fecha)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: new Date(expense.fecha).toLocaleTimeString([], {\n                      hour: '2-digit',\n                      minute: '2-digit'\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [expense.tiene_factura && /*#__PURE__*/_jsxDEV(ReceiptIcon, {\n                    color: \"primary\",\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 51\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: expense.concepto\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: 600,\n                  color: expense.coste > 500 ? 'error.main' : 'text.primary',\n                  children: formatCurrency(expense.coste)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [expense.recurrente && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Fijo\",\n                    size: \"small\",\n                    color: \"success\",\n                    sx: {\n                      height: 20,\n                      fontSize: '0.65rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 27\n                  }, this), expense.coste > 500 && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Alto\",\n                    size: \"small\",\n                    color: \"error\",\n                    sx: {\n                      height: 20,\n                      fontSize: '0.65rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: expense.categoria\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: expense.vehiculo_nombre,\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    height: 24,\n                    fontSize: '0.75rem',\n                    borderColor: 'divider'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: expense.descripcion || 'Sin descripción',\n                  arrow: true,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      display: '-webkit-box',\n                      WebkitLineClamp: 1,\n                      WebkitBoxOrient: 'vertical',\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis',\n                      maxWidth: '200px'\n                    },\n                    children: expense.descripcion || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  gap: 1,\n                  justifyContent: \"flex-end\",\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Editar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleEditExpense(expense),\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Eliminar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleDeleteExpense(expense),\n                      color: \"error\",\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 21\n              }, this)]\n            }, expense.id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        p: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Mostrando \", paginatedExpenses.length, \" de \", expenses.length, \" gastos\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n          rowsPerPageOptions: [10, 25, 50],\n          component: \"div\",\n          count: expenses.length,\n          rowsPerPage: rowsPerPage,\n          page: page,\n          onPageChange: (e, newPage) => setPage(newPage),\n          onRowsPerPageChange: e => {\n            setRowsPerPage(parseInt(e.target.value, 10));\n            setPage(0);\n          },\n          labelRowsPerPage: \"Filas por p\\xE1gina:\",\n          labelDisplayedRows: ({\n            from,\n            to,\n            count\n          }) => `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ExpenseDialog, {\n      open: dialogOpen,\n      onClose: () => setDialogOpen(false),\n      expense: selectedExpense,\n      onSave: handleSaveExpense,\n      vehicles: vehicles\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 378,\n    columnNumber: 5\n  }, this);\n};\n_s2(ExpensesList, \"aJuT98gCYIUMRqsOmj9eA0Fhlzw=\", true, function () {\n  return [useApp];\n});\n_c5 = ExpensesList;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"StatCard\");\n$RefreshReg$(_c2, \"StyledTableCell\");\n$RefreshReg$(_c3, \"StyledTableRow\");\n$RefreshReg$(_c4, \"ExpenseDialog\");\n$RefreshReg$(_c5, \"ExpensesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "format", "es", "styled", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "Paper", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Receipt", "ReceiptIcon", "useApp", "jsxDEV", "_jsxDEV", "StatCard", "theme", "flex", "min<PERSON><PERSON><PERSON>", "transition", "transform", "boxShadow", "shadows", "_c", "StyledTableCell", "borderBottom", "palette", "divider", "padding", "paddingLeft", "paddingRight", "fontWeight", "color", "text", "primary", "backgroundColor", "background", "paper", "_c2", "StyledTableRow", "default", "action", "hover", "_c3", "<PERSON><PERSON><PERSON><PERSON>", "spacing", "borderRadius", "shape", "marginBottom", "display", "justifyContent", "alignItems", "PrimaryButton", "textTransform", "ExpenseDialog", "open", "onClose", "expense", "onSave", "vehicles", "_s", "formData", "setFormData", "vehiculo_id", "fecha", "Date", "toISOString", "split", "kilometros_actuales", "tipo_gasto", "coste", "descripcion", "proveedor", "expenseTypes", "length", "id", "handleChange", "field", "event", "target", "value", "handleSubmit", "dataToSave", "parseFloat", "parseInt", "categoria", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "gap", "pt", "label", "select", "onChange", "required", "map", "vehicle", "nombre", "type", "InputLabelProps", "shrink", "inputProps", "min", "helperText", "step", "multiline", "rows", "placeholder", "onClick", "variant", "disabled", "_c4", "ExpensesList", "_s2", "useTheme", "expenses", "loadExpenses", "addExpense", "dialogOpen", "setDialogOpen", "selectedExpense", "setSelectedExpense", "page", "setPage", "rowsPerPage", "setRowsPerPage", "expenseTypeColors", "handleAddExpense", "handleEditExpense", "handleDeleteExpense", "console", "log", "handleSaveExpense", "expenseData", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "locale", "formatNumber", "num", "getExpenseTypeColor", "colors", "totalExpenses", "totalCost", "reduce", "sum", "avgExpense", "expensesByType", "acc", "topExpenseType", "Object", "entries", "sort", "a", "b", "shift", "paginatedExpenses", "slice", "sx", "p", "xs", "md", "mb", "component", "startIcon", "main", "dark", "flexWrap", "borderLeftColor", "textAlign", "bgcolor", "gutterBottom", "fontSize", "size", "height", "overflow", "minHeight", "align", "index", "toLocaleTimeString", "hour", "minute", "tiene_factura", "concepto", "recurrente", "vehiculo_nombre", "borderColor", "title", "arrow", "WebkitLineClamp", "WebkitBoxOrient", "textOverflow", "rowsPerPageOptions", "count", "onPageChange", "e", "newPage", "onRowsPerPageChange", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c5", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Expenses/ExpensesList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { format } from 'date-fns';\r\nimport { es } from 'date-fns/locale';\r\nimport { styled } from '@mui/material/styles';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Card,\r\n  CardContent,\r\n  Button,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  TablePagination,\r\n  Paper,\r\n  Chip,\r\n  IconButton,\r\n  Tooltip,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  TextField,\r\n  MenuItem,\r\n} from '@mui/material';\r\nimport {\r\n  Add as AddIcon,\r\n  Edit as EditIcon,\r\n  Delete as DeleteIcon,\r\n  Receipt as ReceiptIcon,\r\n} from '@mui/icons-material';\r\nimport { useApp } from '../../context/AppContext';\r\n\r\n// Styled components\r\nconst StatCard = styled(Card)(({ theme }) => ({\r\n  flex: '1 1 180px',\r\n  minWidth: '180px',\r\n  transition: 'all 0.3s ease',\r\n  '&:hover': {\r\n    transform: 'translateY(-4px)',\r\n    boxShadow: theme.shadows[4],\r\n  },\r\n}));\r\n\r\n// Styled components for better visual hierarchy\r\nconst StyledTableCell = styled(TableCell)(({ theme }) => ({\r\n  borderBottom: `1px solid ${theme.palette.divider}`,\r\n  padding: '14px 16px',\r\n  '&:first-of-type': { paddingLeft: 24 },\r\n  '&:last-child': { paddingRight: 24 },\r\n  '&.MuiTableCell-head': {\r\n    fontWeight: 600,\r\n    color: theme.palette.text.primary,\r\n    backgroundColor: theme.palette.background.paper,\r\n  },\r\n}));\r\n\r\nconst StyledTableRow = styled(TableRow)(({ theme }) => ({\r\n  '&:nth-of-type(odd)': {\r\n    backgroundColor: theme.palette.background.default,\r\n  },\r\n  '&:hover': {\r\n    backgroundColor: theme.palette.action.hover,\r\n    transition: 'background-color 0.2s',\r\n  },\r\n  '&:last-child td': {\r\n    borderBottom: 0,\r\n  },\r\n}));\r\n\r\nconst PageHeader = styled(Box)(({ theme }) => ({\r\n  background: theme.palette.background.paper,\r\n  padding: theme.spacing(3, 4),\r\n  borderRadius: theme.shape.borderRadius,\r\n  boxShadow: theme.shadows[1],\r\n  marginBottom: theme.spacing(3),\r\n  display: 'flex',\r\n  justifyContent: 'space-between',\r\n  alignItems: 'center',\r\n}));\r\n\r\nconst PrimaryButton = styled(Button)(({ theme }) => ({\r\n  textTransform: 'none',\r\n  fontWeight: 600,\r\n  padding: '8px 20px',\r\n  borderRadius: 8,\r\n  boxShadow: 'none',\r\n  '&:hover': {\r\n    transform: 'translateY(-1px)',\r\n    boxShadow: theme.shadows[2],\r\n  },\r\n  transition: 'all 0.2s ease',\r\n}));\r\n\r\nconst ExpenseDialog = ({ open, onClose, expense, onSave, vehicles }) => {\r\n  const [formData, setFormData] = useState({\r\n    vehiculo_id: '',\r\n    fecha: new Date().toISOString().split('T')[0],\r\n    kilometros_actuales: '',\r\n    tipo_gasto: 'Mantenimiento',\r\n    coste: '',\r\n    descripcion: '',\r\n    proveedor: '',\r\n  });\r\n\r\n  const expenseTypes = [\r\n    'Mantenimiento',\r\n    'Reparación',\r\n    'Seguro',\r\n    'ITV',\r\n    'Impuestos',\r\n    'Neumáticos',\r\n    'Aceite',\r\n    'Filtros',\r\n    'Frenos',\r\n    'Batería',\r\n    'Otros'\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (expense) {\r\n      setFormData({\r\n        ...expense,\r\n        fecha: expense.fecha.split('T')[0], // Formato para input date\r\n      });\r\n    } else {\r\n      setFormData({\r\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\r\n        fecha: new Date().toISOString().split('T')[0],\r\n        kilometros_actuales: '',\r\n        tipo_gasto: 'Mantenimiento',\r\n        coste: '',\r\n        descripcion: '',\r\n        proveedor: '',\r\n      });\r\n    }\r\n  }, [expense, vehicles, open]);\r\n\r\n  const handleChange = (field) => (event) => {\r\n    setFormData({\r\n      ...formData,\r\n      [field]: event.target.value,\r\n    });\r\n  };\r\n\r\n  const handleSubmit = () => {\r\n    const dataToSave = {\r\n      ...formData,\r\n      coste: parseFloat(formData.coste),\r\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\r\n      categoria: formData.tipo_gasto, // Para compatibilidad\r\n    };\r\n    onSave(dataToSave);\r\n    onClose();\r\n  };\r\n\r\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && \r\n                  formData.coste && formData.descripcion;\r\n\r\n  return (\r\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\r\n      <DialogTitle>\r\n        {expense ? 'Editar Gasto' : 'Nuevo Gasto'}\r\n      </DialogTitle>\r\n      <DialogContent>\r\n        <Box display=\"flex\" flexDirection=\"column\" gap={2} pt={1}>\r\n          <TextField\r\n            label=\"Vehículo\"\r\n            select\r\n            value={formData.vehiculo_id}\r\n            onChange={handleChange('vehiculo_id')}\r\n            fullWidth\r\n            required\r\n          >\r\n            {vehicles.map((vehicle) => (\r\n              <MenuItem key={vehicle.id} value={vehicle.id}>\r\n                {vehicle.nombre}\r\n              </MenuItem>\r\n            ))}\r\n          </TextField>\r\n\r\n          <Box display=\"flex\" gap={2}>\r\n            <TextField\r\n              label=\"Fecha\"\r\n              type=\"date\"\r\n              value={formData.fecha}\r\n              onChange={handleChange('fecha')}\r\n              fullWidth\r\n              required\r\n              InputLabelProps={{ shrink: true }}\r\n            />\r\n            <TextField\r\n              label=\"Kilometraje actual\"\r\n              type=\"number\"\r\n              value={formData.kilometros_actuales}\r\n              onChange={handleChange('kilometros_actuales')}\r\n              fullWidth\r\n              inputProps={{ min: 0 }}\r\n              helperText=\"Opcional\"\r\n            />\r\n          </Box>\r\n\r\n          <Box display=\"flex\" gap={2}>\r\n            <TextField\r\n              label=\"Tipo de gasto\"\r\n              select\r\n              value={formData.tipo_gasto}\r\n              onChange={handleChange('tipo_gasto')}\r\n              fullWidth\r\n              required\r\n            >\r\n              {expenseTypes.map((type) => (\r\n                <MenuItem key={type} value={type}>\r\n                  {type}\r\n                </MenuItem>\r\n              ))}\r\n            </TextField>\r\n            <TextField\r\n              label=\"Coste (€)\"\r\n              type=\"number\"\r\n              value={formData.coste}\r\n              onChange={handleChange('coste')}\r\n              fullWidth\r\n              required\r\n              inputProps={{ min: 0, step: 0.01 }}\r\n            />\r\n          </Box>\r\n\r\n          <TextField\r\n            label=\"Descripción\"\r\n            value={formData.descripcion}\r\n            onChange={handleChange('descripcion')}\r\n            fullWidth\r\n            required\r\n            multiline\r\n            rows={2}\r\n            placeholder=\"Describe el gasto realizado...\"\r\n          />\r\n\r\n          <TextField\r\n            label=\"Proveedor/Taller\"\r\n            value={formData.proveedor}\r\n            onChange={handleChange('proveedor')}\r\n            fullWidth\r\n            placeholder=\"Nombre del taller, tienda, etc.\"\r\n          />\r\n        </Box>\r\n      </DialogContent>\r\n      <DialogActions>\r\n        <Button onClick={onClose}>Cancelar</Button>\r\n        <Button \r\n          onClick={handleSubmit} \r\n          variant=\"contained\"\r\n          disabled={!isValid}\r\n        >\r\n          {expense ? 'Actualizar' : 'Guardar'}\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nconst ExpensesList = () => {\r\n  const theme = useTheme();\r\n  const { vehicles, expenses, loadExpenses, addExpense } = useApp();\r\n  const [dialogOpen, setDialogOpen] = useState(false);\r\n  const [selectedExpense, setSelectedExpense] = useState(null);\r\n  const [page, setPage] = useState(0);\r\n  const [rowsPerPage, setRowsPerPage] = useState(10);\r\n\r\n  // Color mapping for different expense types\r\n  const expenseTypeColors = {\r\n    'Mantenimiento': 'primary.main',\r\n    'Reparación': 'error.main',\r\n    'Seguro': 'info.main',\r\n    'ITV': 'secondary.main',\r\n    'Impuestos': 'warning.main',\r\n    'Neumáticos': 'success.main',\r\n    'Aceite': 'info.dark',\r\n    'Filtros': 'info.light',\r\n    'Frenos': 'error.dark',\r\n    'Batería': 'warning.dark',\r\n    'Otros': 'text.secondary'\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Cargar todos los gastos (sin límite)\r\n    loadExpenses(null, null);\r\n  }, []);\r\n\r\n  const handleAddExpense = () => {\r\n    setSelectedExpense(null);\r\n    setDialogOpen(true);\r\n  };\r\n\r\n  const handleEditExpense = (expense) => {\r\n    setSelectedExpense(expense);\r\n    setDialogOpen(true);\r\n  };\r\n\r\n  const handleDeleteExpense = (expense) => {\r\n    // TODO: Implementar eliminación con confirmación\r\n    console.log('Delete expense:', expense);\r\n  };\r\n\r\n  const handleSaveExpense = async (expenseData) => {\r\n    try {\r\n      if (selectedExpense) {\r\n        // TODO: Implementar actualización\r\n        console.log('Update expense:', expenseData);\r\n      } else {\r\n        await addExpense(expenseData);\r\n        // Recargar la lista\r\n        loadExpenses(null, null);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving expense:', error);\r\n    }\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    return new Intl.NumberFormat('es-ES', {\r\n      style: 'currency',\r\n      currency: 'EUR'\r\n    }).format(amount || 0);\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    try {\r\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\r\n    } catch {\r\n      return dateString;\r\n    }\r\n  };\r\n\r\n  const formatNumber = (num) => {\r\n    return new Intl.NumberFormat('es-ES').format(num || 0);\r\n  };\r\n\r\n  const getExpenseTypeColor = (type) => {\r\n    const colors = {\r\n      'Mantenimiento': 'primary',\r\n      'Reparación': 'error',\r\n      'Seguro': 'info',\r\n      'ITV': 'warning',\r\n      'Impuestos': 'secondary',\r\n      'Neumáticos': 'success',\r\n    };\r\n    return colors[type] || 'default';\r\n  };\r\n\r\n  // Calcular estadísticas rápidas\r\n  const totalExpenses = expenses.length;\r\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\r\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\r\n  \r\n  // Agrupar por tipo\r\n  const expensesByType = expenses.reduce((acc, expense) => {\r\n    const type = expense.tipo_gasto || 'Otros';\r\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\r\n    return acc;\r\n  }, {});\r\n  \r\n  const topExpenseType = Object.entries(expensesByType)\r\n    .sort((a, b) => b[1] - a[1])\r\n    .shift();\r\n\r\n  // Pagination\r\n  const paginatedExpenses = expenses.slice(\r\n    page * rowsPerPage,\r\n    page * rowsPerPage + rowsPerPage\r\n  );\r\n\r\n  return (\r\n    <Box sx={{ p: { xs: 2, md: 3 } }}>\r\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\r\n        <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\" color=\"primary\">\r\n          Gastos\r\n        </Typography>\r\n        <Button\r\n          variant=\"contained\"\r\n          startIcon={<AddIcon />}\r\n          onClick={handleAddExpense}\r\n          disabled={vehicles.length === 0}\r\n          sx={{\r\n            background: `linear-gradient(45deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\r\n          }}\r\n        >\r\n          {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Nuevo Gasto'}\r\n        </Button>\r\n      </Box>\r\n\r\n      {/* Statistics Cards */}\r\n      <Box display=\"flex\" flexWrap=\"wrap\" gap={2} mb={3}>\r\n        <StatCard sx={{ borderLeftColor: 'primary.main' }}>\r\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\r\n            <Typography variant=\"h4\" color=\"primary.main\" fontWeight=\"bold\">\r\n              {expenses.length}\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"text.secondary\">Total Gastos</Typography>\r\n          </CardContent>\r\n        </StatCard>\r\n        <StatCard sx={{ borderLeftColor: 'success.main' }}>\r\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\r\n            <Typography variant=\"h4\" color=\"success.main\" fontWeight=\"bold\">\r\n              {formatCurrency(totalCost)}\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"text.secondary\">Importe Total</Typography>\r\n          </CardContent>\r\n        </StatCard>\r\n        {topExpenseType && (\r\n          <StatCard sx={{ borderLeftColor: 'info.main' }}>\r\n            <CardContent sx={{ textAlign: 'center', p: 2 }}>\r\n              <Typography variant=\"h6\" color=\"info.main\" fontWeight=\"bold\">\r\n                {topExpenseType[0]}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                {formatCurrency(topExpenseType[1])}\r\n              </Typography>\r\n              <Typography variant=\"caption\" color=\"text.secondary\">\r\n                Categoría principal\r\n              </Typography>\r\n            </CardContent>\r\n          </StatCard>\r\n        )}\r\n      </Box>\r\n\r\n      {/* Legend */}\r\n      {expenses.length > 0 && (\r\n        <Card sx={{ mb: 3, bgcolor: 'background.paper' }}>\r\n          <CardContent sx={{ p: 2 }}>\r\n            <Typography variant=\"subtitle2\" color=\"text.secondary\" gutterBottom>\r\n              Símbolos y significados:\r\n            </Typography>\r\n            <Box display=\"flex\" flexWrap=\"wrap\" gap={3}>\r\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                <ReceiptIcon color=\"primary\" fontSize=\"small\" />\r\n                <Typography variant=\"caption\">Gasto con factura</Typography>\r\n              </Box>\r\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                <Chip label=\"Fijo\" size=\"small\" color=\"success\" sx={{ height: 20, fontSize: '0.65rem' }} />\r\n                <Typography variant=\"caption\">Gasto recurrente</Typography>\r\n              </Box>\r\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                <Chip label=\"Alto\" size=\"small\" color=\"error\" sx={{ height: 20, fontSize: '0.65rem' }} />\r\n                <Typography variant=\"caption\">Gasto superior a 500€</Typography>\r\n              </Box>\r\n            </Box>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {expenses.length === 0 ? (\r\n        <Card sx={{\r\n          bgcolor: 'background.default',\r\n          boxShadow: '0 2px 12px rgba(0,0,0,0.05)',\r\n          borderRadius: 2,\r\n          overflow: 'hidden',\r\n          textAlign: 'center',\r\n          p: 4,\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          minHeight: 300,\r\n          mb: 3\r\n        }}>\r\n          <ReceiptIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\r\n          <Typography variant=\"h6\" color=\"textSecondary\" gutterBottom>\r\n            No hay gastos registrados\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\" mb={3}>\r\n            Comienza registrando tu primer gasto para hacer seguimiento de los costes.\r\n          </Typography>\r\n          <Button\r\n            variant=\"contained\"\r\n            startIcon={<AddIcon />}\r\n            onClick={handleAddExpense}\r\n            disabled={vehicles.length === 0}\r\n          >\r\n            {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Agregar Gasto'}\r\n          </Button>\r\n        </Card>\r\n      ) : (\r\n        <Card>\r\n          <TableContainer>\r\n            <Table>\r\n              <TableHead>\r\n                <TableRow>\r\n                  <StyledTableCell>Fecha</StyledTableCell>\r\n                  <StyledTableCell>Concepto</StyledTableCell>\r\n                  <StyledTableCell align=\"right\">Importe</StyledTableCell>\r\n                  <StyledTableCell>Categoría</StyledTableCell>\r\n                  <StyledTableCell>Vehículo</StyledTableCell>\r\n                  <StyledTableCell>Descripción</StyledTableCell>\r\n                  <StyledTableCell align=\"center\">Acciones</StyledTableCell>\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                {paginatedExpenses.map((expense, index) => (\r\n                  <StyledTableRow key={expense.id || index}>\r\n                    <StyledTableCell>\r\n                      <Box display=\"flex\" flexDirection=\"column\">\r\n                        <Typography variant=\"body2\" fontWeight={600} color=\"text.primary\">\r\n                          {formatDate(expense.fecha)}\r\n                        </Typography>\r\n                        <Typography variant=\"caption\" color=\"text.secondary\">\r\n                          {new Date(expense.fecha).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}\r\n                        </Typography>\r\n                      </Box>\r\n                    </StyledTableCell>\r\n                    <StyledTableCell>\r\n                      <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                        {expense.tiene_factura && <ReceiptIcon color=\"primary\" fontSize=\"small\" />}\r\n                        <Typography variant=\"body2\" fontWeight={500}>\r\n                          {expense.concepto}\r\n                        </Typography>\r\n                      </Box>\r\n                    </StyledTableCell>\r\n                    <StyledTableCell align=\"right\">\r\n                      <Typography \r\n                        variant=\"body2\" \r\n                        fontWeight={600}\r\n                        color={expense.coste > 500 ? 'error.main' : 'text.primary'}\r\n                      >\r\n                        {formatCurrency(expense.coste)}\r\n                      </Typography>\r\n                    </StyledTableCell>\r\n                    <StyledTableCell>\r\n                      <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                        {expense.recurrente && (\r\n                          <Chip \r\n                            label=\"Fijo\" \r\n                            size=\"small\" \r\n                            color=\"success\"\r\n                            sx={{ height: 20, fontSize: '0.65rem' }}\r\n                          />\r\n                        )}\r\n                        {expense.coste > 500 && (\r\n                          <Chip \r\n                            label=\"Alto\" \r\n                            size=\"small\" \r\n                            color=\"error\"\r\n                            sx={{ height: 20, fontSize: '0.65rem' }}\r\n                          />\r\n                        )}\r\n                        <Typography variant=\"body2\">\r\n                          {expense.categoria}\r\n                        </Typography>\r\n                      </Box>\r\n                    </StyledTableCell>\r\n                    <StyledTableCell>\r\n                      <Chip \r\n                        label={expense.vehiculo_nombre} \r\n                        size=\"small\" \r\n                        variant=\"outlined\"\r\n                        sx={{ \r\n                          height: 24, \r\n                          fontSize: '0.75rem',\r\n                          borderColor: 'divider'\r\n                        }}\r\n                      />\r\n                    </StyledTableCell>\r\n                    <StyledTableCell>\r\n                      <Tooltip title={expense.descripcion || 'Sin descripción'} arrow>\r\n                        <Typography \r\n                          variant=\"body2\" \r\n                          sx={{\r\n                            display: '-webkit-box',\r\n                            WebkitLineClamp: 1,\r\n                            WebkitBoxOrient: 'vertical',\r\n                            overflow: 'hidden',\r\n                            textOverflow: 'ellipsis',\r\n                            maxWidth: '200px'\r\n                          }}\r\n                        >\r\n                          {expense.descripcion || '-'}\r\n                        </Typography>\r\n                      </Tooltip>\r\n                    </StyledTableCell>\r\n                    <StyledTableCell align=\"right\">\r\n                      <Box display=\"flex\" gap={1} justifyContent=\"flex-end\">\r\n                        <Tooltip title=\"Editar\">\r\n                          <IconButton\r\n                            size=\"small\"\r\n                            onClick={() => handleEditExpense(expense)}\r\n                          >\r\n                            <EditIcon fontSize=\"small\" />\r\n                          </IconButton>\r\n                        </Tooltip>\r\n                        <Tooltip title=\"Eliminar\">\r\n                          <IconButton\r\n                            size=\"small\"\r\n                            onClick={() => handleDeleteExpense(expense)}\r\n                            color=\"error\"\r\n                          >\r\n                            <DeleteIcon fontSize=\"small\" />\r\n                          </IconButton>\r\n                        </Tooltip>\r\n                      </Box>\r\n                    </StyledTableCell>\r\n                  </StyledTableRow>\r\n                ))}\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n          \r\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" p={2}>\r\n            <Typography variant=\"body2\" color=\"text.secondary\">\r\n              Mostrando {paginatedExpenses.length} de {expenses.length} gastos\r\n            </Typography>\r\n            <TablePagination\r\n              rowsPerPageOptions={[10, 25, 50]}\r\n              component=\"div\"\r\n              count={expenses.length}\r\n              rowsPerPage={rowsPerPage}\r\n              page={page}\r\n              onPageChange={(e, newPage) => setPage(newPage)}\r\n              onRowsPerPageChange={(e) => {\r\n                setRowsPerPage(parseInt(e.target.value, 10));\r\n                setPage(0);\r\n              }}\r\n              labelRowsPerPage=\"Filas por página:\"\r\n              labelDisplayedRows={({ from, to, count }) =>\r\n                `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`\r\n              }\r\n            />\r\n          </Box>\r\n        </Card>\r\n      )}\r\n\r\n      <ExpenseDialog\r\n        open={dialogOpen}\r\n        onClose={() => setDialogOpen(false)}\r\n        expense={selectedExpense}\r\n        onSave={handleSaveExpense}\r\n        vehicles={vehicles}\r\n      />\r\n    </Box>\r\n  );\r\n}"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,eAAe,EACfC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,0BAA0B;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAGlC,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC;EAAEgC;AAAM,CAAC,MAAM;EAC5CC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,OAAO;EACjBC,UAAU,EAAE,eAAe;EAC3B,SAAS,EAAE;IACTC,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAEL,KAAK,CAACM,OAAO,CAAC,CAAC;EAC5B;AACF,CAAC,CAAC,CAAC;;AAEH;AAAAC,EAAA,GAVMR,QAAQ;AAWd,MAAMS,eAAe,GAAG3C,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;EAAE2B;AAAM,CAAC,MAAM;EACxDS,YAAY,EAAE,aAAaT,KAAK,CAACU,OAAO,CAACC,OAAO,EAAE;EAClDC,OAAO,EAAE,WAAW;EACpB,iBAAiB,EAAE;IAAEC,WAAW,EAAE;EAAG,CAAC;EACtC,cAAc,EAAE;IAAEC,YAAY,EAAE;EAAG,CAAC;EACpC,qBAAqB,EAAE;IACrBC,UAAU,EAAE,GAAG;IACfC,KAAK,EAAEhB,KAAK,CAACU,OAAO,CAACO,IAAI,CAACC,OAAO;IACjCC,eAAe,EAAEnB,KAAK,CAACU,OAAO,CAACU,UAAU,CAACC;EAC5C;AACF,CAAC,CAAC,CAAC;AAACC,GAAA,GAVEd,eAAe;AAYrB,MAAMe,cAAc,GAAG1D,MAAM,CAACW,QAAQ,CAAC,CAAC,CAAC;EAAEwB;AAAM,CAAC,MAAM;EACtD,oBAAoB,EAAE;IACpBmB,eAAe,EAAEnB,KAAK,CAACU,OAAO,CAACU,UAAU,CAACI;EAC5C,CAAC;EACD,SAAS,EAAE;IACTL,eAAe,EAAEnB,KAAK,CAACU,OAAO,CAACe,MAAM,CAACC,KAAK;IAC3CvB,UAAU,EAAE;EACd,CAAC;EACD,iBAAiB,EAAE;IACjBM,YAAY,EAAE;EAChB;AACF,CAAC,CAAC,CAAC;AAACkB,GAAA,GAXEJ,cAAc;AAapB,MAAMK,UAAU,GAAG/D,MAAM,CAACC,GAAG,CAAC,CAAC,CAAC;EAAEkC;AAAM,CAAC,MAAM;EAC7CoB,UAAU,EAAEpB,KAAK,CAACU,OAAO,CAACU,UAAU,CAACC,KAAK;EAC1CT,OAAO,EAAEZ,KAAK,CAAC6B,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5BC,YAAY,EAAE9B,KAAK,CAAC+B,KAAK,CAACD,YAAY;EACtCzB,SAAS,EAAEL,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;EAC3B0B,YAAY,EAAEhC,KAAK,CAAC6B,OAAO,CAAC,CAAC,CAAC;EAC9BI,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,eAAe;EAC/BC,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAEH,MAAMC,aAAa,GAAGvE,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC;EAAE8B;AAAM,CAAC,MAAM;EACnDqC,aAAa,EAAE,MAAM;EACrBtB,UAAU,EAAE,GAAG;EACfH,OAAO,EAAE,UAAU;EACnBkB,YAAY,EAAE,CAAC;EACfzB,SAAS,EAAE,MAAM;EACjB,SAAS,EAAE;IACTD,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAEL,KAAK,CAACM,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDH,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAEH,MAAMmC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrF,QAAQ,CAAC;IACvCsF,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,mBAAmB,EAAE,EAAE;IACvBC,UAAU,EAAE,eAAe;IAC3BC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,CACnB,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,CACR;EAED/F,SAAS,CAAC,MAAM;IACd,IAAI+E,OAAO,EAAE;MACXK,WAAW,CAAC;QACV,GAAGL,OAAO;QACVO,KAAK,EAAEP,OAAO,CAACO,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE;MACtC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,WAAW,CAAC;QACVC,WAAW,EAAEJ,QAAQ,CAACe,MAAM,GAAG,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAACgB,EAAE,GAAG,EAAE;QACtDX,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7CC,mBAAmB,EAAE,EAAE;QACvBC,UAAU,EAAE,eAAe;QAC3BC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACf,OAAO,EAAEE,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EAE7B,MAAMqB,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzChB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgB,KAAK,GAAGC,KAAK,CAACC,MAAM,CAACC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjB,GAAGrB,QAAQ;MACXS,KAAK,EAAEa,UAAU,CAACtB,QAAQ,CAACS,KAAK,CAAC;MACjCF,mBAAmB,EAAEgB,QAAQ,CAACvB,QAAQ,CAACO,mBAAmB,CAAC,IAAI,CAAC;MAChEiB,SAAS,EAAExB,QAAQ,CAACQ,UAAU,CAAE;IAClC,CAAC;IACDX,MAAM,CAACwB,UAAU,CAAC;IAClB1B,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM8B,OAAO,GAAGzB,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACQ,UAAU,IAC7DR,QAAQ,CAACS,KAAK,IAAIT,QAAQ,CAACU,WAAW;EAEtD,oBACEzD,OAAA,CAAChB,MAAM;IAACyD,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC+B,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3D3E,OAAA,CAACf,WAAW;MAAA0F,QAAA,EACThC,OAAO,GAAG,cAAc,GAAG;IAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACd/E,OAAA,CAACd,aAAa;MAAAyF,QAAA,eACZ3E,OAAA,CAAChC,GAAG;QAACmE,OAAO,EAAC,MAAM;QAAC6C,aAAa,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACvD3E,OAAA,CAACZ,SAAS;UACR+F,KAAK,EAAC,aAAU;UAChBC,MAAM;UACNlB,KAAK,EAAEnB,QAAQ,CAACE,WAAY;UAC5BoC,QAAQ,EAAEvB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTY,QAAQ;UAAAX,QAAA,EAEP9B,QAAQ,CAAC0C,GAAG,CAAEC,OAAO,iBACpBxF,OAAA,CAACX,QAAQ;YAAkB6E,KAAK,EAAEsB,OAAO,CAAC3B,EAAG;YAAAc,QAAA,EAC1Ca,OAAO,CAACC;UAAM,GADFD,OAAO,CAAC3B,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/E,OAAA,CAAChC,GAAG;UAACmE,OAAO,EAAC,MAAM;UAAC8C,GAAG,EAAE,CAAE;UAAAN,QAAA,gBACzB3E,OAAA,CAACZ,SAAS;YACR+F,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,MAAM;YACXxB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;YACtBmC,QAAQ,EAAEvB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTY,QAAQ;YACRK,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACF/E,OAAA,CAACZ,SAAS;YACR+F,KAAK,EAAC,oBAAoB;YAC1BO,IAAI,EAAC,QAAQ;YACbxB,KAAK,EAAEnB,QAAQ,CAACO,mBAAoB;YACpC+B,QAAQ,EAAEvB,YAAY,CAAC,qBAAqB,CAAE;YAC9CY,SAAS;YACTmB,UAAU,EAAE;cAAEC,GAAG,EAAE;YAAE,CAAE;YACvBC,UAAU,EAAC;UAAU;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/E,OAAA,CAAChC,GAAG;UAACmE,OAAO,EAAC,MAAM;UAAC8C,GAAG,EAAE,CAAE;UAAAN,QAAA,gBACzB3E,OAAA,CAACZ,SAAS;YACR+F,KAAK,EAAC,eAAe;YACrBC,MAAM;YACNlB,KAAK,EAAEnB,QAAQ,CAACQ,UAAW;YAC3B8B,QAAQ,EAAEvB,YAAY,CAAC,YAAY,CAAE;YACrCY,SAAS;YACTY,QAAQ;YAAAX,QAAA,EAEPhB,YAAY,CAAC4B,GAAG,CAAEG,IAAI,iBACrB1F,OAAA,CAACX,QAAQ;cAAY6E,KAAK,EAAEwB,IAAK;cAAAf,QAAA,EAC9Be;YAAI,GADQA,IAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZ/E,OAAA,CAACZ,SAAS;YACR+F,KAAK,EAAC,gBAAW;YACjBO,IAAI,EAAC,QAAQ;YACbxB,KAAK,EAAEnB,QAAQ,CAACS,KAAM;YACtB6B,QAAQ,EAAEvB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTY,QAAQ;YACRO,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEE,IAAI,EAAE;YAAK;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/E,OAAA,CAACZ,SAAS;UACR+F,KAAK,EAAC,gBAAa;UACnBjB,KAAK,EAAEnB,QAAQ,CAACU,WAAY;UAC5B4B,QAAQ,EAAEvB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTY,QAAQ;UACRW,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,WAAW,EAAC;QAAgC;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEF/E,OAAA,CAACZ,SAAS;UACR+F,KAAK,EAAC,kBAAkB;UACxBjB,KAAK,EAAEnB,QAAQ,CAACW,SAAU;UAC1B2B,QAAQ,EAAEvB,YAAY,CAAC,WAAW,CAAE;UACpCY,SAAS;UACTyB,WAAW,EAAC;QAAiC;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChB/E,OAAA,CAACb,aAAa;MAAAwF,QAAA,gBACZ3E,OAAA,CAAC5B,MAAM;QAACgI,OAAO,EAAE1D,OAAQ;QAAAiC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3C/E,OAAA,CAAC5B,MAAM;QACLgI,OAAO,EAAEjC,YAAa;QACtBkC,OAAO,EAAC,WAAW;QACnBC,QAAQ,EAAE,CAAC9B,OAAQ;QAAAG,QAAA,EAElBhC,OAAO,GAAG,YAAY,GAAG;MAAS;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACjC,EAAA,CAtKIN,aAAa;AAAA+D,GAAA,GAAb/D,aAAa;AAwKnB,MAAMgE,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAMvG,KAAK,GAAGwG,QAAQ,CAAC,CAAC;EACxB,MAAM;IAAE7D,QAAQ;IAAE8D,QAAQ;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAG/G,MAAM,CAAC,CAAC;EACjE,MAAM,CAACgH,UAAU,EAAEC,aAAa,CAAC,GAAGpJ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGtJ,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuJ,IAAI,EAAEC,OAAO,CAAC,GAAGxJ,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACyJ,WAAW,EAAEC,cAAc,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM2J,iBAAiB,GAAG;IACxB,eAAe,EAAE,cAAc;IAC/B,YAAY,EAAE,YAAY;IAC1B,QAAQ,EAAE,WAAW;IACrB,KAAK,EAAE,gBAAgB;IACvB,WAAW,EAAE,cAAc;IAC3B,YAAY,EAAE,cAAc;IAC5B,QAAQ,EAAE,WAAW;IACrB,SAAS,EAAE,YAAY;IACvB,QAAQ,EAAE,YAAY;IACtB,SAAS,EAAE,cAAc;IACzB,OAAO,EAAE;EACX,CAAC;EAED1J,SAAS,CAAC,MAAM;IACd;IACAgJ,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7BN,kBAAkB,CAAC,IAAI,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMS,iBAAiB,GAAI7E,OAAO,IAAK;IACrCsE,kBAAkB,CAACtE,OAAO,CAAC;IAC3BoE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMU,mBAAmB,GAAI9E,OAAO,IAAK;IACvC;IACA+E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEhF,OAAO,CAAC;EACzC,CAAC;EAED,MAAMiF,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C,IAAI;MACF,IAAIb,eAAe,EAAE;QACnB;QACAU,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,WAAW,CAAC;MAC7C,CAAC,MAAM;QACL,MAAMhB,UAAU,CAACgB,WAAW,CAAC;QAC7B;QACAjB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACvK,MAAM,CAACmK,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOzK,MAAM,CAAC,IAAIsF,IAAI,CAACmF,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEC,MAAM,EAAEzK;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAOwK,UAAU;IACnB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,GAAG,IAAK;IAC5B,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACrK,MAAM,CAAC4K,GAAG,IAAI,CAAC,CAAC;EACxD,CAAC;EAED,MAAMC,mBAAmB,GAAIhD,IAAI,IAAK;IACpC,MAAMiD,MAAM,GAAG;MACb,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE,OAAO;MACrB,QAAQ,EAAE,MAAM;MAChB,KAAK,EAAE,SAAS;MAChB,WAAW,EAAE,WAAW;MACxB,YAAY,EAAE;IAChB,CAAC;IACD,OAAOA,MAAM,CAACjD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;;EAED;EACA,MAAMkD,aAAa,GAAGjC,QAAQ,CAAC/C,MAAM;EACrC,MAAMiF,SAAS,GAAGlC,QAAQ,CAACmC,MAAM,CAAC,CAACC,GAAG,EAAEpG,OAAO,KAAKoG,GAAG,IAAIpG,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAMwF,UAAU,GAAGJ,aAAa,GAAG,CAAC,GAAGC,SAAS,GAAGD,aAAa,GAAG,CAAC;;EAEpE;EACA,MAAMK,cAAc,GAAGtC,QAAQ,CAACmC,MAAM,CAAC,CAACI,GAAG,EAAEvG,OAAO,KAAK;IACvD,MAAM+C,IAAI,GAAG/C,OAAO,CAACY,UAAU,IAAI,OAAO;IAC1C2F,GAAG,CAACxD,IAAI,CAAC,GAAG,CAACwD,GAAG,CAACxD,IAAI,CAAC,IAAI,CAAC,KAAK/C,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC;IACnD,OAAO0F,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO,CAACJ,cAAc,CAAC,CAClDK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAC3BE,KAAK,CAAC,CAAC;;EAEV;EACA,MAAMC,iBAAiB,GAAG/C,QAAQ,CAACgD,KAAK,CACtCzC,IAAI,GAAGE,WAAW,EAClBF,IAAI,GAAGE,WAAW,GAAGA,WACvB,CAAC;EAED,oBACEpH,OAAA,CAAChC,GAAG;IAAC4L,EAAE,EAAE;MAAEC,CAAC,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAApF,QAAA,gBAC/B3E,OAAA,CAAChC,GAAG;MAACmE,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAAC2H,EAAE,EAAE,CAAE;MAAArF,QAAA,gBAC3E3E,OAAA,CAAC/B,UAAU;QAACoI,OAAO,EAAC,IAAI;QAAC4D,SAAS,EAAC,IAAI;QAAChJ,UAAU,EAAC,MAAM;QAACC,KAAK,EAAC,SAAS;QAAAyD,QAAA,EAAC;MAE1E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/E,OAAA,CAAC5B,MAAM;QACLiI,OAAO,EAAC,WAAW;QACnB6D,SAAS,eAAElK,OAAA,CAACT,OAAO;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBqB,OAAO,EAAEmB,gBAAiB;QAC1BjB,QAAQ,EAAEzD,QAAQ,CAACe,MAAM,KAAK,CAAE;QAChCgG,EAAE,EAAE;UACFtI,UAAU,EAAE,0BAA0BpB,KAAK,CAACU,OAAO,CAACQ,OAAO,CAAC+I,IAAI,QAAQjK,KAAK,CAACU,OAAO,CAACQ,OAAO,CAACgJ,IAAI;QACpG,CAAE;QAAAzF,QAAA,EAED9B,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;MAAa;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN/E,OAAA,CAAChC,GAAG;MAACmE,OAAO,EAAC,MAAM;MAACkI,QAAQ,EAAC,MAAM;MAACpF,GAAG,EAAE,CAAE;MAAC+E,EAAE,EAAE,CAAE;MAAArF,QAAA,gBAChD3E,OAAA,CAACC,QAAQ;QAAC2J,EAAE,EAAE;UAAEU,eAAe,EAAE;QAAe,CAAE;QAAA3F,QAAA,eAChD3E,OAAA,CAAC7B,WAAW;UAACyL,EAAE,EAAE;YAAEW,SAAS,EAAE,QAAQ;YAAEV,CAAC,EAAE;UAAE,CAAE;UAAAlF,QAAA,gBAC7C3E,OAAA,CAAC/B,UAAU;YAACoI,OAAO,EAAC,IAAI;YAACnF,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAA0D,QAAA,EAC5DgC,QAAQ,CAAC/C;UAAM;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACb/E,OAAA,CAAC/B,UAAU;YAACoI,OAAO,EAAC,OAAO;YAACnF,KAAK,EAAC,gBAAgB;YAAAyD,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACX/E,OAAA,CAACC,QAAQ;QAAC2J,EAAE,EAAE;UAAEU,eAAe,EAAE;QAAe,CAAE;QAAA3F,QAAA,eAChD3E,OAAA,CAAC7B,WAAW;UAACyL,EAAE,EAAE;YAAEW,SAAS,EAAE,QAAQ;YAAEV,CAAC,EAAE;UAAE,CAAE;UAAAlF,QAAA,gBAC7C3E,OAAA,CAAC/B,UAAU;YAACoI,OAAO,EAAC,IAAI;YAACnF,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAA0D,QAAA,EAC5DoD,cAAc,CAACc,SAAS;UAAC;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACb/E,OAAA,CAAC/B,UAAU;YAACoI,OAAO,EAAC,OAAO;YAACnF,KAAK,EAAC,gBAAgB;YAAAyD,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EACVoE,cAAc,iBACbnJ,OAAA,CAACC,QAAQ;QAAC2J,EAAE,EAAE;UAAEU,eAAe,EAAE;QAAY,CAAE;QAAA3F,QAAA,eAC7C3E,OAAA,CAAC7B,WAAW;UAACyL,EAAE,EAAE;YAAEW,SAAS,EAAE,QAAQ;YAAEV,CAAC,EAAE;UAAE,CAAE;UAAAlF,QAAA,gBAC7C3E,OAAA,CAAC/B,UAAU;YAACoI,OAAO,EAAC,IAAI;YAACnF,KAAK,EAAC,WAAW;YAACD,UAAU,EAAC,MAAM;YAAA0D,QAAA,EACzDwE,cAAc,CAAC,CAAC;UAAC;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACb/E,OAAA,CAAC/B,UAAU;YAACoI,OAAO,EAAC,OAAO;YAACnF,KAAK,EAAC,gBAAgB;YAAAyD,QAAA,EAC/CoD,cAAc,CAACoB,cAAc,CAAC,CAAC,CAAC;UAAC;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACb/E,OAAA,CAAC/B,UAAU;YAACoI,OAAO,EAAC,SAAS;YAACnF,KAAK,EAAC,gBAAgB;YAAAyD,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL4B,QAAQ,CAAC/C,MAAM,GAAG,CAAC,iBAClB5D,OAAA,CAAC9B,IAAI;MAAC0L,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAEQ,OAAO,EAAE;MAAmB,CAAE;MAAA7F,QAAA,eAC/C3E,OAAA,CAAC7B,WAAW;QAACyL,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAlF,QAAA,gBACxB3E,OAAA,CAAC/B,UAAU;UAACoI,OAAO,EAAC,WAAW;UAACnF,KAAK,EAAC,gBAAgB;UAACuJ,YAAY;UAAA9F,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/E,OAAA,CAAChC,GAAG;UAACmE,OAAO,EAAC,MAAM;UAACkI,QAAQ,EAAC,MAAM;UAACpF,GAAG,EAAE,CAAE;UAAAN,QAAA,gBACzC3E,OAAA,CAAChC,GAAG;YAACmE,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAAC4C,GAAG,EAAE,CAAE;YAAAN,QAAA,gBAC7C3E,OAAA,CAACH,WAAW;cAACqB,KAAK,EAAC,SAAS;cAACwJ,QAAQ,EAAC;YAAO;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD/E,OAAA,CAAC/B,UAAU;cAACoI,OAAO,EAAC,SAAS;cAAA1B,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN/E,OAAA,CAAChC,GAAG;YAACmE,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAAC4C,GAAG,EAAE,CAAE;YAAAN,QAAA,gBAC7C3E,OAAA,CAACnB,IAAI;cAACsG,KAAK,EAAC,MAAM;cAACwF,IAAI,EAAC,OAAO;cAACzJ,KAAK,EAAC,SAAS;cAAC0I,EAAE,EAAE;gBAAEgB,MAAM,EAAE,EAAE;gBAAEF,QAAQ,EAAE;cAAU;YAAE;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3F/E,OAAA,CAAC/B,UAAU;cAACoI,OAAO,EAAC,SAAS;cAAA1B,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACN/E,OAAA,CAAChC,GAAG;YAACmE,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAAC4C,GAAG,EAAE,CAAE;YAAAN,QAAA,gBAC7C3E,OAAA,CAACnB,IAAI;cAACsG,KAAK,EAAC,MAAM;cAACwF,IAAI,EAAC,OAAO;cAACzJ,KAAK,EAAC,OAAO;cAAC0I,EAAE,EAAE;gBAAEgB,MAAM,EAAE,EAAE;gBAAEF,QAAQ,EAAE;cAAU;YAAE;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzF/E,OAAA,CAAC/B,UAAU;cAACoI,OAAO,EAAC,SAAS;cAAA1B,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,EAEA4B,QAAQ,CAAC/C,MAAM,KAAK,CAAC,gBACpB5D,OAAA,CAAC9B,IAAI;MAAC0L,EAAE,EAAE;QACRY,OAAO,EAAE,oBAAoB;QAC7BjK,SAAS,EAAE,6BAA6B;QACxCyB,YAAY,EAAE,CAAC;QACf6I,QAAQ,EAAE,QAAQ;QAClBN,SAAS,EAAE,QAAQ;QACnBV,CAAC,EAAE,CAAC;QACJ1H,OAAO,EAAE,MAAM;QACf6C,aAAa,EAAE,QAAQ;QACvB3C,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxB0I,SAAS,EAAE,GAAG;QACdd,EAAE,EAAE;MACN,CAAE;MAAArF,QAAA,gBACA3E,OAAA,CAACH,WAAW;QAAC+J,EAAE,EAAE;UAAEc,QAAQ,EAAE,EAAE;UAAExJ,KAAK,EAAE,gBAAgB;UAAE8I,EAAE,EAAE;QAAE;MAAE;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrE/E,OAAA,CAAC/B,UAAU;QAACoI,OAAO,EAAC,IAAI;QAACnF,KAAK,EAAC,eAAe;QAACuJ,YAAY;QAAA9F,QAAA,EAAC;MAE5D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/E,OAAA,CAAC/B,UAAU;QAACoI,OAAO,EAAC,OAAO;QAACnF,KAAK,EAAC,eAAe;QAAC8I,EAAE,EAAE,CAAE;QAAArF,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/E,OAAA,CAAC5B,MAAM;QACLiI,OAAO,EAAC,WAAW;QACnB6D,SAAS,eAAElK,OAAA,CAACT,OAAO;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBqB,OAAO,EAAEmB,gBAAiB;QAC1BjB,QAAQ,EAAEzD,QAAQ,CAACe,MAAM,KAAK,CAAE;QAAAe,QAAA,EAE/B9B,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;MAAe;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEP/E,OAAA,CAAC9B,IAAI;MAAAyG,QAAA,gBACH3E,OAAA,CAACxB,cAAc;QAAAmG,QAAA,eACb3E,OAAA,CAAC3B,KAAK;UAAAsG,QAAA,gBACJ3E,OAAA,CAACvB,SAAS;YAAAkG,QAAA,eACR3E,OAAA,CAACtB,QAAQ;cAAAiG,QAAA,gBACP3E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACxC/E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC3C/E,OAAA,CAACU,eAAe;gBAACqK,KAAK,EAAC,OAAO;gBAAApG,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACxD/E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC5C/E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC3C/E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC9C/E,OAAA,CAACU,eAAe;gBAACqK,KAAK,EAAC,QAAQ;gBAAApG,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/E,OAAA,CAAC1B,SAAS;YAAAqG,QAAA,EACP+E,iBAAiB,CAACnE,GAAG,CAAC,CAAC5C,OAAO,EAAEqI,KAAK,kBACpChL,OAAA,CAACyB,cAAc;cAAAkD,QAAA,gBACb3E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,eACd3E,OAAA,CAAChC,GAAG;kBAACmE,OAAO,EAAC,MAAM;kBAAC6C,aAAa,EAAC,QAAQ;kBAAAL,QAAA,gBACxC3E,OAAA,CAAC/B,UAAU;oBAACoI,OAAO,EAAC,OAAO;oBAACpF,UAAU,EAAE,GAAI;oBAACC,KAAK,EAAC,cAAc;oBAAAyD,QAAA,EAC9D0D,UAAU,CAAC1F,OAAO,CAACO,KAAK;kBAAC;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eACb/E,OAAA,CAAC/B,UAAU;oBAACoI,OAAO,EAAC,SAAS;oBAACnF,KAAK,EAAC,gBAAgB;oBAAAyD,QAAA,EACjD,IAAIxB,IAAI,CAACR,OAAO,CAACO,KAAK,CAAC,CAAC+H,kBAAkB,CAAC,EAAE,EAAE;sBAACC,IAAI,EAAE,SAAS;sBAAEC,MAAM,EAAC;oBAAS,CAAC;kBAAC;oBAAAvG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAClB/E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,eACd3E,OAAA,CAAChC,GAAG;kBAACmE,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAAC4C,GAAG,EAAE,CAAE;kBAAAN,QAAA,GAC5ChC,OAAO,CAACyI,aAAa,iBAAIpL,OAAA,CAACH,WAAW;oBAACqB,KAAK,EAAC,SAAS;oBAACwJ,QAAQ,EAAC;kBAAO;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1E/E,OAAA,CAAC/B,UAAU;oBAACoI,OAAO,EAAC,OAAO;oBAACpF,UAAU,EAAE,GAAI;oBAAA0D,QAAA,EACzChC,OAAO,CAAC0I;kBAAQ;oBAAAzG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAClB/E,OAAA,CAACU,eAAe;gBAACqK,KAAK,EAAC,OAAO;gBAAApG,QAAA,eAC5B3E,OAAA,CAAC/B,UAAU;kBACToI,OAAO,EAAC,OAAO;kBACfpF,UAAU,EAAE,GAAI;kBAChBC,KAAK,EAAEyB,OAAO,CAACa,KAAK,GAAG,GAAG,GAAG,YAAY,GAAG,cAAe;kBAAAmB,QAAA,EAE1DoD,cAAc,CAACpF,OAAO,CAACa,KAAK;gBAAC;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAClB/E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,eACd3E,OAAA,CAAChC,GAAG;kBAACmE,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAAC4C,GAAG,EAAE,CAAE;kBAAAN,QAAA,GAC5ChC,OAAO,CAAC2I,UAAU,iBACjBtL,OAAA,CAACnB,IAAI;oBACHsG,KAAK,EAAC,MAAM;oBACZwF,IAAI,EAAC,OAAO;oBACZzJ,KAAK,EAAC,SAAS;oBACf0I,EAAE,EAAE;sBAAEgB,MAAM,EAAE,EAAE;sBAAEF,QAAQ,EAAE;oBAAU;kBAAE;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CACF,EACApC,OAAO,CAACa,KAAK,GAAG,GAAG,iBAClBxD,OAAA,CAACnB,IAAI;oBACHsG,KAAK,EAAC,MAAM;oBACZwF,IAAI,EAAC,OAAO;oBACZzJ,KAAK,EAAC,OAAO;oBACb0I,EAAE,EAAE;sBAAEgB,MAAM,EAAE,EAAE;sBAAEF,QAAQ,EAAE;oBAAU;kBAAE;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CACF,eACD/E,OAAA,CAAC/B,UAAU;oBAACoI,OAAO,EAAC,OAAO;oBAAA1B,QAAA,EACxBhC,OAAO,CAAC4B;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAClB/E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,eACd3E,OAAA,CAACnB,IAAI;kBACHsG,KAAK,EAAExC,OAAO,CAAC4I,eAAgB;kBAC/BZ,IAAI,EAAC,OAAO;kBACZtE,OAAO,EAAC,UAAU;kBAClBuD,EAAE,EAAE;oBACFgB,MAAM,EAAE,EAAE;oBACVF,QAAQ,EAAE,SAAS;oBACnBc,WAAW,EAAE;kBACf;gBAAE;kBAAA5G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACa,CAAC,eAClB/E,OAAA,CAACU,eAAe;gBAAAiE,QAAA,eACd3E,OAAA,CAACjB,OAAO;kBAAC0M,KAAK,EAAE9I,OAAO,CAACc,WAAW,IAAI,iBAAkB;kBAACiI,KAAK;kBAAA/G,QAAA,eAC7D3E,OAAA,CAAC/B,UAAU;oBACToI,OAAO,EAAC,OAAO;oBACfuD,EAAE,EAAE;sBACFzH,OAAO,EAAE,aAAa;sBACtBwJ,eAAe,EAAE,CAAC;sBAClBC,eAAe,EAAE,UAAU;sBAC3Bf,QAAQ,EAAE,QAAQ;sBAClBgB,YAAY,EAAE,UAAU;sBACxBpH,QAAQ,EAAE;oBACZ,CAAE;oBAAAE,QAAA,EAEDhC,OAAO,CAACc,WAAW,IAAI;kBAAG;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAClB/E,OAAA,CAACU,eAAe;gBAACqK,KAAK,EAAC,OAAO;gBAAApG,QAAA,eAC5B3E,OAAA,CAAChC,GAAG;kBAACmE,OAAO,EAAC,MAAM;kBAAC8C,GAAG,EAAE,CAAE;kBAAC7C,cAAc,EAAC,UAAU;kBAAAuC,QAAA,gBACnD3E,OAAA,CAACjB,OAAO;oBAAC0M,KAAK,EAAC,QAAQ;oBAAA9G,QAAA,eACrB3E,OAAA,CAAClB,UAAU;sBACT6L,IAAI,EAAC,OAAO;sBACZvE,OAAO,EAAEA,CAAA,KAAMoB,iBAAiB,CAAC7E,OAAO,CAAE;sBAAAgC,QAAA,eAE1C3E,OAAA,CAACP,QAAQ;wBAACiL,QAAQ,EAAC;sBAAO;wBAAA9F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV/E,OAAA,CAACjB,OAAO;oBAAC0M,KAAK,EAAC,UAAU;oBAAA9G,QAAA,eACvB3E,OAAA,CAAClB,UAAU;sBACT6L,IAAI,EAAC,OAAO;sBACZvE,OAAO,EAAEA,CAAA,KAAMqB,mBAAmB,CAAC9E,OAAO,CAAE;sBAC5CzB,KAAK,EAAC,OAAO;sBAAAyD,QAAA,eAEb3E,OAAA,CAACL,UAAU;wBAAC+K,QAAQ,EAAC;sBAAO;wBAAA9F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA,GApGCpC,OAAO,CAACkB,EAAE,IAAImH,KAAK;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqGxB,CACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEjB/E,OAAA,CAAChC,GAAG;QAACmE,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAACwH,CAAC,EAAE,CAAE;QAAAlF,QAAA,gBAC1E3E,OAAA,CAAC/B,UAAU;UAACoI,OAAO,EAAC,OAAO;UAACnF,KAAK,EAAC,gBAAgB;UAAAyD,QAAA,GAAC,YACvC,EAAC+E,iBAAiB,CAAC9F,MAAM,EAAC,MAAI,EAAC+C,QAAQ,CAAC/C,MAAM,EAAC,SAC3D;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/E,OAAA,CAACrB,eAAe;UACdmN,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjC7B,SAAS,EAAC,KAAK;UACf8B,KAAK,EAAEpF,QAAQ,CAAC/C,MAAO;UACvBwD,WAAW,EAAEA,WAAY;UACzBF,IAAI,EAAEA,IAAK;UACX8E,YAAY,EAAEA,CAACC,CAAC,EAAEC,OAAO,KAAK/E,OAAO,CAAC+E,OAAO,CAAE;UAC/CC,mBAAmB,EAAGF,CAAC,IAAK;YAC1B5E,cAAc,CAAC/C,QAAQ,CAAC2H,CAAC,CAAChI,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC5CiD,OAAO,CAAC,CAAC,CAAC;UACZ,CAAE;UACFiF,gBAAgB,EAAC,sBAAmB;UACpCC,kBAAkB,EAAEA,CAAC;YAAEC,IAAI;YAAEC,EAAE;YAAER;UAAM,CAAC,KACtC,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAG,UAAUQ,EAAE,EAAE;QAC1D;UAAA3H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,eAED/E,OAAA,CAACwC,aAAa;MACZC,IAAI,EAAEqE,UAAW;MACjBpE,OAAO,EAAEA,CAAA,KAAMqE,aAAa,CAAC,KAAK,CAAE;MACpCpE,OAAO,EAAEqE,eAAgB;MACzBpE,MAAM,EAAEgF,iBAAkB;MAC1B/E,QAAQ,EAAEA;IAAS;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAA0B,GAAA,CA1XKD,YAAY;EAAA,QAEyC1G,MAAM;AAAA;AAAA0M,GAAA,GAF3DhG,YAAY;AAAA,IAAA/F,EAAA,EAAAe,GAAA,EAAAK,GAAA,EAAA0E,GAAA,EAAAiG,GAAA;AAAAC,YAAA,CAAAhM,EAAA;AAAAgM,YAAA,CAAAjL,GAAA;AAAAiL,YAAA,CAAA5K,GAAA;AAAA4K,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}