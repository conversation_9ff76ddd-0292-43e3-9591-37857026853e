import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Info as InfoIcon,
  Storage as StorageIcon,
  Computer as ComputerIcon,
  Update as UpdateIcon,
  Backup as BackupIcon,
} from '@mui/icons-material';

const SystemInfo = () => {
  const [systemInfo, setSystemInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [backupDialog, setBackupDialog] = useState(false);
  const [updateDialog, setUpdateDialog] = useState(false);

  useEffect(() => {
    loadSystemInfo();
  }, []);

  const loadSystemInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3001/api/system/info');
      const data = await response.json();
      setSystemInfo(data);
    } catch (error) {
      console.error('Error loading system info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBackup = () => {
    // En una aplicación real, esto se comunicaría con Electron
    if (window.electronAPI) {
      window.electronAPI.createBackup();
    }
    setBackupDialog(false);
  };

  const handleCheckUpdates = () => {
    // En una aplicación real, esto se comunicaría con Electron
    if (window.electronAPI) {
      window.electronAPI.checkForUpdates();
    }
    setUpdateDialog(false);
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Información del Sistema
      </Typography>

      <Grid container spacing={3}>
        {/* Información de la aplicación */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <InfoIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Aplicación</Typography>
              </Box>
              <Table size="small">
                <TableBody>
                  <TableRow>
                    <TableCell>Versión</TableCell>
                    <TableCell>
                      <Chip label={systemInfo?.version || '1.0.0'} color="primary" size="small" />
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Tiempo activo</TableCell>
                    <TableCell>{formatUptime(systemInfo?.uptime || 0)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Plataforma</TableCell>
                    <TableCell>{systemInfo?.platform || 'N/A'}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Arquitectura</TableCell>
                    <TableCell>{systemInfo?.arch || 'N/A'}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </Grid>

        {/* Información del sistema */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <ComputerIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Sistema</Typography>
              </Box>
              <Table size="small">
                <TableBody>
                  <TableRow>
                    <TableCell>Node.js</TableCell>
                    <TableCell>{systemInfo?.node_version || 'N/A'}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Memoria usada</TableCell>
                    <TableCell>{formatBytes(systemInfo?.memory_usage?.heapUsed || 0)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Memoria total</TableCell>
                    <TableCell>{formatBytes(systemInfo?.memory_usage?.heapTotal || 0)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>RSS</TableCell>
                    <TableCell>{formatBytes(systemInfo?.memory_usage?.rss || 0)}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </Grid>

        {/* Información de la base de datos */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <StorageIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Base de Datos</Typography>
              </Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                La base de datos se encuentra en la misma carpeta que la aplicación para máxima portabilidad.
              </Alert>
              <Table size="small">
                <TableBody>
                  <TableRow>
                    <TableCell>Ruta de la aplicación</TableCell>
                    <TableCell sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                      {systemInfo?.app_path || 'N/A'}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Ruta de la base de datos</TableCell>
                    <TableCell sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                      {systemInfo?.database_path || 'N/A'}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </Grid>

        {/* Acciones del sistema */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Acciones del Sistema
              </Typography>
              <Box display="flex" gap={2} flexWrap="wrap">
                <Button
                  variant="contained"
                  startIcon={<BackupIcon />}
                  onClick={() => setBackupDialog(true)}
                  color="success"
                >
                  Crear Backup
                </Button>
                <Button
                  variant="contained"
                  startIcon={<UpdateIcon />}
                  onClick={() => setUpdateDialog(true)}
                  color="info"
                >
                  Buscar Actualizaciones
                </Button>
                <Button
                  variant="outlined"
                  onClick={loadSystemInfo}
                >
                  Actualizar Información
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Dialog de backup */}
      <Dialog open={backupDialog} onClose={() => setBackupDialog(false)}>
        <DialogTitle>Crear Backup</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Deseas crear un backup de la base de datos? El backup se guardará en la carpeta 'backups' 
            junto a la aplicación.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBackupDialog(false)}>Cancelar</Button>
          <Button onClick={handleCreateBackup} variant="contained">Crear Backup</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de actualizaciones */}
      <Dialog open={updateDialog} onClose={() => setUpdateDialog(false)}>
        <DialogTitle>Buscar Actualizaciones</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Deseas buscar actualizaciones disponibles? Si hay una nueva versión disponible, 
            se descargará automáticamente.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpdateDialog(false)}>Cancelar</Button>
          <Button onClick={handleCheckUpdates} variant="contained">Buscar Actualizaciones</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SystemInfo;
