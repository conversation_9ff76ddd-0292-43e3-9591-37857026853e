{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Refuels\\\\RefuelsList.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { styled } from '@mui/material/styles';\nimport { Box, Typography, Card, CardContent, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControlLabel, Switch, Tooltip, TablePagination, Avatar, useTheme, alpha } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, LocalGasStation as GasIcon, TrendingUp as TrendingUpIcon, TrendingDown as TrendingDownIcon } from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\n\n// Styled Components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledTableCell = styled(TableCell)(({\n  theme\n}) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '14px 16px',\n  '&:first-of-type': {\n    paddingLeft: 24\n  },\n  '&:last-child': {\n    paddingRight: 24\n  },\n  '&.MuiTableCell-head': {\n    fontWeight: 600,\n    color: theme.palette.text.primary,\n    backgroundColor: theme.palette.background.paper\n  }\n}));\n_c = StyledTableCell;\nconst StyledTableRow = styled(TableRow)(({\n  theme\n}) => ({\n  '&:nth-of-type(odd)': {\n    backgroundColor: theme.palette.background.default\n  },\n  '&:hover': {\n    backgroundColor: theme.palette.action.hover,\n    transition: 'background-color 0.2s'\n  },\n  '&:last-child td, &:last-child th': {\n    border: 0\n  }\n}));\n_c2 = StyledTableRow;\nconst StatCard = styled(Card)(({\n  theme\n}) => ({\n  flex: '1 1 180px',\n  minWidth: '180px',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: theme.shadows[4]\n  },\n  borderLeft: `4px solid ${theme.palette.primary.main}`\n}));\n_c3 = StatCard;\nconst PageHeader = styled(Box)(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: theme.spacing(4),\n  padding: theme.spacing(2, 0),\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  [theme.breakpoints.down('sm')]: {\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    gap: theme.spacing(2)\n  }\n}));\n_c4 = PageHeader;\nconst PrimaryButton = styled(Button)(({\n  theme\n}) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-2px)',\n    boxShadow: theme.shadows[3]\n  },\n  transition: 'all 0.2s ease'\n}));\n_c5 = PrimaryButton;\nconst RefuelDialog = ({\n  open,\n  onClose,\n  refuel,\n  onSave,\n  vehicles\n}) => {\n  _s();\n  const theme = useTheme();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    litros: '',\n    precio_litro: '',\n    coste_total: '',\n    gasolinera: '',\n    deposito_lleno: true,\n    notas: ''\n  });\n  useEffect(() => {\n    if (refuel) {\n      setFormData({\n        ...refuel,\n        fecha: refuel.fecha.split('T')[0],\n        // Formato para input date\n        deposito_lleno: !!refuel.deposito_lleno // Convert 0/1 to false/true\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        litros: '',\n        precio_litro: '',\n        coste_total: '',\n        gasolinera: '',\n        deposito_lleno: true,\n        notas: ''\n      });\n    }\n  }, [refuel, vehicles, open]);\n  const handleChange = field => event => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => {\n      const newData = {\n        ...prev,\n        [field]: value\n      };\n\n      // Cálculos automáticos para precios y costes.\n      if (field === 'coste_total') {\n        // Si el usuario modifica el coste total, calculamos el precio por litro.\n        const litros = parseFloat(newData.litros) || 0;\n        const coste = parseFloat(newData.coste_total) || 0;\n        if (litros > 0) {\n          newData.precio_litro = (coste / litros).toFixed(3);\n        }\n      } else if (field === 'litros' || field === 'precio_litro') {\n        // Si el usuario modifica los litros o el precio por litro, calculamos el coste total.\n        const litros = parseFloat(newData.litros) || 0;\n        const precio = parseFloat(newData.precio_litro) || 0;\n        newData.coste_total = (litros * precio).toFixed(2);\n      }\n      return newData;\n    });\n  };\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      litros: parseFloat(formData.litros),\n      precio_litro: parseFloat(formData.precio_litro),\n      coste_total: parseFloat(formData.coste_total),\n      kilometros_actuales: parseInt(formData.kilometros_actuales),\n      deposito_lleno: formData.deposito_lleno ? 1 : 0\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n  const isValid = formData.vehiculo_id && formData.fecha && formData.kilometros_actuales && formData.litros && formData.precio_litro;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: refuel ? 'Editar Repostaje' : 'Nuevo Repostaje'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: 2,\n        pt: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Veh\\xEDculo\",\n          select: true,\n          value: formData.vehiculo_id,\n          onChange: handleChange('vehiculo_id'),\n          fullWidth: true,\n          required: true,\n          children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: vehicle.id,\n            children: vehicle.nombre\n          }, vehicle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Fecha\",\n            type: \"date\",\n            value: formData.fecha,\n            onChange: handleChange('fecha'),\n            fullWidth: true,\n            required: true,\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Kilometraje actual\",\n            type: \"number\",\n            value: formData.kilometros_actuales,\n            onChange: handleChange('kilometros_actuales'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Litros\",\n            type: \"number\",\n            value: formData.litros,\n            onChange: handleChange('litros'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Precio por litro (\\u20AC)\",\n            type: \"number\",\n            value: formData.precio_litro,\n            onChange: handleChange('precio_litro'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.001\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Coste total (\\u20AC)\",\n            type: \"number\",\n            value: formData.coste_total,\n            onChange: handleChange('coste_total'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Gasolinera\",\n          value: formData.gasolinera,\n          onChange: handleChange('gasolinera'),\n          fullWidth: true,\n          placeholder: \"Nombre de la gasolinera\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: formData.deposito_lleno,\n            onChange: handleChange('deposito_lleno')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this),\n          label: \"Dep\\xF3sito lleno\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Notas\",\n          value: formData.notas,\n          onChange: handleChange('notas'),\n          fullWidth: true,\n          multiline: true,\n          rows: 2,\n          placeholder: \"Notas adicionales...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: !isValid,\n        children: refuel ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n};\n_s(RefuelDialog, \"gbZPbpwPMwXyzjZZcKBu4IyrxSY=\", false, function () {\n  return [useTheme];\n});\n_c6 = RefuelDialog;\nconst RefuelsList = () => {\n  _s2();\n  const {\n    vehicles,\n    refuels,\n    loadRefuels,\n    addRefuel\n  } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedRefuel, setSelectedRefuel] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  useEffect(() => {\n    // Cargar todos los repostajes (sin límite)\n    loadRefuels(null, null);\n  }, []);\n  const handleAddRefuel = () => {\n    setSelectedRefuel(null);\n    setDialogOpen(true);\n  };\n  const handleEditRefuel = refuel => {\n    setSelectedRefuel(refuel);\n    setDialogOpen(true);\n  };\n  const handleDeleteRefuel = refuel => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete refuel:', refuel);\n  };\n  const handleSaveRefuel = async refuelData => {\n    try {\n      if (selectedRefuel) {\n        // TODO: Implementar actualización\n        console.log('Update refuel:', refuelData);\n      } else {\n        await addRefuel(refuelData);\n        // Recargar la lista\n        loadRefuels(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving refuel:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  // Formatear números a máximo 3 decimales\n  const formatDecimal = (num, decimals = 3) => {\n    if (num === null || num === undefined || isNaN(num)) return '-';\n    const factor = Math.pow(10, decimals);\n    const rounded = Math.round(num * factor) / factor;\n    // Eliminar ceros decimales innecesarios\n    return rounded.toString().replace(/\\.?0+$/, '');\n  };\n\n  // Calcular kilómetros recorridos y consumo\n  const calculateTripInfo = (refuel, index) => {\n    // Buscar el repostaje anterior del mismo vehículo (incluyendo parciales)\n    const previousRefuel = refuels.slice(index + 1).find(r => r.vehiculo_id === refuel.vehiculo_id);\n    if (!previousRefuel) return {\n      kmTraveled: null,\n      consumption: null\n    };\n    const kmTraveled = refuel.kilometros_actuales - previousRefuel.kilometros_actuales;\n\n    // Solo calcular consumo para repostajes completos\n    let consumption = null;\n    if (refuel.deposito_lleno && kmTraveled > 0) {\n      // Calcular consumo basado en los litros repostados y la distancia recorrida\n      consumption = refuel.litros * 100 / kmTraveled;\n    }\n    return {\n      kmTraveled: kmTraveled > 0 ? kmTraveled : null,\n      consumption: consumption,\n      isValidForStats: kmTraveled > 0 && refuel.deposito_lleno\n    };\n  };\n\n  // Procesar todos los repostajes para obtener estadísticas\n  const processedRefuels = refuels.map((refuel, index) => {\n    const tripInfo = calculateTripInfo(refuel, index);\n    return {\n      ...refuel,\n      ...tripInfo\n    };\n  });\n\n  // Filtrar repostajes con datos de viaje válidos\n  const allTrips = processedRefuels.filter(r => r.kmTraveled > 0);\n  const validTrips = processedRefuels.filter(r => r.isValidForStats);\n\n  // Calcular estadísticas de distancia (todos los viajes con km válidos)\n  const distances = allTrips.map(trip => trip.kmTraveled);\n  const minDistance = distances.length > 0 ? Math.min(...distances) : 0;\n  const maxDistance = distances.length > 0 ? Math.max(...distances) : 0;\n  const avgDistance = distances.length > 0 ? distances.reduce((a, b) => a + b, 0) / distances.length : 0;\n\n  // Calcular estadísticas rápidas\n  const totalRefuels = refuels.length;\n  const totalLiters = refuels.reduce((sum, refuel) => sum + (refuel.litros || 0), 0);\n  const totalCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);\n  const avgPricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0;\n\n  // Calcular consumo promedio (solo viajes válidos)\n  const avgConsumption = validTrips.length > 0 ? validTrips.reduce((sum, trip) => sum + trip.consumption, 0) / validTrips.length : 0;\n\n  // Paginación\n  const paginatedRefuels = processedRefuels.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  const theme = useTheme();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: {\n        xs: 2,\n        md: 3\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          fontWeight: \"bold\",\n          color: \"primary\",\n          children: \"Repostajes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Gesti\\xF3n de repostajes de veh\\xEDculos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddRefuel,\n        disabled: vehicles.length === 0,\n        sx: {\n          background: `linear-gradient(45deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`\n        },\n        children: \"Nuevo Repostaje\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexWrap: \"wrap\",\n      gap: 2,\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'primary.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary.main\",\n            fontWeight: \"bold\",\n            children: refuels.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Total Repostajes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'success.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"success.main\",\n            fontWeight: \"bold\",\n            children: [formatDecimal(totalLiters, 1), \"L\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Total Litros\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'warning.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"warning.main\",\n            fontWeight: \"bold\",\n            children: formatCurrency(totalCost)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Coste Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'info.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"info.main\",\n            fontWeight: \"bold\",\n            children: [validTrips.length > 0 ? formatDecimal(avgConsumption, 1) : '-', \"L\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Consumo Promedio/100km\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [validTrips.length, \" viajes v\\xE1lidos\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'secondary.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                color: \"text.secondary\",\n                children: \"Distancia entre repostajes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: \"M\\xEDn:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"error.main\",\n                  children: minDistance ? formatDecimal(minDistance, 0) + ' km' : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: \"M\\xE1x:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"success.main\",\n                  children: maxDistance ? formatDecimal(maxDistance, 0) + ' km' : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: \"Media:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"info.main\",\n                  children: avgDistance ? formatDecimal(avgDistance, 0) + ' km' : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this), refuels.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          py: 6,\n          children: [/*#__PURE__*/_jsxDEV(GasIcon, {\n            sx: {\n              fontSize: 64,\n              color: 'text.secondary',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"textSecondary\",\n            gutterBottom: true,\n            children: \"No hay repostajes registrados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            mb: 3,\n            children: \"Comienza registrando tu primer repostaje para hacer seguimiento del consumo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 26\n            }, this),\n            onClick: handleAddRefuel,\n            disabled: vehicles.length === 0,\n            children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Repostaje'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        borderRadius: 2,\n        overflow: 'hidden',\n        boxShadow: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Fecha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Km Actuales\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Km Recorridos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Litros\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Precio/L\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Consumo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Gasolinera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Dep\\xF3sito\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"center\",\n                children: \"Acciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: paginatedRefuels.map((refuel, index) => {\n              const tripInfo = calculateTripInfo(refuel, index);\n              const isFullTank = refuel.deposito_lleno;\n              return /*#__PURE__*/_jsxDEV(StyledTableRow, {\n                children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: 600,\n                      color: \"text.primary\",\n                      children: formatDate(refuel.fecha)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: new Date(refuel.fecha).toLocaleTimeString([], {\n                        hour: '2-digit',\n                        minute: '2-digit'\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: refuel.vehiculo_nombre\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: formatNumber(refuel.kilometros_actuales)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"flex-end\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: 600,\n                      color: refuel.kmTraveled > 0 ? 'primary.main' : 'text.secondary',\n                      children: refuel.kmTraveled ? formatNumber(Math.round(refuel.kmTraveled)) + ' km' : '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 25\n                    }, this), refuel.kmTraveled > 0 && refuel.kmTraveled < avgDistance * 0.7 && /*#__PURE__*/_jsxDEV(Chip, {\n                      label: \"Corto\",\n                      size: \"small\",\n                      color: \"warning\",\n                      sx: {\n                        height: 18,\n                        fontSize: '0.65rem',\n                        mt: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 27\n                    }, this), refuel.kmTraveled > avgDistance * 1.3 && /*#__PURE__*/_jsxDEV(Chip, {\n                      label: \"Largo\",\n                      size: \"small\",\n                      color: \"success\",\n                      sx: {\n                        height: 18,\n                        fontSize: '0.65rem',\n                        mt: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: [formatDecimal(refuel.litros, 1), \"L\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: [formatDecimal(refuel.precio_litro, 3), \"\\u20AC\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: formatCurrency(refuel.coste_total)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"flex-end\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: 600,\n                      color: refuel.consumption ? refuel.consumption < avgConsumption * 0.9 ? 'success.main' : refuel.consumption > avgConsumption * 1.1 ? 'error.main' : 'text.primary' : 'text.secondary',\n                      children: refuel.consumption ? formatDecimal(refuel.consumption, 1) + 'L' : refuel.kmTraveled ? 'Parcial' : '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 25\n                    }, this), refuel.consumption && refuel.consumption < avgConsumption * 0.9 && /*#__PURE__*/_jsxDEV(TrendingDownIcon, {\n                      color: \"success\",\n                      fontSize: \"small\",\n                      sx: {\n                        mt: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 27\n                    }, this), refuel.consumption && refuel.consumption > avgConsumption * 1.1 && /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                      color: \"error\",\n                      fontSize: \"small\",\n                      sx: {\n                        mt: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: refuel.gasolinera || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: refuel.deposito_lleno ? 'Sí' : 'No',\n                    color: refuel.deposito_lleno ? 'success' : 'default',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Editar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleEditRefuel(refuel),\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 663,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Eliminar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteRefuel(refuel),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 21\n                }, this)]\n              }, refuel.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n        component: \"div\",\n        count: refuels.length,\n        page: page,\n        onPageChange: (event, newPage) => setPage(newPage),\n        rowsPerPage: rowsPerPage,\n        onRowsPerPageChange: event => {\n          setRowsPerPage(parseInt(event.target.value, 10));\n          setPage(0);\n        },\n        rowsPerPageOptions: [10, 25, 50, 100],\n        labelRowsPerPage: \"Filas por p\\xE1gina:\",\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} de ${count}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RefuelDialog, {\n      open: dialogOpen,\n      onClose: () => setDialogOpen(false),\n      refuel: selectedRefuel,\n      onSave: handleSaveRefuel,\n      vehicles: vehicles\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 431,\n    columnNumber: 5\n  }, this);\n};\n_s2(RefuelsList, \"UU78kW/clTeGYpF4+JDV4EhWcQ4=\", false, function () {\n  return [useApp, useTheme];\n});\n_c7 = RefuelsList;\nexport default RefuelsList;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"StyledTableCell\");\n$RefreshReg$(_c2, \"StyledTableRow\");\n$RefreshReg$(_c3, \"StatCard\");\n$RefreshReg$(_c4, \"PageHeader\");\n$RefreshReg$(_c5, \"PrimaryButton\");\n$RefreshReg$(_c6, \"RefuelDialog\");\n$RefreshReg$(_c7, \"RefuelsList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "FormControlLabel", "Switch", "<PERSON><PERSON><PERSON>", "TablePagination", "Avatar", "useTheme", "alpha", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "LocalGasStation", "GasIcon", "TrendingUp", "TrendingUpIcon", "TrendingDown", "TrendingDownIcon", "useApp", "format", "es", "jsxDEV", "_jsxDEV", "StyledTableCell", "theme", "borderBottom", "palette", "divider", "padding", "paddingLeft", "paddingRight", "fontWeight", "color", "text", "primary", "backgroundColor", "background", "paper", "_c", "StyledTableRow", "default", "action", "hover", "transition", "border", "_c2", "StatCard", "flex", "min<PERSON><PERSON><PERSON>", "transform", "boxShadow", "shadows", "borderLeft", "main", "_c3", "<PERSON><PERSON><PERSON><PERSON>", "display", "justifyContent", "alignItems", "marginBottom", "spacing", "breakpoints", "down", "flexDirection", "gap", "_c4", "PrimaryButton", "textTransform", "borderRadius", "_c5", "RefuelDialog", "open", "onClose", "refuel", "onSave", "vehicles", "_s", "formData", "setFormData", "vehiculo_id", "fecha", "Date", "toISOString", "split", "kilometros_actuales", "litros", "precio_litro", "coste_total", "gasolinera", "deposito_lleno", "notas", "length", "id", "handleChange", "field", "event", "value", "target", "type", "checked", "prev", "newData", "parseFloat", "coste", "toFixed", "precio", "handleSubmit", "dataToSave", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pt", "label", "select", "onChange", "required", "map", "vehicle", "nombre", "InputLabelProps", "shrink", "inputProps", "min", "step", "placeholder", "control", "multiline", "rows", "onClick", "variant", "disabled", "_c6", "RefuelsList", "_s2", "refuels", "loadRefuels", "addRefuel", "dialogOpen", "setDialogOpen", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRefuel", "page", "setPage", "rowsPerPage", "setRowsPerPage", "handleAddRefuel", "handleEditRefuel", "handleDeleteRefuel", "console", "log", "handleSaveRefuel", "refuelData", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "locale", "formatNumber", "num", "formatDecimal", "decimals", "undefined", "isNaN", "factor", "Math", "pow", "rounded", "round", "toString", "replace", "calculateTripInfo", "index", "previousRefuel", "slice", "find", "r", "kmTraveled", "consumption", "isValidForStats", "processedRefuels", "tripInfo", "allTrips", "filter", "validTrips", "distances", "trip", "minDistance", "maxDistance", "max", "avgDistance", "reduce", "a", "b", "totalRefuels", "totalLiters", "sum", "totalCost", "avgPricePerLiter", "avgConsumption", "paginatedRefuels", "sx", "p", "xs", "md", "component", "startIcon", "dark", "flexWrap", "mb", "borderLeftColor", "textAlign", "py", "fontSize", "gutterBottom", "overflow", "align", "isFullTank", "toLocaleTimeString", "hour", "minute", "vehiculo_nombre", "size", "height", "mt", "title", "count", "onPageChange", "newPage", "onRowsPerPageChange", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c7", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Refuels/RefuelsList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { styled } from '@mui/material/styles';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  FormControlLabel,\n  Switch,\n  Tooltip,\n  TablePagination,\n  Avatar,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  LocalGasStation as GasIcon,\n  TrendingUp as TrendingUpIcon,\n  TrendingDown as TrendingDownIcon,\n} from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\n\n// Styled Components\nconst StyledTableCell = styled(TableCell)(({ theme }) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '14px 16px',\n  '&:first-of-type': { paddingLeft: 24 },\n  '&:last-child': { paddingRight: 24 },\n  '&.MuiTableCell-head': {\n    fontWeight: 600,\n    color: theme.palette.text.primary,\n    backgroundColor: theme.palette.background.paper,\n  },\n}));\n\nconst StyledTableRow = styled(TableRow)(({ theme }) => ({\n  '&:nth-of-type(odd)': {\n    backgroundColor: theme.palette.background.default,\n  },\n  '&:hover': {\n    backgroundColor: theme.palette.action.hover,\n    transition: 'background-color 0.2s',\n  },\n  '&:last-child td, &:last-child th': {\n    border: 0,\n  },\n}));\n\nconst StatCard = styled(Card)(({ theme }) => ({\n  flex: '1 1 180px',\n  minWidth: '180px',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: theme.shadows[4],\n  },\n  borderLeft: `4px solid ${theme.palette.primary.main}`,\n}));\n\nconst PageHeader = styled(Box)(({ theme }) => ({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: theme.spacing(4),\n  padding: theme.spacing(2, 0),\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  [theme.breakpoints.down('sm')]: {\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    gap: theme.spacing(2),\n  },\n}));\n\nconst PrimaryButton = styled(Button)(({ theme }) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-2px)',\n    boxShadow: theme.shadows[3],\n  },\n  transition: 'all 0.2s ease',\n}));\n\nconst RefuelDialog = ({ open, onClose, refuel, onSave, vehicles }) => {\n  const theme = useTheme();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    litros: '',\n    precio_litro: '',\n    coste_total: '',\n    gasolinera: '',\n    deposito_lleno: true,\n    notas: '',\n  });\n\n  useEffect(() => {\n    if (refuel) {\n      setFormData({\n        ...refuel,\n        fecha: refuel.fecha.split('T')[0], // Formato para input date\n        deposito_lleno: !!refuel.deposito_lleno, // Convert 0/1 to false/true\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        litros: '',\n        precio_litro: '',\n        coste_total: '',\n        gasolinera: '',\n        deposito_lleno: true,\n        notas: '',\n      });\n    }\n  }, [refuel, vehicles, open]);\n\n  const handleChange = (field) => (event) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => {\n      const newData = { ...prev, [field]: value };\n      \n      // Cálculos automáticos para precios y costes.\n      if (field === 'coste_total') {\n        // Si el usuario modifica el coste total, calculamos el precio por litro.\n        const litros = parseFloat(newData.litros) || 0;\n        const coste = parseFloat(newData.coste_total) || 0;\n        if (litros > 0) {\n          newData.precio_litro = (coste / litros).toFixed(3);\n        }\n      } else if (field === 'litros' || field === 'precio_litro') {\n        // Si el usuario modifica los litros o el precio por litro, calculamos el coste total.\n        const litros = parseFloat(newData.litros) || 0;\n        const precio = parseFloat(newData.precio_litro) || 0;\n        newData.coste_total = (litros * precio).toFixed(2);\n      }\n      \n      return newData;\n    });\n  };\n\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      litros: parseFloat(formData.litros),\n      precio_litro: parseFloat(formData.precio_litro),\n      coste_total: parseFloat(formData.coste_total),\n      kilometros_actuales: parseInt(formData.kilometros_actuales),\n      deposito_lleno: formData.deposito_lleno ? 1 : 0,\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n\n  const isValid = formData.vehiculo_id && formData.fecha && formData.kilometros_actuales && \n                  formData.litros && formData.precio_litro;\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        {refuel ? 'Editar Repostaje' : 'Nuevo Repostaje'}\n      </DialogTitle>\n      <DialogContent>\n        <Box display=\"flex\" flexDirection=\"column\" gap={2} pt={1}>\n          <TextField\n            label=\"Vehículo\"\n            select\n            value={formData.vehiculo_id}\n            onChange={handleChange('vehiculo_id')}\n            fullWidth\n            required\n          >\n            {vehicles.map((vehicle) => (\n              <MenuItem key={vehicle.id} value={vehicle.id}>\n                {vehicle.nombre}\n              </MenuItem>\n            ))}\n          </TextField>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Fecha\"\n              type=\"date\"\n              value={formData.fecha}\n              onChange={handleChange('fecha')}\n              fullWidth\n              required\n              InputLabelProps={{ shrink: true }}\n            />\n            <TextField\n              label=\"Kilometraje actual\"\n              type=\"number\"\n              value={formData.kilometros_actuales}\n              onChange={handleChange('kilometros_actuales')}\n              fullWidth\n              required\n              inputProps={{ min: 0 }}\n            />\n          </Box>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Litros\"\n              type=\"number\"\n              value={formData.litros}\n              onChange={handleChange('litros')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n            <TextField\n              label=\"Precio por litro (€)\"\n              type=\"number\"\n              value={formData.precio_litro}\n              onChange={handleChange('precio_litro')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.001 }}\n            />\n            <TextField\n              label=\"Coste total (€)\"\n              type=\"number\"\n              value={formData.coste_total}\n              onChange={handleChange('coste_total')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n          </Box>\n\n          <TextField\n            label=\"Gasolinera\"\n            value={formData.gasolinera}\n            onChange={handleChange('gasolinera')}\n            fullWidth\n            placeholder=\"Nombre de la gasolinera\"\n          />\n\n          <FormControlLabel\n            control={\n              <Switch\n                checked={formData.deposito_lleno}\n                onChange={handleChange('deposito_lleno')}\n              />\n            }\n            label=\"Depósito lleno\"\n          />\n\n          <TextField\n            label=\"Notas\"\n            value={formData.notas}\n            onChange={handleChange('notas')}\n            fullWidth\n            multiline\n            rows={2}\n            placeholder=\"Notas adicionales...\"\n          />\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose}>Cancelar</Button>\n        <Button \n          onClick={handleSubmit} \n          variant=\"contained\"\n          disabled={!isValid}\n        >\n          {refuel ? 'Actualizar' : 'Guardar'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nconst RefuelsList = () => {\n  const { vehicles, refuels, loadRefuels, addRefuel } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedRefuel, setSelectedRefuel] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n\n  useEffect(() => {\n    // Cargar todos los repostajes (sin límite)\n    loadRefuels(null, null);\n  }, []);\n\n  const handleAddRefuel = () => {\n    setSelectedRefuel(null);\n    setDialogOpen(true);\n  };\n\n  const handleEditRefuel = (refuel) => {\n    setSelectedRefuel(refuel);\n    setDialogOpen(true);\n  };\n\n  const handleDeleteRefuel = (refuel) => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete refuel:', refuel);\n  };\n\n  const handleSaveRefuel = async (refuelData) => {\n    try {\n      if (selectedRefuel) {\n        // TODO: Implementar actualización\n        console.log('Update refuel:', refuelData);\n      } else {\n        await addRefuel(refuelData);\n        // Recargar la lista\n        loadRefuels(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving refuel:', error);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  const formatDate = (dateString) => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\n    } catch {\n      return dateString;\n    }\n  };\n\n  const formatNumber = (num) => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  // Formatear números a máximo 3 decimales\n  const formatDecimal = (num, decimals = 3) => {\n    if (num === null || num === undefined || isNaN(num)) return '-';\n    const factor = Math.pow(10, decimals);\n    const rounded = Math.round(num * factor) / factor;\n    // Eliminar ceros decimales innecesarios\n    return rounded.toString().replace(/\\.?0+$/, '');\n  };\n\n  // Calcular kilómetros recorridos y consumo\n  const calculateTripInfo = (refuel, index) => {\n    // Buscar el repostaje anterior del mismo vehículo (incluyendo parciales)\n    const previousRefuel = refuels\n      .slice(index + 1)\n      .find(r => r.vehiculo_id === refuel.vehiculo_id);\n    \n    if (!previousRefuel) return { kmTraveled: null, consumption: null };\n    \n    const kmTraveled = refuel.kilometros_actuales - previousRefuel.kilometros_actuales;\n    \n    // Solo calcular consumo para repostajes completos\n    let consumption = null;\n    if (refuel.deposito_lleno && kmTraveled > 0) {\n      // Calcular consumo basado en los litros repostados y la distancia recorrida\n      consumption = (refuel.litros * 100) / kmTraveled;\n    }\n    \n    return {\n      kmTraveled: kmTraveled > 0 ? kmTraveled : null,\n      consumption: consumption,\n      isValidForStats: kmTraveled > 0 && refuel.deposito_lleno\n    };\n  };\n\n  // Procesar todos los repostajes para obtener estadísticas\n  const processedRefuels = refuels.map((refuel, index) => {\n    const tripInfo = calculateTripInfo(refuel, index);\n    return {\n      ...refuel,\n      ...tripInfo\n    };\n  });\n\n  // Filtrar repostajes con datos de viaje válidos\n  const allTrips = processedRefuels.filter(r => r.kmTraveled > 0);\n  const validTrips = processedRefuels.filter(r => r.isValidForStats);\n  \n  // Calcular estadísticas de distancia (todos los viajes con km válidos)\n  const distances = allTrips.map(trip => trip.kmTraveled);\n  const minDistance = distances.length > 0 ? Math.min(...distances) : 0;\n  const maxDistance = distances.length > 0 ? Math.max(...distances) : 0;\n  const avgDistance = distances.length > 0 ? \n    distances.reduce((a, b) => a + b, 0) / distances.length : 0;\n\n  // Calcular estadísticas rápidas\n  const totalRefuels = refuels.length;\n  const totalLiters = refuels.reduce((sum, refuel) => sum + (refuel.litros || 0), 0);\n  const totalCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);\n  const avgPricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0;\n  \n  // Calcular consumo promedio (solo viajes válidos)\n  const avgConsumption = validTrips.length > 0 ? \n    validTrips.reduce((sum, trip) => sum + trip.consumption, 0) / validTrips.length : 0;\n\n  // Paginación\n  const paginatedRefuels = processedRefuels.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n\n  const theme = useTheme();\n  \n  return (\n    <Box sx={{ p: { xs: 2, md: 3 } }}>\n      <PageHeader>\n        <Box>\n          <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\" color=\"primary\">\n            Repostajes\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Gestión de repostajes de vehículos\n          </Typography>\n        </Box>\n        <PrimaryButton\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleAddRefuel}\n          disabled={vehicles.length === 0}\n          sx={{\n            background: `linear-gradient(45deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n          }}\n        >\n          Nuevo Repostaje\n        </PrimaryButton>\n      </PageHeader>\n\n      {/* Estadísticas rápidas */}\n      <Box display=\"flex\" flexWrap=\"wrap\" gap={2} mb={3}>\n        <StatCard sx={{ borderLeftColor: 'primary.main' }}>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Typography variant=\"h4\" color=\"primary.main\" fontWeight=\"bold\">\n              {refuels.length}\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">Total Repostajes</Typography>\n          </CardContent>\n        </StatCard>\n        <StatCard sx={{ borderLeftColor: 'success.main' }}>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Typography variant=\"h4\" color=\"success.main\" fontWeight=\"bold\">\n              {formatDecimal(totalLiters, 1)}L\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">Total Litros</Typography>\n          </CardContent>\n        </StatCard>\n        <StatCard sx={{ borderLeftColor: 'warning.main' }}>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Typography variant=\"h4\" color=\"warning.main\" fontWeight=\"bold\">\n              {formatCurrency(totalCost)}\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">Coste Total</Typography>\n          </CardContent>\n        </StatCard>\n        <StatCard sx={{ borderLeftColor: 'info.main' }}>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Typography variant=\"h4\" color=\"info.main\" fontWeight=\"bold\">\n              {validTrips.length > 0 ? formatDecimal(avgConsumption, 1) : '-'}L\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">Consumo Promedio/100km</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {validTrips.length} viajes válidos\n            </Typography>\n          </CardContent>\n        </StatCard>\n        <StatCard sx={{ borderLeftColor: 'secondary.main' }}>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Box display=\"flex\" flexDirection=\"column\" gap={1}>\n              <Box>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">Distancia entre repostajes</Typography>\n              </Box>\n              <Box display=\"flex\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"caption\" display=\"block\">Mín:</Typography>\n                  <Typography variant=\"h6\" color=\"error.main\">\n                    {minDistance ? formatDecimal(minDistance, 0) + ' km' : '-'}\n                  </Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"caption\" display=\"block\">Máx:</Typography>\n                  <Typography variant=\"h6\" color=\"success.main\">\n                    {maxDistance ? formatDecimal(maxDistance, 0) + ' km' : '-'}\n                  </Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"caption\" display=\"block\">Media:</Typography>\n                  <Typography variant=\"h6\" color=\"info.main\">\n                    {avgDistance ? formatDecimal(avgDistance, 0) + ' km' : '-'}\n                  </Typography>\n                </Box>\n              </Box>\n            </Box>\n          </CardContent>\n        </StatCard>\n      </Box>\n\n      {refuels.length === 0 ? (\n      <Card>\n        <CardContent>\n          <Box textAlign=\"center\" py={6}>\n            <GasIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" color=\"textSecondary\" gutterBottom>\n              No hay repostajes registrados\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\" mb={3}>\n              Comienza registrando tu primer repostaje para hacer seguimiento del consumo.\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={handleAddRefuel}\n              disabled={vehicles.length === 0}\n            >\n              {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Repostaje'}\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n    ) : (\n      <Card sx={{ borderRadius: 2, overflow: 'hidden', boxShadow: 2 }}>\n        <TableContainer>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <StyledTableCell>Fecha</StyledTableCell>\n                <StyledTableCell>Vehículo</StyledTableCell>\n                <StyledTableCell align=\"right\">Km Actuales</StyledTableCell>\n                <StyledTableCell align=\"right\">Km Recorridos</StyledTableCell>\n                <StyledTableCell align=\"right\">Litros</StyledTableCell>\n                <StyledTableCell align=\"right\">Precio/L</StyledTableCell>\n                <StyledTableCell align=\"right\">Total</StyledTableCell>\n                <StyledTableCell align=\"right\">Consumo</StyledTableCell>\n                <StyledTableCell>Gasolinera</StyledTableCell>\n                <StyledTableCell>Depósito</StyledTableCell>\n                <StyledTableCell align=\"center\">Acciones</StyledTableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {paginatedRefuels.map((refuel, index) => {\n                const tripInfo = calculateTripInfo(refuel, index);\n                const isFullTank = refuel.deposito_lleno;\n                \n                return (\n                  <StyledTableRow key={refuel.id}>\n                    <StyledTableCell>\n                      <Box display=\"flex\" flexDirection=\"column\">\n                        <Typography variant=\"body2\" fontWeight={600} color=\"text.primary\">\n                          {formatDate(refuel.fecha)}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {new Date(refuel.fecha).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}\n                        </Typography>\n                      </Box>\n                    </StyledTableCell>\n                    <StyledTableCell>\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {refuel.vehiculo_nombre}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatNumber(refuel.kilometros_actuales)}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"flex-end\">\n                        <Typography variant=\"body2\" fontWeight={600} color={refuel.kmTraveled > 0 ? 'primary.main' : 'text.secondary'}>\n                          {refuel.kmTraveled ? formatNumber(Math.round(refuel.kmTraveled)) + ' km' : '-'}\n                        </Typography>\n                        {refuel.kmTraveled > 0 && refuel.kmTraveled < (avgDistance * 0.7) && (\n                          <Chip \n                            label=\"Corto\" \n                            size=\"small\" \n                            color=\"warning\" \n                            sx={{ height: 18, fontSize: '0.65rem', mt: 0.5 }}\n                          />\n                        )}\n                        {refuel.kmTraveled > (avgDistance * 1.3) && (\n                          <Chip \n                            label=\"Largo\" \n                            size=\"small\" \n                            color=\"success\" \n                            sx={{ height: 18, fontSize: '0.65rem', mt: 0.5 }}\n                          />\n                        )}\n                      </Box>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatDecimal(refuel.litros, 1)}L\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatDecimal(refuel.precio_litro, 3)}€\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatCurrency(refuel.coste_total)}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"flex-end\">\n                        <Typography \n                          variant=\"body2\" \n                          fontWeight={600}\n                          color={refuel.consumption ? \n                            (refuel.consumption < (avgConsumption * 0.9) ? 'success.main' : \n                             refuel.consumption > (avgConsumption * 1.1) ? 'error.main' : 'text.primary') : 'text.secondary'}\n                        >\n                          {refuel.consumption ? formatDecimal(refuel.consumption, 1) + 'L' : \n                           refuel.kmTraveled ? 'Parcial' : '-'}\n                        </Typography>\n                        {refuel.consumption && refuel.consumption < (avgConsumption * 0.9) && (\n                          <TrendingDownIcon color=\"success\" fontSize=\"small\" sx={{ mt: 0.5 }} />\n                        )}\n                        {refuel.consumption && refuel.consumption > (avgConsumption * 1.1) && (\n                          <TrendingUpIcon color=\"error\" fontSize=\"small\" sx={{ mt: 0.5 }} />\n                        )}\n                      </Box>\n                    </StyledTableCell>\n                    <StyledTableCell>\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {refuel.gasolinera || '-'}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell>\n                      <Chip \n                        label={refuel.deposito_lleno ? 'Sí' : 'No'} \n                        color={refuel.deposito_lleno ? 'success' : 'default'}\n                        size=\"small\"\n                      />\n                    </StyledTableCell>\n                    <StyledTableCell align=\"center\">\n                      <Tooltip title=\"Editar\">\n                        <IconButton size=\"small\" onClick={() => handleEditRefuel(refuel)}>\n                          <EditIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Eliminar\">\n                        <IconButton size=\"small\" color=\"error\" onClick={() => handleDeleteRefuel(refuel)}>\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </StyledTableCell>\n                  </StyledTableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n        \n        <TablePagination\n          component=\"div\"\n          count={refuels.length}\n          page={page}\n          onPageChange={(event, newPage) => setPage(newPage)}\n          rowsPerPage={rowsPerPage}\n          onRowsPerPageChange={(event) => {\n            setRowsPerPage(parseInt(event.target.value, 10));\n            setPage(0);\n          }}\n          rowsPerPageOptions={[10, 25, 50, 100]}\n          labelRowsPerPage=\"Filas por página:\"\n          labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}\n        />\n      </Card>\n    )}\n      <RefuelDialog\n        open={dialogOpen}\n        onClose={() => setDialogOpen(false)}\n        refuel={selectedRefuel}\n        onSave={handleSaveRefuel}\n        vehicles={vehicles}\n      />\n    </Box>\n  );\n};\n\nexport default RefuelsList;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,OAAO,EACPC,eAAe,EACfC,MAAM,EACNC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,eAAe,IAAIC,OAAO,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAG7C,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;EAAEsC;AAAM,CAAC,MAAM;EACxDC,YAAY,EAAE,aAAaD,KAAK,CAACE,OAAO,CAACC,OAAO,EAAE;EAClDC,OAAO,EAAE,WAAW;EACpB,iBAAiB,EAAE;IAAEC,WAAW,EAAE;EAAG,CAAC;EACtC,cAAc,EAAE;IAAEC,YAAY,EAAE;EAAG,CAAC;EACpC,qBAAqB,EAAE;IACrBC,UAAU,EAAE,GAAG;IACfC,KAAK,EAAER,KAAK,CAACE,OAAO,CAACO,IAAI,CAACC,OAAO;IACjCC,eAAe,EAAEX,KAAK,CAACE,OAAO,CAACU,UAAU,CAACC;EAC5C;AACF,CAAC,CAAC,CAAC;AAACC,EAAA,GAVEf,eAAe;AAYrB,MAAMgB,cAAc,GAAG7D,MAAM,CAACW,QAAQ,CAAC,CAAC,CAAC;EAAEmC;AAAM,CAAC,MAAM;EACtD,oBAAoB,EAAE;IACpBW,eAAe,EAAEX,KAAK,CAACE,OAAO,CAACU,UAAU,CAACI;EAC5C,CAAC;EACD,SAAS,EAAE;IACTL,eAAe,EAAEX,KAAK,CAACE,OAAO,CAACe,MAAM,CAACC,KAAK;IAC3CC,UAAU,EAAE;EACd,CAAC;EACD,kCAAkC,EAAE;IAClCC,MAAM,EAAE;EACV;AACF,CAAC,CAAC,CAAC;AAACC,GAAA,GAXEN,cAAc;AAapB,MAAMO,QAAQ,GAAGpE,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC;EAAE2C;AAAM,CAAC,MAAM;EAC5CuB,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,OAAO;EACjBL,UAAU,EAAE,eAAe;EAC3B,SAAS,EAAE;IACTM,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAE1B,KAAK,CAAC2B,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDC,UAAU,EAAE,aAAa5B,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACmB,IAAI;AACrD,CAAC,CAAC,CAAC;AAACC,GAAA,GATER,QAAQ;AAWd,MAAMS,UAAU,GAAG7E,MAAM,CAACC,GAAG,CAAC,CAAC,CAAC;EAAE6C;AAAM,CAAC,MAAM;EAC7CgC,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,eAAe;EAC/BC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAEnC,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC;EAC9BhC,OAAO,EAAEJ,KAAK,CAACoC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5BnC,YAAY,EAAE,aAAaD,KAAK,CAACE,OAAO,CAACC,OAAO,EAAE;EAClD,CAACH,KAAK,CAACqC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;IAC9BC,aAAa,EAAE,QAAQ;IACvBL,UAAU,EAAE,YAAY;IACxBM,GAAG,EAAExC,KAAK,CAACoC,OAAO,CAAC,CAAC;EACtB;AACF,CAAC,CAAC,CAAC;AAACK,GAAA,GAZEV,UAAU;AAchB,MAAMW,aAAa,GAAGxF,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC;EAAEyC;AAAM,CAAC,MAAM;EACnD2C,aAAa,EAAE,MAAM;EACrBpC,UAAU,EAAE,GAAG;EACfH,OAAO,EAAE,UAAU;EACnBwC,YAAY,EAAE,CAAC;EACflB,SAAS,EAAE,MAAM;EACjB,SAAS,EAAE;IACTD,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAE1B,KAAK,CAAC2B,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDR,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAAC0B,GAAA,GAXEH,aAAa;AAanB,MAAMI,YAAY,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,MAAM;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAMpD,KAAK,GAAGpB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC;IACvCuG,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,mBAAmB,EAAE,EAAE;IACvBC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,IAAI;IACpBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFjH,SAAS,CAAC,MAAM;IACd,IAAIgG,MAAM,EAAE;MACVK,WAAW,CAAC;QACV,GAAGL,MAAM;QACTO,KAAK,EAAEP,MAAM,CAACO,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAE;QACnCM,cAAc,EAAE,CAAC,CAAChB,MAAM,CAACgB,cAAc,CAAE;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,WAAW,CAAC;QACVC,WAAW,EAAEJ,QAAQ,CAACgB,MAAM,GAAG,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAACiB,EAAE,GAAG,EAAE;QACtDZ,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7CC,mBAAmB,EAAE,EAAE;QACvBC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,IAAI;QACpBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjB,MAAM,EAAEE,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EAE5B,MAAMsB,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,UAAU,GAAGH,KAAK,CAACE,MAAM,CAACE,OAAO,GAAGJ,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1FlB,WAAW,CAACsB,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG;QAAE,GAAGD,IAAI;QAAE,CAACN,KAAK,GAAGE;MAAM,CAAC;;MAE3C;MACA,IAAIF,KAAK,KAAK,aAAa,EAAE;QAC3B;QACA,MAAMT,MAAM,GAAGiB,UAAU,CAACD,OAAO,CAAChB,MAAM,CAAC,IAAI,CAAC;QAC9C,MAAMkB,KAAK,GAAGD,UAAU,CAACD,OAAO,CAACd,WAAW,CAAC,IAAI,CAAC;QAClD,IAAIF,MAAM,GAAG,CAAC,EAAE;UACdgB,OAAO,CAACf,YAAY,GAAG,CAACiB,KAAK,GAAGlB,MAAM,EAAEmB,OAAO,CAAC,CAAC,CAAC;QACpD;MACF,CAAC,MAAM,IAAIV,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,cAAc,EAAE;QACzD;QACA,MAAMT,MAAM,GAAGiB,UAAU,CAACD,OAAO,CAAChB,MAAM,CAAC,IAAI,CAAC;QAC9C,MAAMoB,MAAM,GAAGH,UAAU,CAACD,OAAO,CAACf,YAAY,CAAC,IAAI,CAAC;QACpDe,OAAO,CAACd,WAAW,GAAG,CAACF,MAAM,GAAGoB,MAAM,EAAED,OAAO,CAAC,CAAC,CAAC;MACpD;MAEA,OAAOH,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjB,GAAG9B,QAAQ;MACXQ,MAAM,EAAEiB,UAAU,CAACzB,QAAQ,CAACQ,MAAM,CAAC;MACnCC,YAAY,EAAEgB,UAAU,CAACzB,QAAQ,CAACS,YAAY,CAAC;MAC/CC,WAAW,EAAEe,UAAU,CAACzB,QAAQ,CAACU,WAAW,CAAC;MAC7CH,mBAAmB,EAAEwB,QAAQ,CAAC/B,QAAQ,CAACO,mBAAmB,CAAC;MAC3DK,cAAc,EAAEZ,QAAQ,CAACY,cAAc,GAAG,CAAC,GAAG;IAChD,CAAC;IACDf,MAAM,CAACiC,UAAU,CAAC;IAClBnC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMqC,OAAO,GAAGhC,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACO,mBAAmB,IACtEP,QAAQ,CAACQ,MAAM,IAAIR,QAAQ,CAACS,YAAY;EAExD,oBACEhE,OAAA,CAAC7B,MAAM;IAAC8E,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACsC,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3D1F,OAAA,CAAC5B,WAAW;MAAAsH,QAAA,EACTvC,MAAM,GAAG,kBAAkB,GAAG;IAAiB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACd9F,OAAA,CAAC3B,aAAa;MAAAqH,QAAA,eACZ1F,OAAA,CAAC3C,GAAG;QAAC6E,OAAO,EAAC,MAAM;QAACO,aAAa,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACqD,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACvD1F,OAAA,CAACzB,SAAS;UACRyH,KAAK,EAAC,aAAU;UAChBC,MAAM;UACNvB,KAAK,EAAEnB,QAAQ,CAACE,WAAY;UAC5ByC,QAAQ,EAAE3B,YAAY,CAAC,aAAa,CAAE;UACtCkB,SAAS;UACTU,QAAQ;UAAAT,QAAA,EAEPrC,QAAQ,CAAC+C,GAAG,CAAEC,OAAO,iBACpBrG,OAAA,CAACxB,QAAQ;YAAkBkG,KAAK,EAAE2B,OAAO,CAAC/B,EAAG;YAAAoB,QAAA,EAC1CW,OAAO,CAACC;UAAM,GADFD,OAAO,CAAC/B,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ9F,OAAA,CAAC3C,GAAG;UAAC6E,OAAO,EAAC,MAAM;UAACQ,GAAG,EAAE,CAAE;UAAAgD,QAAA,gBACzB1F,OAAA,CAACzB,SAAS;YACRyH,KAAK,EAAC,OAAO;YACbpB,IAAI,EAAC,MAAM;YACXF,KAAK,EAAEnB,QAAQ,CAACG,KAAM;YACtBwC,QAAQ,EAAE3B,YAAY,CAAC,OAAO,CAAE;YAChCkB,SAAS;YACTU,QAAQ;YACRI,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACF9F,OAAA,CAACzB,SAAS;YACRyH,KAAK,EAAC,oBAAoB;YAC1BpB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACO,mBAAoB;YACpCoC,QAAQ,EAAE3B,YAAY,CAAC,qBAAqB,CAAE;YAC9CkB,SAAS;YACTU,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE;YAAE;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9F,OAAA,CAAC3C,GAAG;UAAC6E,OAAO,EAAC,MAAM;UAACQ,GAAG,EAAE,CAAE;UAAAgD,QAAA,gBACzB1F,OAAA,CAACzB,SAAS;YACRyH,KAAK,EAAC,QAAQ;YACdpB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACQ,MAAO;YACvBmC,QAAQ,EAAE3B,YAAY,CAAC,QAAQ,CAAE;YACjCkB,SAAS;YACTU,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACF9F,OAAA,CAACzB,SAAS;YACRyH,KAAK,EAAC,2BAAsB;YAC5BpB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACS,YAAa;YAC7BkC,QAAQ,EAAE3B,YAAY,CAAC,cAAc,CAAE;YACvCkB,SAAS;YACTU,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAM;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACF9F,OAAA,CAACzB,SAAS;YACRyH,KAAK,EAAC,sBAAiB;YACvBpB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACU,WAAY;YAC5BiC,QAAQ,EAAE3B,YAAY,CAAC,aAAa,CAAE;YACtCkB,SAAS;YACTU,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9F,OAAA,CAACzB,SAAS;UACRyH,KAAK,EAAC,YAAY;UAClBtB,KAAK,EAAEnB,QAAQ,CAACW,UAAW;UAC3BgC,QAAQ,EAAE3B,YAAY,CAAC,YAAY,CAAE;UACrCkB,SAAS;UACTmB,WAAW,EAAC;QAAyB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAEF9F,OAAA,CAACvB,gBAAgB;UACfoI,OAAO,eACL7G,OAAA,CAACtB,MAAM;YACLmG,OAAO,EAAEtB,QAAQ,CAACY,cAAe;YACjC+B,QAAQ,EAAE3B,YAAY,CAAC,gBAAgB;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;UACDE,KAAK,EAAC;QAAgB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAEF9F,OAAA,CAACzB,SAAS;UACRyH,KAAK,EAAC,OAAO;UACbtB,KAAK,EAAEnB,QAAQ,CAACa,KAAM;UACtB8B,QAAQ,EAAE3B,YAAY,CAAC,OAAO,CAAE;UAChCkB,SAAS;UACTqB,SAAS;UACTC,IAAI,EAAE,CAAE;UACRH,WAAW,EAAC;QAAsB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChB9F,OAAA,CAAC1B,aAAa;MAAAoH,QAAA,gBACZ1F,OAAA,CAACvC,MAAM;QAACuJ,OAAO,EAAE9D,OAAQ;QAAAwC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3C9F,OAAA,CAACvC,MAAM;QACLuJ,OAAO,EAAE5B,YAAa;QACtB6B,OAAO,EAAC,WAAW;QACnBC,QAAQ,EAAE,CAAC3B,OAAQ;QAAAG,QAAA,EAElBvC,MAAM,GAAG,YAAY,GAAG;MAAS;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACxC,EAAA,CA9LIN,YAAY;EAAA,QACFlE,QAAQ;AAAA;AAAAqI,GAAA,GADlBnE,YAAY;AAgMlB,MAAMoE,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAM;IAAEhE,QAAQ;IAAEiE,OAAO;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAG5H,MAAM,CAAC,CAAC;EAC9D,MAAM,CAAC6H,UAAU,EAAEC,aAAa,CAAC,GAAGxK,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyK,cAAc,EAAEC,iBAAiB,CAAC,GAAG1K,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2K,IAAI,EAAEC,OAAO,CAAC,GAAG5K,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC6K,WAAW,EAAEC,cAAc,CAAC,GAAG9K,QAAQ,CAAC,EAAE,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd;IACAoK,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BL,iBAAiB,CAAC,IAAI,CAAC;IACvBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMQ,gBAAgB,GAAI/E,MAAM,IAAK;IACnCyE,iBAAiB,CAACzE,MAAM,CAAC;IACzBuE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMS,kBAAkB,GAAIhF,MAAM,IAAK;IACrC;IACAiF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAElF,MAAM,CAAC;EACvC,CAAC;EAED,MAAMmF,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC7C,IAAI;MACF,IAAIZ,cAAc,EAAE;QAClB;QACAS,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,UAAU,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMf,SAAS,CAACe,UAAU,CAAC;QAC3B;QACAhB,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACjJ,MAAM,CAAC6I,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOnJ,MAAM,CAAC,IAAI8D,IAAI,CAACqF,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEC,MAAM,EAAEnJ;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAOkJ,UAAU;IACnB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,GAAG,IAAK;IAC5B,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAAC/I,MAAM,CAACsJ,GAAG,IAAI,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACD,GAAG,EAAEE,QAAQ,GAAG,CAAC,KAAK;IAC3C,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,IAAIC,KAAK,CAACJ,GAAG,CAAC,EAAE,OAAO,GAAG;IAC/D,MAAMK,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEL,QAAQ,CAAC;IACrC,MAAMM,OAAO,GAAGF,IAAI,CAACG,KAAK,CAACT,GAAG,GAAGK,MAAM,CAAC,GAAGA,MAAM;IACjD;IACA,OAAOG,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;EACjD,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAC5G,MAAM,EAAE6G,KAAK,KAAK;IAC3C;IACA,MAAMC,cAAc,GAAG3C,OAAO,CAC3B4C,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC,CAChBG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3G,WAAW,KAAKN,MAAM,CAACM,WAAW,CAAC;IAElD,IAAI,CAACwG,cAAc,EAAE,OAAO;MAAEI,UAAU,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAK,CAAC;IAEnE,MAAMD,UAAU,GAAGlH,MAAM,CAACW,mBAAmB,GAAGmG,cAAc,CAACnG,mBAAmB;;IAElF;IACA,IAAIwG,WAAW,GAAG,IAAI;IACtB,IAAInH,MAAM,CAACgB,cAAc,IAAIkG,UAAU,GAAG,CAAC,EAAE;MAC3C;MACAC,WAAW,GAAInH,MAAM,CAACY,MAAM,GAAG,GAAG,GAAIsG,UAAU;IAClD;IAEA,OAAO;MACLA,UAAU,EAAEA,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,IAAI;MAC9CC,WAAW,EAAEA,WAAW;MACxBC,eAAe,EAAEF,UAAU,GAAG,CAAC,IAAIlH,MAAM,CAACgB;IAC5C,CAAC;EACH,CAAC;;EAED;EACA,MAAMqG,gBAAgB,GAAGlD,OAAO,CAAClB,GAAG,CAAC,CAACjD,MAAM,EAAE6G,KAAK,KAAK;IACtD,MAAMS,QAAQ,GAAGV,iBAAiB,CAAC5G,MAAM,EAAE6G,KAAK,CAAC;IACjD,OAAO;MACL,GAAG7G,MAAM;MACT,GAAGsH;IACL,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAMC,QAAQ,GAAGF,gBAAgB,CAACG,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACC,UAAU,GAAG,CAAC,CAAC;EAC/D,MAAMO,UAAU,GAAGJ,gBAAgB,CAACG,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC;;EAElE;EACA,MAAMM,SAAS,GAAGH,QAAQ,CAACtE,GAAG,CAAC0E,IAAI,IAAIA,IAAI,CAACT,UAAU,CAAC;EACvD,MAAMU,WAAW,GAAGF,SAAS,CAACxG,MAAM,GAAG,CAAC,GAAGoF,IAAI,CAAC/C,GAAG,CAAC,GAAGmE,SAAS,CAAC,GAAG,CAAC;EACrE,MAAMG,WAAW,GAAGH,SAAS,CAACxG,MAAM,GAAG,CAAC,GAAGoF,IAAI,CAACwB,GAAG,CAAC,GAAGJ,SAAS,CAAC,GAAG,CAAC;EACrE,MAAMK,WAAW,GAAGL,SAAS,CAACxG,MAAM,GAAG,CAAC,GACtCwG,SAAS,CAACM,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGR,SAAS,CAACxG,MAAM,GAAG,CAAC;;EAE7D;EACA,MAAMiH,YAAY,GAAGhE,OAAO,CAACjD,MAAM;EACnC,MAAMkH,WAAW,GAAGjE,OAAO,CAAC6D,MAAM,CAAC,CAACK,GAAG,EAAErI,MAAM,KAAKqI,GAAG,IAAIrI,MAAM,CAACY,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAM0H,SAAS,GAAGnE,OAAO,CAAC6D,MAAM,CAAC,CAACK,GAAG,EAAErI,MAAM,KAAKqI,GAAG,IAAIrI,MAAM,CAACc,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF,MAAMyH,gBAAgB,GAAGH,WAAW,GAAG,CAAC,GAAGE,SAAS,GAAGF,WAAW,GAAG,CAAC;;EAEtE;EACA,MAAMI,cAAc,GAAGf,UAAU,CAACvG,MAAM,GAAG,CAAC,GAC1CuG,UAAU,CAACO,MAAM,CAAC,CAACK,GAAG,EAAEV,IAAI,KAAKU,GAAG,GAAGV,IAAI,CAACR,WAAW,EAAE,CAAC,CAAC,GAAGM,UAAU,CAACvG,MAAM,GAAG,CAAC;;EAErF;EACA,MAAMuH,gBAAgB,GAAGpB,gBAAgB,CAACN,KAAK,CAACrC,IAAI,GAAGE,WAAW,EAAEF,IAAI,GAAGE,WAAW,GAAGA,WAAW,CAAC;EAErG,MAAM7H,KAAK,GAAGpB,QAAQ,CAAC,CAAC;EAExB,oBACEkB,OAAA,CAAC3C,GAAG;IAACwO,EAAE,EAAE;MAAEC,CAAC,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAAtG,QAAA,gBAC/B1F,OAAA,CAACiC,UAAU;MAAAyD,QAAA,gBACT1F,OAAA,CAAC3C,GAAG;QAAAqI,QAAA,gBACF1F,OAAA,CAAC1C,UAAU;UAAC2J,OAAO,EAAC,IAAI;UAACgF,SAAS,EAAC,IAAI;UAACxL,UAAU,EAAC,MAAM;UAACC,KAAK,EAAC,SAAS;UAAAgF,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb9F,OAAA,CAAC1C,UAAU;UAAC2J,OAAO,EAAC,OAAO;UAACvG,KAAK,EAAC,gBAAgB;UAAAgF,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN9F,OAAA,CAAC4C,aAAa;QACZqE,OAAO,EAAC,WAAW;QACnBiF,SAAS,eAAElM,OAAA,CAACf,OAAO;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBkB,OAAO,EAAEiB,eAAgB;QACzBf,QAAQ,EAAE7D,QAAQ,CAACgB,MAAM,KAAK,CAAE;QAChCwH,EAAE,EAAE;UACF/K,UAAU,EAAE,0BAA0BZ,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACmB,IAAI,QAAQ7B,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACuL,IAAI;QACpG,CAAE;QAAAzG,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGb9F,OAAA,CAAC3C,GAAG;MAAC6E,OAAO,EAAC,MAAM;MAACkK,QAAQ,EAAC,MAAM;MAAC1J,GAAG,EAAE,CAAE;MAAC2J,EAAE,EAAE,CAAE;MAAA3G,QAAA,gBAChD1F,OAAA,CAACwB,QAAQ;QAACqK,EAAE,EAAE;UAAES,eAAe,EAAE;QAAe,CAAE;QAAA5G,QAAA,eAChD1F,OAAA,CAACxC,WAAW;UAACqO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,gBAC7C1F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACvG,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAAiF,QAAA,EAC5D4B,OAAO,CAACjD;UAAM;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACb9F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAACvG,KAAK,EAAC,gBAAgB;YAAAgF,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACX9F,OAAA,CAACwB,QAAQ;QAACqK,EAAE,EAAE;UAAES,eAAe,EAAE;QAAe,CAAE;QAAA5G,QAAA,eAChD1F,OAAA,CAACxC,WAAW;UAACqO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,gBAC7C1F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACvG,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAAiF,QAAA,GAC5D0D,aAAa,CAACmC,WAAW,EAAE,CAAC,CAAC,EAAC,GACjC;UAAA;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAACvG,KAAK,EAAC,gBAAgB;YAAAgF,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACX9F,OAAA,CAACwB,QAAQ;QAACqK,EAAE,EAAE;UAAES,eAAe,EAAE;QAAe,CAAE;QAAA5G,QAAA,eAChD1F,OAAA,CAACxC,WAAW;UAACqO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,gBAC7C1F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACvG,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAAiF,QAAA,EAC5D+C,cAAc,CAACgD,SAAS;UAAC;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACb9F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAACvG,KAAK,EAAC,gBAAgB;YAAAgF,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACX9F,OAAA,CAACwB,QAAQ;QAACqK,EAAE,EAAE;UAAES,eAAe,EAAE;QAAY,CAAE;QAAA5G,QAAA,eAC7C1F,OAAA,CAACxC,WAAW;UAACqO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,gBAC7C1F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACvG,KAAK,EAAC,WAAW;YAACD,UAAU,EAAC,MAAM;YAAAiF,QAAA,GACzDkF,UAAU,CAACvG,MAAM,GAAG,CAAC,GAAG+E,aAAa,CAACuC,cAAc,EAAE,CAAC,CAAC,GAAG,GAAG,EAAC,GAClE;UAAA;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAACvG,KAAK,EAAC,gBAAgB;YAAAgF,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtF9F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,SAAS;YAACvG,KAAK,EAAC,gBAAgB;YAAAgF,QAAA,GACjDkF,UAAU,CAACvG,MAAM,EAAC,oBACrB;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACX9F,OAAA,CAACwB,QAAQ;QAACqK,EAAE,EAAE;UAAES,eAAe,EAAE;QAAiB,CAAE;QAAA5G,QAAA,eAClD1F,OAAA,CAACxC,WAAW;UAACqO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,eAC7C1F,OAAA,CAAC3C,GAAG;YAAC6E,OAAO,EAAC,MAAM;YAACO,aAAa,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAAAgD,QAAA,gBAChD1F,OAAA,CAAC3C,GAAG;cAAAqI,QAAA,eACF1F,OAAA,CAAC1C,UAAU;gBAAC2J,OAAO,EAAC,WAAW;gBAACvG,KAAK,EAAC,gBAAgB;gBAAAgF,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACN9F,OAAA,CAAC3C,GAAG;cAAC6E,OAAO,EAAC,MAAM;cAACC,cAAc,EAAC,eAAe;cAAAuD,QAAA,gBAChD1F,OAAA,CAAC3C,GAAG;gBAAAqI,QAAA,gBACF1F,OAAA,CAAC1C,UAAU;kBAAC2J,OAAO,EAAC,SAAS;kBAAC/E,OAAO,EAAC,OAAO;kBAAAwD,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/D9F,OAAA,CAAC1C,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAACvG,KAAK,EAAC,YAAY;kBAAAgF,QAAA,EACxCqF,WAAW,GAAG3B,aAAa,CAAC2B,WAAW,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG;gBAAG;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN9F,OAAA,CAAC3C,GAAG;gBAAAqI,QAAA,gBACF1F,OAAA,CAAC1C,UAAU;kBAAC2J,OAAO,EAAC,SAAS;kBAAC/E,OAAO,EAAC,OAAO;kBAAAwD,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/D9F,OAAA,CAAC1C,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAACvG,KAAK,EAAC,cAAc;kBAAAgF,QAAA,EAC1CsF,WAAW,GAAG5B,aAAa,CAAC4B,WAAW,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG;gBAAG;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN9F,OAAA,CAAC3C,GAAG;gBAAAqI,QAAA,gBACF1F,OAAA,CAAC1C,UAAU;kBAAC2J,OAAO,EAAC,SAAS;kBAAC/E,OAAO,EAAC,OAAO;kBAAAwD,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjE9F,OAAA,CAAC1C,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAACvG,KAAK,EAAC,WAAW;kBAAAgF,QAAA,EACvCwF,WAAW,GAAG9B,aAAa,CAAC8B,WAAW,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG;gBAAG;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAELwB,OAAO,CAACjD,MAAM,KAAK,CAAC,gBACrBrE,OAAA,CAACzC,IAAI;MAAAmI,QAAA,eACH1F,OAAA,CAACxC,WAAW;QAAAkI,QAAA,eACV1F,OAAA,CAAC3C,GAAG;UAACkP,SAAS,EAAC,QAAQ;UAACC,EAAE,EAAE,CAAE;UAAA9G,QAAA,gBAC5B1F,OAAA,CAACT,OAAO;YAACsM,EAAE,EAAE;cAAEY,QAAQ,EAAE,EAAE;cAAE/L,KAAK,EAAE,gBAAgB;cAAE2L,EAAE,EAAE;YAAE;UAAE;YAAA1G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjE9F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACvG,KAAK,EAAC,eAAe;YAACgM,YAAY;YAAAhH,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9F,OAAA,CAAC1C,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAACvG,KAAK,EAAC,eAAe;YAAC2L,EAAE,EAAE,CAAE;YAAA3G,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9F,OAAA,CAACvC,MAAM;YACLwJ,OAAO,EAAC,WAAW;YACnBiF,SAAS,eAAElM,OAAA,CAACf,OAAO;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBkB,OAAO,EAAEiB,eAAgB;YACzBf,QAAQ,EAAE7D,QAAQ,CAACgB,MAAM,KAAK,CAAE;YAAAqB,QAAA,EAE/BrC,QAAQ,CAACgB,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;UAA4B;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEP9F,OAAA,CAACzC,IAAI;MAACsO,EAAE,EAAE;QAAE/I,YAAY,EAAE,CAAC;QAAE6J,QAAQ,EAAE,QAAQ;QAAE/K,SAAS,EAAE;MAAE,CAAE;MAAA8D,QAAA,gBAC9D1F,OAAA,CAACnC,cAAc;QAAA6H,QAAA,eACb1F,OAAA,CAACtC,KAAK;UAAAgI,QAAA,gBACJ1F,OAAA,CAAClC,SAAS;YAAA4H,QAAA,eACR1F,OAAA,CAACjC,QAAQ;cAAA2H,QAAA,gBACP1F,OAAA,CAACC,eAAe;gBAAAyF,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACxC9F,OAAA,CAACC,eAAe;gBAAAyF,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC3C9F,OAAA,CAACC,eAAe;gBAAC2M,KAAK,EAAC,OAAO;gBAAAlH,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC5D9F,OAAA,CAACC,eAAe;gBAAC2M,KAAK,EAAC,OAAO;gBAAAlH,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC9D9F,OAAA,CAACC,eAAe;gBAAC2M,KAAK,EAAC,OAAO;gBAAAlH,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACvD9F,OAAA,CAACC,eAAe;gBAAC2M,KAAK,EAAC,OAAO;gBAAAlH,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACzD9F,OAAA,CAACC,eAAe;gBAAC2M,KAAK,EAAC,OAAO;gBAAAlH,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACtD9F,OAAA,CAACC,eAAe;gBAAC2M,KAAK,EAAC,OAAO;gBAAAlH,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACxD9F,OAAA,CAACC,eAAe;gBAAAyF,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC7C9F,OAAA,CAACC,eAAe;gBAAAyF,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC3C9F,OAAA,CAACC,eAAe;gBAAC2M,KAAK,EAAC,QAAQ;gBAAAlH,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ9F,OAAA,CAACrC,SAAS;YAAA+H,QAAA,EACPkG,gBAAgB,CAACxF,GAAG,CAAC,CAACjD,MAAM,EAAE6G,KAAK,KAAK;cACvC,MAAMS,QAAQ,GAAGV,iBAAiB,CAAC5G,MAAM,EAAE6G,KAAK,CAAC;cACjD,MAAM6C,UAAU,GAAG1J,MAAM,CAACgB,cAAc;cAExC,oBACEnE,OAAA,CAACiB,cAAc;gBAAAyE,QAAA,gBACb1F,OAAA,CAACC,eAAe;kBAAAyF,QAAA,eACd1F,OAAA,CAAC3C,GAAG;oBAAC6E,OAAO,EAAC,MAAM;oBAACO,aAAa,EAAC,QAAQ;oBAAAiD,QAAA,gBACxC1F,OAAA,CAAC1C,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACxG,UAAU,EAAE,GAAI;sBAACC,KAAK,EAAC,cAAc;sBAAAgF,QAAA,EAC9DqD,UAAU,CAAC5F,MAAM,CAACO,KAAK;oBAAC;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACb9F,OAAA,CAAC1C,UAAU;sBAAC2J,OAAO,EAAC,SAAS;sBAACvG,KAAK,EAAC,gBAAgB;sBAAAgF,QAAA,EACjD,IAAI/B,IAAI,CAACR,MAAM,CAACO,KAAK,CAAC,CAACoJ,kBAAkB,CAAC,EAAE,EAAE;wBAACC,IAAI,EAAE,SAAS;wBAAEC,MAAM,EAAC;sBAAS,CAAC;oBAAC;sBAAArH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAAyF,QAAA,eACd1F,OAAA,CAAC1C,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACxG,UAAU,EAAE,GAAI;oBAAAiF,QAAA,EACzCvC,MAAM,CAAC8J;kBAAe;oBAAAtH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAC2M,KAAK,EAAC,OAAO;kBAAAlH,QAAA,eAC5B1F,OAAA,CAAC1C,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACxG,UAAU,EAAE,GAAI;oBAAAiF,QAAA,EACzCwD,YAAY,CAAC/F,MAAM,CAACW,mBAAmB;kBAAC;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAC2M,KAAK,EAAC,OAAO;kBAAAlH,QAAA,eAC5B1F,OAAA,CAAC3C,GAAG;oBAAC6E,OAAO,EAAC,MAAM;oBAACO,aAAa,EAAC,QAAQ;oBAACL,UAAU,EAAC,UAAU;oBAAAsD,QAAA,gBAC9D1F,OAAA,CAAC1C,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACxG,UAAU,EAAE,GAAI;sBAACC,KAAK,EAAEyC,MAAM,CAACkH,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAiB;sBAAA3E,QAAA,EAC3GvC,MAAM,CAACkH,UAAU,GAAGnB,YAAY,CAACO,IAAI,CAACG,KAAK,CAACzG,MAAM,CAACkH,UAAU,CAAC,CAAC,GAAG,KAAK,GAAG;oBAAG;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC,EACZ3C,MAAM,CAACkH,UAAU,GAAG,CAAC,IAAIlH,MAAM,CAACkH,UAAU,GAAIa,WAAW,GAAG,GAAI,iBAC/DlL,OAAA,CAAC/B,IAAI;sBACH+H,KAAK,EAAC,OAAO;sBACbkH,IAAI,EAAC,OAAO;sBACZxM,KAAK,EAAC,SAAS;sBACfmL,EAAE,EAAE;wBAAEsB,MAAM,EAAE,EAAE;wBAAEV,QAAQ,EAAE,SAAS;wBAAEW,EAAE,EAAE;sBAAI;oBAAE;sBAAAzH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CACF,EACA3C,MAAM,CAACkH,UAAU,GAAIa,WAAW,GAAG,GAAI,iBACtClL,OAAA,CAAC/B,IAAI;sBACH+H,KAAK,EAAC,OAAO;sBACbkH,IAAI,EAAC,OAAO;sBACZxM,KAAK,EAAC,SAAS;sBACfmL,EAAE,EAAE;wBAAEsB,MAAM,EAAE,EAAE;wBAAEV,QAAQ,EAAE,SAAS;wBAAEW,EAAE,EAAE;sBAAI;oBAAE;sBAAAzH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAC2M,KAAK,EAAC,OAAO;kBAAAlH,QAAA,eAC5B1F,OAAA,CAAC1C,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACxG,UAAU,EAAE,GAAI;oBAAAiF,QAAA,GACzC0D,aAAa,CAACjG,MAAM,CAACY,MAAM,EAAE,CAAC,CAAC,EAAC,GACnC;kBAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAC2M,KAAK,EAAC,OAAO;kBAAAlH,QAAA,eAC5B1F,OAAA,CAAC1C,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACxG,UAAU,EAAE,GAAI;oBAAAiF,QAAA,GACzC0D,aAAa,CAACjG,MAAM,CAACa,YAAY,EAAE,CAAC,CAAC,EAAC,QACzC;kBAAA;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAC2M,KAAK,EAAC,OAAO;kBAAAlH,QAAA,eAC5B1F,OAAA,CAAC1C,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACxG,UAAU,EAAE,GAAI;oBAAAiF,QAAA,EACzC+C,cAAc,CAACtF,MAAM,CAACc,WAAW;kBAAC;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAC2M,KAAK,EAAC,OAAO;kBAAAlH,QAAA,eAC5B1F,OAAA,CAAC3C,GAAG;oBAAC6E,OAAO,EAAC,MAAM;oBAACO,aAAa,EAAC,QAAQ;oBAACL,UAAU,EAAC,UAAU;oBAAAsD,QAAA,gBAC9D1F,OAAA,CAAC1C,UAAU;sBACT2J,OAAO,EAAC,OAAO;sBACfxG,UAAU,EAAE,GAAI;sBAChBC,KAAK,EAAEyC,MAAM,CAACmH,WAAW,GACtBnH,MAAM,CAACmH,WAAW,GAAIqB,cAAc,GAAG,GAAI,GAAG,cAAc,GAC5DxI,MAAM,CAACmH,WAAW,GAAIqB,cAAc,GAAG,GAAI,GAAG,YAAY,GAAG,cAAc,GAAI,gBAAiB;sBAAAjG,QAAA,EAElGvC,MAAM,CAACmH,WAAW,GAAGlB,aAAa,CAACjG,MAAM,CAACmH,WAAW,EAAE,CAAC,CAAC,GAAG,GAAG,GAC/DnH,MAAM,CAACkH,UAAU,GAAG,SAAS,GAAG;oBAAG;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC,EACZ3C,MAAM,CAACmH,WAAW,IAAInH,MAAM,CAACmH,WAAW,GAAIqB,cAAc,GAAG,GAAI,iBAChE3L,OAAA,CAACL,gBAAgB;sBAACe,KAAK,EAAC,SAAS;sBAAC+L,QAAQ,EAAC,OAAO;sBAACZ,EAAE,EAAE;wBAAEuB,EAAE,EAAE;sBAAI;oBAAE;sBAAAzH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CACtE,EACA3C,MAAM,CAACmH,WAAW,IAAInH,MAAM,CAACmH,WAAW,GAAIqB,cAAc,GAAG,GAAI,iBAChE3L,OAAA,CAACP,cAAc;sBAACiB,KAAK,EAAC,OAAO;sBAAC+L,QAAQ,EAAC,OAAO;sBAACZ,EAAE,EAAE;wBAAEuB,EAAE,EAAE;sBAAI;oBAAE;sBAAAzH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAClE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAAyF,QAAA,eACd1F,OAAA,CAAC1C,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACxG,UAAU,EAAE,GAAI;oBAAAiF,QAAA,EACzCvC,MAAM,CAACe,UAAU,IAAI;kBAAG;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAAyF,QAAA,eACd1F,OAAA,CAAC/B,IAAI;oBACH+H,KAAK,EAAE7C,MAAM,CAACgB,cAAc,GAAG,IAAI,GAAG,IAAK;oBAC3CzD,KAAK,EAAEyC,MAAM,CAACgB,cAAc,GAAG,SAAS,GAAG,SAAU;oBACrD+I,IAAI,EAAC;kBAAO;oBAAAvH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACa,CAAC,eAClB9F,OAAA,CAACC,eAAe;kBAAC2M,KAAK,EAAC,QAAQ;kBAAAlH,QAAA,gBAC7B1F,OAAA,CAACrB,OAAO;oBAAC0O,KAAK,EAAC,QAAQ;oBAAA3H,QAAA,eACrB1F,OAAA,CAAC9B,UAAU;sBAACgP,IAAI,EAAC,OAAO;sBAAClG,OAAO,EAAEA,CAAA,KAAMkB,gBAAgB,CAAC/E,MAAM,CAAE;sBAAAuC,QAAA,eAC/D1F,OAAA,CAACb,QAAQ;wBAAAwG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV9F,OAAA,CAACrB,OAAO;oBAAC0O,KAAK,EAAC,UAAU;oBAAA3H,QAAA,eACvB1F,OAAA,CAAC9B,UAAU;sBAACgP,IAAI,EAAC,OAAO;sBAACxM,KAAK,EAAC,OAAO;sBAACsG,OAAO,EAAEA,CAAA,KAAMmB,kBAAkB,CAAChF,MAAM,CAAE;sBAAAuC,QAAA,eAC/E1F,OAAA,CAACX,UAAU;wBAAAsG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA,GAtGC3C,MAAM,CAACmB,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuGd,CAAC;YAErB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEjB9F,OAAA,CAACpB,eAAe;QACdqN,SAAS,EAAC,KAAK;QACfqB,KAAK,EAAEhG,OAAO,CAACjD,MAAO;QACtBwD,IAAI,EAAEA,IAAK;QACX0F,YAAY,EAAEA,CAAC9I,KAAK,EAAE+I,OAAO,KAAK1F,OAAO,CAAC0F,OAAO,CAAE;QACnDzF,WAAW,EAAEA,WAAY;QACzB0F,mBAAmB,EAAGhJ,KAAK,IAAK;UAC9BuD,cAAc,CAAC1C,QAAQ,CAACb,KAAK,CAACE,MAAM,CAACD,KAAK,EAAE,EAAE,CAAC,CAAC;UAChDoD,OAAO,CAAC,CAAC,CAAC;QACZ,CAAE;QACF4F,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QACtCC,gBAAgB,EAAC,sBAAmB;QACpCC,kBAAkB,EAAEA,CAAC;UAAEC,IAAI;UAAEC,EAAE;UAAER;QAAM,CAAC,KAAK,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK;MAAG;QAAA3H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eACC9F,OAAA,CAACgD,YAAY;MACXC,IAAI,EAAEwE,UAAW;MACjBvE,OAAO,EAAEA,CAAA,KAAMwE,aAAa,CAAC,KAAK,CAAE;MACpCvE,MAAM,EAAEwE,cAAe;MACvBvE,MAAM,EAAEkF,gBAAiB;MACzBjF,QAAQ,EAAEA;IAAS;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACuB,GAAA,CApZID,WAAW;EAAA,QACuCxH,MAAM,EA+H9Cd,QAAQ;AAAA;AAAAiP,GAAA,GAhIlB3G,WAAW;AAsZjB,eAAeA,WAAW;AAAC,IAAApG,EAAA,EAAAO,GAAA,EAAAS,GAAA,EAAAW,GAAA,EAAAI,GAAA,EAAAoE,GAAA,EAAA4G,GAAA;AAAAC,YAAA,CAAAhN,EAAA;AAAAgN,YAAA,CAAAzM,GAAA;AAAAyM,YAAA,CAAAhM,GAAA;AAAAgM,YAAA,CAAArL,GAAA;AAAAqL,YAAA,CAAAjL,GAAA;AAAAiL,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}