{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _extractEventHandlers = _interopRequireDefault(require(\"../extractEventHandlers\"));\nvar _omitEventHandlers = _interopRequireDefault(require(\"../omitEventHandlers\"));\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = (0, _clsx.default)(additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n    const mergedStyle = {\n      ...additionalProps?.style,\n      ...externalForwardedProps?.style,\n      ...externalSlotProps?.style\n    };\n    const props = {\n      ...additionalProps,\n      ...externalForwardedProps,\n      ...externalSlotProps\n    };\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = (0, _extractEventHandlers.default)({\n    ...externalForwardedProps,\n    ...externalSlotProps\n  });\n  const componentsPropsWithoutEventHandlers = (0, _omitEventHandlers.default)(externalSlotProps);\n  const otherPropsWithoutEventHandlers = (0, _omitEventHandlers.default)(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = (0, _clsx.default)(internalSlotProps?.className, additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n  const mergedStyle = {\n    ...internalSlotProps?.style,\n    ...additionalProps?.style,\n    ...externalForwardedProps?.style,\n    ...externalSlotProps?.style\n  };\n  const props = {\n    ...internalSlotProps,\n    ...additionalProps,\n    ...otherPropsWithoutEventHandlers,\n    ...componentsPropsWithoutEventHandlers\n  };\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nvar _default = exports.default = mergeSlotProps;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "_clsx", "_extractEventHandlers", "_omitEventHandlers", "mergeSlotProps", "parameters", "getSlotProps", "additionalProps", "externalSlotProps", "externalForwardedProps", "className", "joinedClasses", "mergedStyle", "style", "props", "length", "keys", "internalRef", "undefined", "eventHandlers", "componentsPropsWithoutEventHandlers", "otherPropsWithoutEventHandlers", "internalSlotProps", "ref", "_default"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/mergeSlotProps/mergeSlotProps.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _extractEventHandlers = _interopRequireDefault(require(\"../extractEventHandlers\"));\nvar _omitEventHandlers = _interopRequireDefault(require(\"../omitEventHandlers\"));\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = (0, _clsx.default)(additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n    const mergedStyle = {\n      ...additionalProps?.style,\n      ...externalForwardedProps?.style,\n      ...externalSlotProps?.style\n    };\n    const props = {\n      ...additionalProps,\n      ...externalForwardedProps,\n      ...externalSlotProps\n    };\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = (0, _extractEventHandlers.default)({\n    ...externalForwardedProps,\n    ...externalSlotProps\n  });\n  const componentsPropsWithoutEventHandlers = (0, _omitEventHandlers.default)(externalSlotProps);\n  const otherPropsWithoutEventHandlers = (0, _omitEventHandlers.default)(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = (0, _clsx.default)(internalSlotProps?.className, additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n  const mergedStyle = {\n    ...internalSlotProps?.style,\n    ...additionalProps?.style,\n    ...externalForwardedProps?.style,\n    ...externalSlotProps?.style\n  };\n  const props = {\n    ...internalSlotProps,\n    ...additionalProps,\n    ...otherPropsWithoutEventHandlers,\n    ...componentsPropsWithoutEventHandlers\n  };\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nvar _default = exports.default = mergeSlotProps;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIK,KAAK,GAAGP,sBAAsB,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnD,IAAIO,qBAAqB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AACtF,IAAIQ,kBAAkB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,cAAcA,CAACC,UAAU,EAAE;EAClC,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,sBAAsB;IACtBC;EACF,CAAC,GAAGL,UAAU;EACd,IAAI,CAACC,YAAY,EAAE;IACjB;IACA;IACA,MAAMK,aAAa,GAAG,CAAC,CAAC,EAAEV,KAAK,CAACL,OAAO,EAAEW,eAAe,EAAEG,SAAS,EAAEA,SAAS,EAAED,sBAAsB,EAAEC,SAAS,EAAEF,iBAAiB,EAAEE,SAAS,CAAC;IAChJ,MAAME,WAAW,GAAG;MAClB,GAAGL,eAAe,EAAEM,KAAK;MACzB,GAAGJ,sBAAsB,EAAEI,KAAK;MAChC,GAAGL,iBAAiB,EAAEK;IACxB,CAAC;IACD,MAAMC,KAAK,GAAG;MACZ,GAAGP,eAAe;MAClB,GAAGE,sBAAsB;MACzB,GAAGD;IACL,CAAC;IACD,IAAIG,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;MAC5BD,KAAK,CAACJ,SAAS,GAAGC,aAAa;IACjC;IACA,IAAId,MAAM,CAACmB,IAAI,CAACJ,WAAW,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACvCD,KAAK,CAACD,KAAK,GAAGD,WAAW;IAC3B;IACA,OAAO;MACLE,KAAK;MACLG,WAAW,EAAEC;IACf,CAAC;EACH;;EAEA;EACA;;EAEA,MAAMC,aAAa,GAAG,CAAC,CAAC,EAAEjB,qBAAqB,CAACN,OAAO,EAAE;IACvD,GAAGa,sBAAsB;IACzB,GAAGD;EACL,CAAC,CAAC;EACF,MAAMY,mCAAmC,GAAG,CAAC,CAAC,EAAEjB,kBAAkB,CAACP,OAAO,EAAEY,iBAAiB,CAAC;EAC9F,MAAMa,8BAA8B,GAAG,CAAC,CAAC,EAAElB,kBAAkB,CAACP,OAAO,EAAEa,sBAAsB,CAAC;EAC9F,MAAMa,iBAAiB,GAAGhB,YAAY,CAACa,aAAa,CAAC;;EAErD;EACA;EACA;EACA;EACA,MAAMR,aAAa,GAAG,CAAC,CAAC,EAAEV,KAAK,CAACL,OAAO,EAAE0B,iBAAiB,EAAEZ,SAAS,EAAEH,eAAe,EAAEG,SAAS,EAAEA,SAAS,EAAED,sBAAsB,EAAEC,SAAS,EAAEF,iBAAiB,EAAEE,SAAS,CAAC;EAC9K,MAAME,WAAW,GAAG;IAClB,GAAGU,iBAAiB,EAAET,KAAK;IAC3B,GAAGN,eAAe,EAAEM,KAAK;IACzB,GAAGJ,sBAAsB,EAAEI,KAAK;IAChC,GAAGL,iBAAiB,EAAEK;EACxB,CAAC;EACD,MAAMC,KAAK,GAAG;IACZ,GAAGQ,iBAAiB;IACpB,GAAGf,eAAe;IAClB,GAAGc,8BAA8B;IACjC,GAAGD;EACL,CAAC;EACD,IAAIT,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;IAC5BD,KAAK,CAACJ,SAAS,GAAGC,aAAa;EACjC;EACA,IAAId,MAAM,CAACmB,IAAI,CAACJ,WAAW,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;IACvCD,KAAK,CAACD,KAAK,GAAGD,WAAW;EAC3B;EACA,OAAO;IACLE,KAAK;IACLG,WAAW,EAAEK,iBAAiB,CAACC;EACjC,CAAC;AACH;AACA,IAAIC,QAAQ,GAAGzB,OAAO,CAACH,OAAO,GAAGQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}