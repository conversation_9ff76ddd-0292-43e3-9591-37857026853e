[{"D:\\Proyectos Python\\Repostaje\\src\\index.js": "1", "D:\\Proyectos Python\\Repostaje\\src\\App.js": "2", "D:\\Proyectos Python\\Repostaje\\src\\context\\AppContext.js": "3", "D:\\Proyectos Python\\Repostaje\\src\\components\\Layout\\Layout.js": "4", "D:\\Proyectos Python\\Repostaje\\src\\components\\Dashboard\\Dashboard.js": "5", "D:\\Proyectos Python\\Repostaje\\src\\components\\Refuels\\RefuelsList.js": "6", "D:\\Proyectos Python\\Repostaje\\src\\components\\Expenses\\ExpensesList.js": "7", "D:\\Proyectos Python\\Repostaje\\src\\components\\Settings\\Settings.js": "8", "D:\\Proyectos Python\\Repostaje\\src\\components\\Vehicles\\VehiclesList.js": "9", "D:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\Statistics.js": "10", "D:\\Proyectos Python\\Repostaje\\src\\components\\Debug\\DebugInfo.js": "11", "D:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\VehicleComparison.js": "12", "D:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\AdvancedAnalytics.js": "13", "D:\\Proyectos Python\\Repostaje\\src\\components\\Reminders\\RemindersManager.js": "14", "D:\\Proyectos Python\\Repostaje\\src\\components\\Export\\DataExporter.js": "15", "d:\\Proyectos Python\\Repostaje\\src\\index.js": "16", "d:\\Proyectos Python\\Repostaje\\src\\App.js": "17", "d:\\Proyectos Python\\Repostaje\\src\\context\\AppContext.js": "18", "d:\\Proyectos Python\\Repostaje\\src\\components\\Dashboard\\Dashboard.js": "19", "d:\\Proyectos Python\\Repostaje\\src\\components\\Vehicles\\VehiclesList.js": "20", "d:\\Proyectos Python\\Repostaje\\src\\components\\Expenses\\ExpensesList.js": "21", "d:\\Proyectos Python\\Repostaje\\src\\components\\Settings\\Settings.js": "22", "d:\\Proyectos Python\\Repostaje\\src\\components\\Layout\\Layout.js": "23", "d:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\Statistics.js": "24", "d:\\Proyectos Python\\Repostaje\\src\\components\\Refuels\\RefuelsList.js": "25", "d:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\VehicleComparison.js": "26", "d:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\AdvancedAnalytics.js": "27", "d:\\Proyectos Python\\Repostaje\\src\\components\\Export\\DataExporter.js": "28", "d:\\Proyectos Python\\Repostaje\\src\\components\\Reminders\\RemindersManager.js": "29"}, {"size": 1404, "mtime": 1755320861554, "results": "30", "hashOfConfig": "31"}, {"size": 3221, "mtime": 1755480747028, "results": "32", "hashOfConfig": "31"}, {"size": 16558, "mtime": 1755631239290, "results": "33", "hashOfConfig": "31"}, {"size": 6873, "mtime": 1755320924755, "results": "34", "hashOfConfig": "31"}, {"size": 24033, "mtime": 1755480747047, "results": "35", "hashOfConfig": "31"}, {"size": 26885, "mtime": 1755544738768, "results": "36", "hashOfConfig": "31"}, {"size": 21699, "mtime": 1755768072152, "results": "37", "hashOfConfig": "31"}, {"size": 4905, "mtime": 1755323504037, "results": "38", "hashOfConfig": "31"}, {"size": 11237, "mtime": 1755321410333, "results": "39", "hashOfConfig": "31"}, {"size": 21434, "mtime": 1755769414771, "results": "40", "hashOfConfig": "31"}, {"size": 4109, "mtime": 1755322132893, "results": "41", "hashOfConfig": "31"}, {"size": 9680, "mtime": 1755326293094, "results": "42", "hashOfConfig": "31"}, {"size": 26695, "mtime": 1755769401713, "results": "43", "hashOfConfig": "31"}, {"size": 18991, "mtime": 1755343625327, "results": "44", "hashOfConfig": "31"}, {"size": 17263, "mtime": 1755334216201, "results": "45", "hashOfConfig": "31"}, {"size": 1404, "mtime": 1755320861554, "results": "46", "hashOfConfig": "47"}, {"size": 3221, "mtime": 1755326566987, "results": "48", "hashOfConfig": "47"}, {"size": 16558, "mtime": 1755332324323, "results": "49", "hashOfConfig": "47"}, {"size": 24041, "mtime": 1755476372428, "results": "50", "hashOfConfig": "47"}, {"size": 11237, "mtime": 1755321410333, "results": "51", "hashOfConfig": "47"}, {"size": 14548, "mtime": 1755322629336, "results": "52", "hashOfConfig": "47"}, {"size": 4905, "mtime": 1755323504037, "results": "53", "hashOfConfig": "47"}, {"size": 6873, "mtime": 1755320924755, "results": "54", "hashOfConfig": "47"}, {"size": 19627, "mtime": 1755327284521, "results": "55", "hashOfConfig": "47"}, {"size": 14701, "mtime": 1755471120775, "results": "56", "hashOfConfig": "47"}, {"size": 9680, "mtime": 1755326293094, "results": "57", "hashOfConfig": "47"}, {"size": 26324, "mtime": 1755326533069, "results": "58", "hashOfConfig": "47"}, {"size": 17263, "mtime": 1755334216201, "results": "59", "hashOfConfig": "47"}, {"size": 18991, "mtime": 1755343625327, "results": "60", "hashOfConfig": "47"}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mr39sn", {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yt12m4", {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Proyectos Python\\Repostaje\\src\\index.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\App.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\context\\AppContext.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Layout\\Layout.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Dashboard\\Dashboard.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Refuels\\RefuelsList.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Expenses\\ExpensesList.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Settings\\Settings.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Vehicles\\VehiclesList.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\Statistics.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Debug\\DebugInfo.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\VehicleComparison.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\AdvancedAnalytics.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Reminders\\RemindersManager.js", [], [], "D:\\Proyectos Python\\Repostaje\\src\\components\\Export\\DataExporter.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\index.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\App.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\context\\AppContext.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Dashboard\\Dashboard.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Vehicles\\VehiclesList.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Expenses\\ExpensesList.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Settings\\Settings.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Layout\\Layout.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\Statistics.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Refuels\\RefuelsList.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\VehicleComparison.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Statistics\\AdvancedAnalytics.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Export\\DataExporter.js", [], [], "d:\\Proyectos Python\\Repostaje\\src\\components\\Reminders\\RemindersManager.js", [], []]