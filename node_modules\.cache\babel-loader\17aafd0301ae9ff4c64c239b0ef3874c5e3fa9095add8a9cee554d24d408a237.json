{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldSectionContentProps = useFieldSectionContentProps;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useId = _interopRequireDefault(require(\"@mui/utils/useId\"));\nvar _hooks = require(\"../../../hooks\");\nvar _syncSelectionToDOM = require(\"./syncSelectionToDOM\");\n/**\n * Generate the props to pass to the content element of each section of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the content element of each section of the field.\n */\nfunction useFieldSectionContentProps(parameters) {\n  const adapter = (0, _hooks.usePickerAdapter)();\n  const translations = (0, _hooks.usePickerTranslations)();\n  const id = (0, _useId.default)();\n  const {\n    focused,\n    domGetters,\n    stateResponse,\n    applyCharacterEditing,\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      sectionsValueBoundaries,\n      state,\n      value,\n      // Methods to update the states\n      clearActiveSection,\n      setCharacterQuery,\n      setSelectedSections,\n      updateSectionValue,\n      updateValueFromValueStr\n    },\n    internalPropsWithDefaults: {\n      disabled = false,\n      readOnly = false\n    }\n  } = parameters;\n  const isContainerEditable = parsedSelectedSections === 'all';\n  const isEditable = !isContainerEditable && !disabled && !readOnly;\n\n  /**\n   * If a section content has been updated with a value we don't want to keep,\n   * Then we need to imperatively revert it (we can't let React do it because the value did not change in his internal representation).\n   */\n  const revertDOMSectionChange = (0, _useEventCallback.default)(sectionIndex => {\n    if (!domGetters.isReady()) {\n      return;\n    }\n    const section = state.sections[sectionIndex];\n    domGetters.getSectionContent(sectionIndex).innerHTML = section.value || section.placeholder;\n    (0, _syncSelectionToDOM.syncSelectionToDOM)({\n      focused,\n      domGetters,\n      stateResponse\n    });\n  });\n  const handleInput = (0, _useEventCallback.default)(event => {\n    if (!domGetters.isReady()) {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    const sectionIndex = domGetters.getSectionIndexFromDOMElement(target);\n    const section = state.sections[sectionIndex];\n    if (readOnly) {\n      revertDOMSectionChange(sectionIndex);\n      return;\n    }\n    if (keyPressed.length === 0) {\n      if (section.value === '') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      const inputType = event.nativeEvent.inputType;\n      if (inputType === 'insertParagraph' || inputType === 'insertLineBreak') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      revertDOMSectionChange(sectionIndex);\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex\n    });\n\n    // The DOM value needs to remain the one React is expecting.\n    revertDOMSectionChange(sectionIndex);\n  });\n  const handleMouseUp = (0, _useEventCallback.default)(event => {\n    // Without this, the browser will remove the selected when clicking inside an already-selected section.\n    event.preventDefault();\n  });\n  const handlePaste = (0, _useEventCallback.default)(event => {\n    // prevent default to avoid the input `onInput` handler being called\n    event.preventDefault();\n    if (readOnly || disabled || typeof parsedSelectedSections !== 'number') {\n      return;\n    }\n    const activeSection = state.sections[parsedSelectedSections];\n    const pastedValue = event.clipboardData.getData('text');\n    const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n    const digitsOnly = /^[0-9]+$/.test(pastedValue);\n    const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n    const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n    if (isValidPastedValue) {\n      setCharacterQuery(null);\n      updateSectionValue({\n        section: activeSection,\n        newSectionValue: pastedValue,\n        shouldGoToNextSection: true\n      });\n    }\n    // If the pasted value corresponds to a single section, but not the expected type, we skip the modification\n    else if (!lettersOnly && !digitsOnly) {\n      setCharacterQuery(null);\n      updateValueFromValueStr(pastedValue);\n    }\n  });\n  const handleDragOver = (0, _useEventCallback.default)(event => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'none';\n  });\n  const createFocusHandler = React.useCallback(sectionIndex => () => {\n    if (disabled) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  }, [disabled, setSelectedSections]);\n  return React.useCallback((section, sectionIndex) => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: fieldValueManager.getDateFromSection(value, section),\n      contentType: section.contentType,\n      format: section.format\n    });\n    return {\n      // Event handlers\n      onInput: handleInput,\n      onPaste: handlePaste,\n      onMouseUp: handleMouseUp,\n      onDragOver: handleDragOver,\n      onFocus: createFocusHandler(sectionIndex),\n      // Aria attributes\n      'aria-labelledby': `${id}-${section.type}`,\n      'aria-readonly': readOnly,\n      'aria-valuenow': getSectionValueNow(section, adapter),\n      'aria-valuemin': sectionBoundaries.minimum,\n      'aria-valuemax': sectionBoundaries.maximum,\n      'aria-valuetext': section.value ? getSectionValueText(section, adapter) : translations.empty,\n      'aria-label': translations[section.type],\n      'aria-disabled': disabled,\n      // Other\n      tabIndex: isContainerEditable || sectionIndex > 0 ? -1 : 0,\n      contentEditable: !isContainerEditable && !disabled && !readOnly,\n      role: 'spinbutton',\n      id: `${id}-${section.type}`,\n      'data-range-position': section.dateName || undefined,\n      spellCheck: isEditable ? false : undefined,\n      autoCapitalize: isEditable ? 'off' : undefined,\n      autoCorrect: isEditable ? 'off' : undefined,\n      children: section.value || section.placeholder,\n      inputMode: section.contentType === 'letter' ? 'text' : 'numeric'\n    };\n  }, [sectionsValueBoundaries, id, isContainerEditable, disabled, readOnly, isEditable, translations, adapter, handleInput, handlePaste, handleMouseUp, handleDragOver, createFocusHandler, fieldValueManager, value]);\n}\nfunction getSectionValueText(section, adapter) {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return adapter.format(adapter.setMonth(adapter.date(), Number(section.value) - 1), 'month');\n        }\n        const parsedDate = adapter.parse(section.value, section.format);\n        return parsedDate ? adapter.format(parsedDate, 'month') : undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit' ? adapter.format(adapter.setDate(adapter.startOfYear(adapter.date()), Number(section.value)), 'dayOfMonthFull') : section.value;\n    case 'weekDay':\n      // TODO: improve by providing the label of the week day\n      return undefined;\n    default:\n      return undefined;\n  }\n}\nfunction getSectionValueNow(section, adapter) {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'weekDay':\n      {\n        if (section.contentType === 'letter') {\n          // TODO: improve by resolving the week day number from a letter week day\n          return undefined;\n        }\n        return Number(section.value);\n      }\n    case 'meridiem':\n      {\n        const parsedDate = adapter.parse(`01:00 ${section.value}`, `${adapter.formats.hours12h}:${adapter.formats.minutes} ${section.format}`);\n        if (parsedDate) {\n          return adapter.getHours(parsedDate) >= 12 ? 1 : 0;\n        }\n        return undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit-with-letter' ? parseInt(section.value, 10) : Number(section.value);\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return Number(section.value);\n        }\n        const parsedDate = adapter.parse(section.value, section.format);\n        return parsedDate ? adapter.getMonth(parsedDate) + 1 : undefined;\n      }\n    default:\n      return section.contentType !== 'letter' ? Number(section.value) : undefined;\n  }\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "useFieldSectionContentProps", "React", "_useEventCallback", "_useId", "_hooks", "_syncSelectionToDOM", "parameters", "adapter", "usePickerAdapter", "translations", "usePickerTranslations", "id", "focused", "domGetters", "stateResponse", "applyCharacterEditing", "manager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "parsedSelectedSections", "sectionsValueBoundaries", "state", "clearActiveSection", "setCharacterQuery", "setSelectedSections", "updateSectionValue", "updateValueFromValueStr", "internalPropsWithDefaults", "disabled", "readOnly", "isContainerEditable", "isEditable", "revertDOMSectionChange", "sectionIndex", "isReady", "section", "sections", "getSectionContent", "innerHTML", "placeholder", "syncSelectionToDOM", "handleInput", "event", "target", "keyPressed", "textContent", "getSectionIndexFromDOMElement", "length", "inputType", "nativeEvent", "handleMouseUp", "preventDefault", "handlePaste", "activeSection", "pastedValue", "clipboardData", "getData", "lettersOnly", "test", "digitsOnly", "digitsAndLetterOnly", "isValidPastedValue", "contentType", "newSectionValue", "shouldGoToNextSection", "handleDragOver", "dataTransfer", "dropEffect", "createFocusHandler", "useCallback", "sectionBoundaries", "type", "currentDate", "getDateFromSection", "format", "onInput", "onPaste", "onMouseUp", "onDragOver", "onFocus", "getSectionValueNow", "minimum", "maximum", "getSectionValueText", "empty", "tabIndex", "contentEditable", "role", "dateName", "undefined", "spell<PERSON>heck", "autoCapitalize", "autoCorrect", "children", "inputMode", "setMonth", "date", "Number", "parsedDate", "parse", "setDate", "startOfYear", "formats", "hours12h", "minutes", "getHours", "parseInt", "getMonth"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldSectionContentProps.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldSectionContentProps = useFieldSectionContentProps;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useId = _interopRequireDefault(require(\"@mui/utils/useId\"));\nvar _hooks = require(\"../../../hooks\");\nvar _syncSelectionToDOM = require(\"./syncSelectionToDOM\");\n/**\n * Generate the props to pass to the content element of each section of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the content element of each section of the field.\n */\nfunction useFieldSectionContentProps(parameters) {\n  const adapter = (0, _hooks.usePickerAdapter)();\n  const translations = (0, _hooks.usePickerTranslations)();\n  const id = (0, _useId.default)();\n  const {\n    focused,\n    domGetters,\n    stateResponse,\n    applyCharacterEditing,\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      sectionsValueBoundaries,\n      state,\n      value,\n      // Methods to update the states\n      clearActiveSection,\n      setCharacterQuery,\n      setSelectedSections,\n      updateSectionValue,\n      updateValueFromValueStr\n    },\n    internalPropsWithDefaults: {\n      disabled = false,\n      readOnly = false\n    }\n  } = parameters;\n  const isContainerEditable = parsedSelectedSections === 'all';\n  const isEditable = !isContainerEditable && !disabled && !readOnly;\n\n  /**\n   * If a section content has been updated with a value we don't want to keep,\n   * Then we need to imperatively revert it (we can't let React do it because the value did not change in his internal representation).\n   */\n  const revertDOMSectionChange = (0, _useEventCallback.default)(sectionIndex => {\n    if (!domGetters.isReady()) {\n      return;\n    }\n    const section = state.sections[sectionIndex];\n    domGetters.getSectionContent(sectionIndex).innerHTML = section.value || section.placeholder;\n    (0, _syncSelectionToDOM.syncSelectionToDOM)({\n      focused,\n      domGetters,\n      stateResponse\n    });\n  });\n  const handleInput = (0, _useEventCallback.default)(event => {\n    if (!domGetters.isReady()) {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    const sectionIndex = domGetters.getSectionIndexFromDOMElement(target);\n    const section = state.sections[sectionIndex];\n    if (readOnly) {\n      revertDOMSectionChange(sectionIndex);\n      return;\n    }\n    if (keyPressed.length === 0) {\n      if (section.value === '') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      const inputType = event.nativeEvent.inputType;\n      if (inputType === 'insertParagraph' || inputType === 'insertLineBreak') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      revertDOMSectionChange(sectionIndex);\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex\n    });\n\n    // The DOM value needs to remain the one React is expecting.\n    revertDOMSectionChange(sectionIndex);\n  });\n  const handleMouseUp = (0, _useEventCallback.default)(event => {\n    // Without this, the browser will remove the selected when clicking inside an already-selected section.\n    event.preventDefault();\n  });\n  const handlePaste = (0, _useEventCallback.default)(event => {\n    // prevent default to avoid the input `onInput` handler being called\n    event.preventDefault();\n    if (readOnly || disabled || typeof parsedSelectedSections !== 'number') {\n      return;\n    }\n    const activeSection = state.sections[parsedSelectedSections];\n    const pastedValue = event.clipboardData.getData('text');\n    const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n    const digitsOnly = /^[0-9]+$/.test(pastedValue);\n    const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n    const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n    if (isValidPastedValue) {\n      setCharacterQuery(null);\n      updateSectionValue({\n        section: activeSection,\n        newSectionValue: pastedValue,\n        shouldGoToNextSection: true\n      });\n    }\n    // If the pasted value corresponds to a single section, but not the expected type, we skip the modification\n    else if (!lettersOnly && !digitsOnly) {\n      setCharacterQuery(null);\n      updateValueFromValueStr(pastedValue);\n    }\n  });\n  const handleDragOver = (0, _useEventCallback.default)(event => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'none';\n  });\n  const createFocusHandler = React.useCallback(sectionIndex => () => {\n    if (disabled) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  }, [disabled, setSelectedSections]);\n  return React.useCallback((section, sectionIndex) => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: fieldValueManager.getDateFromSection(value, section),\n      contentType: section.contentType,\n      format: section.format\n    });\n    return {\n      // Event handlers\n      onInput: handleInput,\n      onPaste: handlePaste,\n      onMouseUp: handleMouseUp,\n      onDragOver: handleDragOver,\n      onFocus: createFocusHandler(sectionIndex),\n      // Aria attributes\n      'aria-labelledby': `${id}-${section.type}`,\n      'aria-readonly': readOnly,\n      'aria-valuenow': getSectionValueNow(section, adapter),\n      'aria-valuemin': sectionBoundaries.minimum,\n      'aria-valuemax': sectionBoundaries.maximum,\n      'aria-valuetext': section.value ? getSectionValueText(section, adapter) : translations.empty,\n      'aria-label': translations[section.type],\n      'aria-disabled': disabled,\n      // Other\n      tabIndex: isContainerEditable || sectionIndex > 0 ? -1 : 0,\n      contentEditable: !isContainerEditable && !disabled && !readOnly,\n      role: 'spinbutton',\n      id: `${id}-${section.type}`,\n      'data-range-position': section.dateName || undefined,\n      spellCheck: isEditable ? false : undefined,\n      autoCapitalize: isEditable ? 'off' : undefined,\n      autoCorrect: isEditable ? 'off' : undefined,\n      children: section.value || section.placeholder,\n      inputMode: section.contentType === 'letter' ? 'text' : 'numeric'\n    };\n  }, [sectionsValueBoundaries, id, isContainerEditable, disabled, readOnly, isEditable, translations, adapter, handleInput, handlePaste, handleMouseUp, handleDragOver, createFocusHandler, fieldValueManager, value]);\n}\nfunction getSectionValueText(section, adapter) {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return adapter.format(adapter.setMonth(adapter.date(), Number(section.value) - 1), 'month');\n        }\n        const parsedDate = adapter.parse(section.value, section.format);\n        return parsedDate ? adapter.format(parsedDate, 'month') : undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit' ? adapter.format(adapter.setDate(adapter.startOfYear(adapter.date()), Number(section.value)), 'dayOfMonthFull') : section.value;\n    case 'weekDay':\n      // TODO: improve by providing the label of the week day\n      return undefined;\n    default:\n      return undefined;\n  }\n}\nfunction getSectionValueNow(section, adapter) {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'weekDay':\n      {\n        if (section.contentType === 'letter') {\n          // TODO: improve by resolving the week day number from a letter week day\n          return undefined;\n        }\n        return Number(section.value);\n      }\n    case 'meridiem':\n      {\n        const parsedDate = adapter.parse(`01:00 ${section.value}`, `${adapter.formats.hours12h}:${adapter.formats.minutes} ${section.format}`);\n        if (parsedDate) {\n          return adapter.getHours(parsedDate) >= 12 ? 1 : 0;\n        }\n        return undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit-with-letter' ? parseInt(section.value, 10) : Number(section.value);\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return Number(section.value);\n        }\n        const parsedDate = adapter.parse(section.value, section.format);\n        return parsedDate ? adapter.getMonth(parsedDate) + 1 : undefined;\n      }\n    default:\n      return section.contentType !== 'letter' ? Number(section.value) : undefined;\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,2BAA2B,GAAGA,2BAA2B;AACjE,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,iBAAiB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIU,MAAM,GAAGX,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAChE,IAAIW,MAAM,GAAGX,OAAO,CAAC,gBAAgB,CAAC;AACtC,IAAIY,mBAAmB,GAAGZ,OAAO,CAAC,sBAAsB,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,2BAA2BA,CAACM,UAAU,EAAE;EAC/C,MAAMC,OAAO,GAAG,CAAC,CAAC,EAAEH,MAAM,CAACI,gBAAgB,EAAE,CAAC;EAC9C,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAEL,MAAM,CAACM,qBAAqB,EAAE,CAAC;EACxD,MAAMC,EAAE,GAAG,CAAC,CAAC,EAAER,MAAM,CAACT,OAAO,EAAE,CAAC;EAChC,MAAM;IACJkB,OAAO;IACPC,UAAU;IACVC,aAAa;IACbC,qBAAqB;IACrBC,OAAO,EAAE;MACPC,0BAA0B,EAAEC;IAC9B,CAAC;IACDJ,aAAa,EAAE;MACb;MACAK,sBAAsB;MACtBC,uBAAuB;MACvBC,KAAK;MACLtB,KAAK;MACL;MACAuB,kBAAkB;MAClBC,iBAAiB;MACjBC,mBAAmB;MACnBC,kBAAkB;MAClBC;IACF,CAAC;IACDC,yBAAyB,EAAE;MACzBC,QAAQ,GAAG,KAAK;MAChBC,QAAQ,GAAG;IACb;EACF,CAAC,GAAGvB,UAAU;EACd,MAAMwB,mBAAmB,GAAGX,sBAAsB,KAAK,KAAK;EAC5D,MAAMY,UAAU,GAAG,CAACD,mBAAmB,IAAI,CAACF,QAAQ,IAAI,CAACC,QAAQ;;EAEjE;AACF;AACA;AACA;EACE,MAAMG,sBAAsB,GAAG,CAAC,CAAC,EAAE9B,iBAAiB,CAACR,OAAO,EAAEuC,YAAY,IAAI;IAC5E,IAAI,CAACpB,UAAU,CAACqB,OAAO,CAAC,CAAC,EAAE;MACzB;IACF;IACA,MAAMC,OAAO,GAAGd,KAAK,CAACe,QAAQ,CAACH,YAAY,CAAC;IAC5CpB,UAAU,CAACwB,iBAAiB,CAACJ,YAAY,CAAC,CAACK,SAAS,GAAGH,OAAO,CAACpC,KAAK,IAAIoC,OAAO,CAACI,WAAW;IAC3F,CAAC,CAAC,EAAElC,mBAAmB,CAACmC,kBAAkB,EAAE;MAC1C5B,OAAO;MACPC,UAAU;MACVC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM2B,WAAW,GAAG,CAAC,CAAC,EAAEvC,iBAAiB,CAACR,OAAO,EAAEgD,KAAK,IAAI;IAC1D,IAAI,CAAC7B,UAAU,CAACqB,OAAO,CAAC,CAAC,EAAE;MACzB;IACF;IACA,MAAMS,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,MAAMC,UAAU,GAAGD,MAAM,CAACE,WAAW,IAAI,EAAE;IAC3C,MAAMZ,YAAY,GAAGpB,UAAU,CAACiC,6BAA6B,CAACH,MAAM,CAAC;IACrE,MAAMR,OAAO,GAAGd,KAAK,CAACe,QAAQ,CAACH,YAAY,CAAC;IAC5C,IAAIJ,QAAQ,EAAE;MACZG,sBAAsB,CAACC,YAAY,CAAC;MACpC;IACF;IACA,IAAIW,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAIZ,OAAO,CAACpC,KAAK,KAAK,EAAE,EAAE;QACxBiC,sBAAsB,CAACC,YAAY,CAAC;QACpC;MACF;MACA,MAAMe,SAAS,GAAGN,KAAK,CAACO,WAAW,CAACD,SAAS;MAC7C,IAAIA,SAAS,KAAK,iBAAiB,IAAIA,SAAS,KAAK,iBAAiB,EAAE;QACtEhB,sBAAsB,CAACC,YAAY,CAAC;QACpC;MACF;MACAD,sBAAsB,CAACC,YAAY,CAAC;MACpCX,kBAAkB,CAAC,CAAC;MACpB;IACF;IACAP,qBAAqB,CAAC;MACpB6B,UAAU;MACVX;IACF,CAAC,CAAC;;IAEF;IACAD,sBAAsB,CAACC,YAAY,CAAC;EACtC,CAAC,CAAC;EACF,MAAMiB,aAAa,GAAG,CAAC,CAAC,EAAEhD,iBAAiB,CAACR,OAAO,EAAEgD,KAAK,IAAI;IAC5D;IACAA,KAAK,CAACS,cAAc,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMC,WAAW,GAAG,CAAC,CAAC,EAAElD,iBAAiB,CAACR,OAAO,EAAEgD,KAAK,IAAI;IAC1D;IACAA,KAAK,CAACS,cAAc,CAAC,CAAC;IACtB,IAAItB,QAAQ,IAAID,QAAQ,IAAI,OAAOT,sBAAsB,KAAK,QAAQ,EAAE;MACtE;IACF;IACA,MAAMkC,aAAa,GAAGhC,KAAK,CAACe,QAAQ,CAACjB,sBAAsB,CAAC;IAC5D,MAAMmC,WAAW,GAAGZ,KAAK,CAACa,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvD,MAAMC,WAAW,GAAG,aAAa,CAACC,IAAI,CAACJ,WAAW,CAAC;IACnD,MAAMK,UAAU,GAAG,UAAU,CAACD,IAAI,CAACJ,WAAW,CAAC;IAC/C,MAAMM,mBAAmB,GAAG,wCAAwC,CAACF,IAAI,CAACJ,WAAW,CAAC;IACtF,MAAMO,kBAAkB,GAAGR,aAAa,CAACS,WAAW,KAAK,QAAQ,IAAIL,WAAW,IAAIJ,aAAa,CAACS,WAAW,KAAK,OAAO,IAAIH,UAAU,IAAIN,aAAa,CAACS,WAAW,KAAK,mBAAmB,IAAIF,mBAAmB;IACnN,IAAIC,kBAAkB,EAAE;MACtBtC,iBAAiB,CAAC,IAAI,CAAC;MACvBE,kBAAkB,CAAC;QACjBU,OAAO,EAAEkB,aAAa;QACtBU,eAAe,EAAET,WAAW;QAC5BU,qBAAqB,EAAE;MACzB,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAI,CAACP,WAAW,IAAI,CAACE,UAAU,EAAE;MACpCpC,iBAAiB,CAAC,IAAI,CAAC;MACvBG,uBAAuB,CAAC4B,WAAW,CAAC;IACtC;EACF,CAAC,CAAC;EACF,MAAMW,cAAc,GAAG,CAAC,CAAC,EAAE/D,iBAAiB,CAACR,OAAO,EAAEgD,KAAK,IAAI;IAC7DA,KAAK,CAACS,cAAc,CAAC,CAAC;IACtBT,KAAK,CAACwB,YAAY,CAACC,UAAU,GAAG,MAAM;EACxC,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGnE,KAAK,CAACoE,WAAW,CAACpC,YAAY,IAAI,MAAM;IACjE,IAAIL,QAAQ,EAAE;MACZ;IACF;IACAJ,mBAAmB,CAACS,YAAY,CAAC;EACnC,CAAC,EAAE,CAACL,QAAQ,EAAEJ,mBAAmB,CAAC,CAAC;EACnC,OAAOvB,KAAK,CAACoE,WAAW,CAAC,CAAClC,OAAO,EAAEF,YAAY,KAAK;IAClD,MAAMqC,iBAAiB,GAAGlD,uBAAuB,CAACe,OAAO,CAACoC,IAAI,CAAC,CAAC;MAC9DC,WAAW,EAAEtD,iBAAiB,CAACuD,kBAAkB,CAAC1E,KAAK,EAAEoC,OAAO,CAAC;MACjE2B,WAAW,EAAE3B,OAAO,CAAC2B,WAAW;MAChCY,MAAM,EAAEvC,OAAO,CAACuC;IAClB,CAAC,CAAC;IACF,OAAO;MACL;MACAC,OAAO,EAAElC,WAAW;MACpBmC,OAAO,EAAExB,WAAW;MACpByB,SAAS,EAAE3B,aAAa;MACxB4B,UAAU,EAAEb,cAAc;MAC1Bc,OAAO,EAAEX,kBAAkB,CAACnC,YAAY,CAAC;MACzC;MACA,iBAAiB,EAAE,GAAGtB,EAAE,IAAIwB,OAAO,CAACoC,IAAI,EAAE;MAC1C,eAAe,EAAE1C,QAAQ;MACzB,eAAe,EAAEmD,kBAAkB,CAAC7C,OAAO,EAAE5B,OAAO,CAAC;MACrD,eAAe,EAAE+D,iBAAiB,CAACW,OAAO;MAC1C,eAAe,EAAEX,iBAAiB,CAACY,OAAO;MAC1C,gBAAgB,EAAE/C,OAAO,CAACpC,KAAK,GAAGoF,mBAAmB,CAAChD,OAAO,EAAE5B,OAAO,CAAC,GAAGE,YAAY,CAAC2E,KAAK;MAC5F,YAAY,EAAE3E,YAAY,CAAC0B,OAAO,CAACoC,IAAI,CAAC;MACxC,eAAe,EAAE3C,QAAQ;MACzB;MACAyD,QAAQ,EAAEvD,mBAAmB,IAAIG,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1DqD,eAAe,EAAE,CAACxD,mBAAmB,IAAI,CAACF,QAAQ,IAAI,CAACC,QAAQ;MAC/D0D,IAAI,EAAE,YAAY;MAClB5E,EAAE,EAAE,GAAGA,EAAE,IAAIwB,OAAO,CAACoC,IAAI,EAAE;MAC3B,qBAAqB,EAAEpC,OAAO,CAACqD,QAAQ,IAAIC,SAAS;MACpDC,UAAU,EAAE3D,UAAU,GAAG,KAAK,GAAG0D,SAAS;MAC1CE,cAAc,EAAE5D,UAAU,GAAG,KAAK,GAAG0D,SAAS;MAC9CG,WAAW,EAAE7D,UAAU,GAAG,KAAK,GAAG0D,SAAS;MAC3CI,QAAQ,EAAE1D,OAAO,CAACpC,KAAK,IAAIoC,OAAO,CAACI,WAAW;MAC9CuD,SAAS,EAAE3D,OAAO,CAAC2B,WAAW,KAAK,QAAQ,GAAG,MAAM,GAAG;IACzD,CAAC;EACH,CAAC,EAAE,CAAC1C,uBAAuB,EAAET,EAAE,EAAEmB,mBAAmB,EAAEF,QAAQ,EAAEC,QAAQ,EAAEE,UAAU,EAAEtB,YAAY,EAAEF,OAAO,EAAEkC,WAAW,EAAEW,WAAW,EAAEF,aAAa,EAAEe,cAAc,EAAEG,kBAAkB,EAAElD,iBAAiB,EAAEnB,KAAK,CAAC,CAAC;AACtN;AACA,SAASoF,mBAAmBA,CAAChD,OAAO,EAAE5B,OAAO,EAAE;EAC7C,IAAI,CAAC4B,OAAO,CAACpC,KAAK,EAAE;IAClB,OAAO0F,SAAS;EAClB;EACA,QAAQtD,OAAO,CAACoC,IAAI;IAClB,KAAK,OAAO;MACV;QACE,IAAIpC,OAAO,CAAC2B,WAAW,KAAK,OAAO,EAAE;UACnC,OAAOvD,OAAO,CAACmE,MAAM,CAACnE,OAAO,CAACwF,QAAQ,CAACxF,OAAO,CAACyF,IAAI,CAAC,CAAC,EAAEC,MAAM,CAAC9D,OAAO,CAACpC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;QAC7F;QACA,MAAMmG,UAAU,GAAG3F,OAAO,CAAC4F,KAAK,CAAChE,OAAO,CAACpC,KAAK,EAAEoC,OAAO,CAACuC,MAAM,CAAC;QAC/D,OAAOwB,UAAU,GAAG3F,OAAO,CAACmE,MAAM,CAACwB,UAAU,EAAE,OAAO,CAAC,GAAGT,SAAS;MACrE;IACF,KAAK,KAAK;MACR,OAAOtD,OAAO,CAAC2B,WAAW,KAAK,OAAO,GAAGvD,OAAO,CAACmE,MAAM,CAACnE,OAAO,CAAC6F,OAAO,CAAC7F,OAAO,CAAC8F,WAAW,CAAC9F,OAAO,CAACyF,IAAI,CAAC,CAAC,CAAC,EAAEC,MAAM,CAAC9D,OAAO,CAACpC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,GAAGoC,OAAO,CAACpC,KAAK;IACxK,KAAK,SAAS;MACZ;MACA,OAAO0F,SAAS;IAClB;MACE,OAAOA,SAAS;EACpB;AACF;AACA,SAAST,kBAAkBA,CAAC7C,OAAO,EAAE5B,OAAO,EAAE;EAC5C,IAAI,CAAC4B,OAAO,CAACpC,KAAK,EAAE;IAClB,OAAO0F,SAAS;EAClB;EACA,QAAQtD,OAAO,CAACoC,IAAI;IAClB,KAAK,SAAS;MACZ;QACE,IAAIpC,OAAO,CAAC2B,WAAW,KAAK,QAAQ,EAAE;UACpC;UACA,OAAO2B,SAAS;QAClB;QACA,OAAOQ,MAAM,CAAC9D,OAAO,CAACpC,KAAK,CAAC;MAC9B;IACF,KAAK,UAAU;MACb;QACE,MAAMmG,UAAU,GAAG3F,OAAO,CAAC4F,KAAK,CAAC,SAAShE,OAAO,CAACpC,KAAK,EAAE,EAAE,GAAGQ,OAAO,CAAC+F,OAAO,CAACC,QAAQ,IAAIhG,OAAO,CAAC+F,OAAO,CAACE,OAAO,IAAIrE,OAAO,CAACuC,MAAM,EAAE,CAAC;QACtI,IAAIwB,UAAU,EAAE;UACd,OAAO3F,OAAO,CAACkG,QAAQ,CAACP,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;QACnD;QACA,OAAOT,SAAS;MAClB;IACF,KAAK,KAAK;MACR,OAAOtD,OAAO,CAAC2B,WAAW,KAAK,mBAAmB,GAAG4C,QAAQ,CAACvE,OAAO,CAACpC,KAAK,EAAE,EAAE,CAAC,GAAGkG,MAAM,CAAC9D,OAAO,CAACpC,KAAK,CAAC;IAC1G,KAAK,OAAO;MACV;QACE,IAAIoC,OAAO,CAAC2B,WAAW,KAAK,OAAO,EAAE;UACnC,OAAOmC,MAAM,CAAC9D,OAAO,CAACpC,KAAK,CAAC;QAC9B;QACA,MAAMmG,UAAU,GAAG3F,OAAO,CAAC4F,KAAK,CAAChE,OAAO,CAACpC,KAAK,EAAEoC,OAAO,CAACuC,MAAM,CAAC;QAC/D,OAAOwB,UAAU,GAAG3F,OAAO,CAACoG,QAAQ,CAACT,UAAU,CAAC,GAAG,CAAC,GAAGT,SAAS;MAClE;IACF;MACE,OAAOtD,OAAO,CAAC2B,WAAW,KAAK,QAAQ,GAAGmC,MAAM,CAAC9D,OAAO,CAACpC,KAAK,CAAC,GAAG0F,SAAS;EAC/E;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}