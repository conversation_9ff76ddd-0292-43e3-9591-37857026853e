{"name": "@emotion/hash", "version": "0.9.2", "description": "A MurmurHash2 implementation", "main": "dist/emotion-hash.cjs.js", "module": "dist/emotion-hash.esm.js", "types": "dist/emotion-hash.cjs.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/hash", "files": ["src", "dist"], "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^5.4.5"}, "exports": {".": {"module": "./dist/emotion-hash.esm.js", "import": "./dist/emotion-hash.cjs.mjs", "default": "./dist/emotion-hash.cjs.js"}, "./package.json": "./package.json"}}