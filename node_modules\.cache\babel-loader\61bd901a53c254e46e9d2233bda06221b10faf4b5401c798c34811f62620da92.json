{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _useForkRef = _interopRequireDefault(require(\"../useForkRef\"));\nvar _appendOwnerState = _interopRequireDefault(require(\"../appendOwnerState\"));\nvar _mergeSlotProps = _interopRequireDefault(require(\"../mergeSlotProps\"));\nvar _resolveComponentProps = _interopRequireDefault(require(\"../resolveComponentProps\"));\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  const {\n    elementType,\n    externalSlotProps,\n    ownerState,\n    skipResolvingSlotProps = false,\n    ...other\n  } = parameters;\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : (0, _resolveComponentProps.default)(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = (0, _mergeSlotProps.default)({\n    ...other,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = (0, _useForkRef.default)(internalRef, resolvedComponentsProps?.ref, parameters.additionalProps?.ref);\n  const props = (0, _appendOwnerState.default)(elementType, {\n    ...mergedProps,\n    ref\n  }, ownerState);\n  return props;\n}\nvar _default = exports.default = useSlotProps;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "_useForkRef", "_appendOwnerState", "_mergeSlotProps", "_resolveComponentProps", "useSlotProps", "parameters", "elementType", "externalSlotProps", "ownerState", "skipResolvingSlotProps", "other", "resolvedComponentsProps", "props", "mergedProps", "internalRef", "ref", "additionalProps", "_default"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/useSlotProps/useSlotProps.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _useForkRef = _interopRequireDefault(require(\"../useForkRef\"));\nvar _appendOwnerState = _interopRequireDefault(require(\"../appendOwnerState\"));\nvar _mergeSlotProps = _interopRequireDefault(require(\"../mergeSlotProps\"));\nvar _resolveComponentProps = _interopRequireDefault(require(\"../resolveComponentProps\"));\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  const {\n    elementType,\n    externalSlotProps,\n    ownerState,\n    skipResolvingSlotProps = false,\n    ...other\n  } = parameters;\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : (0, _resolveComponentProps.default)(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = (0, _mergeSlotProps.default)({\n    ...other,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = (0, _useForkRef.default)(internalRef, resolvedComponentsProps?.ref, parameters.additionalProps?.ref);\n  const props = (0, _appendOwnerState.default)(elementType, {\n    ...mergedProps,\n    ref\n  }, ownerState);\n  return props;\n}\nvar _default = exports.default = useSlotProps;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIK,WAAW,GAAGP,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAClE,IAAIO,iBAAiB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC9E,IAAIQ,eAAe,GAAGT,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC1E,IAAIS,sBAAsB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,YAAYA,CAACC,UAAU,EAAE;EAChC,MAAM;IACJC,WAAW;IACXC,iBAAiB;IACjBC,UAAU;IACVC,sBAAsB,GAAG,KAAK;IAC9B,GAAGC;EACL,CAAC,GAAGL,UAAU;EACd,MAAMM,uBAAuB,GAAGF,sBAAsB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEN,sBAAsB,CAACR,OAAO,EAAEY,iBAAiB,EAAEC,UAAU,CAAC;EAChI,MAAM;IACJI,KAAK,EAAEC,WAAW;IAClBC;EACF,CAAC,GAAG,CAAC,CAAC,EAAEZ,eAAe,CAACP,OAAO,EAAE;IAC/B,GAAGe,KAAK;IACRH,iBAAiB,EAAEI;EACrB,CAAC,CAAC;EACF,MAAMI,GAAG,GAAG,CAAC,CAAC,EAAEf,WAAW,CAACL,OAAO,EAAEmB,WAAW,EAAEH,uBAAuB,EAAEI,GAAG,EAAEV,UAAU,CAACW,eAAe,EAAED,GAAG,CAAC;EAChH,MAAMH,KAAK,GAAG,CAAC,CAAC,EAAEX,iBAAiB,CAACN,OAAO,EAAEW,WAAW,EAAE;IACxD,GAAGO,WAAW;IACdE;EACF,CAAC,EAAEP,UAAU,CAAC;EACd,OAAOI,KAAK;AACd;AACA,IAAIK,QAAQ,GAAGnB,OAAO,CAACH,OAAO,GAAGS,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}