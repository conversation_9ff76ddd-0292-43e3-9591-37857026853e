{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.clearWarningsCache = clearWarningsCache;\nexports.warnOnce = warnOnce;\nconst warnedOnceCache = new Set();\n\n/**\n * Logs a message to the console on development mode. The warning will only be logged once.\n *\n * The message is the log's cache key. Two identical messages will only be logged once.\n *\n * This function is a no-op in production.\n *\n * @param message the message to log\n * @param gravity the gravity of the warning. Defaults to `'warning'`.\n * @returns\n */\nfunction warnOnce(message, gravity = 'warning') {\n  if (process.env.NODE_ENV === 'production') {\n    return;\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  if (!warnedOnceCache.has(cleanMessage)) {\n    warnedOnceCache.add(cleanMessage);\n    if (gravity === 'error') {\n      console.error(cleanMessage);\n    } else {\n      console.warn(cleanMessage);\n    }\n  }\n}\nfunction clearWarningsCache() {\n  warnedOnceCache.clear();\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "clearWarningsCache", "warnOnce", "warnedOnceCache", "Set", "message", "gravity", "process", "env", "NODE_ENV", "cleanMessage", "Array", "isArray", "join", "has", "add", "console", "error", "warn", "clear"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-internals/warning/warning.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.clearWarningsCache = clearWarningsCache;\nexports.warnOnce = warnOnce;\nconst warnedOnceCache = new Set();\n\n/**\n * Logs a message to the console on development mode. The warning will only be logged once.\n *\n * The message is the log's cache key. Two identical messages will only be logged once.\n *\n * This function is a no-op in production.\n *\n * @param message the message to log\n * @param gravity the gravity of the warning. Defaults to `'warning'`.\n * @returns\n */\nfunction warnOnce(message, gravity = 'warning') {\n  if (process.env.NODE_ENV === 'production') {\n    return;\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  if (!warnedOnceCache.has(cleanMessage)) {\n    warnedOnceCache.add(cleanMessage);\n    if (gravity === 'error') {\n      console.error(cleanMessage);\n    } else {\n      console.warn(cleanMessage);\n    }\n  }\n}\nfunction clearWarningsCache() {\n  warnedOnceCache.clear();\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/CF,OAAO,CAACG,QAAQ,GAAGA,QAAQ;AAC3B,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,QAAQA,CAACG,OAAO,EAAEC,OAAO,GAAG,SAAS,EAAE;EAC9C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;EACF;EACA,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC,GAAGA,OAAO,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAGR,OAAO;EAC1E,IAAI,CAACF,eAAe,CAACW,GAAG,CAACJ,YAAY,CAAC,EAAE;IACtCP,eAAe,CAACY,GAAG,CAACL,YAAY,CAAC;IACjC,IAAIJ,OAAO,KAAK,OAAO,EAAE;MACvBU,OAAO,CAACC,KAAK,CAACP,YAAY,CAAC;IAC7B,CAAC,MAAM;MACLM,OAAO,CAACE,IAAI,CAACR,YAAY,CAAC;IAC5B;EACF;AACF;AACA,SAAST,kBAAkBA,CAAA,EAAG;EAC5BE,eAAe,CAACgB,KAAK,CAAC,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}