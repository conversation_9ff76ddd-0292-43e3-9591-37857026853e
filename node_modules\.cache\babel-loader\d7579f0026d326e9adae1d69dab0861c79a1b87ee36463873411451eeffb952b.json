{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Statistics\\\\Statistics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, FormControl, InputLabel, Select, MenuItem, TextField, Chip, Divider, Paper } from '@mui/material';\nimport { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip as ChartTooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { useApp } from '../../context/AppContext';\nimport { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport VehicleComparison from './VehicleComparison';\nimport AdvancedAnalytics from './AdvancedAnalytics';\n\n// Colores para los gráficos\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\n\n// Componente para métricas principales\nconst MetricCard = ({\n  title,\n  value,\n  subtitle,\n  color = 'primary',\n  trend\n}) => /*#__PURE__*/_jsxDEV(Card, {\n  children: /*#__PURE__*/_jsxDEV(CardContent, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      color: \"textSecondary\",\n      gutterBottom: true,\n      variant: \"body2\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"div\",\n      color: color,\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 9\n    }, this), trend && /*#__PURE__*/_jsxDEV(Box, {\n      mt: 1,\n      children: /*#__PURE__*/_jsxDEV(Chip, {\n        label: trend,\n        size: \"small\",\n        color: trend.includes('+') ? 'success' : trend.includes('-') ? 'error' : 'default'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 45,\n  columnNumber: 3\n}, this);\n_c = MetricCard;\nconst Statistics = () => {\n  _s();\n  const {\n    vehicles,\n    refuels,\n    expenses,\n    loadStatistics,\n    statistics\n  } = useApp();\n  const [selectedVehicle, setSelectedVehicle] = useState('all');\n  const [dateRange, setDateRange] = useState('6months');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [chartData, setChartData] = useState({\n    consumption: [],\n    costs: [],\n    expensesByType: [],\n    monthlyTrends: []\n  });\n  useEffect(() => {\n    calculateStatistics();\n  }, [refuels, expenses, selectedVehicle, dateRange]);\n  const calculateStatistics = () => {\n    console.log('🔄 Recalculating statistics with filters:', {\n      selectedVehicle,\n      dateRange\n    });\n    console.log('📊 Total data available:', {\n      refuels: refuels.length,\n      expenses: expenses.length\n    });\n    let filteredRefuels = [...refuels]; // Crear copia para evitar mutación\n    let filteredExpenses = [...expenses];\n\n    // Filtrar por vehículo\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n      console.log('🚗 Filtered by vehicle:', vehicleId, {\n        refuels: filteredRefuels.length,\n        expenses: filteredExpenses.length\n      });\n    }\n\n    // Filtrar por fecha\n    const now = new Date();\n    let startDateFilter;\n    if (startDate && endDate) {\n      // Usar fechas personalizadas si están definidas\n      startDateFilter = new Date(startDate);\n      const endDateFilter = new Date(endDate);\n      filteredRefuels = filteredRefuels.filter(r => {\n        const refuelDate = new Date(r.fecha);\n        return refuelDate >= startDateFilter && refuelDate <= endDateFilter;\n      });\n      filteredExpenses = filteredExpenses.filter(e => {\n        const expenseDate = new Date(e.fecha);\n        return expenseDate >= startDateFilter && expenseDate <= endDateFilter;\n      });\n      console.log('📅 Filtered by custom date range:', {\n        startDate: startDateFilter,\n        endDate: endDateFilter,\n        refuels: filteredRefuels.length,\n        expenses: filteredExpenses.length\n      });\n    } else {\n      // Usar rangos predefinidos si no hay fechas personalizadas\n      switch (dateRange) {\n        case '3months':\n          startDateFilter = subMonths(now, 3);\n          break;\n        case '6months':\n          startDateFilter = subMonths(now, 6);\n          break;\n        case '1year':\n          startDateFilter = subMonths(now, 12);\n          break;\n        case '2years':\n          startDateFilter = subMonths(now, 24);\n          break;\n        default:\n          startDateFilter = new Date(0);\n        // Todos los datos\n      }\n      if (dateRange !== 'all') {\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDateFilter);\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDateFilter);\n        console.log('📅 Filtered by date range:', dateRange, {\n          refuels: filteredRefuels.length,\n          expenses: filteredExpenses.length\n        });\n      }\n    }\n\n    // Calcular datos para gráficos\n    calculateChartData(filteredRefuels, filteredExpenses);\n  };\n  const calculateChartData = (refuels, expenses) => {\n    // 1. Datos de consumo por mes\n    const consumptionByMonth = {};\n    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    for (let i = 1; i < sortedRefuels.length; i++) {\n      const current = sortedRefuels[i];\n      const previous = sortedRefuels[i - 1];\n      if (current.vehiculo_id === previous.vehiculo_id) {\n        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;\n        const liters = current.litros;\n        if (kmDiff > 0 && liters > 0) {\n          const consumption = liters / kmDiff * 100; // L/100km\n          const month = format(new Date(current.fecha), 'yyyy-MM');\n          if (!consumptionByMonth[month]) {\n            consumptionByMonth[month] = {\n              total: 0,\n              count: 0,\n              month\n            };\n          }\n          consumptionByMonth[month].total += consumption;\n          consumptionByMonth[month].count += 1;\n        }\n      }\n    }\n    const consumptionData = Object.values(consumptionByMonth).map(item => ({\n      month: format(new Date(item.month + '-01'), 'MMM yyyy', {\n        locale: es\n      }),\n      consumption: (item.total / item.count).toFixed(1),\n      date: item.month\n    })).sort((a, b) => a.date.localeCompare(b.date));\n\n    // 2. Costes por mes\n    const costsByMonth = {};\n    [...refuels, ...expenses].forEach(item => {\n      const month = format(new Date(item.fecha), 'yyyy-MM');\n      const cost = item.coste_total || item.coste || 0;\n      const type = item.litros ? 'Combustible' : 'Gastos';\n      if (!costsByMonth[month]) {\n        costsByMonth[month] = {\n          month,\n          Combustible: 0,\n          Gastos: 0,\n          date: month\n        };\n      }\n      costsByMonth[month][type] += cost;\n    });\n    const costsData = Object.values(costsByMonth).map(item => ({\n      ...item,\n      month: format(new Date(item.month + '-01'), 'MMM yyyy', {\n        locale: es\n      }),\n      Total: item.Combustible + item.Gastos\n    })).sort((a, b) => a.date.localeCompare(b.date));\n\n    // 3. Gastos por tipo\n    const expensesByType = {};\n    expenses.forEach(expense => {\n      const type = expense.tipo_gasto || 'Otros';\n      expensesByType[type] = (expensesByType[type] || 0) + (expense.coste || 0);\n    });\n    const expensesData = Object.entries(expensesByType).map(([name, value]) => ({\n      name,\n      value: parseFloat(value.toFixed(2))\n    })).sort((a, b) => b.value - a.value);\n\n    // 4. Tendencias mensuales\n    const monthlyTrends = costsData.map(item => {\n      var _consumptionData$find;\n      return {\n        month: item.month,\n        date: item.date,\n        totalCost: item.Total,\n        fuelCost: item.Combustible,\n        expenseCost: item.Gastos,\n        consumption: ((_consumptionData$find = consumptionData.find(c => c.date === item.date)) === null || _consumptionData$find === void 0 ? void 0 : _consumptionData$find.consumption) || 0\n      };\n    });\n    setChartData({\n      consumption: consumptionData,\n      costs: costsData,\n      expensesByType: expensesData,\n      monthlyTrends\n    });\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  // Aplicar filtros para métricas principales\n  const getFilteredData = () => {\n    let filteredRefuels = [...refuels];\n    let filteredExpenses = [...expenses];\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n    }\n    if (dateRange !== 'all') {\n      const now = new Date();\n      let startDate;\n      switch (dateRange) {\n        case '3months':\n          startDate = subMonths(now, 3);\n          break;\n        case '6months':\n          startDate = subMonths(now, 6);\n          break;\n        case '1year':\n          startDate = subMonths(now, 12);\n          break;\n        case '2years':\n          startDate = subMonths(now, 24);\n          break;\n        default:\n          startDate = new Date(0);\n      }\n      if (dateRange !== 'all') {\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);\n      }\n    }\n    return {\n      filteredRefuels,\n      filteredExpenses\n    };\n  };\n  const {\n    filteredRefuels,\n    filteredExpenses\n  } = getFilteredData();\n\n  // Calcular métricas principales con datos filtrados\n  const totalRefuels = filteredRefuels.length;\n  const totalExpenses = filteredExpenses.length;\n  const totalFuelCost = filteredRefuels.reduce((sum, r) => sum + (r.coste_total || 0), 0);\n  const totalExpenseCost = filteredExpenses.reduce((sum, e) => sum + (e.coste || 0), 0);\n  const totalCost = totalFuelCost + totalExpenseCost;\n  const totalLiters = filteredRefuels.reduce((sum, r) => sum + (r.litros || 0), 0);\n\n  // Calcular consumo promedio con datos filtrados\n  let avgConsumption = 0;\n  if (filteredRefuels.length >= 2) {\n    const sortedRefuels = [...filteredRefuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales - sortedRefuels[0].kilometros_actuales;\n    if (totalKm > 0) {\n      avgConsumption = totalLiters / totalKm * 100;\n    }\n  }\n  const avgFuelPrice = totalLiters > 0 ? totalFuelCost / totalLiters : 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      fontWeight: \"bold\",\n      mb: 3,\n      children: \"Estad\\xEDsticas y An\\xE1lisis\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedVehicle,\n                onChange: e => setSelectedVehicle(e.target.value),\n                label: \"Veh\\xEDculo\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"Todos los veh\\xEDculos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this), vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: vehicle.id,\n                  children: vehicle.nombre\n                }, vehicle.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Per\\xEDodo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: dateRange,\n                onChange: e => setDateRange(e.target.value),\n                label: \"Per\\xEDodo\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"3months\",\n                  children: \"\\xDAltimos 3 meses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"6months\",\n                  children: \"\\xDAltimos 6 meses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"1year\",\n                  children: \"\\xDAltimo a\\xF1o\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"2years\",\n                  children: \"\\xDAltimos 2 a\\xF1os\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"Todo el historial\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Consumo Promedio\",\n          value: `${avgConsumption.toFixed(1)} L`,\n          subtitle: \"Por 100 km\",\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Precio Promedio\",\n          value: formatCurrency(avgFuelPrice),\n          subtitle: \"Por litro\",\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Gasto Total\",\n          value: formatCurrency(totalCost),\n          subtitle: `${formatCurrency(totalFuelCost)} combustible + ${formatCurrency(totalExpenseCost)} gastos`,\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Total Litros\",\n          value: `${totalLiters.toFixed(1)} L`,\n          subtitle: `En ${totalRefuels} repostajes`,\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Evoluci\\xF3n del Consumo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: chartData.consumption,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [`${value} L/100km`, 'Consumo'],\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"consumption\",\n                    stroke: \"#8884d8\",\n                    strokeWidth: 2,\n                    name: \"L/100km\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Costes Mensuales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                  data: chartData.costs,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [formatCurrency(value)],\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"Combustible\",\n                    stackId: \"1\",\n                    stroke: \"#82ca9d\",\n                    fill: \"#82ca9d\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"Gastos\",\n                    stackId: \"1\",\n                    stroke: \"#ffc658\",\n                    fill: \"#ffc658\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Distribuci\\xF3n de Gastos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(PieChart, {\n                  children: [/*#__PURE__*/_jsxDEV(Pie, {\n                    data: chartData.expensesByType,\n                    cx: \"50%\",\n                    cy: \"50%\",\n                    labelLine: false,\n                    label: ({\n                      name,\n                      percent\n                    }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                    outerRadius: 80,\n                    fill: \"#8884d8\",\n                    dataKey: \"value\",\n                    children: chartData.expensesByType.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                      fill: COLORS[index % COLORS.length]\n                    }, `cell-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [formatCurrency(value)]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Tendencias Generales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(BarChart, {\n                  data: chartData.monthlyTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: (value, name) => {\n                      if (name === 'Coste Total') return [formatCurrency(value), name];\n                      return [value, name];\n                    },\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                    dataKey: \"totalCost\",\n                    fill: \"#8884d8\",\n                    name: \"Coste Total (\\u20AC)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Resumen Estad\\xEDstico\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: totalRefuels\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Repostajes totales\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"success.main\",\n                children: totalExpenses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Gastos registrados\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"warning.main\",\n                children: formatCurrency(totalCost / (totalRefuels + totalExpenses) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Gasto promedio por registro\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"info.main\",\n                children: vehicles.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Veh\\xEDculos activos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mt: 4,\n      children: /*#__PURE__*/_jsxDEV(AdvancedAnalytics, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this), vehicles.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      mt: 4,\n      children: /*#__PURE__*/_jsxDEV(VehicleComparison, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 5\n  }, this);\n};\n_s(Statistics, \"oG7XeVzgnSHDB5hIm9Lx7dYtJbU=\", false, function () {\n  return [useApp];\n});\n_c2 = Statistics;\nexport default Statistics;\nvar _c, _c2;\n$RefreshReg$(_c, \"MetricCard\");\n$RefreshReg$(_c2, \"Statistics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Chip", "Divider", "Paper", "Line<PERSON>hart", "Line", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ChartTooltip", "Legend", "ResponsiveContainer", "useApp", "format", "subMonths", "startOfMonth", "endOfMonth", "es", "VehicleComparison", "AdvancedAnalytics", "jsxDEV", "_jsxDEV", "COLORS", "MetricCard", "title", "value", "subtitle", "color", "trend", "children", "gutterBottom", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "mt", "label", "size", "includes", "_c", "Statistics", "_s", "vehicles", "refuels", "expenses", "loadStatistics", "statistics", "selectedVehicle", "setSelectedVehicle", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "setStartDate", "endDate", "setEndDate", "chartData", "setChartData", "consumption", "costs", "expensesByType", "monthlyTrends", "calculateStatistics", "console", "log", "length", "filteredRefuels", "filteredExpenses", "vehicleId", "parseInt", "filter", "r", "vehiculo_id", "e", "now", "Date", "startDateFilter", "endDateFilter", "refuelDate", "fecha", "expenseDate", "calculateChartData", "consumptionByMonth", "sortedRefuels", "sort", "a", "b", "i", "current", "previous", "kmDiff", "kilometros_actuales", "liters", "litros", "month", "total", "count", "consumptionData", "Object", "values", "map", "item", "locale", "toFixed", "date", "localeCompare", "costsByMonth", "for<PERSON>ach", "cost", "coste_total", "coste", "type", "Combustible", "Gastos", "costsData", "Total", "expense", "tipo_gasto", "expensesData", "entries", "name", "parseFloat", "_consumptionData$find", "totalCost", "fuelCost", "expenseCost", "find", "c", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "getFilteredData", "totalRefuels", "totalExpenses", "totalFuelCost", "reduce", "sum", "totalExpenseCost", "totalLiters", "avgConsumption", "totalKm", "avgFuelPrice", "fontWeight", "mb", "sx", "container", "spacing", "alignItems", "xs", "sm", "md", "fullWidth", "onChange", "target", "vehicle", "id", "nombre", "lg", "height", "width", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "formatter", "labelStyle", "stroke", "strokeWidth", "stackId", "fill", "cx", "cy", "labelLine", "percent", "outerRadius", "entry", "index", "textAlign", "_c2", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Statistics/Statistics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Chip,\n  Divider,\n  Paper,\n} from '@mui/material';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip as ChartTooltip,\n  Legend,\n  ResponsiveContainer,\n} from 'recharts';\nimport { useApp } from '../../context/AppContext';\nimport { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport VehicleComparison from './VehicleComparison';\nimport AdvancedAnalytics from './AdvancedAnalytics';\n\n// Colores para los gráficos\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\n\n// Componente para métricas principales\nconst MetricCard = ({ title, value, subtitle, color = 'primary', trend }) => (\n  <Card>\n    <CardContent>\n      <Typography color=\"textSecondary\" gutterBottom variant=\"body2\">\n        {title}\n      </Typography>\n      <Typography variant=\"h4\" component=\"div\" color={color}>\n        {value}\n      </Typography>\n      {subtitle && (\n        <Typography variant=\"body2\" color=\"textSecondary\">\n          {subtitle}\n        </Typography>\n      )}\n      {trend && (\n        <Box mt={1}>\n          <Chip \n            label={trend} \n            size=\"small\" \n            color={trend.includes('+') ? 'success' : trend.includes('-') ? 'error' : 'default'}\n          />\n        </Box>\n      )}\n    </CardContent>\n  </Card>\n);\n\nconst Statistics = () => {\n  const { vehicles, refuels, expenses, loadStatistics, statistics } = useApp();\n  const [selectedVehicle, setSelectedVehicle] = useState('all');\n  const [dateRange, setDateRange] = useState('6months');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [chartData, setChartData] = useState({\n    consumption: [],\n    costs: [],\n    expensesByType: [],\n    monthlyTrends: []\n  });\n\n  useEffect(() => {\n    calculateStatistics();\n  }, [refuels, expenses, selectedVehicle, dateRange]);\n\n  const calculateStatistics = () => {\n    console.log('🔄 Recalculating statistics with filters:', { selectedVehicle, dateRange });\n    console.log('📊 Total data available:', { refuels: refuels.length, expenses: expenses.length });\n    \n    let filteredRefuels = [...refuels]; // Crear copia para evitar mutación\n    let filteredExpenses = [...expenses];\n\n    // Filtrar por vehículo\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n      console.log('🚗 Filtered by vehicle:', vehicleId, { refuels: filteredRefuels.length, expenses: filteredExpenses.length });\n    }\n\n    // Filtrar por fecha\n    const now = new Date();\n    let startDateFilter;\n    \n    if (startDate && endDate) {\n      // Usar fechas personalizadas si están definidas\n      startDateFilter = new Date(startDate);\n      const endDateFilter = new Date(endDate);\n      \n      filteredRefuels = filteredRefuels.filter(r => {\n        const refuelDate = new Date(r.fecha);\n        return refuelDate >= startDateFilter && refuelDate <= endDateFilter;\n      });\n      \n      filteredExpenses = filteredExpenses.filter(e => {\n        const expenseDate = new Date(e.fecha);\n        return expenseDate >= startDateFilter && expenseDate <= endDateFilter;\n      });\n      \n      console.log('📅 Filtered by custom date range:', { \n        startDate: startDateFilter, \n        endDate: endDateFilter,\n        refuels: filteredRefuels.length, \n        expenses: filteredExpenses.length \n      });\n    } else {\n      // Usar rangos predefinidos si no hay fechas personalizadas\n      switch (dateRange) {\n        case '3months':\n          startDateFilter = subMonths(now, 3);\n          break;\n        case '6months':\n          startDateFilter = subMonths(now, 6);\n          break;\n        case '1year':\n          startDateFilter = subMonths(now, 12);\n          break;\n        case '2years':\n          startDateFilter = subMonths(now, 24);\n          break;\n        default:\n          startDateFilter = new Date(0); // Todos los datos\n      }\n      \n      if (dateRange !== 'all') {\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDateFilter);\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDateFilter);\n        console.log('📅 Filtered by date range:', dateRange, { \n          refuels: filteredRefuels.length, \n          expenses: filteredExpenses.length \n        });\n      }\n    }\n\n    // Calcular datos para gráficos\n    calculateChartData(filteredRefuels, filteredExpenses);\n  };\n\n  const calculateChartData = (refuels, expenses) => {\n    // 1. Datos de consumo por mes\n    const consumptionByMonth = {};\n    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    \n    for (let i = 1; i < sortedRefuels.length; i++) {\n      const current = sortedRefuels[i];\n      const previous = sortedRefuels[i - 1];\n      \n      if (current.vehiculo_id === previous.vehiculo_id) {\n        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;\n        const liters = current.litros;\n        \n        if (kmDiff > 0 && liters > 0) {\n          const consumption = (liters / kmDiff) * 100; // L/100km\n          const month = format(new Date(current.fecha), 'yyyy-MM');\n          \n          if (!consumptionByMonth[month]) {\n            consumptionByMonth[month] = { total: 0, count: 0, month };\n          }\n          consumptionByMonth[month].total += consumption;\n          consumptionByMonth[month].count += 1;\n        }\n      }\n    }\n\n    const consumptionData = Object.values(consumptionByMonth)\n      .map(item => ({\n        month: format(new Date(item.month + '-01'), 'MMM yyyy', { locale: es }),\n        consumption: (item.total / item.count).toFixed(1),\n        date: item.month\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n\n    // 2. Costes por mes\n    const costsByMonth = {};\n    \n    [...refuels, ...expenses].forEach(item => {\n      const month = format(new Date(item.fecha), 'yyyy-MM');\n      const cost = item.coste_total || item.coste || 0;\n      const type = item.litros ? 'Combustible' : 'Gastos';\n      \n      if (!costsByMonth[month]) {\n        costsByMonth[month] = { month, Combustible: 0, Gastos: 0, date: month };\n      }\n      costsByMonth[month][type] += cost;\n    });\n\n    const costsData = Object.values(costsByMonth)\n      .map(item => ({\n        ...item,\n        month: format(new Date(item.month + '-01'), 'MMM yyyy', { locale: es }),\n        Total: item.Combustible + item.Gastos\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n\n    // 3. Gastos por tipo\n    const expensesByType = {};\n    expenses.forEach(expense => {\n      const type = expense.tipo_gasto || 'Otros';\n      expensesByType[type] = (expensesByType[type] || 0) + (expense.coste || 0);\n    });\n\n    const expensesData = Object.entries(expensesByType)\n      .map(([name, value]) => ({ name, value: parseFloat(value.toFixed(2)) }))\n      .sort((a, b) => b.value - a.value);\n\n    // 4. Tendencias mensuales\n    const monthlyTrends = costsData.map(item => ({\n      month: item.month,\n      date: item.date,\n      totalCost: item.Total,\n      fuelCost: item.Combustible,\n      expenseCost: item.Gastos,\n      consumption: consumptionData.find(c => c.date === item.date)?.consumption || 0\n    }));\n\n    setChartData({\n      consumption: consumptionData,\n      costs: costsData,\n      expensesByType: expensesData,\n      monthlyTrends\n    });\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\r\n    }).format(amount || 0);\r\n  };\r\n\r\n  // Aplicar filtros para métricas principales\r\n  const getFilteredData = () => {\r\n    let filteredRefuels = [...refuels];\r\n    let filteredExpenses = [...expenses];\r\n\r\n    if (selectedVehicle !== 'all') {\r\n      const vehicleId = parseInt(selectedVehicle);\r\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\r\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\r\n    }\r\n\r\n    if (dateRange !== 'all') {\r\n      const now = new Date();\r\n      let startDate;\r\n      switch (dateRange) {\r\n        case '3months': startDate = subMonths(now, 3); break;\r\n        case '6months': startDate = subMonths(now, 6); break;\r\n        case '1year': startDate = subMonths(now, 12); break;\r\n        case '2years': startDate = subMonths(now, 24); break;\r\n        default: startDate = new Date(0);\r\n      }\r\n      \r\n      if (dateRange !== 'all') {\r\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);\r\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);\r\n      }\r\n    }\r\n\r\n    return { filteredRefuels, filteredExpenses };\r\n  };\r\n\r\n  const { filteredRefuels, filteredExpenses } = getFilteredData();\r\n\r\n  // Calcular métricas principales con datos filtrados\r\n  const totalRefuels = filteredRefuels.length;\r\n  const totalExpenses = filteredExpenses.length;\r\n  const totalFuelCost = filteredRefuels.reduce((sum, r) => sum + (r.coste_total || 0), 0);\r\n  const totalExpenseCost = filteredExpenses.reduce((sum, e) => sum + (e.coste || 0), 0);\r\n  const totalCost = totalFuelCost + totalExpenseCost;\r\n  const totalLiters = filteredRefuels.reduce((sum, r) => sum + (r.litros || 0), 0);\r\n  \r\n  // Calcular consumo promedio con datos filtrados\r\n  let avgConsumption = 0;\r\n  if (filteredRefuels.length >= 2) {\r\n    const sortedRefuels = [...filteredRefuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\r\n    const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales - \r\n                   sortedRefuels[0].kilometros_actuales;\r\n    if (totalKm > 0) {\r\n      avgConsumption = (totalLiters / totalKm * 100);\r\n    }\r\n  }\r\n\r\n  const avgFuelPrice = totalLiters > 0 ? totalFuelCost / totalLiters : 0;\r\n\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\" mb={3}>\r\n        Estadísticas y Análisis\r\n      </Typography>\r\n\r\n      {/* Filtros */}\r\n      <Card sx={{ mb: 3 }}>\r\n        <CardContent>\r\n          <Grid container spacing={2} alignItems=\"center\">\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <FormControl fullWidth>\r\n                <InputLabel>Vehículo</InputLabel>\r\n                <Select\r\n                  value={selectedVehicle}\r\n                  onChange={(e) => setSelectedVehicle(e.target.value)}\r\n                  label=\"Vehículo\"\r\n                >\r\n                  <MenuItem value=\"all\">Todos los vehículos</MenuItem>\r\n                  {vehicles.map((vehicle) => (\r\n                    <MenuItem key={vehicle.id} value={vehicle.id}>\r\n                      {vehicle.nombre}\r\n                    </MenuItem>\r\n                  ))}\r\n                </Select>\r\n              </FormControl>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <FormControl fullWidth>\r\n                <InputLabel>Período</InputLabel>\r\n                <Select\r\n                  value={dateRange}\r\n                  onChange={(e) => setDateRange(e.target.value)}\r\n                  label=\"Período\"\r\n                >\r\n                  <MenuItem value=\"3months\">Últimos 3 meses</MenuItem>\r\n                  <MenuItem value=\"6months\">Últimos 6 meses</MenuItem>\r\n                  <MenuItem value=\"1year\">Último año</MenuItem>\r\n                  <MenuItem value=\"2years\">Últimos 2 años</MenuItem>\r\n                  <MenuItem value=\"all\">Todo el historial</MenuItem>\r\n                </Select>\r\n              </FormControl>\r\n            </Grid>\r\n          </Grid>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Métricas principales */}\r\n      <Grid container spacing={3} mb={4}>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <MetricCard\r\n            title=\"Consumo Promedio\"\r\n            value={`${avgConsumption.toFixed(1)} L`}\r\n            subtitle=\"Por 100 km\"\r\n            color=\"primary\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <MetricCard\r\n            title=\"Precio Promedio\"\r\n            value={formatCurrency(avgFuelPrice)}\r\n            subtitle=\"Por litro\"\r\n            color=\"success\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <MetricCard\r\n            title=\"Gasto Total\"\r\n            value={formatCurrency(totalCost)}\r\n            subtitle={`${formatCurrency(totalFuelCost)} combustible + ${formatCurrency(totalExpenseCost)} gastos`}\r\n            color=\"warning\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <MetricCard\r\n            title=\"Total Litros\"\r\n            value={`${totalLiters.toFixed(1)} L`}\r\n            subtitle={`En ${totalRefuels} repostajes`}\r\n            color=\"info\"\r\n          />\r\n        </Grid>\r\n      </Grid>\r\n\r\n      {/* Gráficos */}\r\n      <Grid container spacing={3}>\r\n        {/* Gráfico de consumo */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Evolución del Consumo\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <LineChart data={chartData.consumption}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"month\" />\r\n                    <YAxis />\r\n                    <ChartTooltip \r\n                      formatter={(value) => [`${value} L/100km`, 'Consumo']}\r\n                      labelStyle={{ color: '#666' }}\r\n                    />\r\n                    <Legend />\r\n                    <Line \r\n                      type=\"monotone\" \r\n                      dataKey=\"consumption\" \r\n                      stroke=\"#8884d8\" \r\n                      strokeWidth={2}\r\n                      name=\"L/100km\"\r\n                    />\r\n                  </LineChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Gráfico de costes mensuales */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Costes Mensuales\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <AreaChart data={chartData.costs}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"month\" />\r\n                    <YAxis />\r\n                    <ChartTooltip \r\n                      formatter={(value) => [formatCurrency(value)]}\r\n                      labelStyle={{ color: '#666' }}\r\n                    />\r\n                    <Legend />\r\n                    <Area \r\n                      type=\"monotone\" \r\n                      dataKey=\"Combustible\" \r\n                      stackId=\"1\"\r\n                      stroke=\"#82ca9d\" \r\n                      fill=\"#82ca9d\" \r\n                    />\r\n                    <Area \r\n                      type=\"monotone\" \r\n                      dataKey=\"Gastos\" \r\n                      stackId=\"1\"\r\n                      stroke=\"#ffc658\" \r\n                      fill=\"#ffc658\" \r\n                    />\r\n                  </AreaChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Gráfico de gastos por tipo */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Distribución de Gastos\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <PieChart>\r\n                    <Pie\r\n                      data={chartData.expensesByType}\r\n                      cx=\"50%\"\r\n                      cy=\"50%\"\r\n                      labelLine={false}\r\n                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\r\n                      outerRadius={80}\r\n                      fill=\"#8884d8\"\r\n                      dataKey=\"value\"\r\n                    >\r\n                      {chartData.expensesByType.map((entry, index) => (\r\n                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\r\n                      ))}\r\n                    </Pie>\r\n                    <ChartTooltip formatter={(value) => [formatCurrency(value)]} />\r\n                  </PieChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Tendencias combinadas */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Tendencias Generales\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <BarChart data={chartData.monthlyTrends}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"month\" />\r\n                    <YAxis />\r\n                    <ChartTooltip \r\n                      formatter={(value, name) => {\r\n                        if (name === 'Coste Total') return [formatCurrency(value), name];\r\n                        return [value, name];\r\n                      }}\r\n                      labelStyle={{ color: '#666' }}\r\n                    />\r\n                    <Legend />\r\n                    <Bar dataKey=\"totalCost\" fill=\"#8884d8\" name=\"Coste Total (€)\" />\r\n                  </BarChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n\r\n      {/* Resumen estadístico */}\r\n      <Card sx={{ mt: 3 }}>\r\n        <CardContent>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            Resumen Estadístico\r\n          </Typography>\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"primary\">\r\n                  {totalRefuels}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Repostajes totales\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"success.main\">\r\n                  {totalExpenses}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Gastos registrados\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"warning.main\">\r\n                  {formatCurrency(totalCost / (totalRefuels + totalExpenses) || 0)}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Gasto promedio por registro\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"info.main\">\r\n                  {vehicles.length}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Vehículos activos\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Análisis avanzado */}\r\n      <Box mt={4}>\r\n        <AdvancedAnalytics />\r\n      </Box>\r\n\r\n      {/* Comparación de vehículos */}\r\n      {vehicles.length > 1 && (\r\n        <Box mt={4}>\r\n          <VehicleComparison />\r\n        </Box>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default Statistics;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,OAAO,EACPC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,EACTC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,IAAIC,YAAY,EACvBC,MAAM,EACNC,mBAAmB,QACd,UAAU;AACjB,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,UAAU,QAAQ,UAAU;AACtE,SAASC,EAAE,QAAQ,iBAAiB;AACpC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,iBAAiB,MAAM,qBAAqB;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;;AAEjF;AACA,MAAMC,UAAU,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,KAAK,GAAG,SAAS;EAAEC;AAAM,CAAC,kBACtEP,OAAA,CAACpC,IAAI;EAAA4C,QAAA,eACHR,OAAA,CAACnC,WAAW;IAAA2C,QAAA,gBACVR,OAAA,CAACrC,UAAU;MAAC2C,KAAK,EAAC,eAAe;MAACG,YAAY;MAACC,OAAO,EAAC,OAAO;MAAAF,QAAA,EAC3DL;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACbd,OAAA,CAACrC,UAAU;MAAC+C,OAAO,EAAC,IAAI;MAACK,SAAS,EAAC,KAAK;MAACT,KAAK,EAAEA,KAAM;MAAAE,QAAA,EACnDJ;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EACZT,QAAQ,iBACPL,OAAA,CAACrC,UAAU;MAAC+C,OAAO,EAAC,OAAO;MAACJ,KAAK,EAAC,eAAe;MAAAE,QAAA,EAC9CH;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACb,EACAP,KAAK,iBACJP,OAAA,CAACtC,GAAG;MAACsD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTR,OAAA,CAAC5B,IAAI;QACH6C,KAAK,EAAEV,KAAM;QACbW,IAAI,EAAC,OAAO;QACZZ,KAAK,EAAEC,KAAK,CAACY,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGZ,KAAK,CAACY,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG;MAAU;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACP;AAACM,EAAA,GAzBIlB,UAAU;AA2BhB,MAAMmB,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,cAAc;IAAEC;EAAW,CAAC,GAAGpC,MAAM,CAAC,CAAC;EAC5E,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACwE,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0E,OAAO,EAAEC,UAAU,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4E,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC;IACzC8E,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEFhF,SAAS,CAAC,MAAM;IACdiF,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAClB,OAAO,EAAEC,QAAQ,EAAEG,eAAe,EAAEE,SAAS,CAAC,CAAC;EAEnD,MAAMY,mBAAmB,GAAGA,CAAA,KAAM;IAChCC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MAAEhB,eAAe;MAAEE;IAAU,CAAC,CAAC;IACxFa,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MAAEpB,OAAO,EAAEA,OAAO,CAACqB,MAAM;MAAEpB,QAAQ,EAAEA,QAAQ,CAACoB;IAAO,CAAC,CAAC;IAE/F,IAAIC,eAAe,GAAG,CAAC,GAAGtB,OAAO,CAAC,CAAC,CAAC;IACpC,IAAIuB,gBAAgB,GAAG,CAAC,GAAGtB,QAAQ,CAAC;;IAEpC;IACA,IAAIG,eAAe,KAAK,KAAK,EAAE;MAC7B,MAAMoB,SAAS,GAAGC,QAAQ,CAACrB,eAAe,CAAC;MAC3CkB,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,SAAS,CAAC;MAC1ED,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACD,WAAW,KAAKJ,SAAS,CAAC;MAC5EL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,SAAS,EAAE;QAAExB,OAAO,EAAEsB,eAAe,CAACD,MAAM;QAAEpB,QAAQ,EAAEsB,gBAAgB,CAACF;MAAO,CAAC,CAAC;IAC3H;;IAEA;IACA,MAAMS,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAIC,eAAe;IAEnB,IAAIxB,SAAS,IAAIE,OAAO,EAAE;MACxB;MACAsB,eAAe,GAAG,IAAID,IAAI,CAACvB,SAAS,CAAC;MACrC,MAAMyB,aAAa,GAAG,IAAIF,IAAI,CAACrB,OAAO,CAAC;MAEvCY,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAI;QAC5C,MAAMO,UAAU,GAAG,IAAIH,IAAI,CAACJ,CAAC,CAACQ,KAAK,CAAC;QACpC,OAAOD,UAAU,IAAIF,eAAe,IAAIE,UAAU,IAAID,aAAa;MACrE,CAAC,CAAC;MAEFV,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAI;QAC9C,MAAMO,WAAW,GAAG,IAAIL,IAAI,CAACF,CAAC,CAACM,KAAK,CAAC;QACrC,OAAOC,WAAW,IAAIJ,eAAe,IAAII,WAAW,IAAIH,aAAa;MACvE,CAAC,CAAC;MAEFd,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAC/CZ,SAAS,EAAEwB,eAAe;QAC1BtB,OAAO,EAAEuB,aAAa;QACtBjC,OAAO,EAAEsB,eAAe,CAACD,MAAM;QAC/BpB,QAAQ,EAAEsB,gBAAgB,CAACF;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,QAAQf,SAAS;QACf,KAAK,SAAS;UACZ0B,eAAe,GAAG/D,SAAS,CAAC6D,GAAG,EAAE,CAAC,CAAC;UACnC;QACF,KAAK,SAAS;UACZE,eAAe,GAAG/D,SAAS,CAAC6D,GAAG,EAAE,CAAC,CAAC;UACnC;QACF,KAAK,OAAO;UACVE,eAAe,GAAG/D,SAAS,CAAC6D,GAAG,EAAE,EAAE,CAAC;UACpC;QACF,KAAK,QAAQ;UACXE,eAAe,GAAG/D,SAAS,CAAC6D,GAAG,EAAE,EAAE,CAAC;UACpC;QACF;UACEE,eAAe,GAAG,IAAID,IAAI,CAAC,CAAC,CAAC;QAAE;MACnC;MAEA,IAAIzB,SAAS,KAAK,KAAK,EAAE;QACvBgB,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAI,IAAII,IAAI,CAACJ,CAAC,CAACQ,KAAK,CAAC,IAAIH,eAAe,CAAC;QACnFT,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAI,IAAIE,IAAI,CAACF,CAAC,CAACM,KAAK,CAAC,IAAIH,eAAe,CAAC;QACrFb,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEd,SAAS,EAAE;UACnDN,OAAO,EAAEsB,eAAe,CAACD,MAAM;UAC/BpB,QAAQ,EAAEsB,gBAAgB,CAACF;QAC7B,CAAC,CAAC;MACJ;IACF;;IAEA;IACAgB,kBAAkB,CAACf,eAAe,EAAEC,gBAAgB,CAAC;EACvD,CAAC;EAED,MAAMc,kBAAkB,GAAGA,CAACrC,OAAO,EAAEC,QAAQ,KAAK;IAChD;IACA,MAAMqC,kBAAkB,GAAG,CAAC,CAAC;IAC7B,MAAMC,aAAa,GAAG,CAAC,GAAGvC,OAAO,CAAC,CAACwC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIX,IAAI,CAACU,CAAC,CAACN,KAAK,CAAC,GAAG,IAAIJ,IAAI,CAACW,CAAC,CAACP,KAAK,CAAC,CAAC;IAExF,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,aAAa,CAAClB,MAAM,EAAEsB,CAAC,EAAE,EAAE;MAC7C,MAAMC,OAAO,GAAGL,aAAa,CAACI,CAAC,CAAC;MAChC,MAAME,QAAQ,GAAGN,aAAa,CAACI,CAAC,GAAG,CAAC,CAAC;MAErC,IAAIC,OAAO,CAAChB,WAAW,KAAKiB,QAAQ,CAACjB,WAAW,EAAE;QAChD,MAAMkB,MAAM,GAAGF,OAAO,CAACG,mBAAmB,GAAGF,QAAQ,CAACE,mBAAmB;QACzE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,MAAM;QAE7B,IAAIH,MAAM,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAMlC,WAAW,GAAIkC,MAAM,GAAGF,MAAM,GAAI,GAAG,CAAC,CAAC;UAC7C,MAAMI,KAAK,GAAGlF,MAAM,CAAC,IAAI+D,IAAI,CAACa,OAAO,CAACT,KAAK,CAAC,EAAE,SAAS,CAAC;UAExD,IAAI,CAACG,kBAAkB,CAACY,KAAK,CAAC,EAAE;YAC9BZ,kBAAkB,CAACY,KAAK,CAAC,GAAG;cAAEC,KAAK,EAAE,CAAC;cAAEC,KAAK,EAAE,CAAC;cAAEF;YAAM,CAAC;UAC3D;UACAZ,kBAAkB,CAACY,KAAK,CAAC,CAACC,KAAK,IAAIrC,WAAW;UAC9CwB,kBAAkB,CAACY,KAAK,CAAC,CAACE,KAAK,IAAI,CAAC;QACtC;MACF;IACF;IAEA,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAACjB,kBAAkB,CAAC,CACtDkB,GAAG,CAACC,IAAI,KAAK;MACZP,KAAK,EAAElF,MAAM,CAAC,IAAI+D,IAAI,CAAC0B,IAAI,CAACP,KAAK,GAAG,KAAK,CAAC,EAAE,UAAU,EAAE;QAAEQ,MAAM,EAAEtF;MAAG,CAAC,CAAC;MACvE0C,WAAW,EAAE,CAAC2C,IAAI,CAACN,KAAK,GAAGM,IAAI,CAACL,KAAK,EAAEO,OAAO,CAAC,CAAC,CAAC;MACjDC,IAAI,EAAEH,IAAI,CAACP;IACb,CAAC,CAAC,CAAC,CACFV,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACmB,IAAI,CAACC,aAAa,CAACnB,CAAC,CAACkB,IAAI,CAAC,CAAC;;IAE/C;IACA,MAAME,YAAY,GAAG,CAAC,CAAC;IAEvB,CAAC,GAAG9D,OAAO,EAAE,GAAGC,QAAQ,CAAC,CAAC8D,OAAO,CAACN,IAAI,IAAI;MACxC,MAAMP,KAAK,GAAGlF,MAAM,CAAC,IAAI+D,IAAI,CAAC0B,IAAI,CAACtB,KAAK,CAAC,EAAE,SAAS,CAAC;MACrD,MAAM6B,IAAI,GAAGP,IAAI,CAACQ,WAAW,IAAIR,IAAI,CAACS,KAAK,IAAI,CAAC;MAChD,MAAMC,IAAI,GAAGV,IAAI,CAACR,MAAM,GAAG,aAAa,GAAG,QAAQ;MAEnD,IAAI,CAACa,YAAY,CAACZ,KAAK,CAAC,EAAE;QACxBY,YAAY,CAACZ,KAAK,CAAC,GAAG;UAAEA,KAAK;UAAEkB,WAAW,EAAE,CAAC;UAAEC,MAAM,EAAE,CAAC;UAAET,IAAI,EAAEV;QAAM,CAAC;MACzE;MACAY,YAAY,CAACZ,KAAK,CAAC,CAACiB,IAAI,CAAC,IAAIH,IAAI;IACnC,CAAC,CAAC;IAEF,MAAMM,SAAS,GAAGhB,MAAM,CAACC,MAAM,CAACO,YAAY,CAAC,CAC1CN,GAAG,CAACC,IAAI,KAAK;MACZ,GAAGA,IAAI;MACPP,KAAK,EAAElF,MAAM,CAAC,IAAI+D,IAAI,CAAC0B,IAAI,CAACP,KAAK,GAAG,KAAK,CAAC,EAAE,UAAU,EAAE;QAAEQ,MAAM,EAAEtF;MAAG,CAAC,CAAC;MACvEmG,KAAK,EAAEd,IAAI,CAACW,WAAW,GAAGX,IAAI,CAACY;IACjC,CAAC,CAAC,CAAC,CACF7B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACmB,IAAI,CAACC,aAAa,CAACnB,CAAC,CAACkB,IAAI,CAAC,CAAC;;IAE/C;IACA,MAAM5C,cAAc,GAAG,CAAC,CAAC;IACzBf,QAAQ,CAAC8D,OAAO,CAACS,OAAO,IAAI;MAC1B,MAAML,IAAI,GAAGK,OAAO,CAACC,UAAU,IAAI,OAAO;MAC1CzD,cAAc,CAACmD,IAAI,CAAC,GAAG,CAACnD,cAAc,CAACmD,IAAI,CAAC,IAAI,CAAC,KAAKK,OAAO,CAACN,KAAK,IAAI,CAAC,CAAC;IAC3E,CAAC,CAAC;IAEF,MAAMQ,YAAY,GAAGpB,MAAM,CAACqB,OAAO,CAAC3D,cAAc,CAAC,CAChDwC,GAAG,CAAC,CAAC,CAACoB,IAAI,EAAEhG,KAAK,CAAC,MAAM;MAAEgG,IAAI;MAAEhG,KAAK,EAAEiG,UAAU,CAACjG,KAAK,CAAC+E,OAAO,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC,CACvEnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC9D,KAAK,GAAG6D,CAAC,CAAC7D,KAAK,CAAC;;IAEpC;IACA,MAAMqC,aAAa,GAAGqD,SAAS,CAACd,GAAG,CAACC,IAAI;MAAA,IAAAqB,qBAAA;MAAA,OAAK;QAC3C5B,KAAK,EAAEO,IAAI,CAACP,KAAK;QACjBU,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfmB,SAAS,EAAEtB,IAAI,CAACc,KAAK;QACrBS,QAAQ,EAAEvB,IAAI,CAACW,WAAW;QAC1Ba,WAAW,EAAExB,IAAI,CAACY,MAAM;QACxBvD,WAAW,EAAE,EAAAgE,qBAAA,GAAAzB,eAAe,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,KAAKH,IAAI,CAACG,IAAI,CAAC,cAAAkB,qBAAA,uBAA/CA,qBAAA,CAAiDhE,WAAW,KAAI;MAC/E,CAAC;IAAA,CAAC,CAAC;IAEHD,YAAY,CAAC;MACXC,WAAW,EAAEuC,eAAe;MAC5BtC,KAAK,EAAEuD,SAAS;MAChBtD,cAAc,EAAE0D,YAAY;MAC5BzD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmE,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACzH,MAAM,CAACqH,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIpE,eAAe,GAAG,CAAC,GAAGtB,OAAO,CAAC;IAClC,IAAIuB,gBAAgB,GAAG,CAAC,GAAGtB,QAAQ,CAAC;IAEpC,IAAIG,eAAe,KAAK,KAAK,EAAE;MAC7B,MAAMoB,SAAS,GAAGC,QAAQ,CAACrB,eAAe,CAAC;MAC3CkB,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,SAAS,CAAC;MAC1ED,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACD,WAAW,KAAKJ,SAAS,CAAC;IAC9E;IAEA,IAAIlB,SAAS,KAAK,KAAK,EAAE;MACvB,MAAMwB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtB,IAAIvB,SAAS;MACb,QAAQF,SAAS;QACf,KAAK,SAAS;UAAEE,SAAS,GAAGvC,SAAS,CAAC6D,GAAG,EAAE,CAAC,CAAC;UAAE;QAC/C,KAAK,SAAS;UAAEtB,SAAS,GAAGvC,SAAS,CAAC6D,GAAG,EAAE,CAAC,CAAC;UAAE;QAC/C,KAAK,OAAO;UAAEtB,SAAS,GAAGvC,SAAS,CAAC6D,GAAG,EAAE,EAAE,CAAC;UAAE;QAC9C,KAAK,QAAQ;UAAEtB,SAAS,GAAGvC,SAAS,CAAC6D,GAAG,EAAE,EAAE,CAAC;UAAE;QAC/C;UAAStB,SAAS,GAAG,IAAIuB,IAAI,CAAC,CAAC,CAAC;MAClC;MAEA,IAAIzB,SAAS,KAAK,KAAK,EAAE;QACvBgB,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAI,IAAII,IAAI,CAACJ,CAAC,CAACQ,KAAK,CAAC,IAAI3B,SAAS,CAAC;QAC7Ee,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAI,IAAIE,IAAI,CAACF,CAAC,CAACM,KAAK,CAAC,IAAI3B,SAAS,CAAC;MACjF;IACF;IAEA,OAAO;MAAEc,eAAe;MAAEC;IAAiB,CAAC;EAC9C,CAAC;EAED,MAAM;IAAED,eAAe;IAAEC;EAAiB,CAAC,GAAGmE,eAAe,CAAC,CAAC;;EAE/D;EACA,MAAMC,YAAY,GAAGrE,eAAe,CAACD,MAAM;EAC3C,MAAMuE,aAAa,GAAGrE,gBAAgB,CAACF,MAAM;EAC7C,MAAMwE,aAAa,GAAGvE,eAAe,CAACwE,MAAM,CAAC,CAACC,GAAG,EAAEpE,CAAC,KAAKoE,GAAG,IAAIpE,CAAC,CAACsC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACvF,MAAM+B,gBAAgB,GAAGzE,gBAAgB,CAACuE,MAAM,CAAC,CAACC,GAAG,EAAElE,CAAC,KAAKkE,GAAG,IAAIlE,CAAC,CAACqC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF,MAAMa,SAAS,GAAGc,aAAa,GAAGG,gBAAgB;EAClD,MAAMC,WAAW,GAAG3E,eAAe,CAACwE,MAAM,CAAC,CAACC,GAAG,EAAEpE,CAAC,KAAKoE,GAAG,IAAIpE,CAAC,CAACsB,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEhF;EACA,IAAIiD,cAAc,GAAG,CAAC;EACtB,IAAI5E,eAAe,CAACD,MAAM,IAAI,CAAC,EAAE;IAC/B,MAAMkB,aAAa,GAAG,CAAC,GAAGjB,eAAe,CAAC,CAACkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIX,IAAI,CAACU,CAAC,CAACN,KAAK,CAAC,GAAG,IAAIJ,IAAI,CAACW,CAAC,CAACP,KAAK,CAAC,CAAC;IAChG,MAAMgE,OAAO,GAAG5D,aAAa,CAACA,aAAa,CAAClB,MAAM,GAAG,CAAC,CAAC,CAAC0B,mBAAmB,GAC5DR,aAAa,CAAC,CAAC,CAAC,CAACQ,mBAAmB;IACnD,IAAIoD,OAAO,GAAG,CAAC,EAAE;MACfD,cAAc,GAAID,WAAW,GAAGE,OAAO,GAAG,GAAI;IAChD;EACF;EAEA,MAAMC,YAAY,GAAGH,WAAW,GAAG,CAAC,GAAGJ,aAAa,GAAGI,WAAW,GAAG,CAAC;EAEtE,oBACEzH,OAAA,CAACtC,GAAG;IAAA8C,QAAA,gBACFR,OAAA,CAACrC,UAAU;MAAC+C,OAAO,EAAC,IAAI;MAACK,SAAS,EAAC,IAAI;MAAC8G,UAAU,EAAC,MAAM;MAACC,EAAE,EAAE,CAAE;MAAAtH,QAAA,EAAC;IAEjE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbd,OAAA,CAACpC,IAAI;MAACmK,EAAE,EAAE;QAAED,EAAE,EAAE;MAAE,CAAE;MAAAtH,QAAA,eAClBR,OAAA,CAACnC,WAAW;QAAA2C,QAAA,eACVR,OAAA,CAAClC,IAAI;UAACkK,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAA1H,QAAA,gBAC7CR,OAAA,CAAClC,IAAI;YAACmH,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7H,QAAA,eAC9BR,OAAA,CAACjC,WAAW;cAACuK,SAAS;cAAA9H,QAAA,gBACpBR,OAAA,CAAChC,UAAU;gBAAAwC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCd,OAAA,CAAC/B,MAAM;gBACLmC,KAAK,EAAEwB,eAAgB;gBACvB2G,QAAQ,EAAGlF,CAAC,IAAKxB,kBAAkB,CAACwB,CAAC,CAACmF,MAAM,CAACpI,KAAK,CAAE;gBACpDa,KAAK,EAAC,aAAU;gBAAAT,QAAA,gBAEhBR,OAAA,CAAC9B,QAAQ;kBAACkC,KAAK,EAAC,KAAK;kBAAAI,QAAA,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EACnDS,QAAQ,CAACyD,GAAG,CAAEyD,OAAO,iBACpBzI,OAAA,CAAC9B,QAAQ;kBAAkBkC,KAAK,EAAEqI,OAAO,CAACC,EAAG;kBAAAlI,QAAA,EAC1CiI,OAAO,CAACE;gBAAM,GADFF,OAAO,CAACC,EAAE;kBAAA/H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPd,OAAA,CAAClC,IAAI;YAACmH,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7H,QAAA,eAC9BR,OAAA,CAACjC,WAAW;cAACuK,SAAS;cAAA9H,QAAA,gBACpBR,OAAA,CAAChC,UAAU;gBAAAwC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChCd,OAAA,CAAC/B,MAAM;gBACLmC,KAAK,EAAE0B,SAAU;gBACjByG,QAAQ,EAAGlF,CAAC,IAAKtB,YAAY,CAACsB,CAAC,CAACmF,MAAM,CAACpI,KAAK,CAAE;gBAC9Ca,KAAK,EAAC,YAAS;gBAAAT,QAAA,gBAEfR,OAAA,CAAC9B,QAAQ;kBAACkC,KAAK,EAAC,SAAS;kBAAAI,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDd,OAAA,CAAC9B,QAAQ;kBAACkC,KAAK,EAAC,SAAS;kBAAAI,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDd,OAAA,CAAC9B,QAAQ;kBAACkC,KAAK,EAAC,OAAO;kBAAAI,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC7Cd,OAAA,CAAC9B,QAAQ;kBAACkC,KAAK,EAAC,QAAQ;kBAAAI,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClDd,OAAA,CAAC9B,QAAQ;kBAACkC,KAAK,EAAC,KAAK;kBAAAI,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPd,OAAA,CAAClC,IAAI;MAACkK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACH,EAAE,EAAE,CAAE;MAAAtH,QAAA,gBAChCR,OAAA,CAAClC,IAAI;QAACmH,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7H,QAAA,eAC9BR,OAAA,CAACE,UAAU;UACTC,KAAK,EAAC,kBAAkB;UACxBC,KAAK,EAAE,GAAGsH,cAAc,CAACvC,OAAO,CAAC,CAAC,CAAC,IAAK;UACxC9E,QAAQ,EAAC,YAAY;UACrBC,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPd,OAAA,CAAClC,IAAI;QAACmH,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7H,QAAA,eAC9BR,OAAA,CAACE,UAAU;UACTC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEwG,cAAc,CAACgB,YAAY,CAAE;UACpCvH,QAAQ,EAAC,WAAW;UACpBC,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPd,OAAA,CAAClC,IAAI;QAACmH,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7H,QAAA,eAC9BR,OAAA,CAACE,UAAU;UACTC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAEwG,cAAc,CAACL,SAAS,CAAE;UACjClG,QAAQ,EAAE,GAAGuG,cAAc,CAACS,aAAa,CAAC,kBAAkBT,cAAc,CAACY,gBAAgB,CAAC,SAAU;UACtGlH,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPd,OAAA,CAAClC,IAAI;QAACmH,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7H,QAAA,eAC9BR,OAAA,CAACE,UAAU;UACTC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAE,GAAGqH,WAAW,CAACtC,OAAO,CAAC,CAAC,CAAC,IAAK;UACrC9E,QAAQ,EAAE,MAAM8G,YAAY,aAAc;UAC1C7G,KAAK,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPd,OAAA,CAAClC,IAAI;MAACkK,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAzH,QAAA,gBAEzBR,OAAA,CAAClC,IAAI;QAACmH,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAApI,QAAA,eACvBR,OAAA,CAACpC,IAAI;UAAA4C,QAAA,eACHR,OAAA,CAACnC,WAAW;YAAA2C,QAAA,gBACVR,OAAA,CAACrC,UAAU;cAAC+C,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbd,OAAA,CAACtC,GAAG;cAACmL,MAAM,EAAE,GAAI;cAAArI,QAAA,eACfR,OAAA,CAACV,mBAAmB;gBAACwJ,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAArI,QAAA,eAC7CR,OAAA,CAACzB,SAAS;kBAACwK,IAAI,EAAE3G,SAAS,CAACE,WAAY;kBAAA9B,QAAA,gBACrCR,OAAA,CAACd,aAAa;oBAAC8J,eAAe,EAAC;kBAAK;oBAAArI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCd,OAAA,CAAChB,KAAK;oBAACiK,OAAO,EAAC;kBAAO;oBAAAtI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBd,OAAA,CAACf,KAAK;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTd,OAAA,CAACZ,YAAY;oBACX8J,SAAS,EAAG9I,KAAK,IAAK,CAAC,GAAGA,KAAK,UAAU,EAAE,SAAS,CAAE;oBACtD+I,UAAU,EAAE;sBAAE7I,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFd,OAAA,CAACX,MAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVd,OAAA,CAACxB,IAAI;oBACHmH,IAAI,EAAC,UAAU;oBACfsD,OAAO,EAAC,aAAa;oBACrBG,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfjD,IAAI,EAAC;kBAAS;oBAAAzF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPd,OAAA,CAAClC,IAAI;QAACmH,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAApI,QAAA,eACvBR,OAAA,CAACpC,IAAI;UAAA4C,QAAA,eACHR,OAAA,CAACnC,WAAW;YAAA2C,QAAA,gBACVR,OAAA,CAACrC,UAAU;cAAC+C,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbd,OAAA,CAACtC,GAAG;cAACmL,MAAM,EAAE,GAAI;cAAArI,QAAA,eACfR,OAAA,CAACV,mBAAmB;gBAACwJ,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAArI,QAAA,eAC7CR,OAAA,CAACvB,SAAS;kBAACsK,IAAI,EAAE3G,SAAS,CAACG,KAAM;kBAAA/B,QAAA,gBAC/BR,OAAA,CAACd,aAAa;oBAAC8J,eAAe,EAAC;kBAAK;oBAAArI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCd,OAAA,CAAChB,KAAK;oBAACiK,OAAO,EAAC;kBAAO;oBAAAtI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBd,OAAA,CAACf,KAAK;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTd,OAAA,CAACZ,YAAY;oBACX8J,SAAS,EAAG9I,KAAK,IAAK,CAACwG,cAAc,CAACxG,KAAK,CAAC,CAAE;oBAC9C+I,UAAU,EAAE;sBAAE7I,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFd,OAAA,CAACX,MAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVd,OAAA,CAACtB,IAAI;oBACHiH,IAAI,EAAC,UAAU;oBACfsD,OAAO,EAAC,aAAa;oBACrBK,OAAO,EAAC,GAAG;oBACXF,MAAM,EAAC,SAAS;oBAChBG,IAAI,EAAC;kBAAS;oBAAA5I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFd,OAAA,CAACtB,IAAI;oBACHiH,IAAI,EAAC,UAAU;oBACfsD,OAAO,EAAC,QAAQ;oBAChBK,OAAO,EAAC,GAAG;oBACXF,MAAM,EAAC,SAAS;oBAChBG,IAAI,EAAC;kBAAS;oBAAA5I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPd,OAAA,CAAClC,IAAI;QAACmH,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAApI,QAAA,eACvBR,OAAA,CAACpC,IAAI;UAAA4C,QAAA,eACHR,OAAA,CAACnC,WAAW;YAAA2C,QAAA,gBACVR,OAAA,CAACrC,UAAU;cAAC+C,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbd,OAAA,CAACtC,GAAG;cAACmL,MAAM,EAAE,GAAI;cAAArI,QAAA,eACfR,OAAA,CAACV,mBAAmB;gBAACwJ,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAArI,QAAA,eAC7CR,OAAA,CAACnB,QAAQ;kBAAA2B,QAAA,gBACPR,OAAA,CAAClB,GAAG;oBACFiK,IAAI,EAAE3G,SAAS,CAACI,cAAe;oBAC/BgH,EAAE,EAAC,KAAK;oBACRC,EAAE,EAAC,KAAK;oBACRC,SAAS,EAAE,KAAM;oBACjBzI,KAAK,EAAEA,CAAC;sBAAEmF,IAAI;sBAAEuD;oBAAQ,CAAC,KAAK,GAAGvD,IAAI,IAAI,CAACuD,OAAO,GAAG,GAAG,EAAExE,OAAO,CAAC,CAAC,CAAC,GAAI;oBACvEyE,WAAW,EAAE,EAAG;oBAChBL,IAAI,EAAC,SAAS;oBACdN,OAAO,EAAC,OAAO;oBAAAzI,QAAA,EAEd4B,SAAS,CAACI,cAAc,CAACwC,GAAG,CAAC,CAAC6E,KAAK,EAAEC,KAAK,kBACzC9J,OAAA,CAACjB,IAAI;sBAAuBwK,IAAI,EAAEtJ,MAAM,CAAC6J,KAAK,GAAG7J,MAAM,CAAC4C,MAAM;oBAAE,GAArD,QAAQiH,KAAK,EAAE;sBAAAnJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAwC,CACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNd,OAAA,CAACZ,YAAY;oBAAC8J,SAAS,EAAG9I,KAAK,IAAK,CAACwG,cAAc,CAACxG,KAAK,CAAC;kBAAE;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPd,OAAA,CAAClC,IAAI;QAACmH,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAApI,QAAA,eACvBR,OAAA,CAACpC,IAAI;UAAA4C,QAAA,eACHR,OAAA,CAACnC,WAAW;YAAA2C,QAAA,gBACVR,OAAA,CAACrC,UAAU;cAAC+C,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbd,OAAA,CAACtC,GAAG;cAACmL,MAAM,EAAE,GAAI;cAAArI,QAAA,eACfR,OAAA,CAACV,mBAAmB;gBAACwJ,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAArI,QAAA,eAC7CR,OAAA,CAACrB,QAAQ;kBAACoK,IAAI,EAAE3G,SAAS,CAACK,aAAc;kBAAAjC,QAAA,gBACtCR,OAAA,CAACd,aAAa;oBAAC8J,eAAe,EAAC;kBAAK;oBAAArI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCd,OAAA,CAAChB,KAAK;oBAACiK,OAAO,EAAC;kBAAO;oBAAAtI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBd,OAAA,CAACf,KAAK;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTd,OAAA,CAACZ,YAAY;oBACX8J,SAAS,EAAEA,CAAC9I,KAAK,EAAEgG,IAAI,KAAK;sBAC1B,IAAIA,IAAI,KAAK,aAAa,EAAE,OAAO,CAACQ,cAAc,CAACxG,KAAK,CAAC,EAAEgG,IAAI,CAAC;sBAChE,OAAO,CAAChG,KAAK,EAAEgG,IAAI,CAAC;oBACtB,CAAE;oBACF+C,UAAU,EAAE;sBAAE7I,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFd,OAAA,CAACX,MAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVd,OAAA,CAACpB,GAAG;oBAACqK,OAAO,EAAC,WAAW;oBAACM,IAAI,EAAC,SAAS;oBAACnD,IAAI,EAAC;kBAAiB;oBAAAzF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPd,OAAA,CAACpC,IAAI;MAACmK,EAAE,EAAE;QAAE/G,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eAClBR,OAAA,CAACnC,WAAW;QAAA2C,QAAA,gBACVR,OAAA,CAACrC,UAAU;UAAC+C,OAAO,EAAC,IAAI;UAACD,YAAY;UAAAD,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbd,OAAA,CAAClC,IAAI;UAACkK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzH,QAAA,gBACzBR,OAAA,CAAClC,IAAI;YAACmH,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7H,QAAA,eAC9BR,OAAA,CAACtC,GAAG;cAACqM,SAAS,EAAC,QAAQ;cAAAvJ,QAAA,gBACrBR,OAAA,CAACrC,UAAU;gBAAC+C,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,SAAS;gBAAAE,QAAA,EACrC2G;cAAY;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACbd,OAAA,CAACrC,UAAU;gBAAC+C,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPd,OAAA,CAAClC,IAAI;YAACmH,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7H,QAAA,eAC9BR,OAAA,CAACtC,GAAG;cAACqM,SAAS,EAAC,QAAQ;cAAAvJ,QAAA,gBACrBR,OAAA,CAACrC,UAAU;gBAAC+C,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,cAAc;gBAAAE,QAAA,EAC1C4G;cAAa;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbd,OAAA,CAACrC,UAAU;gBAAC+C,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPd,OAAA,CAAClC,IAAI;YAACmH,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7H,QAAA,eAC9BR,OAAA,CAACtC,GAAG;cAACqM,SAAS,EAAC,QAAQ;cAAAvJ,QAAA,gBACrBR,OAAA,CAACrC,UAAU;gBAAC+C,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,cAAc;gBAAAE,QAAA,EAC1CoG,cAAc,CAACL,SAAS,IAAIY,YAAY,GAAGC,aAAa,CAAC,IAAI,CAAC;cAAC;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACbd,OAAA,CAACrC,UAAU;gBAAC+C,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPd,OAAA,CAAClC,IAAI;YAACmH,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7H,QAAA,eAC9BR,OAAA,CAACtC,GAAG;cAACqM,SAAS,EAAC,QAAQ;cAAAvJ,QAAA,gBACrBR,OAAA,CAACrC,UAAU;gBAAC+C,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,WAAW;gBAAAE,QAAA,EACvCe,QAAQ,CAACsB;cAAM;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACbd,OAAA,CAACrC,UAAU;gBAAC+C,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPd,OAAA,CAACtC,GAAG;MAACsD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTR,OAAA,CAACF,iBAAiB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,EAGLS,QAAQ,CAACsB,MAAM,GAAG,CAAC,iBAClB7C,OAAA,CAACtC,GAAG;MAACsD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTR,OAAA,CAACH,iBAAiB;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACQ,EAAA,CAtgBID,UAAU;EAAA,QACsD9B,MAAM;AAAA;AAAAyK,GAAA,GADtE3I,UAAU;AAwgBhB,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAA4I,GAAA;AAAAC,YAAA,CAAA7I,EAAA;AAAA6I,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}