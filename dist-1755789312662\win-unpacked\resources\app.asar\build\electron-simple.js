const { app, <PERSON><PERSON>erWindow, <PERSON><PERSON>, dialog } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// Variables globales
let mainWindow;
let serverProcess;

// Detectar si estamos en desarrollo
const isDev = !app.isPackaged;

// Función para obtener la ruta de la aplicación (portable)
function getAppPath() {
  if (isDev) {
    return __dirname;
  }
  // En producción, usar la ruta donde está el ejecutable
  return path.dirname(process.execPath);
}

// Función para crear logs básicos
function setupLogging() {
  const appPath = getAppPath();
  const logPath = path.join(appPath, 'app.log');
  
  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;
  
  console.log = (...args) => {
    const timestamp = new Date().toISOString();
    const message = `[${timestamp}] LOG: ${args.join(' ')}\n`;
    try {
      fs.appendFileSync(logPath, message);
    } catch (e) {
      // Ignorar errores de escritura de logs
    }
    originalConsoleLog(...args);
  };
  
  console.error = (...args) => {
    const timestamp = new Date().toISOString();
    const message = `[${timestamp}] ERROR: ${args.join(' ')}\n`;
    try {
      fs.appendFileSync(logPath, message);
    } catch (e) {
      // Ignorar errores de escritura de logs
    }
    originalConsoleError(...args);
  };
}

// Función para iniciar el servidor backend
function startBackendServer() {
  const appPath = getAppPath();
  const serverPath = isDev 
    ? path.join(__dirname, '..', 'server', 'server.js')
    : path.join(process.resourcesPath, 'server', 'server.js');
  
  console.log('Iniciando servidor desde:', serverPath);
  console.log('Ruta de la aplicación:', appPath);
  
  serverProcess = spawn('node', [serverPath], {
    cwd: appPath,
    env: { 
      ...process.env, 
      APP_PATH: appPath,
      NODE_ENV: isDev ? 'development' : 'production'
    }
  });

  serverProcess.stdout.on('data', (data) => {
    console.log(`Servidor: ${data}`);
  });

  serverProcess.stderr.on('data', (data) => {
    console.error(`Error del servidor: ${data}`);
  });

  serverProcess.on('close', (code) => {
    console.log(`Servidor cerrado con código ${code}`);
  });
}

// Función para crear el menú básico
function createMenu() {
  const template = [
    {
      label: 'Archivo',
      submenu: [
        {
          label: 'Salir',
          accelerator: 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Ayuda',
      submenu: [
        {
          label: 'Acerca de',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Acerca de Repostajes Manager',
              message: `Repostajes Manager v1.0.0\n\nSistema de gestión de repostajes y gastos vehiculares.\n\nDesarrollado con Electron y React.`
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Función para crear la ventana principal
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'favicon.ico'),
    show: false,
    titleBarStyle: 'default'
  });

  // Cargar la aplicación
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Mostrar ventana cuando esté lista
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Abrir DevTools en desarrollo
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Inicialización de la aplicación
app.whenReady().then(() => {
  // Configurar logs
  setupLogging();
  console.log('Aplicación iniciando...');
  
  // Crear menú
  createMenu();
  
  // Iniciar el servidor backend
  startBackendServer();
  
  // Esperar un poco para que el servidor se inicie
  setTimeout(() => {
    createWindow();
  }, 2000);

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Salir cuando todas las ventanas estén cerradas
app.on('window-all-closed', () => {
  // Cerrar el servidor backend
  if (serverProcess) {
    serverProcess.kill();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  // Cerrar el servidor backend antes de salir
  if (serverProcess) {
    serverProcess.kill();
  }
});
