{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getSectionTypeGranularity = exports.getDefaultReferenceDate = exports.SECTION_TYPE_GRANULARITY = void 0;\nvar _timeUtils = require(\"./time-utils\");\nvar _dateUtils = require(\"./date-utils\");\nconst SECTION_TYPE_GRANULARITY = exports.SECTION_TYPE_GRANULARITY = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hours: 4,\n  minutes: 5,\n  seconds: 6,\n  milliseconds: 7\n};\nconst getSectionTypeGranularity = sections => Math.max(...sections.map(section => SECTION_TYPE_GRANULARITY[section.type] ?? 1));\nexports.getSectionTypeGranularity = getSectionTypeGranularity;\nconst roundDate = (adapter, granularity, date) => {\n  if (granularity === SECTION_TYPE_GRANULARITY.year) {\n    return adapter.startOfYear(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.month) {\n    return adapter.startOfMonth(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.day) {\n    return adapter.startOfDay(date);\n  }\n\n  // We don't have startOfHour / startOfMinute / startOfSecond\n  let roundedDate = date;\n  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {\n    roundedDate = adapter.setMinutes(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {\n    roundedDate = adapter.setSeconds(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {\n    roundedDate = adapter.setMilliseconds(roundedDate, 0);\n  }\n  return roundedDate;\n};\nconst getDefaultReferenceDate = ({\n  props,\n  adapter,\n  granularity,\n  timezone,\n  getTodayDate: inGetTodayDate\n}) => {\n  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(adapter, granularity, (0, _dateUtils.getTodayDate)(adapter, timezone));\n  if (props.minDate != null && adapter.isAfterDay(props.minDate, referenceDate)) {\n    referenceDate = roundDate(adapter, granularity, props.minDate);\n  }\n  if (props.maxDate != null && adapter.isBeforeDay(props.maxDate, referenceDate)) {\n    referenceDate = roundDate(adapter, granularity, props.maxDate);\n  }\n  const isAfter = (0, _timeUtils.createIsAfterIgnoreDatePart)(props.disableIgnoringDatePartForTimeValidation ?? false, adapter);\n  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {\n    referenceDate = roundDate(adapter, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : (0, _dateUtils.mergeDateAndTime)(adapter, referenceDate, props.minTime));\n  }\n  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {\n    referenceDate = roundDate(adapter, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : (0, _dateUtils.mergeDateAndTime)(adapter, referenceDate, props.maxTime));\n  }\n  return referenceDate;\n};\nexports.getDefaultReferenceDate = getDefaultReferenceDate;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getSectionTypeGranularity", "getDefaultReferenceDate", "SECTION_TYPE_GRANULARITY", "_timeUtils", "require", "_dateUtils", "year", "month", "day", "hours", "minutes", "seconds", "milliseconds", "sections", "Math", "max", "map", "section", "type", "roundDate", "adapter", "granularity", "date", "startOfYear", "startOfMonth", "startOfDay", "roundedDate", "setMinutes", "setSeconds", "setMilliseconds", "props", "timezone", "getTodayDate", "inGetTodayDate", "referenceDate", "minDate", "isAfterDay", "maxDate", "isBeforeDay", "isAfter", "createIsAfterIgnoreDatePart", "disableIgnoringDatePartForTimeValidation", "minTime", "mergeDateAndTime", "maxTime"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/utils/getDefaultReferenceDate.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getSectionTypeGranularity = exports.getDefaultReferenceDate = exports.SECTION_TYPE_GRANULARITY = void 0;\nvar _timeUtils = require(\"./time-utils\");\nvar _dateUtils = require(\"./date-utils\");\nconst SECTION_TYPE_GRANULARITY = exports.SECTION_TYPE_GRANULARITY = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hours: 4,\n  minutes: 5,\n  seconds: 6,\n  milliseconds: 7\n};\nconst getSectionTypeGranularity = sections => Math.max(...sections.map(section => SECTION_TYPE_GRANULARITY[section.type] ?? 1));\nexports.getSectionTypeGranularity = getSectionTypeGranularity;\nconst roundDate = (adapter, granularity, date) => {\n  if (granularity === SECTION_TYPE_GRANULARITY.year) {\n    return adapter.startOfYear(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.month) {\n    return adapter.startOfMonth(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.day) {\n    return adapter.startOfDay(date);\n  }\n\n  // We don't have startOfHour / startOfMinute / startOfSecond\n  let roundedDate = date;\n  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {\n    roundedDate = adapter.setMinutes(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {\n    roundedDate = adapter.setSeconds(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {\n    roundedDate = adapter.setMilliseconds(roundedDate, 0);\n  }\n  return roundedDate;\n};\nconst getDefaultReferenceDate = ({\n  props,\n  adapter,\n  granularity,\n  timezone,\n  getTodayDate: inGetTodayDate\n}) => {\n  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(adapter, granularity, (0, _dateUtils.getTodayDate)(adapter, timezone));\n  if (props.minDate != null && adapter.isAfterDay(props.minDate, referenceDate)) {\n    referenceDate = roundDate(adapter, granularity, props.minDate);\n  }\n  if (props.maxDate != null && adapter.isBeforeDay(props.maxDate, referenceDate)) {\n    referenceDate = roundDate(adapter, granularity, props.maxDate);\n  }\n  const isAfter = (0, _timeUtils.createIsAfterIgnoreDatePart)(props.disableIgnoringDatePartForTimeValidation ?? false, adapter);\n  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {\n    referenceDate = roundDate(adapter, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : (0, _dateUtils.mergeDateAndTime)(adapter, referenceDate, props.minTime));\n  }\n  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {\n    referenceDate = roundDate(adapter, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : (0, _dateUtils.mergeDateAndTime)(adapter, referenceDate, props.maxTime));\n  }\n  return referenceDate;\n};\nexports.getDefaultReferenceDate = getDefaultReferenceDate;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,yBAAyB,GAAGF,OAAO,CAACG,uBAAuB,GAAGH,OAAO,CAACI,wBAAwB,GAAG,KAAK,CAAC;AAC/G,IAAIC,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;AACxC,IAAIC,UAAU,GAAGD,OAAO,CAAC,cAAc,CAAC;AACxC,MAAMF,wBAAwB,GAAGJ,OAAO,CAACI,wBAAwB,GAAG;EAClEI,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,YAAY,EAAE;AAChB,CAAC;AACD,MAAMZ,yBAAyB,GAAGa,QAAQ,IAAIC,IAAI,CAACC,GAAG,CAAC,GAAGF,QAAQ,CAACG,GAAG,CAACC,OAAO,IAAIf,wBAAwB,CAACe,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/HpB,OAAO,CAACE,yBAAyB,GAAGA,yBAAyB;AAC7D,MAAMmB,SAAS,GAAGA,CAACC,OAAO,EAAEC,WAAW,EAAEC,IAAI,KAAK;EAChD,IAAID,WAAW,KAAKnB,wBAAwB,CAACI,IAAI,EAAE;IACjD,OAAOc,OAAO,CAACG,WAAW,CAACD,IAAI,CAAC;EAClC;EACA,IAAID,WAAW,KAAKnB,wBAAwB,CAACK,KAAK,EAAE;IAClD,OAAOa,OAAO,CAACI,YAAY,CAACF,IAAI,CAAC;EACnC;EACA,IAAID,WAAW,KAAKnB,wBAAwB,CAACM,GAAG,EAAE;IAChD,OAAOY,OAAO,CAACK,UAAU,CAACH,IAAI,CAAC;EACjC;;EAEA;EACA,IAAII,WAAW,GAAGJ,IAAI;EACtB,IAAID,WAAW,GAAGnB,wBAAwB,CAACQ,OAAO,EAAE;IAClDgB,WAAW,GAAGN,OAAO,CAACO,UAAU,CAACD,WAAW,EAAE,CAAC,CAAC;EAClD;EACA,IAAIL,WAAW,GAAGnB,wBAAwB,CAACS,OAAO,EAAE;IAClDe,WAAW,GAAGN,OAAO,CAACQ,UAAU,CAACF,WAAW,EAAE,CAAC,CAAC;EAClD;EACA,IAAIL,WAAW,GAAGnB,wBAAwB,CAACU,YAAY,EAAE;IACvDc,WAAW,GAAGN,OAAO,CAACS,eAAe,CAACH,WAAW,EAAE,CAAC,CAAC;EACvD;EACA,OAAOA,WAAW;AACpB,CAAC;AACD,MAAMzB,uBAAuB,GAAGA,CAAC;EAC/B6B,KAAK;EACLV,OAAO;EACPC,WAAW;EACXU,QAAQ;EACRC,YAAY,EAAEC;AAChB,CAAC,KAAK;EACJ,IAAIC,aAAa,GAAGD,cAAc,GAAGA,cAAc,CAAC,CAAC,GAAGd,SAAS,CAACC,OAAO,EAAEC,WAAW,EAAE,CAAC,CAAC,EAAEhB,UAAU,CAAC2B,YAAY,EAAEZ,OAAO,EAAEW,QAAQ,CAAC,CAAC;EACxI,IAAID,KAAK,CAACK,OAAO,IAAI,IAAI,IAAIf,OAAO,CAACgB,UAAU,CAACN,KAAK,CAACK,OAAO,EAAED,aAAa,CAAC,EAAE;IAC7EA,aAAa,GAAGf,SAAS,CAACC,OAAO,EAAEC,WAAW,EAAES,KAAK,CAACK,OAAO,CAAC;EAChE;EACA,IAAIL,KAAK,CAACO,OAAO,IAAI,IAAI,IAAIjB,OAAO,CAACkB,WAAW,CAACR,KAAK,CAACO,OAAO,EAAEH,aAAa,CAAC,EAAE;IAC9EA,aAAa,GAAGf,SAAS,CAACC,OAAO,EAAEC,WAAW,EAAES,KAAK,CAACO,OAAO,CAAC;EAChE;EACA,MAAME,OAAO,GAAG,CAAC,CAAC,EAAEpC,UAAU,CAACqC,2BAA2B,EAAEV,KAAK,CAACW,wCAAwC,IAAI,KAAK,EAAErB,OAAO,CAAC;EAC7H,IAAIU,KAAK,CAACY,OAAO,IAAI,IAAI,IAAIH,OAAO,CAACT,KAAK,CAACY,OAAO,EAAER,aAAa,CAAC,EAAE;IAClEA,aAAa,GAAGf,SAAS,CAACC,OAAO,EAAEC,WAAW,EAAES,KAAK,CAACW,wCAAwC,GAAGX,KAAK,CAACY,OAAO,GAAG,CAAC,CAAC,EAAErC,UAAU,CAACsC,gBAAgB,EAAEvB,OAAO,EAAEc,aAAa,EAAEJ,KAAK,CAACY,OAAO,CAAC,CAAC;EAC3L;EACA,IAAIZ,KAAK,CAACc,OAAO,IAAI,IAAI,IAAIL,OAAO,CAACL,aAAa,EAAEJ,KAAK,CAACc,OAAO,CAAC,EAAE;IAClEV,aAAa,GAAGf,SAAS,CAACC,OAAO,EAAEC,WAAW,EAAES,KAAK,CAACW,wCAAwC,GAAGX,KAAK,CAACc,OAAO,GAAG,CAAC,CAAC,EAAEvC,UAAU,CAACsC,gBAAgB,EAAEvB,OAAO,EAAEc,aAAa,EAAEJ,KAAK,CAACc,OAAO,CAAC,CAAC;EAC3L;EACA,OAAOV,aAAa;AACtB,CAAC;AACDpC,OAAO,CAACG,uBAAuB,GAAGA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}