import { DesktopDateTimePickerProps, DesktopDateTimePickerSlots, DesktopDateTimePickerSlotProps } from "../DesktopDateTimePicker/index.js";
import { BaseSingleInputFieldProps } from "../internals/models/index.js";
import { MobileDateTimePickerProps, MobileDateTimePickerSlots, MobileDateTimePickerSlotProps } from "../MobileDateTimePicker/index.js";
import { ValidateDateTimeProps } from "../validation/index.js";
import { ExportedYearCalendarProps } from "../YearCalendar/YearCalendar.types.js";
export interface DateTimePickerSlots extends DesktopDateTimePickerSlots, MobileDateTimePickerSlots {}
export interface DateTimePickerSlotProps<TEnableAccessibleFieldDOMStructure extends boolean> extends DesktopDateTimePickerSlotProps<TEnableAccessibleFieldDOMStructure>, MobileDateTimePickerSlotProps<TEnableAccessibleFieldDOMStructure> {}
export interface DateTimePickerProps<TEnableAccessibleFieldDOMStructure extends boolean = true> extends DesktopDateTimePickerProps<TEnableAccessibleFieldDOMStructure>, ExportedYearCalendarProps, Omit<MobileDateTimePickerProps<TEnableAccessibleFieldDOMStructure>, 'views'> {
  /**
   * CSS media query when `Mobile` mode will be changed to `Desktop`.
   * @default '@media (pointer: fine)'
   * @example '@media (min-width: 720px)' or theme.breakpoints.up("sm")
   */
  desktopModeMediaQuery?: string;
  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: DateTimePickerSlots;
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: DateTimePickerSlotProps<TEnableAccessibleFieldDOMStructure>;
  /**
   * Years rendered per row.
   * @default 4 on desktop, 3 on mobile
   */
  yearsPerRow?: 3 | 4;
}
/**
 * Props the field can receive when used inside a Date Time Picker (<DateTimePicker />, <DesktopDateTimePicker /> or <MobileDateTimePicker /> component).
 */
export type DateTimePickerFieldProps = ValidateDateTimeProps & BaseSingleInputFieldProps;