# 🚗 Repostajes Manager - Aplicación Portable

## 📋 Descripción
Sistema de gestión de repostajes y gastos vehiculares completamente portable para Windows.

## 🎯 Características de Portabilidad
- ✅ **Sin instalación**: Ejecutable único (.exe)
- ✅ **Base de datos portable**: SQLite se crea en la misma carpeta del ejecutable
- ✅ **Completamente autónomo**: No requiere instalaciones adicionales
- ✅ **Multiplataforma**: Funciona desde cualquier ubicación (C:\, D:\, USB, etc.)

## 🛠️ Construcción de la Aplicación Portable

### Prerrequisitos
- Node.js 16 o superior
- npm

### Pasos para construir
1. **Instalar dependencias**:
   ```bash
   npm install
   ```

2. **Construir aplicación portable**:
   ```bash
   npm run build-portable
   ```

3. **Ubicar el ejecutable**:
   - El archivo se genera en: `dist/Repostajes Manager-1.0.0-portable.exe`

## 🚀 Uso de la Aplicación Portable

### Instalación
1. Copia el archivo `.exe` a cualquier carpeta
2. Ejecuta el archivo
3. La base de datos `repostaje.db` se creará automáticamente en la misma carpeta

### Ejemplos de uso
- **En C: raíz**: `C:\Repostajes Manager.exe` → Base de datos en `C:\repostaje.db`
- **En carpeta específica**: `D:\MisApps\Repostajes Manager.exe` → Base de datos en `D:\MisApps\repostaje.db`
- **En USB**: `E:\Portable\Repostajes Manager.exe` → Base de datos en `E:\Portable\repostaje.db`

## 📁 Estructura de Archivos
```
📁 Carpeta de ejecución/
├── 📄 Repostajes Manager.exe    # Aplicación principal
├── 📄 repostaje.db             # Base de datos (se crea automáticamente)
└── 📁 resources/               # Recursos internos (creado automáticamente)
```

## 🔧 Desarrollo

### Modo desarrollo
```bash
# Ejecutar en modo desarrollo
npm run electron-dev
```

### Scripts disponibles
- `npm start` - Ejecutar React en desarrollo
- `npm run electron` - Ejecutar Electron
- `npm run electron-dev` - Desarrollo completo (React + Electron)
- `npm run build` - Construir React para producción
- `npm run build-electron` - Construir Electron
- `npm run build-portable` - Construir aplicación portable completa

## 📊 Base de Datos
- **Tipo**: SQLite
- **Ubicación**: Misma carpeta que el ejecutable
- **Nombre**: `repostaje.db`
- **Creación**: Automática al primer uso

### Tablas creadas automáticamente
- `vehiculos` - Información de vehículos
- `repostajes` - Registros de repostajes
- `gastos` - Gastos y mantenimientos

## 🔒 Seguridad
- Base de datos local (no se envía información a internet)
- Datos completamente privados
- Sin telemetría ni tracking

## 📝 Notas Importantes
- La aplicación debe tener permisos de escritura en la carpeta donde se ejecuta
- En Windows, puede aparecer un aviso de SmartScreen la primera vez
- Para usar en USB, asegúrate de que no esté protegida contra escritura

## 🐛 Solución de Problemas

### La aplicación no inicia
- Verifica que tienes permisos de escritura en la carpeta
- Ejecuta como administrador si es necesario

### No se guardan los datos
- Verifica que el archivo `repostaje.db` se puede crear/modificar
- Comprueba los permisos de la carpeta

### Error de base de datos
- Elimina el archivo `repostaje.db` para recrear la base de datos
- Reinicia la aplicación

## 📞 Soporte
Para reportar problemas o sugerencias, contacta al desarrollador.
