{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useApplyDefaultValuesToTimeValidationProps = useApplyDefaultValuesToTimeValidationProps;\nexports.useTimeManager = useTimeManager;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _valueManagers = require(\"../internals/utils/valueManagers\");\nvar _validation = require(\"../validation\");\nvar _hooks = require(\"../hooks\");\nfunction useTimeManager(parameters = {}) {\n  const {\n    enableAccessibleFieldDOMStructure = true,\n    ampm\n  } = parameters;\n  return React.useMemo(() => ({\n    valueType: 'time',\n    validator: _validation.validateTime,\n    internal_valueManager: _valueManagers.singleItemValueManager,\n    internal_fieldValueManager: _valueManagers.singleItemFieldValueManager,\n    internal_enableAccessibleFieldDOMStructure: enableAccessibleFieldDOMStructure,\n    internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToTimeFieldInternalProps,\n    internal_useOpenPickerButtonAriaLabel: createUseOpenPickerButtonAriaLabel(ampm)\n  }), [ampm, enableAccessibleFieldDOMStructure]);\n}\nfunction createUseOpenPickerButtonAriaLabel(ampm) {\n  return function useOpenPickerButtonAriaLabel(value) {\n    const adapter = (0, _hooks.usePickerAdapter)();\n    const translations = (0, _hooks.usePickerTranslations)();\n    return React.useMemo(() => {\n      const formatKey = ampm ?? adapter.is12HourCycleInCurrentLocale() ? 'fullTime12h' : 'fullTime24h';\n      const formattedValue = adapter.isValid(value) ? adapter.format(value, formatKey) : null;\n      return translations.openTimePickerDialogue(formattedValue);\n    }, [value, translations, adapter]);\n  };\n}\nfunction useApplyDefaultValuesToTimeFieldInternalProps(internalProps) {\n  const adapter = (0, _hooks.usePickerAdapter)();\n  const validationProps = useApplyDefaultValuesToTimeValidationProps(internalProps);\n  const ampm = React.useMemo(() => internalProps.ampm ?? adapter.is12HourCycleInCurrentLocale(), [internalProps.ampm, adapter]);\n  return React.useMemo(() => (0, _extends2.default)({}, internalProps, validationProps, {\n    format: internalProps.format ?? (ampm ? adapter.formats.fullTime12h : adapter.formats.fullTime24h)\n  }), [internalProps, validationProps, ampm, adapter]);\n}\nfunction useApplyDefaultValuesToTimeValidationProps(props) {\n  return React.useMemo(() => ({\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false\n  }), [props.disablePast, props.disableFuture]);\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "useApplyDefaultValuesToTimeValidationProps", "useTimeManager", "_extends2", "React", "_valueManagers", "_validation", "_hooks", "parameters", "enableAccessibleFieldDOMStructure", "ampm", "useMemo", "valueType", "validator", "validateTime", "internal_valueManager", "singleItemValueManager", "internal_fieldValueManager", "singleItemFieldValueManager", "internal_enableAccessibleFieldDOMStructure", "internal_useApplyDefaultValuesToFieldInternalProps", "useApplyDefaultValuesToTimeFieldInternalProps", "internal_useOpenPickerButtonAriaLabel", "createUseOpenPickerButtonAriaLabel", "useOpenPickerButtonAriaLabel", "adapter", "usePickerAdapter", "translations", "usePickerTranslations", "formatKey", "is12HourCycleInCurrentLocale", "formattedValue", "<PERSON><PERSON><PERSON><PERSON>", "format", "openTimePickerDialogue", "internalProps", "validationProps", "formats", "fullTime12h", "fullTime24h", "props", "disablePast", "disableFuture"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/managers/useTimeManager.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useApplyDefaultValuesToTimeValidationProps = useApplyDefaultValuesToTimeValidationProps;\nexports.useTimeManager = useTimeManager;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _valueManagers = require(\"../internals/utils/valueManagers\");\nvar _validation = require(\"../validation\");\nvar _hooks = require(\"../hooks\");\nfunction useTimeManager(parameters = {}) {\n  const {\n    enableAccessibleFieldDOMStructure = true,\n    ampm\n  } = parameters;\n  return React.useMemo(() => ({\n    valueType: 'time',\n    validator: _validation.validateTime,\n    internal_valueManager: _valueManagers.singleItemValueManager,\n    internal_fieldValueManager: _valueManagers.singleItemFieldValueManager,\n    internal_enableAccessibleFieldDOMStructure: enableAccessibleFieldDOMStructure,\n    internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToTimeFieldInternalProps,\n    internal_useOpenPickerButtonAriaLabel: createUseOpenPickerButtonAriaLabel(ampm)\n  }), [ampm, enableAccessibleFieldDOMStructure]);\n}\nfunction createUseOpenPickerButtonAriaLabel(ampm) {\n  return function useOpenPickerButtonAriaLabel(value) {\n    const adapter = (0, _hooks.usePickerAdapter)();\n    const translations = (0, _hooks.usePickerTranslations)();\n    return React.useMemo(() => {\n      const formatKey = ampm ?? adapter.is12HourCycleInCurrentLocale() ? 'fullTime12h' : 'fullTime24h';\n      const formattedValue = adapter.isValid(value) ? adapter.format(value, formatKey) : null;\n      return translations.openTimePickerDialogue(formattedValue);\n    }, [value, translations, adapter]);\n  };\n}\nfunction useApplyDefaultValuesToTimeFieldInternalProps(internalProps) {\n  const adapter = (0, _hooks.usePickerAdapter)();\n  const validationProps = useApplyDefaultValuesToTimeValidationProps(internalProps);\n  const ampm = React.useMemo(() => internalProps.ampm ?? adapter.is12HourCycleInCurrentLocale(), [internalProps.ampm, adapter]);\n  return React.useMemo(() => (0, _extends2.default)({}, internalProps, validationProps, {\n    format: internalProps.format ?? (ampm ? adapter.formats.fullTime12h : adapter.formats.fullTime24h)\n  }), [internalProps, validationProps, ampm, adapter]);\n}\nfunction useApplyDefaultValuesToTimeValidationProps(props) {\n  return React.useMemo(() => ({\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false\n  }), [props.disablePast, props.disableFuture]);\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,0CAA0C,GAAGA,0CAA0C;AAC/FF,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,IAAIC,SAAS,GAAGP,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIU,KAAK,GAAGX,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIW,cAAc,GAAGX,OAAO,CAAC,kCAAkC,CAAC;AAChE,IAAIY,WAAW,GAAGZ,OAAO,CAAC,eAAe,CAAC;AAC1C,IAAIa,MAAM,GAAGb,OAAO,CAAC,UAAU,CAAC;AAChC,SAASQ,cAAcA,CAACM,UAAU,GAAG,CAAC,CAAC,EAAE;EACvC,MAAM;IACJC,iCAAiC,GAAG,IAAI;IACxCC;EACF,CAAC,GAAGF,UAAU;EACd,OAAOJ,KAAK,CAACO,OAAO,CAAC,OAAO;IAC1BC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAEP,WAAW,CAACQ,YAAY;IACnCC,qBAAqB,EAAEV,cAAc,CAACW,sBAAsB;IAC5DC,0BAA0B,EAAEZ,cAAc,CAACa,2BAA2B;IACtEC,0CAA0C,EAAEV,iCAAiC;IAC7EW,kDAAkD,EAAEC,6CAA6C;IACjGC,qCAAqC,EAAEC,kCAAkC,CAACb,IAAI;EAChF,CAAC,CAAC,EAAE,CAACA,IAAI,EAAED,iCAAiC,CAAC,CAAC;AAChD;AACA,SAASc,kCAAkCA,CAACb,IAAI,EAAE;EAChD,OAAO,SAASc,4BAA4BA,CAACxB,KAAK,EAAE;IAClD,MAAMyB,OAAO,GAAG,CAAC,CAAC,EAAElB,MAAM,CAACmB,gBAAgB,EAAE,CAAC;IAC9C,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAEpB,MAAM,CAACqB,qBAAqB,EAAE,CAAC;IACxD,OAAOxB,KAAK,CAACO,OAAO,CAAC,MAAM;MACzB,MAAMkB,SAAS,GAAGnB,IAAI,IAAIe,OAAO,CAACK,4BAA4B,CAAC,CAAC,GAAG,aAAa,GAAG,aAAa;MAChG,MAAMC,cAAc,GAAGN,OAAO,CAACO,OAAO,CAAChC,KAAK,CAAC,GAAGyB,OAAO,CAACQ,MAAM,CAACjC,KAAK,EAAE6B,SAAS,CAAC,GAAG,IAAI;MACvF,OAAOF,YAAY,CAACO,sBAAsB,CAACH,cAAc,CAAC;IAC5D,CAAC,EAAE,CAAC/B,KAAK,EAAE2B,YAAY,EAAEF,OAAO,CAAC,CAAC;EACpC,CAAC;AACH;AACA,SAASJ,6CAA6CA,CAACc,aAAa,EAAE;EACpE,MAAMV,OAAO,GAAG,CAAC,CAAC,EAAElB,MAAM,CAACmB,gBAAgB,EAAE,CAAC;EAC9C,MAAMU,eAAe,GAAGnC,0CAA0C,CAACkC,aAAa,CAAC;EACjF,MAAMzB,IAAI,GAAGN,KAAK,CAACO,OAAO,CAAC,MAAMwB,aAAa,CAACzB,IAAI,IAAIe,OAAO,CAACK,4BAA4B,CAAC,CAAC,EAAE,CAACK,aAAa,CAACzB,IAAI,EAAEe,OAAO,CAAC,CAAC;EAC7H,OAAOrB,KAAK,CAACO,OAAO,CAAC,MAAM,CAAC,CAAC,EAAER,SAAS,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEwC,aAAa,EAAEC,eAAe,EAAE;IACpFH,MAAM,EAAEE,aAAa,CAACF,MAAM,KAAKvB,IAAI,GAAGe,OAAO,CAACY,OAAO,CAACC,WAAW,GAAGb,OAAO,CAACY,OAAO,CAACE,WAAW;EACnG,CAAC,CAAC,EAAE,CAACJ,aAAa,EAAEC,eAAe,EAAE1B,IAAI,EAAEe,OAAO,CAAC,CAAC;AACtD;AACA,SAASxB,0CAA0CA,CAACuC,KAAK,EAAE;EACzD,OAAOpC,KAAK,CAACO,OAAO,CAAC,OAAO;IAC1B8B,WAAW,EAAED,KAAK,CAACC,WAAW,IAAI,KAAK;IACvCC,aAAa,EAAEF,KAAK,CAACE,aAAa,IAAI;EACxC,CAAC,CAAC,EAAE,CAACF,KAAK,CAACC,WAAW,EAAED,KAAK,CAACE,aAAa,CAAC,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}