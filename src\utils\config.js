// Configuración de la aplicación
export const APP_CONFIG = {
  // Información de la aplicación
  APP_NAME: 'Repostajes Manager',
  APP_VERSION: '1.0.0',
  
  // Configuración de la base de datos
  DB_NAME: 'repostaje.db',
  BACKUP_RETENTION_DAYS: 30,
  MAX_BACKUPS: 10,
  
  // Configuración de la API
  API_BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'http://localhost:3001/api' 
    : 'http://localhost:3001/api',
  API_TIMEOUT: 10000,
  
  // Configuración de actualizaciones
  UPDATE_CHECK_INTERVAL: 24 * 60 * 60 * 1000, // 24 horas
  AUTO_UPDATE_ENABLED: true,
  
  // Configuración de logs
  LOG_LEVEL: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  LOG_MAX_SIZE: 10 * 1024 * 1024, // 10MB
  
  // Configuración de la interfaz
  DEFAULT_THEME: 'light',
  DEFAULT_LANGUAGE: 'es',
  
  // Configuración de exportación
  EXPORT_FORMATS: ['pdf', 'excel', 'csv'],
  
  // Configuración de notificaciones
  NOTIFICATIONS_ENABLED: true,
  REMINDER_NOTIFICATIONS: true,
  
  // Configuración de rendimiento
  PAGINATION_SIZE: 50,
  CHART_ANIMATION_DURATION: 300,
  
  // URLs útiles
  SUPPORT_URL: 'mailto:<EMAIL>',
  DOCUMENTATION_URL: 'https://docs.repostajes-manager.com',
  GITHUB_URL: 'https://github.com/repostajes-manager/app'
};

// Función para obtener configuración del usuario
export function getUserConfig() {
  try {
    const config = localStorage.getItem('app-config');
    return config ? JSON.parse(config) : {};
  } catch (error) {
    console.error('Error loading user config:', error);
    return {};
  }
}

// Función para guardar configuración del usuario
export function saveUserConfig(config) {
  try {
    const currentConfig = getUserConfig();
    const newConfig = { ...currentConfig, ...config };
    localStorage.setItem('app-config', JSON.stringify(newConfig));
    return true;
  } catch (error) {
    console.error('Error saving user config:', error);
    return false;
  }
}

// Función para obtener configuración combinada
export function getConfig() {
  const userConfig = getUserConfig();
  return { ...APP_CONFIG, ...userConfig };
}

// Función para resetear configuración
export function resetConfig() {
  try {
    localStorage.removeItem('app-config');
    return true;
  } catch (error) {
    console.error('Error resetting config:', error);
    return false;
  }
}
