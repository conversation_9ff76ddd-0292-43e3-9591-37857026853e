{"version": 3, "names": ["_t", "require", "FLIPPED_ALIAS_KEYS", "isArrayExpression", "isAssignmentExpression", "isBinary", "isBlockStatement", "isCallExpression", "isFunction", "isIdentifier", "isLiteral", "isMemberExpression", "isObjectExpression", "isOptionalCallExpression", "isOptionalMemberExpression", "isStringLiteral", "crawlInternal", "node", "state", "object", "computed", "property", "left", "right", "hasCall", "callee", "hasFunction", "<PERSON><PERSON><PERSON><PERSON>", "is<PERSON><PERSON>per", "crawl", "name", "charCodeAt", "isType", "nodes", "exports", "AssignmentExpression", "SwitchCase", "parent", "consequent", "length", "cases", "LogicalExpression", "Literal", "value", "CallExpression", "OptionalCallExpression", "VariableDeclaration", "i", "declarations", "declar", "enabled", "id", "init", "IfStatement", "ObjectProperty", "ObjectTypeProperty", "ObjectMethod", "properties", "ObjectTypeCallProperty", "_parent$properties", "callProperties", "ObjectTypeIndexer", "_parent$properties2", "_parent$callPropertie", "indexers", "ObjectTypeInternalSlot", "_parent$properties3", "_parent$callPropertie2", "_parent$indexers", "internalSlots", "for<PERSON>ach", "type", "amounts", "concat", "ret"], "sources": ["../../src/node/whitespace.ts"], "sourcesContent": ["import {\n  FLIPPED_ALIAS_KEYS,\n  isArrayExpression,\n  isAssignmentExpression,\n  isBinary,\n  isBlockStatement,\n  isCallExpression,\n  isFunction,\n  isIdentifier,\n  isLiteral,\n  isMemberExpression,\n  isObjectExpression,\n  isOptionalCallExpression,\n  isOptionalMemberExpression,\n  isStringLiteral,\n} from \"@babel/types\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nimport type { NodeHandlers } from \"./index.ts\";\n\nimport type * as t from \"@babel/types\";\n\nconst enum WhitespaceFlag {\n  before = 1 << 0,\n  after = 1 << 1,\n}\n\nexport type { WhitespaceFlag };\n\nfunction crawlInternal(\n  node: t.Node,\n  state: { hasCall: boolean; hasFunction: boolean; hasHelper: boolean },\n) {\n  if (!node) return state;\n\n  if (isMemberExpression(node) || isOptionalMemberExpression(node)) {\n    crawlInternal(node.object, state);\n    if (node.computed) crawlInternal(node.property, state);\n  } else if (isBinary(node) || isAssignmentExpression(node)) {\n    crawlInternal(node.left, state);\n    crawlInternal(node.right, state);\n  } else if (isCallExpression(node) || isOptionalCallExpression(node)) {\n    state.hasCall = true;\n    crawlInternal(node.callee, state);\n  } else if (isFunction(node)) {\n    state.hasFunction = true;\n  } else if (isIdentifier(node)) {\n    state.hasHelper =\n      // @ts-expect-error todo(flow->ts): node.callee is not really expected here…\n      state.hasHelper || (node.callee && isHelper(node.callee));\n  }\n\n  return state;\n}\n\n/**\n * Crawl a node to test if it contains a CallExpression, a Function, or a Helper.\n *\n * @example\n * crawl(node)\n * // { hasCall: false, hasFunction: true, hasHelper: false }\n */\n\nfunction crawl(node: t.Node) {\n  return crawlInternal(node, {\n    hasCall: false,\n    hasFunction: false,\n    hasHelper: false,\n  });\n}\n\n/**\n * Test if a node is or has a helper.\n */\n\nfunction isHelper(node: t.Node): boolean {\n  if (!node) return false;\n\n  if (isMemberExpression(node)) {\n    return isHelper(node.object) || isHelper(node.property);\n  } else if (isIdentifier(node)) {\n    return (\n      node.name === \"require\" ||\n      node.name.charCodeAt(0) === charCodes.underscore\n    );\n  } else if (isCallExpression(node)) {\n    return isHelper(node.callee);\n  } else if (isBinary(node) || isAssignmentExpression(node)) {\n    return (\n      (isIdentifier(node.left) && isHelper(node.left)) || isHelper(node.right)\n    );\n  } else {\n    return false;\n  }\n}\n\nfunction isType(node: t.Node) {\n  return (\n    isLiteral(node) ||\n    isObjectExpression(node) ||\n    isArrayExpression(node) ||\n    isIdentifier(node) ||\n    isMemberExpression(node)\n  );\n}\n\n/**\n * Tests for node types that need whitespace.\n */\n\nexport const nodes: NodeHandlers<WhitespaceFlag> = {\n  /**\n   * Test if AssignmentExpression needs whitespace.\n   */\n\n  AssignmentExpression(node: t.AssignmentExpression): WhitespaceFlag {\n    const state = crawl(node.right);\n    if ((state.hasCall && state.hasHelper) || state.hasFunction) {\n      return state.hasFunction\n        ? WhitespaceFlag.before | WhitespaceFlag.after\n        : WhitespaceFlag.after;\n    }\n  },\n\n  /**\n   * Test if SwitchCase needs whitespace.\n   */\n\n  SwitchCase(node: t.SwitchCase, parent: t.SwitchStatement): WhitespaceFlag {\n    return (\n      (!!node.consequent.length || parent.cases[0] === node\n        ? WhitespaceFlag.before\n        : 0) |\n      (!node.consequent.length && parent.cases[parent.cases.length - 1] === node\n        ? WhitespaceFlag.after\n        : 0)\n    );\n  },\n\n  /**\n   * Test if LogicalExpression needs whitespace.\n   */\n\n  LogicalExpression(node: t.LogicalExpression): WhitespaceFlag {\n    if (isFunction(node.left) || isFunction(node.right)) {\n      return WhitespaceFlag.after;\n    }\n  },\n\n  /**\n   * Test if Literal needs whitespace.\n   */\n\n  Literal(node: t.Literal): WhitespaceFlag {\n    if (isStringLiteral(node) && node.value === \"use strict\") {\n      return WhitespaceFlag.after;\n    }\n  },\n\n  /**\n   * Test if CallExpressionish needs whitespace.\n   */\n\n  CallExpression(node: t.CallExpression): WhitespaceFlag {\n    if (isFunction(node.callee) || isHelper(node)) {\n      return WhitespaceFlag.before | WhitespaceFlag.after;\n    }\n  },\n\n  OptionalCallExpression(node: t.OptionalCallExpression): WhitespaceFlag {\n    if (isFunction(node.callee)) {\n      return WhitespaceFlag.before | WhitespaceFlag.after;\n    }\n  },\n\n  /**\n   * Test if VariableDeclaration needs whitespace.\n   */\n\n  VariableDeclaration(node: t.VariableDeclaration): WhitespaceFlag {\n    for (let i = 0; i < node.declarations.length; i++) {\n      const declar = node.declarations[i];\n\n      let enabled = isHelper(declar.id) && !isType(declar.init);\n      if (!enabled && declar.init) {\n        const state = crawl(declar.init);\n        enabled = (isHelper(declar.init) && state.hasCall) || state.hasFunction;\n      }\n\n      if (enabled) {\n        return WhitespaceFlag.before | WhitespaceFlag.after;\n      }\n    }\n  },\n\n  /**\n   * Test if IfStatement needs whitespace.\n   */\n\n  IfStatement(node: t.IfStatement): WhitespaceFlag {\n    if (isBlockStatement(node.consequent)) {\n      return WhitespaceFlag.before | WhitespaceFlag.after;\n    }\n  },\n};\n\n/**\n * Test if Property needs whitespace.\n */\n\nnodes.ObjectProperty =\n  nodes.ObjectTypeProperty =\n  nodes.ObjectMethod =\n    function (\n      node: t.ObjectProperty | t.ObjectTypeProperty | t.ObjectMethod,\n      parent: t.ObjectExpression,\n    ): WhitespaceFlag {\n      if (parent.properties[0] === node) {\n        return WhitespaceFlag.before;\n      }\n    };\n\nnodes.ObjectTypeCallProperty = function (\n  node: t.ObjectTypeCallProperty,\n  parent: t.ObjectTypeAnnotation,\n): WhitespaceFlag {\n  if (parent.callProperties[0] === node && !parent.properties?.length) {\n    return WhitespaceFlag.before;\n  }\n};\n\nnodes.ObjectTypeIndexer = function (\n  node: t.ObjectTypeIndexer,\n  parent: t.ObjectTypeAnnotation,\n): WhitespaceFlag {\n  if (\n    parent.indexers[0] === node &&\n    !parent.properties?.length &&\n    !parent.callProperties?.length\n  ) {\n    return WhitespaceFlag.before;\n  }\n};\n\nnodes.ObjectTypeInternalSlot = function (\n  node: t.ObjectTypeInternalSlot,\n  parent: t.ObjectTypeAnnotation,\n): WhitespaceFlag {\n  if (\n    parent.internalSlots[0] === node &&\n    !parent.properties?.length &&\n    !parent.callProperties?.length &&\n    !parent.indexers?.length\n  ) {\n    return WhitespaceFlag.before;\n  }\n};\n\n/**\n * Add whitespace tests for nodes and their aliases.\n */\n\n(\n  [\n    [\"Function\", true],\n    [\"Class\", true],\n    [\"Loop\", true],\n    [\"LabeledStatement\", true],\n    [\"SwitchStatement\", true],\n    [\"TryStatement\", true],\n  ] as const\n).forEach(function ([type, amounts]) {\n  [type as string]\n    .concat(FLIPPED_ALIAS_KEYS[type] || [])\n    .forEach(function (type) {\n      const ret = amounts ? WhitespaceFlag.before | WhitespaceFlag.after : 0;\n      nodes[type] = () => ret;\n    });\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAesB;EAdpBC,kBAAkB;EAClBC,iBAAiB;EACjBC,sBAAsB;EACtBC,QAAQ;EACRC,gBAAgB;EAChBC,gBAAgB;EAChBC,UAAU;EACVC,YAAY;EACZC,SAAS;EACTC,kBAAkB;EAClBC,kBAAkB;EAClBC,wBAAwB;EACxBC,0BAA0B;EAC1BC;AAAe,IAAAf,EAAA;AAkBjB,SAASgB,aAAaA,CACpBC,IAAY,EACZC,KAAqE,EACrE;EACA,IAAI,CAACD,IAAI,EAAE,OAAOC,KAAK;EAEvB,IAAIP,kBAAkB,CAACM,IAAI,CAAC,IAAIH,0BAA0B,CAACG,IAAI,CAAC,EAAE;IAChED,aAAa,CAACC,IAAI,CAACE,MAAM,EAAED,KAAK,CAAC;IACjC,IAAID,IAAI,CAACG,QAAQ,EAAEJ,aAAa,CAACC,IAAI,CAACI,QAAQ,EAAEH,KAAK,CAAC;EACxD,CAAC,MAAM,IAAIb,QAAQ,CAACY,IAAI,CAAC,IAAIb,sBAAsB,CAACa,IAAI,CAAC,EAAE;IACzDD,aAAa,CAACC,IAAI,CAACK,IAAI,EAAEJ,KAAK,CAAC;IAC/BF,aAAa,CAACC,IAAI,CAACM,KAAK,EAAEL,KAAK,CAAC;EAClC,CAAC,MAAM,IAAIX,gBAAgB,CAACU,IAAI,CAAC,IAAIJ,wBAAwB,CAACI,IAAI,CAAC,EAAE;IACnEC,KAAK,CAACM,OAAO,GAAG,IAAI;IACpBR,aAAa,CAACC,IAAI,CAACQ,MAAM,EAAEP,KAAK,CAAC;EACnC,CAAC,MAAM,IAAIV,UAAU,CAACS,IAAI,CAAC,EAAE;IAC3BC,KAAK,CAACQ,WAAW,GAAG,IAAI;EAC1B,CAAC,MAAM,IAAIjB,YAAY,CAACQ,IAAI,CAAC,EAAE;IAC7BC,KAAK,CAACS,SAAS,GAEbT,KAAK,CAACS,SAAS,IAAKV,IAAI,CAACQ,MAAM,IAAIG,QAAQ,CAACX,IAAI,CAACQ,MAAM,CAAE;EAC7D;EAEA,OAAOP,KAAK;AACd;AAUA,SAASW,KAAKA,CAACZ,IAAY,EAAE;EAC3B,OAAOD,aAAa,CAACC,IAAI,EAAE;IACzBO,OAAO,EAAE,KAAK;IACdE,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE;EACb,CAAC,CAAC;AACJ;AAMA,SAASC,QAAQA,CAACX,IAAY,EAAW;EACvC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EAEvB,IAAIN,kBAAkB,CAACM,IAAI,CAAC,EAAE;IAC5B,OAAOW,QAAQ,CAACX,IAAI,CAACE,MAAM,CAAC,IAAIS,QAAQ,CAACX,IAAI,CAACI,QAAQ,CAAC;EACzD,CAAC,MAAM,IAAIZ,YAAY,CAACQ,IAAI,CAAC,EAAE;IAC7B,OACEA,IAAI,CAACa,IAAI,KAAK,SAAS,IACvBb,IAAI,CAACa,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,OAAyB;EAEpD,CAAC,MAAM,IAAIxB,gBAAgB,CAACU,IAAI,CAAC,EAAE;IACjC,OAAOW,QAAQ,CAACX,IAAI,CAACQ,MAAM,CAAC;EAC9B,CAAC,MAAM,IAAIpB,QAAQ,CAACY,IAAI,CAAC,IAAIb,sBAAsB,CAACa,IAAI,CAAC,EAAE;IACzD,OACGR,YAAY,CAACQ,IAAI,CAACK,IAAI,CAAC,IAAIM,QAAQ,CAACX,IAAI,CAACK,IAAI,CAAC,IAAKM,QAAQ,CAACX,IAAI,CAACM,KAAK,CAAC;EAE5E,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAEA,SAASS,MAAMA,CAACf,IAAY,EAAE;EAC5B,OACEP,SAAS,CAACO,IAAI,CAAC,IACfL,kBAAkB,CAACK,IAAI,CAAC,IACxBd,iBAAiB,CAACc,IAAI,CAAC,IACvBR,YAAY,CAACQ,IAAI,CAAC,IAClBN,kBAAkB,CAACM,IAAI,CAAC;AAE5B;AAMO,MAAMgB,KAAmC,GAAAC,OAAA,CAAAD,KAAA,GAAG;EAKjDE,oBAAoBA,CAAClB,IAA4B,EAAkB;IACjE,MAAMC,KAAK,GAAGW,KAAK,CAACZ,IAAI,CAACM,KAAK,CAAC;IAC/B,IAAKL,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACS,SAAS,IAAKT,KAAK,CAACQ,WAAW,EAAE;MAC3D,OAAOR,KAAK,CAACQ,WAAW,GACpB,KAA4C,IACxB;IAC1B;EACF,CAAC;EAMDU,UAAUA,CAACnB,IAAkB,EAAEoB,MAAyB,EAAkB;IACxE,OACE,CAAC,CAAC,CAACpB,IAAI,CAACqB,UAAU,CAACC,MAAM,IAAIF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,KAAKvB,IAAI,OAEjD,CAAC,KACJ,CAACA,IAAI,CAACqB,UAAU,CAACC,MAAM,IAAIF,MAAM,CAACG,KAAK,CAACH,MAAM,CAACG,KAAK,CAACD,MAAM,GAAG,CAAC,CAAC,KAAKtB,IAAI,OAEtE,CAAC,CAAC;EAEV,CAAC;EAMDwB,iBAAiBA,CAACxB,IAAyB,EAAkB;IAC3D,IAAIT,UAAU,CAACS,IAAI,CAACK,IAAI,CAAC,IAAId,UAAU,CAACS,IAAI,CAACM,KAAK,CAAC,EAAE;MACnD;IACF;EACF,CAAC;EAMDmB,OAAOA,CAACzB,IAAe,EAAkB;IACvC,IAAIF,eAAe,CAACE,IAAI,CAAC,IAAIA,IAAI,CAAC0B,KAAK,KAAK,YAAY,EAAE;MACxD;IACF;EACF,CAAC;EAMDC,cAAcA,CAAC3B,IAAsB,EAAkB;IACrD,IAAIT,UAAU,CAACS,IAAI,CAACQ,MAAM,CAAC,IAAIG,QAAQ,CAACX,IAAI,CAAC,EAAE;MAC7C,OAAO,KAA4C;IACrD;EACF,CAAC;EAED4B,sBAAsBA,CAAC5B,IAA8B,EAAkB;IACrE,IAAIT,UAAU,CAACS,IAAI,CAACQ,MAAM,CAAC,EAAE;MAC3B,OAAO,KAA4C;IACrD;EACF,CAAC;EAMDqB,mBAAmBA,CAAC7B,IAA2B,EAAkB;IAC/D,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,IAAI,CAAC+B,YAAY,CAACT,MAAM,EAAEQ,CAAC,EAAE,EAAE;MACjD,MAAME,MAAM,GAAGhC,IAAI,CAAC+B,YAAY,CAACD,CAAC,CAAC;MAEnC,IAAIG,OAAO,GAAGtB,QAAQ,CAACqB,MAAM,CAACE,EAAE,CAAC,IAAI,CAACnB,MAAM,CAACiB,MAAM,CAACG,IAAI,CAAC;MACzD,IAAI,CAACF,OAAO,IAAID,MAAM,CAACG,IAAI,EAAE;QAC3B,MAAMlC,KAAK,GAAGW,KAAK,CAACoB,MAAM,CAACG,IAAI,CAAC;QAChCF,OAAO,GAAItB,QAAQ,CAACqB,MAAM,CAACG,IAAI,CAAC,IAAIlC,KAAK,CAACM,OAAO,IAAKN,KAAK,CAACQ,WAAW;MACzE;MAEA,IAAIwB,OAAO,EAAE;QACX,OAAO,KAA4C;MACrD;IACF;EACF,CAAC;EAMDG,WAAWA,CAACpC,IAAmB,EAAkB;IAC/C,IAAIX,gBAAgB,CAACW,IAAI,CAACqB,UAAU,CAAC,EAAE;MACrC,OAAO,KAA4C;IACrD;EACF;AACF,CAAC;AAMDL,KAAK,CAACqB,cAAc,GAClBrB,KAAK,CAACsB,kBAAkB,GACxBtB,KAAK,CAACuB,YAAY,GAChB,UACEvC,IAA8D,EAC9DoB,MAA0B,EACV;EAChB,IAAIA,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,KAAKxC,IAAI,EAAE;IACjC;EACF;AACF,CAAC;AAELgB,KAAK,CAACyB,sBAAsB,GAAG,UAC7BzC,IAA8B,EAC9BoB,MAA8B,EACd;EAAA,IAAAsB,kBAAA;EAChB,IAAItB,MAAM,CAACuB,cAAc,CAAC,CAAC,CAAC,KAAK3C,IAAI,IAAI,GAAA0C,kBAAA,GAACtB,MAAM,CAACoB,UAAU,aAAjBE,kBAAA,CAAmBpB,MAAM,GAAE;IACnE;EACF;AACF,CAAC;AAEDN,KAAK,CAAC4B,iBAAiB,GAAG,UACxB5C,IAAyB,EACzBoB,MAA8B,EACd;EAAA,IAAAyB,mBAAA,EAAAC,qBAAA;EAChB,IACE1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,CAAC,KAAK/C,IAAI,IAC3B,GAAA6C,mBAAA,GAACzB,MAAM,CAACoB,UAAU,aAAjBK,mBAAA,CAAmBvB,MAAM,KAC1B,GAAAwB,qBAAA,GAAC1B,MAAM,CAACuB,cAAc,aAArBG,qBAAA,CAAuBxB,MAAM,GAC9B;IACA;EACF;AACF,CAAC;AAEDN,KAAK,CAACgC,sBAAsB,GAAG,UAC7BhD,IAA8B,EAC9BoB,MAA8B,EACd;EAAA,IAAA6B,mBAAA,EAAAC,sBAAA,EAAAC,gBAAA;EAChB,IACE/B,MAAM,CAACgC,aAAa,CAAC,CAAC,CAAC,KAAKpD,IAAI,IAChC,GAAAiD,mBAAA,GAAC7B,MAAM,CAACoB,UAAU,aAAjBS,mBAAA,CAAmB3B,MAAM,KAC1B,GAAA4B,sBAAA,GAAC9B,MAAM,CAACuB,cAAc,aAArBO,sBAAA,CAAuB5B,MAAM,KAC9B,GAAA6B,gBAAA,GAAC/B,MAAM,CAAC2B,QAAQ,aAAfI,gBAAA,CAAiB7B,MAAM,GACxB;IACA;EACF;AACF,CAAC;AAOC,CACE,CAAC,UAAU,EAAE,IAAI,CAAC,EAClB,CAAC,OAAO,EAAE,IAAI,CAAC,EACf,CAAC,MAAM,EAAE,IAAI,CAAC,EACd,CAAC,kBAAkB,EAAE,IAAI,CAAC,EAC1B,CAAC,iBAAiB,EAAE,IAAI,CAAC,EACzB,CAAC,cAAc,EAAE,IAAI,CAAC,CACvB,CACD+B,OAAO,CAAC,UAAU,CAACC,IAAI,EAAEC,OAAO,CAAC,EAAE;EACnC,CAACD,IAAI,CAAW,CACbE,MAAM,CAACvE,kBAAkB,CAACqE,IAAI,CAAC,IAAI,EAAE,CAAC,CACtCD,OAAO,CAAC,UAAUC,IAAI,EAAE;IACvB,MAAMG,GAAG,GAAGF,OAAO,GAAG,KAA4C,GAAG,CAAC;IACtEvC,KAAK,CAACsC,IAAI,CAAC,GAAG,MAAMG,GAAG;EACzB,CAAC,CAAC;AACN,CAAC,CAAC", "ignoreList": []}