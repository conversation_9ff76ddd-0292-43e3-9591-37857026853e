{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.syncSelectionToDOM = syncSelectionToDOM;\nvar _ownerDocument = _interopRequireDefault(require(\"@mui/utils/ownerDocument\"));\nvar _utils = require(\"../../utils/utils\");\nfunction syncSelectionToDOM(parameters) {\n  const {\n    focused,\n    domGetters,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      state\n    }\n  } = parameters;\n  if (!domGetters.isReady()) {\n    return;\n  }\n  const selection = (0, _ownerDocument.default)(domGetters.getRoot()).getSelection();\n  if (!selection) {\n    return;\n  }\n  if (parsedSelectedSections == null) {\n    // If the selection contains an element inside the field, we reset it.\n    if (selection.rangeCount > 0 && domGetters.getRoot().contains(selection.getRangeAt(0).startContainer)) {\n      selection.removeAllRanges();\n    }\n    if (focused) {\n      domGetters.getRoot().blur();\n    }\n    return;\n  }\n\n  // On multi input range pickers we want to update selection range only for the active input\n  if (!domGetters.getRoot().contains((0, _utils.getActiveElement)(domGetters.getRoot()))) {\n    return;\n  }\n  const range = new window.Range();\n  let target;\n  if (parsedSelectedSections === 'all') {\n    target = domGetters.getRoot();\n  } else {\n    const section = state.sections[parsedSelectedSections];\n    if (section.type === 'empty') {\n      target = domGetters.getSectionContainer(parsedSelectedSections);\n    } else {\n      target = domGetters.getSectionContent(parsedSelectedSections);\n    }\n  }\n  range.selectNodeContents(target);\n  target.focus();\n  selection.removeAllRanges();\n  selection.addRange(range);\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "syncSelectionToDOM", "_ownerDocument", "_utils", "parameters", "focused", "domGetters", "stateResponse", "parsedSelectedSections", "state", "isReady", "selection", "getRoot", "getSelection", "rangeCount", "contains", "getRangeAt", "startContainer", "removeAllRanges", "blur", "getActiveElement", "range", "window", "Range", "target", "section", "sections", "type", "getSectionContainer", "getSectionContent", "selectNodeContents", "focus", "addRange"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/useField/syncSelectionToDOM.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.syncSelectionToDOM = syncSelectionToDOM;\nvar _ownerDocument = _interopRequireDefault(require(\"@mui/utils/ownerDocument\"));\nvar _utils = require(\"../../utils/utils\");\nfunction syncSelectionToDOM(parameters) {\n  const {\n    focused,\n    domGetters,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      state\n    }\n  } = parameters;\n  if (!domGetters.isReady()) {\n    return;\n  }\n  const selection = (0, _ownerDocument.default)(domGetters.getRoot()).getSelection();\n  if (!selection) {\n    return;\n  }\n  if (parsedSelectedSections == null) {\n    // If the selection contains an element inside the field, we reset it.\n    if (selection.rangeCount > 0 && domGetters.getRoot().contains(selection.getRangeAt(0).startContainer)) {\n      selection.removeAllRanges();\n    }\n    if (focused) {\n      domGetters.getRoot().blur();\n    }\n    return;\n  }\n\n  // On multi input range pickers we want to update selection range only for the active input\n  if (!domGetters.getRoot().contains((0, _utils.getActiveElement)(domGetters.getRoot()))) {\n    return;\n  }\n  const range = new window.Range();\n  let target;\n  if (parsedSelectedSections === 'all') {\n    target = domGetters.getRoot();\n  } else {\n    const section = state.sections[parsedSelectedSections];\n    if (section.type === 'empty') {\n      target = domGetters.getSectionContainer(parsedSelectedSections);\n    } else {\n      target = domGetters.getSectionContent(parsedSelectedSections);\n    }\n  }\n  range.selectNodeContents(target);\n  target.focus();\n  selection.removeAllRanges();\n  selection.addRange(range);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIC,cAAc,GAAGR,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF,IAAIQ,MAAM,GAAGR,OAAO,CAAC,mBAAmB,CAAC;AACzC,SAASM,kBAAkBA,CAACG,UAAU,EAAE;EACtC,MAAM;IACJC,OAAO;IACPC,UAAU;IACVC,aAAa,EAAE;MACb;MACAC,sBAAsB;MACtBC;IACF;EACF,CAAC,GAAGL,UAAU;EACd,IAAI,CAACE,UAAU,CAACI,OAAO,CAAC,CAAC,EAAE;IACzB;EACF;EACA,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAET,cAAc,CAACN,OAAO,EAAEU,UAAU,CAACM,OAAO,CAAC,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;EAClF,IAAI,CAACF,SAAS,EAAE;IACd;EACF;EACA,IAAIH,sBAAsB,IAAI,IAAI,EAAE;IAClC;IACA,IAAIG,SAAS,CAACG,UAAU,GAAG,CAAC,IAAIR,UAAU,CAACM,OAAO,CAAC,CAAC,CAACG,QAAQ,CAACJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,EAAE;MACrGN,SAAS,CAACO,eAAe,CAAC,CAAC;IAC7B;IACA,IAAIb,OAAO,EAAE;MACXC,UAAU,CAACM,OAAO,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC;IAC7B;IACA;EACF;;EAEA;EACA,IAAI,CAACb,UAAU,CAACM,OAAO,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC,CAAC,EAAEZ,MAAM,CAACiB,gBAAgB,EAAEd,UAAU,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;IACtF;EACF;EACA,MAAMS,KAAK,GAAG,IAAIC,MAAM,CAACC,KAAK,CAAC,CAAC;EAChC,IAAIC,MAAM;EACV,IAAIhB,sBAAsB,KAAK,KAAK,EAAE;IACpCgB,MAAM,GAAGlB,UAAU,CAACM,OAAO,CAAC,CAAC;EAC/B,CAAC,MAAM;IACL,MAAMa,OAAO,GAAGhB,KAAK,CAACiB,QAAQ,CAAClB,sBAAsB,CAAC;IACtD,IAAIiB,OAAO,CAACE,IAAI,KAAK,OAAO,EAAE;MAC5BH,MAAM,GAAGlB,UAAU,CAACsB,mBAAmB,CAACpB,sBAAsB,CAAC;IACjE,CAAC,MAAM;MACLgB,MAAM,GAAGlB,UAAU,CAACuB,iBAAiB,CAACrB,sBAAsB,CAAC;IAC/D;EACF;EACAa,KAAK,CAACS,kBAAkB,CAACN,MAAM,CAAC;EAChCA,MAAM,CAACO,KAAK,CAAC,CAAC;EACdpB,SAAS,CAACO,eAAe,CAAC,CAAC;EAC3BP,SAAS,CAACqB,QAAQ,CAACX,KAAK,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}