{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldHiddenInputProps = useFieldHiddenInputProps;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\n/**\n * Generate the props to pass to the hidden input element of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldHiddenInputPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldHiddenInputPropsReturnValue} The props to forward to the hidden input element of the field.\n */\nfunction useFieldHiddenInputProps(parameters) {\n  const {\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    stateResponse: {\n      // States and derived states\n      areAllSectionsEmpty,\n      state,\n      // Methods to update the states\n      updateValueFromValueStr\n    }\n  } = parameters;\n  const handleChange = (0, _useEventCallback.default)(event => {\n    updateValueFromValueStr(event.target.value);\n  });\n  const valueStr = React.useMemo(() => areAllSectionsEmpty ? '' : fieldValueManager.getV7HiddenInputValueFromSections(state.sections), [areAllSectionsEmpty, state.sections, fieldValueManager]);\n  return {\n    value: valueStr,\n    onChange: handleChange\n  };\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "useFieldHiddenInputProps", "React", "_useEventCallback", "parameters", "manager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "stateResponse", "areAllSectionsEmpty", "state", "updateValueFromValueStr", "handleChange", "event", "target", "valueStr", "useMemo", "getV7HiddenInputValueFromSections", "sections", "onChange"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldHiddenInputProps.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldHiddenInputProps = useFieldHiddenInputProps;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\n/**\n * Generate the props to pass to the hidden input element of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldHiddenInputPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldHiddenInputPropsReturnValue} The props to forward to the hidden input element of the field.\n */\nfunction useFieldHiddenInputProps(parameters) {\n  const {\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    stateResponse: {\n      // States and derived states\n      areAllSectionsEmpty,\n      state,\n      // Methods to update the states\n      updateValueFromValueStr\n    }\n  } = parameters;\n  const handleChange = (0, _useEventCallback.default)(event => {\n    updateValueFromValueStr(event.target.value);\n  });\n  const valueStr = React.useMemo(() => areAllSectionsEmpty ? '' : fieldValueManager.getV7HiddenInputValueFromSections(state.sections), [areAllSectionsEmpty, state.sections, fieldValueManager]);\n  return {\n    value: valueStr,\n    onChange: handleChange\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,wBAAwB,GAAGA,wBAAwB;AAC3D,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,iBAAiB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,wBAAwBA,CAACG,UAAU,EAAE;EAC5C,MAAM;IACJC,OAAO,EAAE;MACPC,0BAA0B,EAAEC;IAC9B,CAAC;IACDC,aAAa,EAAE;MACb;MACAC,mBAAmB;MACnBC,KAAK;MACL;MACAC;IACF;EACF,CAAC,GAAGP,UAAU;EACd,MAAMQ,YAAY,GAAG,CAAC,CAAC,EAAET,iBAAiB,CAACR,OAAO,EAAEkB,KAAK,IAAI;IAC3DF,uBAAuB,CAACE,KAAK,CAACC,MAAM,CAACd,KAAK,CAAC;EAC7C,CAAC,CAAC;EACF,MAAMe,QAAQ,GAAGb,KAAK,CAACc,OAAO,CAAC,MAAMP,mBAAmB,GAAG,EAAE,GAAGF,iBAAiB,CAACU,iCAAiC,CAACP,KAAK,CAACQ,QAAQ,CAAC,EAAE,CAACT,mBAAmB,EAAEC,KAAK,CAACQ,QAAQ,EAAEX,iBAAiB,CAAC,CAAC;EAC9L,OAAO;IACLP,KAAK,EAAEe,QAAQ;IACfI,QAAQ,EAAEP;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}