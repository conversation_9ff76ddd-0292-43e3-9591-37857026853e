{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useLazyRef;\nvar React = _interopRequireWildcard(require(\"react\"));\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nfunction useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "Object", "defineProperty", "exports", "value", "useLazyRef", "React", "UNINITIALIZED", "init", "initArg", "ref", "useRef", "current"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/useLazyRef/useLazyRef.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useLazyRef;\nvar React = _interopRequireWildcard(require(\"react\"));\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nfunction useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAGK,UAAU;AAC5B,IAAIC,KAAK,GAAGR,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,MAAMQ,aAAa,GAAG,CAAC,CAAC;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,UAAUA,CAACG,IAAI,EAAEC,OAAO,EAAE;EACjC,MAAMC,GAAG,GAAGJ,KAAK,CAACK,MAAM,CAACJ,aAAa,CAAC;EACvC,IAAIG,GAAG,CAACE,OAAO,KAAKL,aAAa,EAAE;IACjCG,GAAG,CAACE,OAAO,GAAGJ,IAAI,CAACC,OAAO,CAAC;EAC7B;EACA,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}