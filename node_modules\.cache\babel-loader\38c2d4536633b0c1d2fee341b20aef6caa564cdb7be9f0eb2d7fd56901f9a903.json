{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Statistics\\\\AdvancedAnalytics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Accordion, AccordionSummary, AccordionDetails, Alert, LinearProgress } from '@mui/material';\nimport { ExpandMore as ExpandMoreIcon, TrendingUp as TrendingUpIcon, TrendingDown as TrendingDownIcon, Speed as SpeedIcon, LocalGasStation as GasIcon } from '@mui/icons-material';\nimport { LineChart, Line, AreaChart, Area, BarChart, Bar, ScatterChart, Scatter, XAxis, YAxis, CartesianGrid, Tooltip as ChartTooltip, Legend, ResponsiveContainer, ReferenceLine } from 'recharts';\nimport { useApp } from '../../context/AppContext';\nimport { format, differenceInDays, parseISO } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdvancedAnalytics = () => {\n  _s();\n  var _analytics$efficiency, _analytics$efficiency2, _analytics$efficiency3, _analytics$efficiency4, _analytics$efficiency5, _analytics$efficiency6, _analytics$efficiency7, _analytics$efficiency8, _analytics$efficiency9, _analytics$efficiency0, _analytics$prediction, _analytics$prediction2;\n  const {\n    vehicles,\n    refuels,\n    expenses\n  } = useApp();\n  const [analytics, setAnalytics] = useState({\n    mileageEvolution: [],\n    consumptionTrends: [],\n    fuelPriceTrends: [],\n    efficiencyMetrics: {},\n    periodicAnalysis: {},\n    extremeValues: {},\n    predictions: {}\n  });\n  useEffect(() => {\n    calculateAdvancedAnalytics();\n  }, [refuels, expenses, vehicles]);\n  const calculateAdvancedAnalytics = () => {\n    if (refuels.length === 0) return;\n    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n\n    // 1. Evolución del kilometraje\n    const mileageEvolution = sortedRefuels.map((refuel, index) => {\n      const prevRefuel = index > 0 ? sortedRefuels[index - 1] : null;\n      const kmDiff = prevRefuel ? refuel.kilometros_actuales - prevRefuel.kilometros_actuales : 0;\n      const daysDiff = prevRefuel ? differenceInDays(new Date(refuel.fecha), new Date(prevRefuel.fecha)) : 0;\n      const dailyKm = daysDiff > 0 ? kmDiff / daysDiff : 0;\n      return {\n        fecha: refuel.fecha,\n        kilometraje: refuel.kilometros_actuales,\n        kmRecorridos: kmDiff,\n        diasTranscurridos: daysDiff,\n        kmDiarios: dailyKm,\n        vehiculo: refuel.vehiculo_nombre,\n        month: format(new Date(refuel.fecha), 'MMM yyyy', {\n          locale: es\n        })\n      };\n    });\n\n    // 2. Tendencias de consumo\n    const consumptionTrends = [];\n    for (let i = 1; i < sortedRefuels.length; i++) {\n      const current = sortedRefuels[i];\n      const previous = sortedRefuels[i - 1];\n      if (current.vehiculo_id === previous.vehiculo_id) {\n        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;\n        const consumption = kmDiff > 0 ? current.litros / kmDiff * 100 : 0;\n        const efficiency = kmDiff > 0 ? kmDiff / current.litros : 0;\n        consumptionTrends.push({\n          fecha: current.fecha,\n          consumo: consumption,\n          eficiencia: efficiency,\n          litros: current.litros,\n          kilometros: kmDiff,\n          vehiculo: current.vehiculo_nombre,\n          month: format(new Date(current.fecha), 'MMM yyyy', {\n            locale: es\n          })\n        });\n      }\n    }\n\n    // 3. Tendencias de precios de combustible\n    const fuelPriceTrends = sortedRefuels.map(refuel => ({\n      fecha: refuel.fecha,\n      precio: refuel.precio_litro,\n      costeTotal: refuel.coste_total,\n      litros: refuel.litros,\n      gasolinera: refuel.gasolinera,\n      month: format(new Date(refuel.fecha), 'MMM yyyy', {\n        locale: es\n      })\n    }));\n\n    // 4. Métricas de eficiencia\n    const efficiencyMetrics = calculateEfficiencyMetrics(consumptionTrends, mileageEvolution);\n\n    // 5. Análisis periódico\n    const periodicAnalysis = calculatePeriodicAnalysis(sortedRefuels, consumptionTrends);\n\n    // 6. Valores extremos\n    const extremeValues = calculateExtremeValues(consumptionTrends, fuelPriceTrends, mileageEvolution);\n\n    // 7. Predicciones simples\n    const predictions = calculatePredictions(consumptionTrends, mileageEvolution);\n    setAnalytics({\n      mileageEvolution,\n      consumptionTrends,\n      fuelPriceTrends,\n      efficiencyMetrics,\n      periodicAnalysis,\n      extremeValues,\n      predictions\n    });\n  };\n  const calculateEfficiencyMetrics = (consumptionTrends, mileageEvolution) => {\n    if (consumptionTrends.length === 0) return {};\n    const consumptions = consumptionTrends.map(t => t.consumo).filter(c => c > 0);\n    const efficiencies = consumptionTrends.map(t => t.eficiencia).filter(e => e > 0);\n    const dailyKms = mileageEvolution.map(m => m.kmDiarios).filter(k => k > 0);\n    return {\n      avgConsumption: consumptions.reduce((a, b) => a + b, 0) / consumptions.length,\n      minConsumption: Math.min(...consumptions),\n      maxConsumption: Math.max(...consumptions),\n      avgEfficiency: efficiencies.reduce((a, b) => a + b, 0) / efficiencies.length,\n      minEfficiency: Math.min(...efficiencies),\n      maxEfficiency: Math.max(...efficiencies),\n      avgDailyKm: dailyKms.reduce((a, b) => a + b, 0) / dailyKms.length,\n      minDailyKm: Math.min(...dailyKms),\n      maxDailyKm: Math.max(...dailyKms),\n      totalKm: mileageEvolution.length > 0 ? mileageEvolution[mileageEvolution.length - 1].kilometraje - mileageEvolution[0].kilometraje : 0\n    };\n  };\n  const calculatePeriodicAnalysis = (refuels, consumptionTrends) => {\n    // Análisis por mes\n    const monthlyData = {};\n    refuels.forEach(refuel => {\n      const month = format(new Date(refuel.fecha), 'yyyy-MM');\n      if (!monthlyData[month]) {\n        monthlyData[month] = {\n          refuels: 0,\n          totalLiters: 0,\n          totalCost: 0,\n          avgPrice: 0,\n          consumption: []\n        };\n      }\n      monthlyData[month].refuels++;\n      monthlyData[month].totalLiters += refuel.litros;\n      monthlyData[month].totalCost += refuel.coste_total;\n    });\n    consumptionTrends.forEach(trend => {\n      const month = format(new Date(trend.fecha), 'yyyy-MM');\n      if (monthlyData[month]) {\n        monthlyData[month].consumption.push(trend.consumo);\n      }\n    });\n\n    // Calcular promedios mensuales\n    Object.keys(monthlyData).forEach(month => {\n      const data = monthlyData[month];\n      data.avgPrice = data.totalLiters > 0 ? data.totalCost / data.totalLiters : 0;\n      data.avgConsumption = data.consumption.length > 0 ? data.consumption.reduce((a, b) => a + b, 0) / data.consumption.length : 0;\n    });\n    return {\n      monthlyData\n    };\n  };\n  const calculateExtremeValues = (consumptionTrends, fuelPriceTrends, mileageEvolution) => {\n    if (consumptionTrends.length === 0 || fuelPriceTrends.length === 0) return {};\n    const consumptions = consumptionTrends.filter(t => t.consumo > 0);\n    const prices = fuelPriceTrends.filter(p => p.precio > 0);\n    const dailyKms = mileageEvolution.filter(m => m.kmDiarios > 0);\n    const bestConsumption = consumptions.reduce((min, current) => current.consumo < min.consumo ? current : min, consumptions[0]);\n    const worstConsumption = consumptions.reduce((max, current) => current.consumo > max.consumo ? current : max, consumptions[0]);\n    const cheapestFuel = prices.reduce((min, current) => current.precio < min.precio ? current : min, prices[0]);\n    const expensiveFuel = prices.reduce((max, current) => current.precio > max.precio ? current : max, prices[0]);\n    const maxDailyKm = dailyKms.reduce((max, current) => current.kmDiarios > max.kmDiarios ? current : max, dailyKms[0] || {});\n    const minDailyKm = dailyKms.reduce((min, current) => current.kmDiarios < min.kmDiarios ? current : min, dailyKms[0] || {});\n    return {\n      bestConsumption,\n      worstConsumption,\n      cheapestFuel,\n      expensiveFuel,\n      maxDailyKm,\n      minDailyKm\n    };\n  };\n  const calculatePredictions = (consumptionTrends, mileageEvolution) => {\n    if (consumptionTrends.length < 3) return {};\n\n    // Tendencia de consumo (últimos 6 registros)\n    const recentConsumption = consumptionTrends.slice(-6);\n    const consumptionTrend = recentConsumption.length > 1 ? (recentConsumption[recentConsumption.length - 1].consumo - recentConsumption[0].consumo) / recentConsumption.length : 0;\n\n    // Tendencia de kilometraje diario\n    const recentMileage = mileageEvolution.slice(-6).filter(m => m.kmDiarios > 0);\n    const mileageTrend = recentMileage.length > 1 ? (recentMileage[recentMileage.length - 1].kmDiarios - recentMileage[0].kmDiarios) / recentMileage.length : 0;\n    return {\n      consumptionTrend,\n      mileageTrend,\n      consumptionDirection: consumptionTrend > 0.1 ? 'increasing' : consumptionTrend < -0.1 ? 'decreasing' : 'stable',\n      mileageDirection: mileageTrend > 1 ? 'increasing' : mileageTrend < -1 ? 'decreasing' : 'stable'\n    };\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const getTrendIcon = direction => {\n    switch (direction) {\n      case 'increasing':\n        return /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 33\n        }, this);\n      case 'decreasing':\n        return /*#__PURE__*/_jsxDEV(TrendingDownIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 33\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(SpeedIcon, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const getTrendColor = (direction, isGood) => {\n    if (direction === 'stable') return 'info';\n    return direction === 'increasing' === isGood ? 'success' : 'warning';\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"An\\xE1lisis Avanzado\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"M\\xE9tricas de Rendimiento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                color: \"primary\",\n                children: [((_analytics$efficiency = analytics.efficiencyMetrics.avgConsumption) === null || _analytics$efficiency === void 0 ? void 0 : _analytics$efficiency.toFixed(1)) || 'N/A', \" L\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Consumo Promedio/100km\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"textSecondary\",\n                children: [\"Min: \", ((_analytics$efficiency2 = analytics.efficiencyMetrics.minConsumption) === null || _analytics$efficiency2 === void 0 ? void 0 : _analytics$efficiency2.toFixed(1)) || 'N/A', \" | Max: \", ((_analytics$efficiency3 = analytics.efficiencyMetrics.maxConsumption) === null || _analytics$efficiency3 === void 0 ? void 0 : _analytics$efficiency3.toFixed(1)) || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                color: \"success.main\",\n                children: [((_analytics$efficiency4 = analytics.efficiencyMetrics.avgEfficiency) === null || _analytics$efficiency4 === void 0 ? void 0 : _analytics$efficiency4.toFixed(1)) || 'N/A', \" km/L\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Eficiencia Promedio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"textSecondary\",\n                children: [\"Min: \", ((_analytics$efficiency5 = analytics.efficiencyMetrics.minEfficiency) === null || _analytics$efficiency5 === void 0 ? void 0 : _analytics$efficiency5.toFixed(1)) || 'N/A', \" | Max: \", ((_analytics$efficiency6 = analytics.efficiencyMetrics.maxEfficiency) === null || _analytics$efficiency6 === void 0 ? void 0 : _analytics$efficiency6.toFixed(1)) || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                color: \"info.main\",\n                children: [((_analytics$efficiency7 = analytics.efficiencyMetrics.avgDailyKm) === null || _analytics$efficiency7 === void 0 ? void 0 : _analytics$efficiency7.toFixed(0)) || 'N/A', \" km\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Kilometraje Diario Promedio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"textSecondary\",\n                children: [\"Min: \", ((_analytics$efficiency8 = analytics.efficiencyMetrics.minDailyKm) === null || _analytics$efficiency8 === void 0 ? void 0 : _analytics$efficiency8.toFixed(0)) || 'N/A', \" | Max: \", ((_analytics$efficiency9 = analytics.efficiencyMetrics.maxDailyKm) === null || _analytics$efficiency9 === void 0 ? void 0 : _analytics$efficiency9.toFixed(0)) || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                color: \"warning.main\",\n                children: [((_analytics$efficiency0 = analytics.efficiencyMetrics.totalKm) === null || _analytics$efficiency0 === void 0 ? void 0 : _analytics$efficiency0.toLocaleString()) || 'N/A', \" km\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Total Recorrido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Tendencias y Predicciones\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              severity: getTrendColor(analytics.predictions.consumptionDirection, false),\n              icon: getTrendIcon(analytics.predictions.consumptionDirection),\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tendencia de Consumo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this), \" \", analytics.predictions.consumptionDirection === 'increasing' ? 'Aumentando' : analytics.predictions.consumptionDirection === 'decreasing' ? 'Disminuyendo' : 'Estable']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: [analytics.predictions.consumptionTrend > 0 ? '+' : '', ((_analytics$prediction = analytics.predictions.consumptionTrend) === null || _analytics$prediction === void 0 ? void 0 : _analytics$prediction.toFixed(2)) || 0, \" L/100km por repostaje\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              severity: getTrendColor(analytics.predictions.mileageDirection, true),\n              icon: getTrendIcon(analytics.predictions.mileageDirection),\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tendencia de Uso:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 19\n                }, this), \" \", analytics.predictions.mileageDirection === 'increasing' ? 'Aumentando' : analytics.predictions.mileageDirection === 'decreasing' ? 'Disminuyendo' : 'Estable']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: [analytics.predictions.mileageTrend > 0 ? '+' : '', ((_analytics$prediction2 = analytics.predictions.mileageTrend) === null || _analytics$prediction2 === void 0 ? void 0 : _analytics$prediction2.toFixed(1)) || 0, \" km/d\\xEDa promedio\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Evoluci\\xF3n del Kilometraje\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: analytics.mileageEvolution,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\",\n                    tick: {\n                      fontSize: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                    yAxisId: \"left\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                    yAxisId: \"right\",\n                    orientation: \"right\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: (value, name) => {\n                      if (name === 'Kilometraje Total') {\n                        return [`${value.toLocaleString()} km`, 'Kilometraje Total'];\n                      } else if (name === 'Km Diarios') {\n                        return [`${value.toFixed(1)} km/día`, 'Km Diarios'];\n                      }\n                      return [value, name];\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"kilometraje\",\n                    stroke: \"#8884d8\",\n                    strokeWidth: 2,\n                    name: \"Kilometraje Total\",\n                    yAxisId: \"left\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"kmDiarios\",\n                    stroke: \"#82ca9d\",\n                    strokeWidth: 2,\n                    name: \"Km Diarios\",\n                    yAxisId: \"right\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Evoluci\\xF3n de Precios de Combustible\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                  data: analytics.fuelPriceTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\",\n                    tick: {\n                      fontSize: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [formatCurrency(value), 'Precio/Litro']\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"precio\",\n                    stroke: \"#ffc658\",\n                    fill: \"#ffc658\",\n                    fillOpacity: 0.6\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ReferenceLine, {\n                    y: analytics.fuelPriceTrends.reduce((sum, item) => sum + item.precio, 0) / analytics.fuelPriceTrends.length,\n                    stroke: \"red\",\n                    strokeDasharray: \"5 5\",\n                    label: \"Promedio\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Consumo vs Eficiencia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(ScatterChart, {\n                  data: analytics.consumptionTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"consumo\",\n                    name: \"Consumo L/100km\",\n                    tick: {\n                      fontSize: 12\n                    },\n                    tickFormatter: value => value.toFixed(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                    dataKey: \"eficiencia\",\n                    name: \"Eficiencia km/L\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: (value, name, props) => {\n                      if (name === 'eficiencia') {\n                        return [`${value.toFixed(1)} km/L`, 'Eficiencia'];\n                      }\n                      return [value, name];\n                    },\n                    labelFormatter: label => `Consumo: ${label.toFixed(1)} L/100km`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Scatter, {\n                    dataKey: \"eficiencia\",\n                    fill: \"#8884d8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Distribuci\\xF3n de Consumo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(BarChart, {\n                  data: analytics.consumptionTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\",\n                    tick: {\n                      fontSize: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [`${value.toFixed(1)} L/100km`, 'Consumo']\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                    dataKey: \"consumo\",\n                    fill: \"#82ca9d\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ReferenceLine, {\n                    y: analytics.efficiencyMetrics.avgConsumption,\n                    stroke: \"red\",\n                    strokeDasharray: \"5 5\",\n                    label: \"Promedio\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 39\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          children: \"Registros Destacados (M\\xE1ximos y M\\xEDnimos)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              color: \"success.main\",\n              children: \"Mejores Registros\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: [/*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Mejor Consumo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 34\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.bestConsumption ? `${analytics.extremeValues.bestConsumption.consumo.toFixed(1)} L/100km` : 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.bestConsumption ? formatDate(analytics.extremeValues.bestConsumption.fecha) : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Combustible M\\xE1s Barato\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 603,\n                        columnNumber: 34\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 603,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.cheapestFuel ? formatCurrency(analytics.extremeValues.cheapestFuel.precio) : 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.cheapestFuel ? formatDate(analytics.extremeValues.cheapestFuel.fecha) : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Mayor Uso Diario\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 34\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.maxDailyKm ? `${analytics.extremeValues.maxDailyKm.kmDiarios.toFixed(0)} km/día` : 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 615,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.maxDailyKm ? formatDate(analytics.extremeValues.maxDailyKm.fecha) : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 619,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              color: \"warning.main\",\n              children: \"Registros a Mejorar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: [/*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Peor Consumo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 34\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 636,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.worstConsumption ? `${analytics.extremeValues.worstConsumption.consumo.toFixed(1)} L/100km` : 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 637,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.worstConsumption ? formatDate(analytics.extremeValues.worstConsumption.fecha) : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Combustible M\\xE1s Caro\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 647,\n                        columnNumber: 34\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 647,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.expensiveFuel ? formatCurrency(analytics.extremeValues.expensiveFuel.precio) : 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.expensiveFuel ? formatDate(analytics.extremeValues.expensiveFuel.fecha) : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Menor Uso Diario\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 658,\n                        columnNumber: 34\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 658,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.minDailyKm ? `${analytics.extremeValues.minDailyKm.kmDiarios.toFixed(0)} km/día` : 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: analytics.extremeValues.minDailyKm ? formatDate(analytics.extremeValues.minDailyKm.fecha) : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 657,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 287,\n    columnNumber: 5\n  }, this);\n};\n_s(AdvancedAnalytics, \"73hHQkIRgRWYZSrMYhCip4kr5S4=\", false, function () {\n  return [useApp];\n});\n_c = AdvancedAnalytics;\nexport default AdvancedAnalytics;\nvar _c;\n$RefreshReg$(_c, \"AdvancedAnalytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "Accordion", "AccordionSummary", "AccordionDetails", "<PERSON><PERSON>", "LinearProgress", "ExpandMore", "ExpandMoreIcon", "TrendingUp", "TrendingUpIcon", "TrendingDown", "TrendingDownIcon", "Speed", "SpeedIcon", "LocalGasStation", "GasIcon", "Line<PERSON>hart", "Line", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ChartTooltip", "Legend", "ResponsiveContainer", "ReferenceLine", "useApp", "format", "differenceInDays", "parseISO", "es", "jsxDEV", "_jsxDEV", "AdvancedAnalytics", "_s", "_analytics$efficiency", "_analytics$efficiency2", "_analytics$efficiency3", "_analytics$efficiency4", "_analytics$efficiency5", "_analytics$efficiency6", "_analytics$efficiency7", "_analytics$efficiency8", "_analytics$efficiency9", "_analytics$efficiency0", "_analytics$prediction", "_analytics$prediction2", "vehicles", "refuels", "expenses", "analytics", "setAnalytics", "mileageEvolution", "consumptionTrends", "fuelPriceTrends", "efficiencyMetrics", "periodicAnalysis", "extremeValues", "predictions", "calculateAdvancedAnalytics", "length", "sortedRefuels", "sort", "a", "b", "Date", "fecha", "map", "refuel", "index", "prevRefuel", "kmDiff", "kilometros_actuales", "daysDiff", "dailyKm", "kilometraje", "kmRecorridos", "diasTranscurridos", "kmDiarios", "vehiculo", "vehiculo_nombre", "month", "locale", "i", "current", "previous", "vehiculo_id", "consumption", "litros", "efficiency", "push", "consumo", "eficiencia", "kilometros", "precio", "precio_litro", "costeTotal", "coste_total", "gasolinera", "calculateEfficiencyMetrics", "calculatePeriodicAnalysis", "calculateExtremeValues", "calculatePredictions", "consumptions", "t", "filter", "c", "efficiencies", "e", "dailyKms", "m", "k", "avgConsumption", "reduce", "minConsumption", "Math", "min", "maxConsumption", "max", "avgEfficiency", "minEfficiency", "maxEfficiency", "avgDailyKm", "minDailyKm", "maxDailyKm", "totalKm", "monthlyData", "for<PERSON>ach", "totalLiters", "totalCost", "avgPrice", "trend", "Object", "keys", "data", "prices", "p", "bestConsumption", "worstConsumption", "cheapestFuel", "expensiveFuel", "recentConsumption", "slice", "consumptionTrend", "recentMileage", "mileageTrend", "consumptionDirection", "mileageDirection", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "getTrendIcon", "direction", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTrendColor", "isGood", "children", "variant", "gutterBottom", "sx", "mb", "container", "spacing", "item", "xs", "sm", "md", "textAlign", "toFixed", "toLocaleString", "severity", "icon", "lg", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick", "fontSize", "yAxisId", "orientation", "formatter", "value", "name", "type", "stroke", "strokeWidth", "fill", "fillOpacity", "y", "sum", "label", "tick<PERSON><PERSON><PERSON><PERSON>", "props", "labelFormatter", "expandIcon", "component", "size", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Statistics/AdvancedAnalytics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Card,\r\n  CardContent,\r\n  Grid,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Paper,\r\n  Chip,\r\n  Accordion,\r\n  AccordionSummary,\r\n  AccordionDetails,\r\n  Alert,\r\n  LinearProgress,\r\n} from '@mui/material';\r\nimport {\r\n  ExpandMore as ExpandMoreIcon,\r\n  TrendingUp as TrendingUpIcon,\r\n  TrendingDown as TrendingDownIcon,\r\n  Speed as SpeedIcon,\r\n  LocalGasStation as GasIcon,\r\n} from '@mui/icons-material';\r\nimport {\r\n  LineChart,\r\n  Line,\r\n  AreaChart,\r\n  Area,\r\n  BarChart,\r\n  Bar,\r\n  ScatterChart,\r\n  Scatter,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  Tooltip as ChartTooltip,\r\n  Legend,\r\n  ResponsiveContainer,\r\n  ReferenceLine,\r\n} from 'recharts';\r\nimport { useApp } from '../../context/AppContext';\r\nimport { format, differenceInDays, parseISO } from 'date-fns';\r\nimport { es } from 'date-fns/locale';\r\n\r\nconst AdvancedAnalytics = () => {\r\n  const { vehicles, refuels, expenses } = useApp();\r\n  const [analytics, setAnalytics] = useState({\r\n    mileageEvolution: [],\r\n    consumptionTrends: [],\r\n    fuelPriceTrends: [],\r\n    efficiencyMetrics: {},\r\n    periodicAnalysis: {},\r\n    extremeValues: {},\r\n    predictions: {}\r\n  });\r\n\r\n  useEffect(() => {\r\n    calculateAdvancedAnalytics();\r\n  }, [refuels, expenses, vehicles]);\r\n\r\n  const calculateAdvancedAnalytics = () => {\r\n    if (refuels.length === 0) return;\r\n\r\n    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\r\n\r\n    // 1. Evolución del kilometraje\r\n    const mileageEvolution = sortedRefuels.map((refuel, index) => {\r\n      const prevRefuel = index > 0 ? sortedRefuels[index - 1] : null;\r\n      const kmDiff = prevRefuel ? refuel.kilometros_actuales - prevRefuel.kilometros_actuales : 0;\r\n      const daysDiff = prevRefuel ? differenceInDays(new Date(refuel.fecha), new Date(prevRefuel.fecha)) : 0;\r\n      const dailyKm = daysDiff > 0 ? kmDiff / daysDiff : 0;\r\n\r\n      return {\r\n        fecha: refuel.fecha,\r\n        kilometraje: refuel.kilometros_actuales,\r\n        kmRecorridos: kmDiff,\r\n        diasTranscurridos: daysDiff,\r\n        kmDiarios: dailyKm,\r\n        vehiculo: refuel.vehiculo_nombre,\r\n        month: format(new Date(refuel.fecha), 'MMM yyyy', { locale: es })\r\n      };\r\n    });\r\n\r\n    // 2. Tendencias de consumo\r\n    const consumptionTrends = [];\r\n    for (let i = 1; i < sortedRefuels.length; i++) {\r\n      const current = sortedRefuels[i];\r\n      const previous = sortedRefuels[i - 1];\r\n\r\n      if (current.vehiculo_id === previous.vehiculo_id) {\r\n        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;\r\n        const consumption = kmDiff > 0 ? (current.litros / kmDiff) * 100 : 0;\r\n        const efficiency = kmDiff > 0 ? kmDiff / current.litros : 0;\r\n\r\n        consumptionTrends.push({\r\n          fecha: current.fecha,\r\n          consumo: consumption,\r\n          eficiencia: efficiency,\r\n          litros: current.litros,\r\n          kilometros: kmDiff,\r\n          vehiculo: current.vehiculo_nombre,\r\n          month: format(new Date(current.fecha), 'MMM yyyy', { locale: es })\r\n        });\r\n      }\r\n    }\r\n\r\n    // 3. Tendencias de precios de combustible\r\n    const fuelPriceTrends = sortedRefuels.map(refuel => ({\r\n      fecha: refuel.fecha,\r\n      precio: refuel.precio_litro,\r\n      costeTotal: refuel.coste_total,\r\n      litros: refuel.litros,\r\n      gasolinera: refuel.gasolinera,\r\n      month: format(new Date(refuel.fecha), 'MMM yyyy', { locale: es })\r\n    }));\r\n\r\n    // 4. Métricas de eficiencia\r\n    const efficiencyMetrics = calculateEfficiencyMetrics(consumptionTrends, mileageEvolution);\r\n\r\n    // 5. Análisis periódico\r\n    const periodicAnalysis = calculatePeriodicAnalysis(sortedRefuels, consumptionTrends);\r\n\r\n    // 6. Valores extremos\r\n    const extremeValues = calculateExtremeValues(consumptionTrends, fuelPriceTrends, mileageEvolution);\r\n\r\n    // 7. Predicciones simples\r\n    const predictions = calculatePredictions(consumptionTrends, mileageEvolution);\r\n\r\n    setAnalytics({\r\n      mileageEvolution,\r\n      consumptionTrends,\r\n      fuelPriceTrends,\r\n      efficiencyMetrics,\r\n      periodicAnalysis,\r\n      extremeValues,\r\n      predictions\r\n    });\r\n  };\r\n\r\n  const calculateEfficiencyMetrics = (consumptionTrends, mileageEvolution) => {\r\n    if (consumptionTrends.length === 0) return {};\r\n\r\n    const consumptions = consumptionTrends.map(t => t.consumo).filter(c => c > 0);\r\n    const efficiencies = consumptionTrends.map(t => t.eficiencia).filter(e => e > 0);\r\n    const dailyKms = mileageEvolution.map(m => m.kmDiarios).filter(k => k > 0);\r\n\r\n    return {\r\n      avgConsumption: consumptions.reduce((a, b) => a + b, 0) / consumptions.length,\r\n      minConsumption: Math.min(...consumptions),\r\n      maxConsumption: Math.max(...consumptions),\r\n      avgEfficiency: efficiencies.reduce((a, b) => a + b, 0) / efficiencies.length,\r\n      minEfficiency: Math.min(...efficiencies),\r\n      maxEfficiency: Math.max(...efficiencies),\r\n      avgDailyKm: dailyKms.reduce((a, b) => a + b, 0) / dailyKms.length,\r\n      minDailyKm: Math.min(...dailyKms),\r\n      maxDailyKm: Math.max(...dailyKms),\r\n      totalKm: mileageEvolution.length > 0 ?\r\n        mileageEvolution[mileageEvolution.length - 1].kilometraje - mileageEvolution[0].kilometraje : 0\r\n    };\r\n  };\r\n\r\n  const calculatePeriodicAnalysis = (refuels, consumptionTrends) => {\r\n    // Análisis por mes\r\n    const monthlyData = {};\r\n\r\n    refuels.forEach(refuel => {\r\n      const month = format(new Date(refuel.fecha), 'yyyy-MM');\r\n      if (!monthlyData[month]) {\r\n        monthlyData[month] = {\r\n          refuels: 0,\r\n          totalLiters: 0,\r\n          totalCost: 0,\r\n          avgPrice: 0,\r\n          consumption: []\r\n        };\r\n      }\r\n      monthlyData[month].refuels++;\r\n      monthlyData[month].totalLiters += refuel.litros;\r\n      monthlyData[month].totalCost += refuel.coste_total;\r\n    });\r\n\r\n    consumptionTrends.forEach(trend => {\r\n      const month = format(new Date(trend.fecha), 'yyyy-MM');\r\n      if (monthlyData[month]) {\r\n        monthlyData[month].consumption.push(trend.consumo);\r\n      }\r\n    });\r\n\r\n    // Calcular promedios mensuales\r\n    Object.keys(monthlyData).forEach(month => {\r\n      const data = monthlyData[month];\r\n      data.avgPrice = data.totalLiters > 0 ? data.totalCost / data.totalLiters : 0;\r\n      data.avgConsumption = data.consumption.length > 0 ?\r\n        data.consumption.reduce((a, b) => a + b, 0) / data.consumption.length : 0;\r\n    });\r\n\r\n    return { monthlyData };\r\n  };\r\n\r\n  const calculateExtremeValues = (consumptionTrends, fuelPriceTrends, mileageEvolution) => {\r\n    if (consumptionTrends.length === 0 || fuelPriceTrends.length === 0) return {};\r\n\r\n    const consumptions = consumptionTrends.filter(t => t.consumo > 0);\r\n    const prices = fuelPriceTrends.filter(p => p.precio > 0);\r\n    const dailyKms = mileageEvolution.filter(m => m.kmDiarios > 0);\r\n\r\n    const bestConsumption = consumptions.reduce((min, current) =>\r\n      current.consumo < min.consumo ? current : min, consumptions[0]);\r\n    const worstConsumption = consumptions.reduce((max, current) =>\r\n      current.consumo > max.consumo ? current : max, consumptions[0]);\r\n\r\n    const cheapestFuel = prices.reduce((min, current) =>\r\n      current.precio < min.precio ? current : min, prices[0]);\r\n    const expensiveFuel = prices.reduce((max, current) =>\r\n      current.precio > max.precio ? current : max, prices[0]);\r\n\r\n    const maxDailyKm = dailyKms.reduce((max, current) =>\r\n      current.kmDiarios > max.kmDiarios ? current : max, dailyKms[0] || {});\r\n    const minDailyKm = dailyKms.reduce((min, current) =>\r\n      current.kmDiarios < min.kmDiarios ? current : min, dailyKms[0] || {});\r\n\r\n    return {\r\n      bestConsumption,\r\n      worstConsumption,\r\n      cheapestFuel,\r\n      expensiveFuel,\r\n      maxDailyKm,\r\n      minDailyKm\r\n    };\r\n  };\r\n\r\n  const calculatePredictions = (consumptionTrends, mileageEvolution) => {\r\n    if (consumptionTrends.length < 3) return {};\r\n\r\n    // Tendencia de consumo (últimos 6 registros)\r\n    const recentConsumption = consumptionTrends.slice(-6);\r\n    const consumptionTrend = recentConsumption.length > 1 ?\r\n      (recentConsumption[recentConsumption.length - 1].consumo - recentConsumption[0].consumo) / recentConsumption.length : 0;\r\n\r\n    // Tendencia de kilometraje diario\r\n    const recentMileage = mileageEvolution.slice(-6).filter(m => m.kmDiarios > 0);\r\n    const mileageTrend = recentMileage.length > 1 ?\r\n      (recentMileage[recentMileage.length - 1].kmDiarios - recentMileage[0].kmDiarios) / recentMileage.length : 0;\r\n\r\n    return {\r\n      consumptionTrend,\r\n      mileageTrend,\r\n      consumptionDirection: consumptionTrend > 0.1 ? 'increasing' : consumptionTrend < -0.1 ? 'decreasing' : 'stable',\r\n      mileageDirection: mileageTrend > 1 ? 'increasing' : mileageTrend < -1 ? 'decreasing' : 'stable'\r\n    };\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    return new Intl.NumberFormat('es-ES', {\r\n      style: 'currency',\r\n      currency: 'EUR'\r\n    }).format(amount || 0);\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    try {\r\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\r\n    } catch {\r\n      return dateString;\r\n    }\r\n  };\r\n\r\n  const getTrendIcon = (direction) => {\r\n    switch (direction) {\r\n      case 'increasing': return <TrendingUpIcon color=\"error\" />;\r\n      case 'decreasing': return <TrendingDownIcon color=\"success\" />;\r\n      default: return <SpeedIcon color=\"info\" />;\r\n    }\r\n  };\r\n\r\n  const getTrendColor = (direction, isGood) => {\r\n    if (direction === 'stable') return 'info';\r\n    return (direction === 'increasing') === isGood ? 'success' : 'warning';\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h6\" gutterBottom>\r\n        Análisis Avanzado\r\n      </Typography>\r\n\r\n      {/* Métricas de eficiencia */}\r\n      <Card sx={{ mb: 3 }}>\r\n        <CardContent>\r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Métricas de Rendimiento\r\n          </Typography>\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h5\" color=\"primary\">\r\n                  {analytics.efficiencyMetrics.avgConsumption?.toFixed(1) || 'N/A'} L\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Consumo Promedio/100km\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"textSecondary\">\r\n                  Min: {analytics.efficiencyMetrics.minConsumption?.toFixed(1) || 'N/A'} |\r\n                  Max: {analytics.efficiencyMetrics.maxConsumption?.toFixed(1) || 'N/A'}\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h5\" color=\"success.main\">\r\n                  {analytics.efficiencyMetrics.avgEfficiency?.toFixed(1) || 'N/A'} km/L\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Eficiencia Promedio\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"textSecondary\">\r\n                  Min: {analytics.efficiencyMetrics.minEfficiency?.toFixed(1) || 'N/A'} |\r\n                  Max: {analytics.efficiencyMetrics.maxEfficiency?.toFixed(1) || 'N/A'}\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h5\" color=\"info.main\">\r\n                  {analytics.efficiencyMetrics.avgDailyKm?.toFixed(0) || 'N/A'} km\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Kilometraje Diario Promedio\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"textSecondary\">\r\n                  Min: {analytics.efficiencyMetrics.minDailyKm?.toFixed(0) || 'N/A'} |\r\n                  Max: {analytics.efficiencyMetrics.maxDailyKm?.toFixed(0) || 'N/A'}\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h5\" color=\"warning.main\">\r\n                  {analytics.efficiencyMetrics.totalKm?.toLocaleString() || 'N/A'} km\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Total Recorrido\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Tendencias y predicciones */}\r\n      <Card sx={{ mb: 3 }}>\r\n        <CardContent>\r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Tendencias y Predicciones\r\n          </Typography>\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={12} sm={6}>\r\n              <Alert\r\n                severity={getTrendColor(analytics.predictions.consumptionDirection, false)}\r\n                icon={getTrendIcon(analytics.predictions.consumptionDirection)}\r\n              >\r\n                <Typography variant=\"body2\">\r\n                  <strong>Tendencia de Consumo:</strong> {\r\n                    analytics.predictions.consumptionDirection === 'increasing' ? 'Aumentando' :\r\n                      analytics.predictions.consumptionDirection === 'decreasing' ? 'Disminuyendo' :\r\n                        'Estable'\r\n                  }\r\n                </Typography>\r\n                <Typography variant=\"caption\">\r\n                  {analytics.predictions.consumptionTrend > 0 ? '+' : ''}\r\n                  {analytics.predictions.consumptionTrend?.toFixed(2) || 0} L/100km por repostaje\r\n                </Typography>\r\n              </Alert>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6}>\r\n              <Alert\r\n                severity={getTrendColor(analytics.predictions.mileageDirection, true)}\r\n                icon={getTrendIcon(analytics.predictions.mileageDirection)}\r\n              >\r\n                <Typography variant=\"body2\">\r\n                  <strong>Tendencia de Uso:</strong> {\r\n                    analytics.predictions.mileageDirection === 'increasing' ? 'Aumentando' :\r\n                      analytics.predictions.mileageDirection === 'decreasing' ? 'Disminuyendo' :\r\n                        'Estable'\r\n                  }\r\n                </Typography>\r\n                <Typography variant=\"caption\">\r\n                  {analytics.predictions.mileageTrend > 0 ? '+' : ''}\r\n                  {analytics.predictions.mileageTrend?.toFixed(1) || 0} km/día promedio\r\n                </Typography>\r\n              </Alert>\r\n            </Grid>\r\n          </Grid>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Gráficos avanzados */}\r\n      <Grid container spacing={3} sx={{ mb: 3 }}>\r\n        {/* Evolución del kilometraje */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"subtitle1\" gutterBottom>\r\n                Evolución del Kilometraje\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <LineChart data={analytics.mileageEvolution}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis\r\n                      dataKey=\"month\"\r\n                      tick={{ fontSize: 12 }}\r\n                    />\r\n                    <YAxis yAxisId=\"left\" />\r\n                    <YAxis yAxisId=\"right\" orientation=\"right\" />\r\n                    <ChartTooltip\r\n                      formatter={(value, name) => {\r\n                        if (name === 'Kilometraje Total') {\r\n                          return [`${value.toLocaleString()} km`, 'Kilometraje Total'];\r\n                        } else if (name === 'Km Diarios') {\r\n                          return [`${value.toFixed(1)} km/día`, 'Km Diarios'];\r\n                        }\r\n                        return [value, name];\r\n                      }}\r\n                    />\r\n                    <Legend />\r\n                    <Line\r\n                      type=\"monotone\"\r\n                      dataKey=\"kilometraje\"\r\n                      stroke=\"#8884d8\"\r\n                      strokeWidth={2}\r\n                      name=\"Kilometraje Total\"\r\n                      yAxisId=\"left\"\r\n                    />\r\n                    <Line\r\n                      type=\"monotone\"\r\n                      dataKey=\"kmDiarios\"\r\n                      stroke=\"#82ca9d\"\r\n                      strokeWidth={2}\r\n                      name=\"Km Diarios\"\r\n                      yAxisId=\"right\"\r\n                    />\r\n                  </LineChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Tendencia de precios */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"subtitle1\" gutterBottom>\r\n                Evolución de Precios de Combustible\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <AreaChart data={analytics.fuelPriceTrends}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis\r\n                      dataKey=\"month\"\r\n                      tick={{ fontSize: 12 }}\r\n                    />\r\n                    <YAxis />\r\n                    <ChartTooltip\r\n                      formatter={(value) => [formatCurrency(value), 'Precio/Litro']}\r\n                    />\r\n                    <Area\r\n                      type=\"monotone\"\r\n                      dataKey=\"precio\"\r\n                      stroke=\"#ffc658\"\r\n                      fill=\"#ffc658\"\r\n                      fillOpacity={0.6}\r\n                    />\r\n                    <ReferenceLine\r\n                      y={analytics.fuelPriceTrends.reduce((sum, item) => sum + item.precio, 0) / analytics.fuelPriceTrends.length}\r\n                      stroke=\"red\"\r\n                      strokeDasharray=\"5 5\"\r\n                      label=\"Promedio\"\r\n                    />\r\n                  </AreaChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Análisis de consumo vs eficiencia */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"subtitle1\" gutterBottom>\r\n                Consumo vs Eficiencia\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <ScatterChart data={analytics.consumptionTrends}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis\r\n                      dataKey=\"consumo\"\r\n                      name=\"Consumo L/100km\"\r\n                      tick={{ fontSize: 12 }}\r\n                      tickFormatter={(value) => value.toFixed(1)}\r\n                    />\r\n                    <YAxis\r\n                      dataKey=\"eficiencia\"\r\n                      name=\"Eficiencia km/L\"\r\n                    />\r\n                    <ChartTooltip\r\n                      formatter={(value, name, props) => {\r\n                        if (name === 'eficiencia') {\r\n                          return [\r\n                            `${value.toFixed(1)} km/L`,\r\n                            'Eficiencia'\r\n                          ];\r\n                        }\r\n                        return [value, name];\r\n                      }}\r\n                      labelFormatter={(label) => `Consumo: ${label.toFixed(1)} L/100km`}\r\n                    />\r\n                    <Scatter\r\n                      dataKey=\"eficiencia\"\r\n                      fill=\"#8884d8\"\r\n                    />\r\n                  </ScatterChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Distribución de consumo */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"subtitle1\" gutterBottom>\r\n                Distribución de Consumo\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <BarChart data={analytics.consumptionTrends}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis\r\n                      dataKey=\"month\"\r\n                      tick={{ fontSize: 12 }}\r\n                    />\r\n                    <YAxis />\r\n                    <ChartTooltip\r\n                      formatter={(value) => [`${value.toFixed(1)} L/100km`, 'Consumo']}\r\n                    />\r\n                    <Bar\r\n                      dataKey=\"consumo\"\r\n                      fill=\"#82ca9d\"\r\n                    />\r\n                    <ReferenceLine\r\n                      y={analytics.efficiencyMetrics.avgConsumption}\r\n                      stroke=\"red\"\r\n                      strokeDasharray=\"5 5\"\r\n                      label=\"Promedio\"\r\n                    />\r\n                  </BarChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n\r\n      {/* Valores extremos */}\r\n      <Accordion>\r\n        <AccordionSummary expandIcon={<ExpandMoreIcon />}>\r\n          <Typography variant=\"subtitle1\">\r\n            Registros Destacados (Máximos y Mínimos)\r\n          </Typography>\r\n        </AccordionSummary>\r\n        <AccordionDetails>\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={12} md={6}>\r\n              <Typography variant=\"subtitle2\" gutterBottom color=\"success.main\">\r\n                Mejores Registros\r\n              </Typography>\r\n              <TableContainer component={Paper} variant=\"outlined\">\r\n                <Table size=\"small\">\r\n                  <TableBody>\r\n                    <TableRow>\r\n                      <TableCell><strong>Mejor Consumo</strong></TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.bestConsumption ?\r\n                          `${analytics.extremeValues.bestConsumption.consumo.toFixed(1)} L/100km` : 'N/A'}\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.bestConsumption ?\r\n                          formatDate(analytics.extremeValues.bestConsumption.fecha) : ''}\r\n                      </TableCell>\r\n                    </TableRow>\r\n                    <TableRow>\r\n                      <TableCell><strong>Combustible Más Barato</strong></TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.cheapestFuel ?\r\n                          formatCurrency(analytics.extremeValues.cheapestFuel.precio) : 'N/A'}\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.cheapestFuel ?\r\n                          formatDate(analytics.extremeValues.cheapestFuel.fecha) : ''}\r\n                      </TableCell>\r\n                    </TableRow>\r\n                    <TableRow>\r\n                      <TableCell><strong>Mayor Uso Diario</strong></TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.maxDailyKm ?\r\n                          `${analytics.extremeValues.maxDailyKm.kmDiarios.toFixed(0)} km/día` : 'N/A'}\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.maxDailyKm ?\r\n                          formatDate(analytics.extremeValues.maxDailyKm.fecha) : ''}\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  </TableBody>\r\n                </Table>\r\n              </TableContainer>\r\n            </Grid>\r\n            <Grid item xs={12} md={6}>\r\n              <Typography variant=\"subtitle2\" gutterBottom color=\"warning.main\">\r\n                Registros a Mejorar\r\n              </Typography>\r\n              <TableContainer component={Paper} variant=\"outlined\">\r\n                <Table size=\"small\">\r\n                  <TableBody>\r\n                    <TableRow>\r\n                      <TableCell><strong>Peor Consumo</strong></TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.worstConsumption ?\r\n                          `${analytics.extremeValues.worstConsumption.consumo.toFixed(1)} L/100km` : 'N/A'}\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.worstConsumption ?\r\n                          formatDate(analytics.extremeValues.worstConsumption.fecha) : ''}\r\n                      </TableCell>\r\n                    </TableRow>\r\n                    <TableRow>\r\n                      <TableCell><strong>Combustible Más Caro</strong></TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.expensiveFuel ?\r\n                          formatCurrency(analytics.extremeValues.expensiveFuel.precio) : 'N/A'}\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.expensiveFuel ?\r\n                          formatDate(analytics.extremeValues.expensiveFuel.fecha) : ''}\r\n                      </TableCell>\r\n                    </TableRow>\r\n                    <TableRow>\r\n                      <TableCell><strong>Menor Uso Diario</strong></TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.minDailyKm ?\r\n                          `${analytics.extremeValues.minDailyKm.kmDiarios.toFixed(0)} km/día` : 'N/A'}\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        {analytics.extremeValues.minDailyKm ?\r\n                          formatDate(analytics.extremeValues.minDailyKm.fecha) : ''}\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  </TableBody>\r\n                </Table>\r\n              </TableContainer>\r\n            </Grid>\r\n          </Grid>\r\n        </AccordionDetails>\r\n      </Accordion>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AdvancedAnalytics;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,KAAK,EACLC,cAAc,QACT,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,eAAe,IAAIC,OAAO,QACrB,qBAAqB;AAC5B,SACEC,SAAS,EACTC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,IAAIC,YAAY,EACvBC,MAAM,EACNC,mBAAmB,EACnBC,aAAa,QACR,UAAU;AACjB,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,UAAU;AAC7D,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAC9B,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC;EAAS,CAAC,GAAGvB,MAAM,CAAC,CAAC;EAChD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC;IACzCwE,gBAAgB,EAAE,EAAE;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,iBAAiB,EAAE,CAAC,CAAC;IACrBC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,aAAa,EAAE,CAAC,CAAC;IACjBC,WAAW,EAAE,CAAC;EAChB,CAAC,CAAC;EAEF7E,SAAS,CAAC,MAAM;IACd8E,0BAA0B,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACX,OAAO,EAAEC,QAAQ,EAAEF,QAAQ,CAAC,CAAC;EAEjC,MAAMY,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAIX,OAAO,CAACY,MAAM,KAAK,CAAC,EAAE;IAE1B,MAAMC,aAAa,GAAG,CAAC,GAAGb,OAAO,CAAC,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACF,CAAC,CAACG,KAAK,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,KAAK,CAAC,CAAC;;IAExF;IACA,MAAMd,gBAAgB,GAAGS,aAAa,CAACM,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MAC5D,MAAMC,UAAU,GAAGD,KAAK,GAAG,CAAC,GAAGR,aAAa,CAACQ,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;MAC9D,MAAME,MAAM,GAAGD,UAAU,GAAGF,MAAM,CAACI,mBAAmB,GAAGF,UAAU,CAACE,mBAAmB,GAAG,CAAC;MAC3F,MAAMC,QAAQ,GAAGH,UAAU,GAAG1C,gBAAgB,CAAC,IAAIqC,IAAI,CAACG,MAAM,CAACF,KAAK,CAAC,EAAE,IAAID,IAAI,CAACK,UAAU,CAACJ,KAAK,CAAC,CAAC,GAAG,CAAC;MACtG,MAAMQ,OAAO,GAAGD,QAAQ,GAAG,CAAC,GAAGF,MAAM,GAAGE,QAAQ,GAAG,CAAC;MAEpD,OAAO;QACLP,KAAK,EAAEE,MAAM,CAACF,KAAK;QACnBS,WAAW,EAAEP,MAAM,CAACI,mBAAmB;QACvCI,YAAY,EAAEL,MAAM;QACpBM,iBAAiB,EAAEJ,QAAQ;QAC3BK,SAAS,EAAEJ,OAAO;QAClBK,QAAQ,EAAEX,MAAM,CAACY,eAAe;QAChCC,KAAK,EAAEtD,MAAM,CAAC,IAAIsC,IAAI,CAACG,MAAM,CAACF,KAAK,CAAC,EAAE,UAAU,EAAE;UAAEgB,MAAM,EAAEpD;QAAG,CAAC;MAClE,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMuB,iBAAiB,GAAG,EAAE;IAC5B,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,aAAa,CAACD,MAAM,EAAEuB,CAAC,EAAE,EAAE;MAC7C,MAAMC,OAAO,GAAGvB,aAAa,CAACsB,CAAC,CAAC;MAChC,MAAME,QAAQ,GAAGxB,aAAa,CAACsB,CAAC,GAAG,CAAC,CAAC;MAErC,IAAIC,OAAO,CAACE,WAAW,KAAKD,QAAQ,CAACC,WAAW,EAAE;QAChD,MAAMf,MAAM,GAAGa,OAAO,CAACZ,mBAAmB,GAAGa,QAAQ,CAACb,mBAAmB;QACzE,MAAMe,WAAW,GAAGhB,MAAM,GAAG,CAAC,GAAIa,OAAO,CAACI,MAAM,GAAGjB,MAAM,GAAI,GAAG,GAAG,CAAC;QACpE,MAAMkB,UAAU,GAAGlB,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAGa,OAAO,CAACI,MAAM,GAAG,CAAC;QAE3DnC,iBAAiB,CAACqC,IAAI,CAAC;UACrBxB,KAAK,EAAEkB,OAAO,CAAClB,KAAK;UACpByB,OAAO,EAAEJ,WAAW;UACpBK,UAAU,EAAEH,UAAU;UACtBD,MAAM,EAAEJ,OAAO,CAACI,MAAM;UACtBK,UAAU,EAAEtB,MAAM;UAClBQ,QAAQ,EAAEK,OAAO,CAACJ,eAAe;UACjCC,KAAK,EAAEtD,MAAM,CAAC,IAAIsC,IAAI,CAACmB,OAAO,CAAClB,KAAK,CAAC,EAAE,UAAU,EAAE;YAAEgB,MAAM,EAAEpD;UAAG,CAAC;QACnE,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,MAAMwB,eAAe,GAAGO,aAAa,CAACM,GAAG,CAACC,MAAM,KAAK;MACnDF,KAAK,EAAEE,MAAM,CAACF,KAAK;MACnB4B,MAAM,EAAE1B,MAAM,CAAC2B,YAAY;MAC3BC,UAAU,EAAE5B,MAAM,CAAC6B,WAAW;MAC9BT,MAAM,EAAEpB,MAAM,CAACoB,MAAM;MACrBU,UAAU,EAAE9B,MAAM,CAAC8B,UAAU;MAC7BjB,KAAK,EAAEtD,MAAM,CAAC,IAAIsC,IAAI,CAACG,MAAM,CAACF,KAAK,CAAC,EAAE,UAAU,EAAE;QAAEgB,MAAM,EAAEpD;MAAG,CAAC;IAClE,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMyB,iBAAiB,GAAG4C,0BAA0B,CAAC9C,iBAAiB,EAAED,gBAAgB,CAAC;;IAEzF;IACA,MAAMI,gBAAgB,GAAG4C,yBAAyB,CAACvC,aAAa,EAAER,iBAAiB,CAAC;;IAEpF;IACA,MAAMI,aAAa,GAAG4C,sBAAsB,CAAChD,iBAAiB,EAAEC,eAAe,EAAEF,gBAAgB,CAAC;;IAElG;IACA,MAAMM,WAAW,GAAG4C,oBAAoB,CAACjD,iBAAiB,EAAED,gBAAgB,CAAC;IAE7ED,YAAY,CAAC;MACXC,gBAAgB;MAChBC,iBAAiB;MACjBC,eAAe;MACfC,iBAAiB;MACjBC,gBAAgB;MAChBC,aAAa;MACbC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyC,0BAA0B,GAAGA,CAAC9C,iBAAiB,EAAED,gBAAgB,KAAK;IAC1E,IAAIC,iBAAiB,CAACO,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;IAE7C,MAAM2C,YAAY,GAAGlD,iBAAiB,CAACc,GAAG,CAACqC,CAAC,IAAIA,CAAC,CAACb,OAAO,CAAC,CAACc,MAAM,CAACC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IAC7E,MAAMC,YAAY,GAAGtD,iBAAiB,CAACc,GAAG,CAACqC,CAAC,IAAIA,CAAC,CAACZ,UAAU,CAAC,CAACa,MAAM,CAACG,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IAChF,MAAMC,QAAQ,GAAGzD,gBAAgB,CAACe,GAAG,CAAC2C,CAAC,IAAIA,CAAC,CAAChC,SAAS,CAAC,CAAC2B,MAAM,CAACM,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IAE1E,OAAO;MACLC,cAAc,EAAET,YAAY,CAACU,MAAM,CAAC,CAAClD,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGuC,YAAY,CAAC3C,MAAM;MAC7EsD,cAAc,EAAEC,IAAI,CAACC,GAAG,CAAC,GAAGb,YAAY,CAAC;MACzCc,cAAc,EAAEF,IAAI,CAACG,GAAG,CAAC,GAAGf,YAAY,CAAC;MACzCgB,aAAa,EAAEZ,YAAY,CAACM,MAAM,CAAC,CAAClD,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAG2C,YAAY,CAAC/C,MAAM;MAC5E4D,aAAa,EAAEL,IAAI,CAACC,GAAG,CAAC,GAAGT,YAAY,CAAC;MACxCc,aAAa,EAAEN,IAAI,CAACG,GAAG,CAAC,GAAGX,YAAY,CAAC;MACxCe,UAAU,EAAEb,QAAQ,CAACI,MAAM,CAAC,CAAClD,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAG6C,QAAQ,CAACjD,MAAM;MACjE+D,UAAU,EAAER,IAAI,CAACC,GAAG,CAAC,GAAGP,QAAQ,CAAC;MACjCe,UAAU,EAAET,IAAI,CAACG,GAAG,CAAC,GAAGT,QAAQ,CAAC;MACjCgB,OAAO,EAAEzE,gBAAgB,CAACQ,MAAM,GAAG,CAAC,GAClCR,gBAAgB,CAACA,gBAAgB,CAACQ,MAAM,GAAG,CAAC,CAAC,CAACe,WAAW,GAAGvB,gBAAgB,CAAC,CAAC,CAAC,CAACuB,WAAW,GAAG;IAClG,CAAC;EACH,CAAC;EAED,MAAMyB,yBAAyB,GAAGA,CAACpD,OAAO,EAAEK,iBAAiB,KAAK;IAChE;IACA,MAAMyE,WAAW,GAAG,CAAC,CAAC;IAEtB9E,OAAO,CAAC+E,OAAO,CAAC3D,MAAM,IAAI;MACxB,MAAMa,KAAK,GAAGtD,MAAM,CAAC,IAAIsC,IAAI,CAACG,MAAM,CAACF,KAAK,CAAC,EAAE,SAAS,CAAC;MACvD,IAAI,CAAC4D,WAAW,CAAC7C,KAAK,CAAC,EAAE;QACvB6C,WAAW,CAAC7C,KAAK,CAAC,GAAG;UACnBjC,OAAO,EAAE,CAAC;UACVgF,WAAW,EAAE,CAAC;UACdC,SAAS,EAAE,CAAC;UACZC,QAAQ,EAAE,CAAC;UACX3C,WAAW,EAAE;QACf,CAAC;MACH;MACAuC,WAAW,CAAC7C,KAAK,CAAC,CAACjC,OAAO,EAAE;MAC5B8E,WAAW,CAAC7C,KAAK,CAAC,CAAC+C,WAAW,IAAI5D,MAAM,CAACoB,MAAM;MAC/CsC,WAAW,CAAC7C,KAAK,CAAC,CAACgD,SAAS,IAAI7D,MAAM,CAAC6B,WAAW;IACpD,CAAC,CAAC;IAEF5C,iBAAiB,CAAC0E,OAAO,CAACI,KAAK,IAAI;MACjC,MAAMlD,KAAK,GAAGtD,MAAM,CAAC,IAAIsC,IAAI,CAACkE,KAAK,CAACjE,KAAK,CAAC,EAAE,SAAS,CAAC;MACtD,IAAI4D,WAAW,CAAC7C,KAAK,CAAC,EAAE;QACtB6C,WAAW,CAAC7C,KAAK,CAAC,CAACM,WAAW,CAACG,IAAI,CAACyC,KAAK,CAACxC,OAAO,CAAC;MACpD;IACF,CAAC,CAAC;;IAEF;IACAyC,MAAM,CAACC,IAAI,CAACP,WAAW,CAAC,CAACC,OAAO,CAAC9C,KAAK,IAAI;MACxC,MAAMqD,IAAI,GAAGR,WAAW,CAAC7C,KAAK,CAAC;MAC/BqD,IAAI,CAACJ,QAAQ,GAAGI,IAAI,CAACN,WAAW,GAAG,CAAC,GAAGM,IAAI,CAACL,SAAS,GAAGK,IAAI,CAACN,WAAW,GAAG,CAAC;MAC5EM,IAAI,CAACtB,cAAc,GAAGsB,IAAI,CAAC/C,WAAW,CAAC3B,MAAM,GAAG,CAAC,GAC/C0E,IAAI,CAAC/C,WAAW,CAAC0B,MAAM,CAAC,CAAClD,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGsE,IAAI,CAAC/C,WAAW,CAAC3B,MAAM,GAAG,CAAC;IAC7E,CAAC,CAAC;IAEF,OAAO;MAAEkE;IAAY,CAAC;EACxB,CAAC;EAED,MAAMzB,sBAAsB,GAAGA,CAAChD,iBAAiB,EAAEC,eAAe,EAAEF,gBAAgB,KAAK;IACvF,IAAIC,iBAAiB,CAACO,MAAM,KAAK,CAAC,IAAIN,eAAe,CAACM,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;IAE7E,MAAM2C,YAAY,GAAGlD,iBAAiB,CAACoD,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACb,OAAO,GAAG,CAAC,CAAC;IACjE,MAAM4C,MAAM,GAAGjF,eAAe,CAACmD,MAAM,CAAC+B,CAAC,IAAIA,CAAC,CAAC1C,MAAM,GAAG,CAAC,CAAC;IACxD,MAAMe,QAAQ,GAAGzD,gBAAgB,CAACqD,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAChC,SAAS,GAAG,CAAC,CAAC;IAE9D,MAAM2D,eAAe,GAAGlC,YAAY,CAACU,MAAM,CAAC,CAACG,GAAG,EAAEhC,OAAO,KACvDA,OAAO,CAACO,OAAO,GAAGyB,GAAG,CAACzB,OAAO,GAAGP,OAAO,GAAGgC,GAAG,EAAEb,YAAY,CAAC,CAAC,CAAC,CAAC;IACjE,MAAMmC,gBAAgB,GAAGnC,YAAY,CAACU,MAAM,CAAC,CAACK,GAAG,EAAElC,OAAO,KACxDA,OAAO,CAACO,OAAO,GAAG2B,GAAG,CAAC3B,OAAO,GAAGP,OAAO,GAAGkC,GAAG,EAAEf,YAAY,CAAC,CAAC,CAAC,CAAC;IAEjE,MAAMoC,YAAY,GAAGJ,MAAM,CAACtB,MAAM,CAAC,CAACG,GAAG,EAAEhC,OAAO,KAC9CA,OAAO,CAACU,MAAM,GAAGsB,GAAG,CAACtB,MAAM,GAAGV,OAAO,GAAGgC,GAAG,EAAEmB,MAAM,CAAC,CAAC,CAAC,CAAC;IACzD,MAAMK,aAAa,GAAGL,MAAM,CAACtB,MAAM,CAAC,CAACK,GAAG,EAAElC,OAAO,KAC/CA,OAAO,CAACU,MAAM,GAAGwB,GAAG,CAACxB,MAAM,GAAGV,OAAO,GAAGkC,GAAG,EAAEiB,MAAM,CAAC,CAAC,CAAC,CAAC;IAEzD,MAAMX,UAAU,GAAGf,QAAQ,CAACI,MAAM,CAAC,CAACK,GAAG,EAAElC,OAAO,KAC9CA,OAAO,CAACN,SAAS,GAAGwC,GAAG,CAACxC,SAAS,GAAGM,OAAO,GAAGkC,GAAG,EAAET,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvE,MAAMc,UAAU,GAAGd,QAAQ,CAACI,MAAM,CAAC,CAACG,GAAG,EAAEhC,OAAO,KAC9CA,OAAO,CAACN,SAAS,GAAGsC,GAAG,CAACtC,SAAS,GAAGM,OAAO,GAAGgC,GAAG,EAAEP,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAEvE,OAAO;MACL4B,eAAe;MACfC,gBAAgB;MAChBC,YAAY;MACZC,aAAa;MACbhB,UAAU;MACVD;IACF,CAAC;EACH,CAAC;EAED,MAAMrB,oBAAoB,GAAGA,CAACjD,iBAAiB,EAAED,gBAAgB,KAAK;IACpE,IAAIC,iBAAiB,CAACO,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;;IAE3C;IACA,MAAMiF,iBAAiB,GAAGxF,iBAAiB,CAACyF,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMC,gBAAgB,GAAGF,iBAAiB,CAACjF,MAAM,GAAG,CAAC,GACnD,CAACiF,iBAAiB,CAACA,iBAAiB,CAACjF,MAAM,GAAG,CAAC,CAAC,CAAC+B,OAAO,GAAGkD,iBAAiB,CAAC,CAAC,CAAC,CAAClD,OAAO,IAAIkD,iBAAiB,CAACjF,MAAM,GAAG,CAAC;;IAEzH;IACA,MAAMoF,aAAa,GAAG5F,gBAAgB,CAAC0F,KAAK,CAAC,CAAC,CAAC,CAAC,CAACrC,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAChC,SAAS,GAAG,CAAC,CAAC;IAC7E,MAAMmE,YAAY,GAAGD,aAAa,CAACpF,MAAM,GAAG,CAAC,GAC3C,CAACoF,aAAa,CAACA,aAAa,CAACpF,MAAM,GAAG,CAAC,CAAC,CAACkB,SAAS,GAAGkE,aAAa,CAAC,CAAC,CAAC,CAAClE,SAAS,IAAIkE,aAAa,CAACpF,MAAM,GAAG,CAAC;IAE7G,OAAO;MACLmF,gBAAgB;MAChBE,YAAY;MACZC,oBAAoB,EAAEH,gBAAgB,GAAG,GAAG,GAAG,YAAY,GAAGA,gBAAgB,GAAG,CAAC,GAAG,GAAG,YAAY,GAAG,QAAQ;MAC/GI,gBAAgB,EAAEF,YAAY,GAAG,CAAC,GAAG,YAAY,GAAGA,YAAY,GAAG,CAAC,CAAC,GAAG,YAAY,GAAG;IACzF,CAAC;EACH,CAAC;EAED,MAAMG,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC9H,MAAM,CAAC0H,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOhI,MAAM,CAAC,IAAIsC,IAAI,CAAC0F,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEzE,MAAM,EAAEpD;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAO6H,UAAU;IACnB;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,SAAS,IAAK;IAClC,QAAQA,SAAS;MACf,KAAK,YAAY;QAAE,oBAAO7H,OAAA,CAAC7B,cAAc;UAAC2J,KAAK,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D,KAAK,YAAY;QAAE,oBAAOlI,OAAA,CAAC3B,gBAAgB;UAACyJ,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9D;QAAS,oBAAOlI,OAAA,CAACzB,SAAS;UAACuJ,KAAK,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC5C;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACN,SAAS,EAAEO,MAAM,KAAK;IAC3C,IAAIP,SAAS,KAAK,QAAQ,EAAE,OAAO,MAAM;IACzC,OAAQA,SAAS,KAAK,YAAY,KAAMO,MAAM,GAAG,SAAS,GAAG,SAAS;EACxE,CAAC;EAED,oBACEpI,OAAA,CAAClD,GAAG;IAAAuL,QAAA,gBACFrI,OAAA,CAACjD,UAAU;MAACuL,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGblI,OAAA,CAAChD,IAAI;MAACwL,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClBrI,OAAA,CAAC/C,WAAW;QAAAoL,QAAA,gBACVrI,OAAA,CAACjD,UAAU;UAACuL,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblI,OAAA,CAAC9C,IAAI;UAACwL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAN,QAAA,gBACzBrI,OAAA,CAAC9C,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eAC9BrI,OAAA,CAAClD,GAAG;cAACkM,SAAS,EAAC,QAAQ;cAAAX,QAAA,gBACrBrI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,IAAI;gBAACR,KAAK,EAAC,SAAS;gBAAAO,QAAA,GACrC,EAAAlI,qBAAA,GAAAe,SAAS,CAACK,iBAAiB,CAACyD,cAAc,cAAA7E,qBAAA,uBAA1CA,qBAAA,CAA4C8I,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,IACnE;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,OAAO;gBAACR,KAAK,EAAC,eAAe;gBAAAO,QAAA,EAAC;cAElD;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,SAAS;gBAACR,KAAK,EAAC,eAAe;gBAAAO,QAAA,GAAC,OAC7C,EAAC,EAAAjI,sBAAA,GAAAc,SAAS,CAACK,iBAAiB,CAAC2D,cAAc,cAAA9E,sBAAA,uBAA1CA,sBAAA,CAA4C6I,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,UACjE,EAAC,EAAA5I,sBAAA,GAAAa,SAAS,CAACK,iBAAiB,CAAC8D,cAAc,cAAAhF,sBAAA,uBAA1CA,sBAAA,CAA4C4I,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPlI,OAAA,CAAC9C,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eAC9BrI,OAAA,CAAClD,GAAG;cAACkM,SAAS,EAAC,QAAQ;cAAAX,QAAA,gBACrBrI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,IAAI;gBAACR,KAAK,EAAC,cAAc;gBAAAO,QAAA,GAC1C,EAAA/H,sBAAA,GAAAY,SAAS,CAACK,iBAAiB,CAACgE,aAAa,cAAAjF,sBAAA,uBAAzCA,sBAAA,CAA2C2I,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,OAClE;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,OAAO;gBAACR,KAAK,EAAC,eAAe;gBAAAO,QAAA,EAAC;cAElD;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,SAAS;gBAACR,KAAK,EAAC,eAAe;gBAAAO,QAAA,GAAC,OAC7C,EAAC,EAAA9H,sBAAA,GAAAW,SAAS,CAACK,iBAAiB,CAACiE,aAAa,cAAAjF,sBAAA,uBAAzCA,sBAAA,CAA2C0I,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,UAChE,EAAC,EAAAzI,sBAAA,GAAAU,SAAS,CAACK,iBAAiB,CAACkE,aAAa,cAAAjF,sBAAA,uBAAzCA,sBAAA,CAA2CyI,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPlI,OAAA,CAAC9C,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eAC9BrI,OAAA,CAAClD,GAAG;cAACkM,SAAS,EAAC,QAAQ;cAAAX,QAAA,gBACrBrI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,IAAI;gBAACR,KAAK,EAAC,WAAW;gBAAAO,QAAA,GACvC,EAAA5H,sBAAA,GAAAS,SAAS,CAACK,iBAAiB,CAACmE,UAAU,cAAAjF,sBAAA,uBAAtCA,sBAAA,CAAwCwI,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,KAC/D;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,OAAO;gBAACR,KAAK,EAAC,eAAe;gBAAAO,QAAA,EAAC;cAElD;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,SAAS;gBAACR,KAAK,EAAC,eAAe;gBAAAO,QAAA,GAAC,OAC7C,EAAC,EAAA3H,sBAAA,GAAAQ,SAAS,CAACK,iBAAiB,CAACoE,UAAU,cAAAjF,sBAAA,uBAAtCA,sBAAA,CAAwCuI,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,UAC7D,EAAC,EAAAtI,sBAAA,GAAAO,SAAS,CAACK,iBAAiB,CAACqE,UAAU,cAAAjF,sBAAA,uBAAtCA,sBAAA,CAAwCsI,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPlI,OAAA,CAAC9C,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eAC9BrI,OAAA,CAAClD,GAAG;cAACkM,SAAS,EAAC,QAAQ;cAAAX,QAAA,gBACrBrI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,IAAI;gBAACR,KAAK,EAAC,cAAc;gBAAAO,QAAA,GAC1C,EAAAzH,sBAAA,GAAAM,SAAS,CAACK,iBAAiB,CAACsE,OAAO,cAAAjF,sBAAA,uBAAnCA,sBAAA,CAAqCsI,cAAc,CAAC,CAAC,KAAI,KAAK,EAAC,KAClE;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,OAAO;gBAACR,KAAK,EAAC,eAAe;gBAAAO,QAAA,EAAC;cAElD;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPlI,OAAA,CAAChD,IAAI;MAACwL,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClBrI,OAAA,CAAC/C,WAAW;QAAAoL,QAAA,gBACVrI,OAAA,CAACjD,UAAU;UAACuL,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblI,OAAA,CAAC9C,IAAI;UAACwL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAN,QAAA,gBACzBrI,OAAA,CAAC9C,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACvBrI,OAAA,CAAClC,KAAK;cACJqL,QAAQ,EAAEhB,aAAa,CAACjH,SAAS,CAACQ,WAAW,CAACwF,oBAAoB,EAAE,KAAK,CAAE;cAC3EkC,IAAI,EAAExB,YAAY,CAAC1G,SAAS,CAACQ,WAAW,CAACwF,oBAAoB,CAAE;cAAAmB,QAAA,gBAE/DrI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAqB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EACrChH,SAAS,CAACQ,WAAW,CAACwF,oBAAoB,KAAK,YAAY,GAAG,YAAY,GACxEhG,SAAS,CAACQ,WAAW,CAACwF,oBAAoB,KAAK,YAAY,GAAG,cAAc,GAC1E,SAAS;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,SAAS;gBAAAD,QAAA,GAC1BnH,SAAS,CAACQ,WAAW,CAACqF,gBAAgB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EACrD,EAAAlG,qBAAA,GAAAK,SAAS,CAACQ,WAAW,CAACqF,gBAAgB,cAAAlG,qBAAA,uBAAtCA,qBAAA,CAAwCoI,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,wBAC3D;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACPlI,OAAA,CAAC9C,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACvBrI,OAAA,CAAClC,KAAK;cACJqL,QAAQ,EAAEhB,aAAa,CAACjH,SAAS,CAACQ,WAAW,CAACyF,gBAAgB,EAAE,IAAI,CAAE;cACtEiC,IAAI,EAAExB,YAAY,CAAC1G,SAAS,CAACQ,WAAW,CAACyF,gBAAgB,CAAE;cAAAkB,QAAA,gBAE3DrI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAiB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EACjChH,SAAS,CAACQ,WAAW,CAACyF,gBAAgB,KAAK,YAAY,GAAG,YAAY,GACpEjG,SAAS,CAACQ,WAAW,CAACyF,gBAAgB,KAAK,YAAY,GAAG,cAAc,GACtE,SAAS;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC,eACblI,OAAA,CAACjD,UAAU;gBAACuL,OAAO,EAAC,SAAS;gBAAAD,QAAA,GAC1BnH,SAAS,CAACQ,WAAW,CAACuF,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EACjD,EAAAnG,sBAAA,GAAAI,SAAS,CAACQ,WAAW,CAACuF,YAAY,cAAAnG,sBAAA,uBAAlCA,sBAAA,CAAoCmI,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,qBACvD;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPlI,OAAA,CAAC9C,IAAI;MAACwL,SAAS;MAACC,OAAO,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAExCrI,OAAA,CAAC9C,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACQ,EAAE,EAAE,CAAE;QAAAhB,QAAA,eACvBrI,OAAA,CAAChD,IAAI;UAAAqL,QAAA,eACHrI,OAAA,CAAC/C,WAAW;YAAAoL,QAAA,gBACVrI,OAAA,CAACjD,UAAU;cAACuL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblI,OAAA,CAAClD,GAAG;cAACwM,MAAM,EAAE,GAAI;cAAAjB,QAAA,eACfrI,OAAA,CAACR,mBAAmB;gBAAC+J,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAAjB,QAAA,eAC7CrI,OAAA,CAACtB,SAAS;kBAAC4H,IAAI,EAAEpF,SAAS,CAACE,gBAAiB;kBAAAiH,QAAA,gBAC1CrI,OAAA,CAACZ,aAAa;oBAACoK,eAAe,EAAC;kBAAK;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvClI,OAAA,CAACd,KAAK;oBACJuK,OAAO,EAAC,OAAO;oBACfC,IAAI,EAAE;sBAAEC,QAAQ,EAAE;oBAAG;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFlI,OAAA,CAACb,KAAK;oBAACyK,OAAO,EAAC;kBAAM;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBlI,OAAA,CAACb,KAAK;oBAACyK,OAAO,EAAC,OAAO;oBAACC,WAAW,EAAC;kBAAO;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7ClI,OAAA,CAACV,YAAY;oBACXwK,SAAS,EAAEA,CAACC,KAAK,EAAEC,IAAI,KAAK;sBAC1B,IAAIA,IAAI,KAAK,mBAAmB,EAAE;wBAChC,OAAO,CAAC,GAAGD,KAAK,CAACb,cAAc,CAAC,CAAC,KAAK,EAAE,mBAAmB,CAAC;sBAC9D,CAAC,MAAM,IAAIc,IAAI,KAAK,YAAY,EAAE;wBAChC,OAAO,CAAC,GAAGD,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,YAAY,CAAC;sBACrD;sBACA,OAAO,CAACc,KAAK,EAAEC,IAAI,CAAC;oBACtB;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFlI,OAAA,CAACT,MAAM;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVlI,OAAA,CAACrB,IAAI;oBACHsL,IAAI,EAAC,UAAU;oBACfR,OAAO,EAAC,aAAa;oBACrBS,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfH,IAAI,EAAC,mBAAmB;oBACxBJ,OAAO,EAAC;kBAAM;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFlI,OAAA,CAACrB,IAAI;oBACHsL,IAAI,EAAC,UAAU;oBACfR,OAAO,EAAC,WAAW;oBACnBS,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfH,IAAI,EAAC,YAAY;oBACjBJ,OAAO,EAAC;kBAAO;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlI,OAAA,CAAC9C,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACQ,EAAE,EAAE,CAAE;QAAAhB,QAAA,eACvBrI,OAAA,CAAChD,IAAI;UAAAqL,QAAA,eACHrI,OAAA,CAAC/C,WAAW;YAAAoL,QAAA,gBACVrI,OAAA,CAACjD,UAAU;cAACuL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblI,OAAA,CAAClD,GAAG;cAACwM,MAAM,EAAE,GAAI;cAAAjB,QAAA,eACfrI,OAAA,CAACR,mBAAmB;gBAAC+J,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAAjB,QAAA,eAC7CrI,OAAA,CAACpB,SAAS;kBAAC0H,IAAI,EAAEpF,SAAS,CAACI,eAAgB;kBAAA+G,QAAA,gBACzCrI,OAAA,CAACZ,aAAa;oBAACoK,eAAe,EAAC;kBAAK;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvClI,OAAA,CAACd,KAAK;oBACJuK,OAAO,EAAC,OAAO;oBACfC,IAAI,EAAE;sBAAEC,QAAQ,EAAE;oBAAG;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFlI,OAAA,CAACb,KAAK;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTlI,OAAA,CAACV,YAAY;oBACXwK,SAAS,EAAGC,KAAK,IAAK,CAAC3C,cAAc,CAAC2C,KAAK,CAAC,EAAE,cAAc;kBAAE;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACFlI,OAAA,CAACnB,IAAI;oBACHoL,IAAI,EAAC,UAAU;oBACfR,OAAO,EAAC,QAAQ;oBAChBS,MAAM,EAAC,SAAS;oBAChBE,IAAI,EAAC,SAAS;oBACdC,WAAW,EAAE;kBAAI;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACFlI,OAAA,CAACP,aAAa;oBACZ6K,CAAC,EAAEpJ,SAAS,CAACI,eAAe,CAAC2D,MAAM,CAAC,CAACsF,GAAG,EAAE3B,IAAI,KAAK2B,GAAG,GAAG3B,IAAI,CAAC9E,MAAM,EAAE,CAAC,CAAC,GAAG5C,SAAS,CAACI,eAAe,CAACM,MAAO;oBAC5GsI,MAAM,EAAC,KAAK;oBACZV,eAAe,EAAC,KAAK;oBACrBgB,KAAK,EAAC;kBAAU;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlI,OAAA,CAAC9C,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACQ,EAAE,EAAE,CAAE;QAAAhB,QAAA,eACvBrI,OAAA,CAAChD,IAAI;UAAAqL,QAAA,eACHrI,OAAA,CAAC/C,WAAW;YAAAoL,QAAA,gBACVrI,OAAA,CAACjD,UAAU;cAACuL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblI,OAAA,CAAClD,GAAG;cAACwM,MAAM,EAAE,GAAI;cAAAjB,QAAA,eACfrI,OAAA,CAACR,mBAAmB;gBAAC+J,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAAjB,QAAA,eAC7CrI,OAAA,CAAChB,YAAY;kBAACsH,IAAI,EAAEpF,SAAS,CAACG,iBAAkB;kBAAAgH,QAAA,gBAC9CrI,OAAA,CAACZ,aAAa;oBAACoK,eAAe,EAAC;kBAAK;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvClI,OAAA,CAACd,KAAK;oBACJuK,OAAO,EAAC,SAAS;oBACjBO,IAAI,EAAC,iBAAiB;oBACtBN,IAAI,EAAE;sBAAEC,QAAQ,EAAE;oBAAG,CAAE;oBACvBc,aAAa,EAAGV,KAAK,IAAKA,KAAK,CAACd,OAAO,CAAC,CAAC;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACFlI,OAAA,CAACb,KAAK;oBACJsK,OAAO,EAAC,YAAY;oBACpBO,IAAI,EAAC;kBAAiB;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFlI,OAAA,CAACV,YAAY;oBACXwK,SAAS,EAAEA,CAACC,KAAK,EAAEC,IAAI,EAAEU,KAAK,KAAK;sBACjC,IAAIV,IAAI,KAAK,YAAY,EAAE;wBACzB,OAAO,CACL,GAAGD,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,OAAO,EAC1B,YAAY,CACb;sBACH;sBACA,OAAO,CAACc,KAAK,EAAEC,IAAI,CAAC;oBACtB,CAAE;oBACFW,cAAc,EAAGH,KAAK,IAAK,YAAYA,KAAK,CAACvB,OAAO,CAAC,CAAC,CAAC;kBAAW;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACFlI,OAAA,CAACf,OAAO;oBACNwK,OAAO,EAAC,YAAY;oBACpBW,IAAI,EAAC;kBAAS;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlI,OAAA,CAAC9C,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACQ,EAAE,EAAE,CAAE;QAAAhB,QAAA,eACvBrI,OAAA,CAAChD,IAAI;UAAAqL,QAAA,eACHrI,OAAA,CAAC/C,WAAW;YAAAoL,QAAA,gBACVrI,OAAA,CAACjD,UAAU;cAACuL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblI,OAAA,CAAClD,GAAG;cAACwM,MAAM,EAAE,GAAI;cAAAjB,QAAA,eACfrI,OAAA,CAACR,mBAAmB;gBAAC+J,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAAjB,QAAA,eAC7CrI,OAAA,CAAClB,QAAQ;kBAACwH,IAAI,EAAEpF,SAAS,CAACG,iBAAkB;kBAAAgH,QAAA,gBAC1CrI,OAAA,CAACZ,aAAa;oBAACoK,eAAe,EAAC;kBAAK;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvClI,OAAA,CAACd,KAAK;oBACJuK,OAAO,EAAC,OAAO;oBACfC,IAAI,EAAE;sBAAEC,QAAQ,EAAE;oBAAG;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFlI,OAAA,CAACb,KAAK;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTlI,OAAA,CAACV,YAAY;oBACXwK,SAAS,EAAGC,KAAK,IAAK,CAAC,GAAGA,KAAK,CAACd,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,SAAS;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACFlI,OAAA,CAACjB,GAAG;oBACF0K,OAAO,EAAC,SAAS;oBACjBW,IAAI,EAAC;kBAAS;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFlI,OAAA,CAACP,aAAa;oBACZ6K,CAAC,EAAEpJ,SAAS,CAACK,iBAAiB,CAACyD,cAAe;oBAC9CkF,MAAM,EAAC,KAAK;oBACZV,eAAe,EAAC,KAAK;oBACrBgB,KAAK,EAAC;kBAAU;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPlI,OAAA,CAACrC,SAAS;MAAA0K,QAAA,gBACRrI,OAAA,CAACpC,gBAAgB;QAACgN,UAAU,eAAE5K,OAAA,CAAC/B,cAAc;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAG,QAAA,eAC/CrI,OAAA,CAACjD,UAAU;UAACuL,OAAO,EAAC,WAAW;UAAAD,QAAA,EAAC;QAEhC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACnBlI,OAAA,CAACnC,gBAAgB;QAAAwK,QAAA,eACfrI,OAAA,CAAC9C,IAAI;UAACwL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAN,QAAA,gBACzBrI,OAAA,CAAC9C,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBrI,OAAA,CAACjD,UAAU;cAACuL,OAAO,EAAC,WAAW;cAACC,YAAY;cAACT,KAAK,EAAC,cAAc;cAAAO,QAAA,EAAC;YAElE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblI,OAAA,CAAC1C,cAAc;cAACuN,SAAS,EAAEpN,KAAM;cAAC6K,OAAO,EAAC,UAAU;cAAAD,QAAA,eAClDrI,OAAA,CAAC7C,KAAK;gBAAC2N,IAAI,EAAC,OAAO;gBAAAzC,QAAA,eACjBrI,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,gBACRrI,OAAA,CAACxC,QAAQ;oBAAA6K,QAAA,gBACPrI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,eAACrI,OAAA;wBAAAqI,QAAA,EAAQ;sBAAa;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACrDlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACgF,eAAe,GACtC,GAAGvF,SAAS,CAACO,aAAa,CAACgF,eAAe,CAAC9C,OAAO,CAACsF,OAAO,CAAC,CAAC,CAAC,UAAU,GAAG;oBAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC,eACZlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACgF,eAAe,GACtCiB,UAAU,CAACxG,SAAS,CAACO,aAAa,CAACgF,eAAe,CAACvE,KAAK,CAAC,GAAG;oBAAE;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACXlI,OAAA,CAACxC,QAAQ;oBAAA6K,QAAA,gBACPrI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,eAACrI,OAAA;wBAAAqI,QAAA,EAAQ;sBAAsB;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9DlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACkF,YAAY,GACnCS,cAAc,CAAClG,SAAS,CAACO,aAAa,CAACkF,YAAY,CAAC7C,MAAM,CAAC,GAAG;oBAAK;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACZlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACkF,YAAY,GACnCe,UAAU,CAACxG,SAAS,CAACO,aAAa,CAACkF,YAAY,CAACzE,KAAK,CAAC,GAAG;oBAAE;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACXlI,OAAA,CAACxC,QAAQ;oBAAA6K,QAAA,gBACPrI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,eAACrI,OAAA;wBAAAqI,QAAA,EAAQ;sBAAgB;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACxDlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACmE,UAAU,GACjC,GAAG1E,SAAS,CAACO,aAAa,CAACmE,UAAU,CAAC9C,SAAS,CAACmG,OAAO,CAAC,CAAC,CAAC,SAAS,GAAG;oBAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC,eACZlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACmE,UAAU,GACjC8B,UAAU,CAACxG,SAAS,CAACO,aAAa,CAACmE,UAAU,CAAC1D,KAAK,CAAC,GAAG;oBAAE;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACPlI,OAAA,CAAC9C,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBrI,OAAA,CAACjD,UAAU;cAACuL,OAAO,EAAC,WAAW;cAACC,YAAY;cAACT,KAAK,EAAC,cAAc;cAAAO,QAAA,EAAC;YAElE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblI,OAAA,CAAC1C,cAAc;cAACuN,SAAS,EAAEpN,KAAM;cAAC6K,OAAO,EAAC,UAAU;cAAAD,QAAA,eAClDrI,OAAA,CAAC7C,KAAK;gBAAC2N,IAAI,EAAC,OAAO;gBAAAzC,QAAA,eACjBrI,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,gBACRrI,OAAA,CAACxC,QAAQ;oBAAA6K,QAAA,gBACPrI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,eAACrI,OAAA;wBAAAqI,QAAA,EAAQ;sBAAY;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACpDlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACiF,gBAAgB,GACvC,GAAGxF,SAAS,CAACO,aAAa,CAACiF,gBAAgB,CAAC/C,OAAO,CAACsF,OAAO,CAAC,CAAC,CAAC,UAAU,GAAG;oBAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC,eACZlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACiF,gBAAgB,GACvCgB,UAAU,CAACxG,SAAS,CAACO,aAAa,CAACiF,gBAAgB,CAACxE,KAAK,CAAC,GAAG;oBAAE;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACXlI,OAAA,CAACxC,QAAQ;oBAAA6K,QAAA,gBACPrI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,eAACrI,OAAA;wBAAAqI,QAAA,EAAQ;sBAAoB;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5DlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACmF,aAAa,GACpCQ,cAAc,CAAClG,SAAS,CAACO,aAAa,CAACmF,aAAa,CAAC9C,MAAM,CAAC,GAAG;oBAAK;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACZlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACmF,aAAa,GACpCc,UAAU,CAACxG,SAAS,CAACO,aAAa,CAACmF,aAAa,CAAC1E,KAAK,CAAC,GAAG;oBAAE;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACXlI,OAAA,CAACxC,QAAQ;oBAAA6K,QAAA,gBACPrI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,eAACrI,OAAA;wBAAAqI,QAAA,EAAQ;sBAAgB;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACxDlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACkE,UAAU,GACjC,GAAGzE,SAAS,CAACO,aAAa,CAACkE,UAAU,CAAC7C,SAAS,CAACmG,OAAO,CAAC,CAAC,CAAC,SAAS,GAAG;oBAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC,eACZlI,OAAA,CAAC3C,SAAS;sBAAAgL,QAAA,EACPnH,SAAS,CAACO,aAAa,CAACkE,UAAU,GACjC+B,UAAU,CAACxG,SAAS,CAACO,aAAa,CAACkE,UAAU,CAACzD,KAAK,CAAC,GAAG;oBAAE;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAChI,EAAA,CAnnBID,iBAAiB;EAAA,QACmBP,MAAM;AAAA;AAAAqL,EAAA,GAD1C9K,iBAAiB;AAqnBvB,eAAeA,iBAAiB;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}