const { app, BrowserWindow, ipc<PERSON>ain, dialog, Menu } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const { spawn } = require('child_process');
const { autoUpdater } = require('electron-updater');
const fs = require('fs');

// Mantener una referencia global del objeto ventana
let mainWindow;
let serverProcess;

// Configuración del auto-updater
if (!isDev) {
  autoUpdater.checkForUpdatesAndNotify();
}

// Función para crear logs
function createLogFile() {
  const appPath = getAppPath();
  const logPath = path.join(appPath, 'app.log');

  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;

  console.log = (...args) => {
    const timestamp = new Date().toISOString();
    const message = `[${timestamp}] LOG: ${args.join(' ')}\n`;
    fs.appendFileSync(logPath, message);
    originalConsoleLog(...args);
  };

  console.error = (...args) => {
    const timestamp = new Date().toISOString();
    const message = `[${timestamp}] ERROR: ${args.join(' ')}\n`;
    fs.appendFileSync(logPath, message);
    originalConsoleError(...args);
  };
}

// Función para crear backup de la base de datos
function createDatabaseBackup() {
  const appPath = getAppPath();
  const dbPath = path.join(appPath, 'repostaje.db');
  const backupPath = path.join(appPath, 'backups');

  if (fs.existsSync(dbPath)) {
    if (!fs.existsSync(backupPath)) {
      fs.mkdirSync(backupPath);
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupPath, `repostaje-backup-${timestamp}.db`);

    try {
      fs.copyFileSync(dbPath, backupFile);
      console.log('Backup creado:', backupFile);

      // Mantener solo los últimos 10 backups
      const backupFiles = fs.readdirSync(backupPath)
        .filter(file => file.startsWith('repostaje-backup-'))
        .sort()
        .reverse();

      if (backupFiles.length > 10) {
        backupFiles.slice(10).forEach(file => {
          fs.unlinkSync(path.join(backupPath, file));
        });
      }
    } catch (error) {
      console.error('Error creando backup:', error);
    }
  }
}

// Función para obtener la ruta de la aplicación (portable)
function getAppPath() {
  if (isDev) {
    return __dirname;
  }
  // En producción, usar la ruta donde está el ejecutable
  return path.dirname(process.execPath);
}

// Función para iniciar el servidor backend
function startBackendServer() {
  const appPath = getAppPath();
  const serverPath = isDev
    ? path.join(__dirname, '..', 'server', 'server.js')
    : path.join(process.resourcesPath, 'server', 'server.js');

  console.log('Iniciando servidor desde:', serverPath);
  console.log('Ruta de la aplicación:', appPath);

  serverProcess = spawn('node', [serverPath], {
    cwd: appPath,
    env: {
      ...process.env,
      APP_PATH: appPath,
      NODE_ENV: isDev ? 'development' : 'production'
    }
  });

  serverProcess.stdout.on('data', (data) => {
    console.log(`Servidor: ${data}`);
  });

  serverProcess.stderr.on('data', (data) => {
    console.error(`Error del servidor: ${data}`);
  });

  serverProcess.on('close', (code) => {
    console.log(`Servidor cerrado con código ${code}`);
  });
}

// Función para crear el menú de la aplicación
function createMenu() {
  const template = [
    {
      label: 'Archivo',
      submenu: [
        {
          label: 'Crear Backup',
          click: () => {
            createDatabaseBackup();
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Backup Creado',
              message: 'Se ha creado un backup de la base de datos exitosamente.'
            });
          }
        },
        { type: 'separator' },
        {
          label: 'Salir',
          accelerator: 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Ayuda',
      submenu: [
        {
          label: 'Buscar Actualizaciones',
          click: () => {
            if (!isDev) {
              autoUpdater.checkForUpdatesAndNotify();
            } else {
              dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: 'Modo Desarrollo',
                message: 'Las actualizaciones no están disponibles en modo desarrollo.'
              });
            }
          }
        },
        {
          label: 'Acerca de',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Acerca de Repostajes Manager',
              message: `Repostajes Manager v1.0.0\n\nSistema de gestión de repostajes y gastos vehiculares.\n\nDesarrollado con Electron y React.`
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

function createWindow() {
  // Crear la ventana del navegador
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'favicon.ico'),
    show: false,
    titleBarStyle: 'default'
  });

  // Cargar la aplicación
  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, '../build/index.html')}`;

  mainWindow.loadURL(startUrl);

  // Mostrar ventana cuando esté lista
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Abrir DevTools en desarrollo
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Eventos del auto-updater
autoUpdater.on('checking-for-update', () => {
  console.log('Buscando actualizaciones...');
});

autoUpdater.on('update-available', (info) => {
  console.log('Actualización disponible:', info);
  if (mainWindow) {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Actualización Disponible',
      message: `Una nueva versión (${info.version}) está disponible. Se descargará automáticamente.`,
      buttons: ['OK']
    });
  }
});

autoUpdater.on('update-not-available', (info) => {
  console.log('No hay actualizaciones disponibles');
});

autoUpdater.on('error', (err) => {
  console.error('Error en auto-updater:', err);
});

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = "Velocidad de descarga: " + progressObj.bytesPerSecond;
  log_message = log_message + ' - Descargado ' + progressObj.percent + '%';
  log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
  console.log(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
  console.log('Actualización descargada');
  if (mainWindow) {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Actualización Lista',
      message: 'La actualización se ha descargado. La aplicación se reiniciará para aplicar los cambios.',
      buttons: ['Reiniciar Ahora', 'Más Tarde']
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  }
});

// Este método será llamado cuando Electron haya terminado la inicialización
app.whenReady().then(() => {
  // Inicializar logs
  createLogFile();
  console.log('Aplicación iniciando...');

  // Crear menú
  createMenu();

  // Crear backup automático al iniciar (una vez al día)
  const appPath = getAppPath();
  const lastBackupFile = path.join(appPath, 'last-backup.txt');
  const today = new Date().toDateString();

  if (!fs.existsSync(lastBackupFile) || fs.readFileSync(lastBackupFile, 'utf8') !== today) {
    createDatabaseBackup();
    fs.writeFileSync(lastBackupFile, today);
  }

  // Iniciar el servidor backend primero
  startBackendServer();

  // Esperar un poco para que el servidor se inicie
  setTimeout(() => {
    createWindow();
  }, 2000);

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Salir cuando todas las ventanas estén cerradas
app.on('window-all-closed', () => {
  // Cerrar el servidor backend
  if (serverProcess) {
    serverProcess.kill();
  }

  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  // Cerrar el servidor backend antes de salir
  if (serverProcess) {
    serverProcess.kill();
  }
});
