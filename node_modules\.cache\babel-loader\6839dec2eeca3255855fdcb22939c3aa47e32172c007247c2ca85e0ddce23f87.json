{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Refuels\\\\RefuelsList.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { styled } from '@mui/material/styles';\nimport { Box, Typography, Card, CardContent, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControlLabel, Switch, Tooltip, TablePagination, Avatar, useTheme, alpha } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, LocalGasStation as GasIcon } from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\n\n// Styled Components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledTableCell = styled(TableCell)(({\n  theme\n}) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '14px 16px',\n  '&:first-of-type': {\n    paddingLeft: 24\n  },\n  '&:last-child': {\n    paddingRight: 24\n  },\n  '&.MuiTableCell-head': {\n    fontWeight: 600,\n    color: theme.palette.text.primary,\n    backgroundColor: theme.palette.background.paper\n  }\n}));\n_c = StyledTableCell;\nconst StyledTableRow = styled(TableRow)(({\n  theme\n}) => ({\n  '&:nth-of-type(odd)': {\n    backgroundColor: alpha(theme.palette.primary.light, 0.03)\n  },\n  '&:hover': {\n    backgroundColor: alpha(theme.palette.primary.main, 0.08)\n  },\n  '&:last-child td': {\n    borderBottom: 0\n  }\n}));\n_c2 = StyledTableRow;\nconst StatCard = styled(Card)(({\n  theme\n}) => ({\n  flex: '1 1 180px',\n  minWidth: '180px',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: theme.shadows[4]\n  },\n  borderLeft: `4px solid ${theme.palette.primary.main}`\n}));\n_c3 = StatCard;\nconst PageHeader = styled(Box)(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: theme.spacing(4),\n  padding: theme.spacing(2, 0),\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  [theme.breakpoints.down('sm')]: {\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    gap: theme.spacing(2)\n  }\n}));\n_c4 = PageHeader;\nconst PrimaryButton = styled(Button)(({\n  theme\n}) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-2px)',\n    boxShadow: theme.shadows[3]\n  },\n  transition: 'all 0.2s ease'\n}));\n_c5 = PrimaryButton;\nconst RefuelDialog = ({\n  open,\n  onClose,\n  refuel,\n  onSave,\n  vehicles\n}) => {\n  _s();\n  const theme = useTheme();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    litros: '',\n    precio_litro: '',\n    coste_total: '',\n    gasolinera: '',\n    deposito_lleno: true,\n    notas: ''\n  });\n  useEffect(() => {\n    if (refuel) {\n      setFormData({\n        ...refuel,\n        fecha: refuel.fecha.split('T')[0],\n        // Formato para input date\n        deposito_lleno: !!refuel.deposito_lleno // Convert 0/1 to false/true\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        litros: '',\n        precio_litro: '',\n        coste_total: '',\n        gasolinera: '',\n        deposito_lleno: true,\n        notas: ''\n      });\n    }\n  }, [refuel, vehicles, open]);\n  const handleChange = field => event => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => {\n      const newData = {\n        ...prev,\n        [field]: value\n      };\n\n      // Cálculos automáticos para precios y costes.\n      if (field === 'coste_total') {\n        // Si el usuario modifica el coste total, calculamos el precio por litro.\n        const litros = parseFloat(newData.litros) || 0;\n        const coste = parseFloat(newData.coste_total) || 0;\n        if (litros > 0) {\n          newData.precio_litro = (coste / litros).toFixed(3);\n        }\n      } else if (field === 'litros' || field === 'precio_litro') {\n        // Si el usuario modifica los litros o el precio por litro, calculamos el coste total.\n        const litros = parseFloat(newData.litros) || 0;\n        const precio = parseFloat(newData.precio_litro) || 0;\n        newData.coste_total = (litros * precio).toFixed(2);\n      }\n      return newData;\n    });\n  };\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      litros: parseFloat(formData.litros),\n      precio_litro: parseFloat(formData.precio_litro),\n      coste_total: parseFloat(formData.coste_total),\n      kilometros_actuales: parseInt(formData.kilometros_actuales),\n      deposito_lleno: formData.deposito_lleno ? 1 : 0\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n  const isValid = formData.vehiculo_id && formData.fecha && formData.kilometros_actuales && formData.litros && formData.precio_litro;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: refuel ? 'Editar Repostaje' : 'Nuevo Repostaje'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: 2,\n        pt: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Veh\\xEDculo\",\n          select: true,\n          value: formData.vehiculo_id,\n          onChange: handleChange('vehiculo_id'),\n          fullWidth: true,\n          required: true,\n          children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: vehicle.id,\n            children: vehicle.nombre\n          }, vehicle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Fecha\",\n            type: \"date\",\n            value: formData.fecha,\n            onChange: handleChange('fecha'),\n            fullWidth: true,\n            required: true,\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Kilometraje actual\",\n            type: \"number\",\n            value: formData.kilometros_actuales,\n            onChange: handleChange('kilometros_actuales'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Litros\",\n            type: \"number\",\n            value: formData.litros,\n            onChange: handleChange('litros'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Precio por litro (\\u20AC)\",\n            type: \"number\",\n            value: formData.precio_litro,\n            onChange: handleChange('precio_litro'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.001\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Coste total (\\u20AC)\",\n            type: \"number\",\n            value: formData.coste_total,\n            onChange: handleChange('coste_total'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Gasolinera\",\n          value: formData.gasolinera,\n          onChange: handleChange('gasolinera'),\n          fullWidth: true,\n          placeholder: \"Nombre de la gasolinera\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: formData.deposito_lleno,\n            onChange: handleChange('deposito_lleno')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this),\n          label: \"Dep\\xF3sito lleno\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Notas\",\n          value: formData.notas,\n          onChange: handleChange('notas'),\n          fullWidth: true,\n          multiline: true,\n          rows: 2,\n          placeholder: \"Notas adicionales...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: !isValid,\n        children: refuel ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(RefuelDialog, \"gbZPbpwPMwXyzjZZcKBu4IyrxSY=\", false, function () {\n  return [useTheme];\n});\n_c6 = RefuelDialog;\nconst RefuelsList = () => {\n  _s2();\n  const {\n    vehicles,\n    refuels,\n    loadRefuels,\n    addRefuel\n  } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedRefuel, setSelectedRefuel] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  useEffect(() => {\n    // Cargar todos los repostajes (sin límite)\n    loadRefuels(null, null);\n  }, []);\n  const handleAddRefuel = () => {\n    setSelectedRefuel(null);\n    setDialogOpen(true);\n  };\n  const handleEditRefuel = refuel => {\n    setSelectedRefuel(refuel);\n    setDialogOpen(true);\n  };\n  const handleDeleteRefuel = refuel => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete refuel:', refuel);\n  };\n  const handleSaveRefuel = async refuelData => {\n    try {\n      if (selectedRefuel) {\n        // TODO: Implementar actualización\n        console.log('Update refuel:', refuelData);\n      } else {\n        await addRefuel(refuelData);\n        // Recargar la lista\n        loadRefuels(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving refuel:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  // Formatear números a máximo 3 decimales\n  const formatDecimal = (num, decimals = 3) => {\n    if (num === null || num === undefined || isNaN(num)) return '-';\n    const factor = Math.pow(10, decimals);\n    const rounded = Math.round(num * factor) / factor;\n    // Eliminar ceros decimales innecesarios\n    return rounded.toString().replace(/\\.?0+$/, '');\n  };\n\n  // Calcular kilómetros recorridos y consumo\n  const calculateTripInfo = (refuel, index) => {\n    // Buscar el repostaje anterior del mismo vehículo (incluyendo parciales)\n    const previousRefuel = refuels.slice(index + 1).find(r => r.vehiculo_id === refuel.vehiculo_id);\n    if (!previousRefuel) return {\n      kmTraveled: null,\n      consumption: null\n    };\n    const kmTraveled = refuel.kilometros_actuales - previousRefuel.kilometros_actuales;\n\n    // Solo calcular consumo para repostajes completos\n    let consumption = null;\n    if (refuel.deposito_lleno && kmTraveled > 0) {\n      // Calcular consumo basado en los litros repostados y la distancia recorrida\n      consumption = refuel.litros * 100 / kmTraveled;\n    }\n    return {\n      kmTraveled: kmTraveled > 0 ? kmTraveled : null,\n      consumption: consumption,\n      isValidForStats: kmTraveled > 0 && refuel.deposito_lleno\n    };\n  };\n\n  // Procesar todos los repostajes para obtener estadísticas\n  const processedRefuels = refuels.map((refuel, index) => {\n    const tripInfo = calculateTripInfo(refuel, index);\n    return {\n      ...refuel,\n      ...tripInfo\n    };\n  });\n\n  // Filtrar repostajes con datos de viaje válidos\n  const allTrips = processedRefuels.filter(r => r.kmTraveled > 0);\n  const validTrips = processedRefuels.filter(r => r.isValidForStats);\n\n  // Calcular estadísticas de distancia (todos los viajes con km válidos)\n  const distances = allTrips.map(trip => trip.kmTraveled);\n  const minDistance = distances.length > 0 ? Math.min(...distances) : 0;\n  const maxDistance = distances.length > 0 ? Math.max(...distances) : 0;\n  const avgDistance = distances.length > 0 ? distances.reduce((a, b) => a + b, 0) / distances.length : 0;\n\n  // Calcular estadísticas rápidas\n  const totalRefuels = refuels.length;\n  const totalLiters = refuels.reduce((sum, refuel) => sum + (refuel.litros || 0), 0);\n  const totalCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);\n  const avgPricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0;\n\n  // Calcular consumo promedio (solo viajes válidos)\n  const avgConsumption = validTrips.length > 0 ? validTrips.reduce((sum, trip) => sum + trip.consumption, 0) / validTrips.length : 0;\n\n  // Paginación\n  const paginatedRefuels = processedRefuels.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  const theme = useTheme();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: {\n        xs: 2,\n        md: 3\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          fontWeight: \"bold\",\n          color: \"primary\",\n          children: \"Repostajes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Gesti\\xF3n de repostajes de veh\\xEDculos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddRefuel,\n        disabled: vehicles.length === 0,\n        sx: {\n          background: `linear-gradient(45deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`\n        },\n        children: \"Nuevo Repostaje\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 3,\n      mb: 4,\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(StatCard, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'primary.light',\n              color: 'primary.contrastText',\n              width: 56,\n              height: 56,\n              margin: '0 auto 12px'\n            },\n            children: /*#__PURE__*/_jsxDEV(GasIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary\",\n            fontWeight: \"bold\",\n            children: formatNumber(totalRefuels)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Total Repostajes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'success.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"success.main\",\n            fontWeight: \"bold\",\n            children: [formatDecimal(totalLiters, 1), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '0.8em',\n                opacity: 0.8\n              },\n              children: \"L\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 47\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Total Combustible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 1,\n              height: 4,\n              background: theme.palette.divider,\n              borderRadius: 2,\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${Math.min(100, totalLiters / 500 * 100)}%`,\n                height: '100%',\n                background: theme.palette.success.main,\n                transition: 'width 0.5s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'warning.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"warning.main\",\n            fontWeight: \"bold\",\n            children: formatCurrency(totalCost)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Coste Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [formatCurrency(avgPricePerLiter), \"/L\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        sx: {\n          borderLeftColor: 'info.main'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"info.main\",\n            fontWeight: \"bold\",\n            children: [validTrips.length > 0 ? formatDecimal(avgConsumption, 1) : '-', \"L\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Consumo Promedio/100km\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [validTrips.length, \" viajes v\\xE1lidos\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this), refuels.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          py: 6,\n          children: [/*#__PURE__*/_jsxDEV(GasIcon, {\n            sx: {\n              fontSize: 64,\n              color: 'text.secondary',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"textSecondary\",\n            gutterBottom: true,\n            children: \"No hay repostajes registrados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            mb: 3,\n            children: \"Comienza registrando tu primer repostaje para hacer seguimiento del consumo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 26\n            }, this),\n            onClick: handleAddRefuel,\n            disabled: vehicles.length === 0,\n            children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Repostaje'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        borderRadius: 2,\n        overflow: 'hidden',\n        boxShadow: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Fecha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Km Actuales\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Km Recorridos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Litros\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Precio/L\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Consumo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Gasolinera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Dep\\xF3sito\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"center\",\n                children: \"Acciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: paginatedRefuels.map((refuel, index) => {\n              const tripInfo = calculateTripInfo(refuel, index);\n              const isFullTank = refuel.deposito_lleno;\n              return /*#__PURE__*/_jsxDEV(StyledTableRow, {\n                children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: formatDate(refuel.fecha)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: refuel.vehiculo_nombre\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: formatNumber(refuel.kilometros_actuales)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: refuel.kmTraveled ? formatNumber(Math.round(refuel.kmTraveled)) : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: [formatDecimal(refuel.litros, 1), \"L\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: [formatDecimal(refuel.precio_litro, 3), \"\\u20AC\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: formatCurrency(refuel.coste_total)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: refuel.consumption ? formatDecimal(refuel.consumption, 1) + 'L' : refuel.kmTraveled ? 'Parcial' : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 500,\n                    children: refuel.gasolinera || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: refuel.deposito_lleno ? 'Sí' : 'No',\n                    color: refuel.deposito_lleno ? 'success' : 'default',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                  align: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Editar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleEditRefuel(refuel),\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 617,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Eliminar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteRefuel(refuel),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 622,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this)]\n              }, refuel.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n        component: \"div\",\n        count: refuels.length,\n        page: page,\n        onPageChange: (event, newPage) => setPage(newPage),\n        rowsPerPage: rowsPerPage,\n        onRowsPerPageChange: event => {\n          setRowsPerPage(parseInt(event.target.value, 10));\n          setPage(0);\n        },\n        rowsPerPageOptions: [10, 25, 50, 100],\n        labelRowsPerPage: \"Filas por p\\xE1gina:\",\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} de ${count}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RefuelDialog, {\n      open: dialogOpen,\n      onClose: () => setDialogOpen(false),\n      refuel: selectedRefuel,\n      onSave: handleSaveRefuel,\n      vehicles: vehicles\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 428,\n    columnNumber: 5\n  }, this);\n};\n_s2(RefuelsList, \"UU78kW/clTeGYpF4+JDV4EhWcQ4=\", false, function () {\n  return [useApp, useTheme];\n});\n_c7 = RefuelsList;\nexport default RefuelsList;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"StyledTableCell\");\n$RefreshReg$(_c2, \"StyledTableRow\");\n$RefreshReg$(_c3, \"StatCard\");\n$RefreshReg$(_c4, \"PageHeader\");\n$RefreshReg$(_c5, \"PrimaryButton\");\n$RefreshReg$(_c6, \"RefuelDialog\");\n$RefreshReg$(_c7, \"RefuelsList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "FormControlLabel", "Switch", "<PERSON><PERSON><PERSON>", "TablePagination", "Avatar", "useTheme", "alpha", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "LocalGasStation", "GasIcon", "useApp", "format", "es", "jsxDEV", "_jsxDEV", "StyledTableCell", "theme", "borderBottom", "palette", "divider", "padding", "paddingLeft", "paddingRight", "fontWeight", "color", "text", "primary", "backgroundColor", "background", "paper", "_c", "StyledTableRow", "light", "main", "_c2", "StatCard", "flex", "min<PERSON><PERSON><PERSON>", "transition", "transform", "boxShadow", "shadows", "borderLeft", "_c3", "<PERSON><PERSON><PERSON><PERSON>", "display", "justifyContent", "alignItems", "marginBottom", "spacing", "breakpoints", "down", "flexDirection", "gap", "_c4", "PrimaryButton", "textTransform", "borderRadius", "_c5", "RefuelDialog", "open", "onClose", "refuel", "onSave", "vehicles", "_s", "formData", "setFormData", "vehiculo_id", "fecha", "Date", "toISOString", "split", "kilometros_actuales", "litros", "precio_litro", "coste_total", "gasolinera", "deposito_lleno", "notas", "length", "id", "handleChange", "field", "event", "value", "target", "type", "checked", "prev", "newData", "parseFloat", "coste", "toFixed", "precio", "handleSubmit", "dataToSave", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pt", "label", "select", "onChange", "required", "map", "vehicle", "nombre", "InputLabelProps", "shrink", "inputProps", "min", "step", "placeholder", "control", "multiline", "rows", "onClick", "variant", "disabled", "_c6", "RefuelsList", "_s2", "refuels", "loadRefuels", "addRefuel", "dialogOpen", "setDialogOpen", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRefuel", "page", "setPage", "rowsPerPage", "setRowsPerPage", "handleAddRefuel", "handleEditRefuel", "handleDeleteRefuel", "console", "log", "handleSaveRefuel", "refuelData", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "locale", "formatNumber", "num", "formatDecimal", "decimals", "undefined", "isNaN", "factor", "Math", "pow", "rounded", "round", "toString", "replace", "calculateTripInfo", "index", "previousRefuel", "slice", "find", "r", "kmTraveled", "consumption", "isValidForStats", "processedRefuels", "tripInfo", "allTrips", "filter", "validTrips", "distances", "trip", "minDistance", "maxDistance", "max", "avgDistance", "reduce", "a", "b", "totalRefuels", "totalLiters", "sum", "totalCost", "avgPricePerLiter", "avgConsumption", "paginatedRefuels", "sx", "p", "xs", "md", "component", "startIcon", "dark", "mb", "flexWrap", "textAlign", "bgcolor", "width", "height", "margin", "fontSize", "borderLeftColor", "opacity", "mt", "overflow", "success", "py", "gutterBottom", "align", "isFullTank", "vehiculo_nombre", "size", "title", "count", "onPageChange", "newPage", "onRowsPerPageChange", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c7", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Refuels/RefuelsList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { styled } from '@mui/material/styles';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  FormControlLabel,\n  Switch,\n  Tooltip,\n  TablePagination,\n  Avatar,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  LocalGasStation as GasIcon,\n} from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\n\n// Styled Components\nconst StyledTableCell = styled(TableCell)(({ theme }) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '14px 16px',\n  '&:first-of-type': { paddingLeft: 24 },\n  '&:last-child': { paddingRight: 24 },\n  '&.MuiTableCell-head': {\n    fontWeight: 600,\n    color: theme.palette.text.primary,\n    backgroundColor: theme.palette.background.paper,\n  },\n}));\n\nconst StyledTableRow = styled(TableRow)(({ theme }) => ({\n  '&:nth-of-type(odd)': {\n    backgroundColor: alpha(theme.palette.primary.light, 0.03),\n  },\n  '&:hover': {\n    backgroundColor: alpha(theme.palette.primary.main, 0.08),\n  },\n  '&:last-child td': {\n    borderBottom: 0,\n  },\n}));\n\nconst StatCard = styled(Card)(({ theme }) => ({\n  flex: '1 1 180px',\n  minWidth: '180px',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: theme.shadows[4],\n  },\n  borderLeft: `4px solid ${theme.palette.primary.main}`,\n}));\n\nconst PageHeader = styled(Box)(({ theme }) => ({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: theme.spacing(4),\n  padding: theme.spacing(2, 0),\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  [theme.breakpoints.down('sm')]: {\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    gap: theme.spacing(2),\n  },\n}));\n\nconst PrimaryButton = styled(Button)(({ theme }) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-2px)',\n    boxShadow: theme.shadows[3],\n  },\n  transition: 'all 0.2s ease',\n}));\n\nconst RefuelDialog = ({ open, onClose, refuel, onSave, vehicles }) => {\n  const theme = useTheme();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    litros: '',\n    precio_litro: '',\n    coste_total: '',\n    gasolinera: '',\n    deposito_lleno: true,\n    notas: '',\n  });\n\n  useEffect(() => {\n    if (refuel) {\n      setFormData({\n        ...refuel,\n        fecha: refuel.fecha.split('T')[0], // Formato para input date\n        deposito_lleno: !!refuel.deposito_lleno, // Convert 0/1 to false/true\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        litros: '',\n        precio_litro: '',\n        coste_total: '',\n        gasolinera: '',\n        deposito_lleno: true,\n        notas: '',\n      });\n    }\n  }, [refuel, vehicles, open]);\n\n  const handleChange = (field) => (event) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => {\n      const newData = { ...prev, [field]: value };\n      \n      // Cálculos automáticos para precios y costes.\n      if (field === 'coste_total') {\n        // Si el usuario modifica el coste total, calculamos el precio por litro.\n        const litros = parseFloat(newData.litros) || 0;\n        const coste = parseFloat(newData.coste_total) || 0;\n        if (litros > 0) {\n          newData.precio_litro = (coste / litros).toFixed(3);\n        }\n      } else if (field === 'litros' || field === 'precio_litro') {\n        // Si el usuario modifica los litros o el precio por litro, calculamos el coste total.\n        const litros = parseFloat(newData.litros) || 0;\n        const precio = parseFloat(newData.precio_litro) || 0;\n        newData.coste_total = (litros * precio).toFixed(2);\n      }\n      \n      return newData;\n    });\n  };\n\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      litros: parseFloat(formData.litros),\n      precio_litro: parseFloat(formData.precio_litro),\n      coste_total: parseFloat(formData.coste_total),\n      kilometros_actuales: parseInt(formData.kilometros_actuales),\n      deposito_lleno: formData.deposito_lleno ? 1 : 0,\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n\n  const isValid = formData.vehiculo_id && formData.fecha && formData.kilometros_actuales && \n                  formData.litros && formData.precio_litro;\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        {refuel ? 'Editar Repostaje' : 'Nuevo Repostaje'}\n      </DialogTitle>\n      <DialogContent>\n        <Box display=\"flex\" flexDirection=\"column\" gap={2} pt={1}>\n          <TextField\n            label=\"Vehículo\"\n            select\n            value={formData.vehiculo_id}\n            onChange={handleChange('vehiculo_id')}\n            fullWidth\n            required\n          >\n            {vehicles.map((vehicle) => (\n              <MenuItem key={vehicle.id} value={vehicle.id}>\n                {vehicle.nombre}\n              </MenuItem>\n            ))}\n          </TextField>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Fecha\"\n              type=\"date\"\n              value={formData.fecha}\n              onChange={handleChange('fecha')}\n              fullWidth\n              required\n              InputLabelProps={{ shrink: true }}\n            />\n            <TextField\n              label=\"Kilometraje actual\"\n              type=\"number\"\n              value={formData.kilometros_actuales}\n              onChange={handleChange('kilometros_actuales')}\n              fullWidth\n              required\n              inputProps={{ min: 0 }}\n            />\n          </Box>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Litros\"\n              type=\"number\"\n              value={formData.litros}\n              onChange={handleChange('litros')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n            <TextField\n              label=\"Precio por litro (€)\"\n              type=\"number\"\n              value={formData.precio_litro}\n              onChange={handleChange('precio_litro')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.001 }}\n            />\n            <TextField\n              label=\"Coste total (€)\"\n              type=\"number\"\n              value={formData.coste_total}\n              onChange={handleChange('coste_total')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n          </Box>\n\n          <TextField\n            label=\"Gasolinera\"\n            value={formData.gasolinera}\n            onChange={handleChange('gasolinera')}\n            fullWidth\n            placeholder=\"Nombre de la gasolinera\"\n          />\n\n          <FormControlLabel\n            control={\n              <Switch\n                checked={formData.deposito_lleno}\n                onChange={handleChange('deposito_lleno')}\n              />\n            }\n            label=\"Depósito lleno\"\n          />\n\n          <TextField\n            label=\"Notas\"\n            value={formData.notas}\n            onChange={handleChange('notas')}\n            fullWidth\n            multiline\n            rows={2}\n            placeholder=\"Notas adicionales...\"\n          />\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose}>Cancelar</Button>\n        <Button \n          onClick={handleSubmit} \n          variant=\"contained\"\n          disabled={!isValid}\n        >\n          {refuel ? 'Actualizar' : 'Guardar'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nconst RefuelsList = () => {\n  const { vehicles, refuels, loadRefuels, addRefuel } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedRefuel, setSelectedRefuel] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n\n  useEffect(() => {\n    // Cargar todos los repostajes (sin límite)\n    loadRefuels(null, null);\n  }, []);\n\n  const handleAddRefuel = () => {\n    setSelectedRefuel(null);\n    setDialogOpen(true);\n  };\n\n  const handleEditRefuel = (refuel) => {\n    setSelectedRefuel(refuel);\n    setDialogOpen(true);\n  };\n\n  const handleDeleteRefuel = (refuel) => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete refuel:', refuel);\n  };\n\n  const handleSaveRefuel = async (refuelData) => {\n    try {\n      if (selectedRefuel) {\n        // TODO: Implementar actualización\n        console.log('Update refuel:', refuelData);\n      } else {\n        await addRefuel(refuelData);\n        // Recargar la lista\n        loadRefuels(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving refuel:', error);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  const formatDate = (dateString) => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\n    } catch {\n      return dateString;\n    }\n  };\n\n  const formatNumber = (num) => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  // Formatear números a máximo 3 decimales\n  const formatDecimal = (num, decimals = 3) => {\n    if (num === null || num === undefined || isNaN(num)) return '-';\n    const factor = Math.pow(10, decimals);\n    const rounded = Math.round(num * factor) / factor;\n    // Eliminar ceros decimales innecesarios\n    return rounded.toString().replace(/\\.?0+$/, '');\n  };\n\n  // Calcular kilómetros recorridos y consumo\n  const calculateTripInfo = (refuel, index) => {\n    // Buscar el repostaje anterior del mismo vehículo (incluyendo parciales)\n    const previousRefuel = refuels\n      .slice(index + 1)\n      .find(r => r.vehiculo_id === refuel.vehiculo_id);\n    \n    if (!previousRefuel) return { kmTraveled: null, consumption: null };\n    \n    const kmTraveled = refuel.kilometros_actuales - previousRefuel.kilometros_actuales;\n    \n    // Solo calcular consumo para repostajes completos\n    let consumption = null;\n    if (refuel.deposito_lleno && kmTraveled > 0) {\n      // Calcular consumo basado en los litros repostados y la distancia recorrida\n      consumption = (refuel.litros * 100) / kmTraveled;\n    }\n    \n    return {\n      kmTraveled: kmTraveled > 0 ? kmTraveled : null,\n      consumption: consumption,\n      isValidForStats: kmTraveled > 0 && refuel.deposito_lleno\n    };\n  };\n\n  // Procesar todos los repostajes para obtener estadísticas\n  const processedRefuels = refuels.map((refuel, index) => {\n    const tripInfo = calculateTripInfo(refuel, index);\n    return {\n      ...refuel,\n      ...tripInfo\n    };\n  });\n\n  // Filtrar repostajes con datos de viaje válidos\n  const allTrips = processedRefuels.filter(r => r.kmTraveled > 0);\n  const validTrips = processedRefuels.filter(r => r.isValidForStats);\n  \n  // Calcular estadísticas de distancia (todos los viajes con km válidos)\n  const distances = allTrips.map(trip => trip.kmTraveled);\n  const minDistance = distances.length > 0 ? Math.min(...distances) : 0;\n  const maxDistance = distances.length > 0 ? Math.max(...distances) : 0;\n  const avgDistance = distances.length > 0 ? \n    distances.reduce((a, b) => a + b, 0) / distances.length : 0;\n\n  // Calcular estadísticas rápidas\n  const totalRefuels = refuels.length;\n  const totalLiters = refuels.reduce((sum, refuel) => sum + (refuel.litros || 0), 0);\n  const totalCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);\n  const avgPricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0;\n  \n  // Calcular consumo promedio (solo viajes válidos)\n  const avgConsumption = validTrips.length > 0 ? \n    validTrips.reduce((sum, trip) => sum + trip.consumption, 0) / validTrips.length : 0;\n\n  // Paginación\n  const paginatedRefuels = processedRefuels.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n\n  const theme = useTheme();\n  \n  return (\n    <Box sx={{ p: { xs: 2, md: 3 } }}>\n      <PageHeader>\n        <Box>\n          <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\" color=\"primary\">\n            Repostajes\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Gestión de repostajes de vehículos\n          </Typography>\n        </Box>\n        <PrimaryButton\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleAddRefuel}\n          disabled={vehicles.length === 0}\n          sx={{\n            background: `linear-gradient(45deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n          }}\n        >\n          Nuevo Repostaje\n        </PrimaryButton>\n      </PageHeader>\n\n      {/* Estadísticas rápidas */}\n      <Box display=\"flex\" gap={3} mb={4} flexWrap=\"wrap\">\n        <StatCard>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Avatar sx={{ \n              bgcolor: 'primary.light', \n              color: 'primary.contrastText',\n              width: 56,\n              height: 56,\n              margin: '0 auto 12px',\n            }}>\n              <GasIcon fontSize=\"large\" />\n            </Avatar>\n            <Typography variant=\"h4\" color=\"primary\" fontWeight=\"bold\">\n              {formatNumber(totalRefuels)}\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">Total Repostajes</Typography>\n          </CardContent>\n        </StatCard>\n        \n        <StatCard sx={{ borderLeftColor: 'success.main' }}>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Typography variant=\"h4\" color=\"success.main\" fontWeight=\"bold\">\n              {formatDecimal(totalLiters, 1)} <span style={{ fontSize: '0.8em', opacity: 0.8 }}>L</span>\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">Total Combustible</Typography>\n            <Box sx={{ mt: 1, height: 4, background: theme.palette.divider, borderRadius: 2, overflow: 'hidden' }}>\n              <Box sx={{ \n                width: `${Math.min(100, (totalLiters / 500) * 100)}%`, \n                height: '100%', \n                background: theme.palette.success.main,\n                transition: 'width 0.5s ease'\n              }} />\n            </Box>\n          </CardContent>\n        </StatCard>\n        \n        <StatCard sx={{ borderLeftColor: 'warning.main' }}>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Typography variant=\"h4\" color=\"warning.main\" fontWeight=\"bold\">\n              {formatCurrency(totalCost)}\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">Coste Total</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {formatCurrency(avgPricePerLiter)}/L\n            </Typography>\n          </CardContent>\n        </StatCard>\n        \n        <StatCard sx={{ borderLeftColor: 'info.main' }}>\n          <CardContent sx={{ textAlign: 'center', p: 2 }}>\n            <Typography variant=\"h4\" color=\"info.main\" fontWeight=\"bold\">\n              {validTrips.length > 0 ? formatDecimal(avgConsumption, 1) : '-'}L\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">Consumo Promedio/100km</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {validTrips.length} viajes válidos\n            </Typography>\n          </CardContent>\n        </StatCard>\n      </Box>\n\n      {refuels.length === 0 ? (\n      <Card>\n        <CardContent>\n          <Box textAlign=\"center\" py={6}>\n            <GasIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" color=\"textSecondary\" gutterBottom>\n              No hay repostajes registrados\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\" mb={3}>\n              Comienza registrando tu primer repostaje para hacer seguimiento del consumo.\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={handleAddRefuel}\n              disabled={vehicles.length === 0}\n            >\n              {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Repostaje'}\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n    ) : (\n      <Card sx={{ borderRadius: 2, overflow: 'hidden', boxShadow: 2 }}>\n        <TableContainer>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <StyledTableCell>Fecha</StyledTableCell>\n                <StyledTableCell>Vehículo</StyledTableCell>\n                <StyledTableCell align=\"right\">Km Actuales</StyledTableCell>\n                <StyledTableCell align=\"right\">Km Recorridos</StyledTableCell>\n                <StyledTableCell align=\"right\">Litros</StyledTableCell>\n                <StyledTableCell align=\"right\">Precio/L</StyledTableCell>\n                <StyledTableCell align=\"right\">Total</StyledTableCell>\n                <StyledTableCell align=\"right\">Consumo</StyledTableCell>\n                <StyledTableCell>Gasolinera</StyledTableCell>\n                <StyledTableCell>Depósito</StyledTableCell>\n                <StyledTableCell align=\"center\">Acciones</StyledTableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {paginatedRefuels.map((refuel, index) => {\n                const tripInfo = calculateTripInfo(refuel, index);\n                const isFullTank = refuel.deposito_lleno;\n                \n                return (\n                  <StyledTableRow key={refuel.id}>\n                    <StyledTableCell>\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatDate(refuel.fecha)}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell>\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {refuel.vehiculo_nombre}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatNumber(refuel.kilometros_actuales)}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {refuel.kmTraveled ? formatNumber(Math.round(refuel.kmTraveled)) : '-'}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatDecimal(refuel.litros, 1)}L\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatDecimal(refuel.precio_litro, 3)}€\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {formatCurrency(refuel.coste_total)}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {refuel.consumption ? formatDecimal(refuel.consumption, 1) + 'L' : \n                         refuel.kmTraveled ? 'Parcial' : '-'}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell>\n                      <Typography variant=\"body2\" fontWeight={500}>\n                        {refuel.gasolinera || '-'}\n                      </Typography>\n                    </StyledTableCell>\n                    <StyledTableCell>\n                      <Chip \n                        label={refuel.deposito_lleno ? 'Sí' : 'No'} \n                        color={refuel.deposito_lleno ? 'success' : 'default'}\n                        size=\"small\"\n                      />\n                    </StyledTableCell>\n                    <StyledTableCell align=\"center\">\n                      <Tooltip title=\"Editar\">\n                        <IconButton size=\"small\" onClick={() => handleEditRefuel(refuel)}>\n                          <EditIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Eliminar\">\n                        <IconButton size=\"small\" color=\"error\" onClick={() => handleDeleteRefuel(refuel)}>\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </StyledTableCell>\n                  </StyledTableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n        \n        <TablePagination\n          component=\"div\"\n          count={refuels.length}\n          page={page}\n          onPageChange={(event, newPage) => setPage(newPage)}\n          rowsPerPage={rowsPerPage}\n          onRowsPerPageChange={(event) => {\n            setRowsPerPage(parseInt(event.target.value, 10));\n            setPage(0);\n          }}\n          rowsPerPageOptions={[10, 25, 50, 100]}\n          labelRowsPerPage=\"Filas por página:\"\n          labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}\n        />\n      </Card>\n    )}\n      <RefuelDialog\n        open={dialogOpen}\n        onClose={() => setDialogOpen(false)}\n        refuel={selectedRefuel}\n        onSave={handleSaveRefuel}\n        vehicles={vehicles}\n      />\n    </Box>\n  );\n};\n\nexport default RefuelsList;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,OAAO,EACPC,eAAe,EACfC,MAAM,EACNC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,eAAe,IAAIC,OAAO,QACrB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAGzC,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;EAAEkC;AAAM,CAAC,MAAM;EACxDC,YAAY,EAAE,aAAaD,KAAK,CAACE,OAAO,CAACC,OAAO,EAAE;EAClDC,OAAO,EAAE,WAAW;EACpB,iBAAiB,EAAE;IAAEC,WAAW,EAAE;EAAG,CAAC;EACtC,cAAc,EAAE;IAAEC,YAAY,EAAE;EAAG,CAAC;EACpC,qBAAqB,EAAE;IACrBC,UAAU,EAAE,GAAG;IACfC,KAAK,EAAER,KAAK,CAACE,OAAO,CAACO,IAAI,CAACC,OAAO;IACjCC,eAAe,EAAEX,KAAK,CAACE,OAAO,CAACU,UAAU,CAACC;EAC5C;AACF,CAAC,CAAC,CAAC;AAACC,EAAA,GAVEf,eAAe;AAYrB,MAAMgB,cAAc,GAAGzD,MAAM,CAACW,QAAQ,CAAC,CAAC,CAAC;EAAE+B;AAAM,CAAC,MAAM;EACtD,oBAAoB,EAAE;IACpBW,eAAe,EAAE1B,KAAK,CAACe,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACM,KAAK,EAAE,IAAI;EAC1D,CAAC;EACD,SAAS,EAAE;IACTL,eAAe,EAAE1B,KAAK,CAACe,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACO,IAAI,EAAE,IAAI;EACzD,CAAC;EACD,iBAAiB,EAAE;IACjBhB,YAAY,EAAE;EAChB;AACF,CAAC,CAAC,CAAC;AAACiB,GAAA,GAVEH,cAAc;AAYpB,MAAMI,QAAQ,GAAG7D,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC;EAAEuC;AAAM,CAAC,MAAM;EAC5CoB,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,OAAO;EACjBC,UAAU,EAAE,eAAe;EAC3B,SAAS,EAAE;IACTC,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAExB,KAAK,CAACyB,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDC,UAAU,EAAE,aAAa1B,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACO,IAAI;AACrD,CAAC,CAAC,CAAC;AAACU,GAAA,GATER,QAAQ;AAWd,MAAMS,UAAU,GAAGtE,MAAM,CAACC,GAAG,CAAC,CAAC,CAAC;EAAEyC;AAAM,CAAC,MAAM;EAC7C6B,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,eAAe;EAC/BC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAEhC,KAAK,CAACiC,OAAO,CAAC,CAAC,CAAC;EAC9B7B,OAAO,EAAEJ,KAAK,CAACiC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5BhC,YAAY,EAAE,aAAaD,KAAK,CAACE,OAAO,CAACC,OAAO,EAAE;EAClD,CAACH,KAAK,CAACkC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;IAC9BC,aAAa,EAAE,QAAQ;IACvBL,UAAU,EAAE,YAAY;IACxBM,GAAG,EAAErC,KAAK,CAACiC,OAAO,CAAC,CAAC;EACtB;AACF,CAAC,CAAC,CAAC;AAACK,GAAA,GAZEV,UAAU;AAchB,MAAMW,aAAa,GAAGjF,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC;EAAEqC;AAAM,CAAC,MAAM;EACnDwC,aAAa,EAAE,MAAM;EACrBjC,UAAU,EAAE,GAAG;EACfH,OAAO,EAAE,UAAU;EACnBqC,YAAY,EAAE,CAAC;EACfjB,SAAS,EAAE,MAAM;EACjB,SAAS,EAAE;IACTD,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAExB,KAAK,CAACyB,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDH,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAACoB,GAAA,GAXEH,aAAa;AAanB,MAAMI,YAAY,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,MAAM;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAMjD,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAC;IACvCgG,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,mBAAmB,EAAE,EAAE;IACvBC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,IAAI;IACpBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF1G,SAAS,CAAC,MAAM;IACd,IAAIyF,MAAM,EAAE;MACVK,WAAW,CAAC;QACV,GAAGL,MAAM;QACTO,KAAK,EAAEP,MAAM,CAACO,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAE;QACnCM,cAAc,EAAE,CAAC,CAAChB,MAAM,CAACgB,cAAc,CAAE;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,WAAW,CAAC;QACVC,WAAW,EAAEJ,QAAQ,CAACgB,MAAM,GAAG,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAACiB,EAAE,GAAG,EAAE;QACtDZ,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7CC,mBAAmB,EAAE,EAAE;QACvBC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,IAAI;QACpBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjB,MAAM,EAAEE,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EAE5B,MAAMsB,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,UAAU,GAAGH,KAAK,CAACE,MAAM,CAACE,OAAO,GAAGJ,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1FlB,WAAW,CAACsB,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG;QAAE,GAAGD,IAAI;QAAE,CAACN,KAAK,GAAGE;MAAM,CAAC;;MAE3C;MACA,IAAIF,KAAK,KAAK,aAAa,EAAE;QAC3B;QACA,MAAMT,MAAM,GAAGiB,UAAU,CAACD,OAAO,CAAChB,MAAM,CAAC,IAAI,CAAC;QAC9C,MAAMkB,KAAK,GAAGD,UAAU,CAACD,OAAO,CAACd,WAAW,CAAC,IAAI,CAAC;QAClD,IAAIF,MAAM,GAAG,CAAC,EAAE;UACdgB,OAAO,CAACf,YAAY,GAAG,CAACiB,KAAK,GAAGlB,MAAM,EAAEmB,OAAO,CAAC,CAAC,CAAC;QACpD;MACF,CAAC,MAAM,IAAIV,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,cAAc,EAAE;QACzD;QACA,MAAMT,MAAM,GAAGiB,UAAU,CAACD,OAAO,CAAChB,MAAM,CAAC,IAAI,CAAC;QAC9C,MAAMoB,MAAM,GAAGH,UAAU,CAACD,OAAO,CAACf,YAAY,CAAC,IAAI,CAAC;QACpDe,OAAO,CAACd,WAAW,GAAG,CAACF,MAAM,GAAGoB,MAAM,EAAED,OAAO,CAAC,CAAC,CAAC;MACpD;MAEA,OAAOH,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjB,GAAG9B,QAAQ;MACXQ,MAAM,EAAEiB,UAAU,CAACzB,QAAQ,CAACQ,MAAM,CAAC;MACnCC,YAAY,EAAEgB,UAAU,CAACzB,QAAQ,CAACS,YAAY,CAAC;MAC/CC,WAAW,EAAEe,UAAU,CAACzB,QAAQ,CAACU,WAAW,CAAC;MAC7CH,mBAAmB,EAAEwB,QAAQ,CAAC/B,QAAQ,CAACO,mBAAmB,CAAC;MAC3DK,cAAc,EAAEZ,QAAQ,CAACY,cAAc,GAAG,CAAC,GAAG;IAChD,CAAC;IACDf,MAAM,CAACiC,UAAU,CAAC;IAClBnC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMqC,OAAO,GAAGhC,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACO,mBAAmB,IACtEP,QAAQ,CAACQ,MAAM,IAAIR,QAAQ,CAACS,YAAY;EAExD,oBACE7D,OAAA,CAACzB,MAAM;IAACuE,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACsC,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DvF,OAAA,CAACxB,WAAW;MAAA+G,QAAA,EACTvC,MAAM,GAAG,kBAAkB,GAAG;IAAiB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACd3F,OAAA,CAACvB,aAAa;MAAA8G,QAAA,eACZvF,OAAA,CAACvC,GAAG;QAACsE,OAAO,EAAC,MAAM;QAACO,aAAa,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACqD,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACvDvF,OAAA,CAACrB,SAAS;UACRkH,KAAK,EAAC,aAAU;UAChBC,MAAM;UACNvB,KAAK,EAAEnB,QAAQ,CAACE,WAAY;UAC5ByC,QAAQ,EAAE3B,YAAY,CAAC,aAAa,CAAE;UACtCkB,SAAS;UACTU,QAAQ;UAAAT,QAAA,EAEPrC,QAAQ,CAAC+C,GAAG,CAAEC,OAAO,iBACpBlG,OAAA,CAACpB,QAAQ;YAAkB2F,KAAK,EAAE2B,OAAO,CAAC/B,EAAG;YAAAoB,QAAA,EAC1CW,OAAO,CAACC;UAAM,GADFD,OAAO,CAAC/B,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ3F,OAAA,CAACvC,GAAG;UAACsE,OAAO,EAAC,MAAM;UAACQ,GAAG,EAAE,CAAE;UAAAgD,QAAA,gBACzBvF,OAAA,CAACrB,SAAS;YACRkH,KAAK,EAAC,OAAO;YACbpB,IAAI,EAAC,MAAM;YACXF,KAAK,EAAEnB,QAAQ,CAACG,KAAM;YACtBwC,QAAQ,EAAE3B,YAAY,CAAC,OAAO,CAAE;YAChCkB,SAAS;YACTU,QAAQ;YACRI,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACF3F,OAAA,CAACrB,SAAS;YACRkH,KAAK,EAAC,oBAAoB;YAC1BpB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACO,mBAAoB;YACpCoC,QAAQ,EAAE3B,YAAY,CAAC,qBAAqB,CAAE;YAC9CkB,SAAS;YACTU,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE;YAAE;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3F,OAAA,CAACvC,GAAG;UAACsE,OAAO,EAAC,MAAM;UAACQ,GAAG,EAAE,CAAE;UAAAgD,QAAA,gBACzBvF,OAAA,CAACrB,SAAS;YACRkH,KAAK,EAAC,QAAQ;YACdpB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACQ,MAAO;YACvBmC,QAAQ,EAAE3B,YAAY,CAAC,QAAQ,CAAE;YACjCkB,SAAS;YACTU,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACF3F,OAAA,CAACrB,SAAS;YACRkH,KAAK,EAAC,2BAAsB;YAC5BpB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACS,YAAa;YAC7BkC,QAAQ,EAAE3B,YAAY,CAAC,cAAc,CAAE;YACvCkB,SAAS;YACTU,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAM;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACF3F,OAAA,CAACrB,SAAS;YACRkH,KAAK,EAAC,sBAAiB;YACvBpB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACU,WAAY;YAC5BiC,QAAQ,EAAE3B,YAAY,CAAC,aAAa,CAAE;YACtCkB,SAAS;YACTU,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3F,OAAA,CAACrB,SAAS;UACRkH,KAAK,EAAC,YAAY;UAClBtB,KAAK,EAAEnB,QAAQ,CAACW,UAAW;UAC3BgC,QAAQ,EAAE3B,YAAY,CAAC,YAAY,CAAE;UACrCkB,SAAS;UACTmB,WAAW,EAAC;QAAyB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAEF3F,OAAA,CAACnB,gBAAgB;UACf6H,OAAO,eACL1G,OAAA,CAAClB,MAAM;YACL4F,OAAO,EAAEtB,QAAQ,CAACY,cAAe;YACjC+B,QAAQ,EAAE3B,YAAY,CAAC,gBAAgB;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;UACDE,KAAK,EAAC;QAAgB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAEF3F,OAAA,CAACrB,SAAS;UACRkH,KAAK,EAAC,OAAO;UACbtB,KAAK,EAAEnB,QAAQ,CAACa,KAAM;UACtB8B,QAAQ,EAAE3B,YAAY,CAAC,OAAO,CAAE;UAChCkB,SAAS;UACTqB,SAAS;UACTC,IAAI,EAAE,CAAE;UACRH,WAAW,EAAC;QAAsB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChB3F,OAAA,CAACtB,aAAa;MAAA6G,QAAA,gBACZvF,OAAA,CAACnC,MAAM;QAACgJ,OAAO,EAAE9D,OAAQ;QAAAwC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3C3F,OAAA,CAACnC,MAAM;QACLgJ,OAAO,EAAE5B,YAAa;QACtB6B,OAAO,EAAC,WAAW;QACnBC,QAAQ,EAAE,CAAC3B,OAAQ;QAAAG,QAAA,EAElBvC,MAAM,GAAG,YAAY,GAAG;MAAS;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACxC,EAAA,CA9LIN,YAAY;EAAA,QACF3D,QAAQ;AAAA;AAAA8H,GAAA,GADlBnE,YAAY;AAgMlB,MAAMoE,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAM;IAAEhE,QAAQ;IAAEiE,OAAO;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGzH,MAAM,CAAC,CAAC;EAC9D,MAAM,CAAC0H,UAAU,EAAEC,aAAa,CAAC,GAAGjK,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkK,cAAc,EAAEC,iBAAiB,CAAC,GAAGnK,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoK,IAAI,EAAEC,OAAO,CAAC,GAAGrK,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACsK,WAAW,EAAEC,cAAc,CAAC,GAAGvK,QAAQ,CAAC,EAAE,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd;IACA6J,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BL,iBAAiB,CAAC,IAAI,CAAC;IACvBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMQ,gBAAgB,GAAI/E,MAAM,IAAK;IACnCyE,iBAAiB,CAACzE,MAAM,CAAC;IACzBuE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMS,kBAAkB,GAAIhF,MAAM,IAAK;IACrC;IACAiF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAElF,MAAM,CAAC;EACvC,CAAC;EAED,MAAMmF,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC7C,IAAI;MACF,IAAIZ,cAAc,EAAE;QAClB;QACAS,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,UAAU,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMf,SAAS,CAACe,UAAU,CAAC;QAC3B;QACAhB,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC9I,MAAM,CAAC0I,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOhJ,MAAM,CAAC,IAAI2D,IAAI,CAACqF,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEC,MAAM,EAAEhJ;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAO+I,UAAU;IACnB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,GAAG,IAAK;IAC5B,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAAC5I,MAAM,CAACmJ,GAAG,IAAI,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACD,GAAG,EAAEE,QAAQ,GAAG,CAAC,KAAK;IAC3C,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,IAAIC,KAAK,CAACJ,GAAG,CAAC,EAAE,OAAO,GAAG;IAC/D,MAAMK,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEL,QAAQ,CAAC;IACrC,MAAMM,OAAO,GAAGF,IAAI,CAACG,KAAK,CAACT,GAAG,GAAGK,MAAM,CAAC,GAAGA,MAAM;IACjD;IACA,OAAOG,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;EACjD,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAC5G,MAAM,EAAE6G,KAAK,KAAK;IAC3C;IACA,MAAMC,cAAc,GAAG3C,OAAO,CAC3B4C,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC,CAChBG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3G,WAAW,KAAKN,MAAM,CAACM,WAAW,CAAC;IAElD,IAAI,CAACwG,cAAc,EAAE,OAAO;MAAEI,UAAU,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAK,CAAC;IAEnE,MAAMD,UAAU,GAAGlH,MAAM,CAACW,mBAAmB,GAAGmG,cAAc,CAACnG,mBAAmB;;IAElF;IACA,IAAIwG,WAAW,GAAG,IAAI;IACtB,IAAInH,MAAM,CAACgB,cAAc,IAAIkG,UAAU,GAAG,CAAC,EAAE;MAC3C;MACAC,WAAW,GAAInH,MAAM,CAACY,MAAM,GAAG,GAAG,GAAIsG,UAAU;IAClD;IAEA,OAAO;MACLA,UAAU,EAAEA,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,IAAI;MAC9CC,WAAW,EAAEA,WAAW;MACxBC,eAAe,EAAEF,UAAU,GAAG,CAAC,IAAIlH,MAAM,CAACgB;IAC5C,CAAC;EACH,CAAC;;EAED;EACA,MAAMqG,gBAAgB,GAAGlD,OAAO,CAAClB,GAAG,CAAC,CAACjD,MAAM,EAAE6G,KAAK,KAAK;IACtD,MAAMS,QAAQ,GAAGV,iBAAiB,CAAC5G,MAAM,EAAE6G,KAAK,CAAC;IACjD,OAAO;MACL,GAAG7G,MAAM;MACT,GAAGsH;IACL,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAMC,QAAQ,GAAGF,gBAAgB,CAACG,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACC,UAAU,GAAG,CAAC,CAAC;EAC/D,MAAMO,UAAU,GAAGJ,gBAAgB,CAACG,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC;;EAElE;EACA,MAAMM,SAAS,GAAGH,QAAQ,CAACtE,GAAG,CAAC0E,IAAI,IAAIA,IAAI,CAACT,UAAU,CAAC;EACvD,MAAMU,WAAW,GAAGF,SAAS,CAACxG,MAAM,GAAG,CAAC,GAAGoF,IAAI,CAAC/C,GAAG,CAAC,GAAGmE,SAAS,CAAC,GAAG,CAAC;EACrE,MAAMG,WAAW,GAAGH,SAAS,CAACxG,MAAM,GAAG,CAAC,GAAGoF,IAAI,CAACwB,GAAG,CAAC,GAAGJ,SAAS,CAAC,GAAG,CAAC;EACrE,MAAMK,WAAW,GAAGL,SAAS,CAACxG,MAAM,GAAG,CAAC,GACtCwG,SAAS,CAACM,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGR,SAAS,CAACxG,MAAM,GAAG,CAAC;;EAE7D;EACA,MAAMiH,YAAY,GAAGhE,OAAO,CAACjD,MAAM;EACnC,MAAMkH,WAAW,GAAGjE,OAAO,CAAC6D,MAAM,CAAC,CAACK,GAAG,EAAErI,MAAM,KAAKqI,GAAG,IAAIrI,MAAM,CAACY,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAM0H,SAAS,GAAGnE,OAAO,CAAC6D,MAAM,CAAC,CAACK,GAAG,EAAErI,MAAM,KAAKqI,GAAG,IAAIrI,MAAM,CAACc,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF,MAAMyH,gBAAgB,GAAGH,WAAW,GAAG,CAAC,GAAGE,SAAS,GAAGF,WAAW,GAAG,CAAC;;EAEtE;EACA,MAAMI,cAAc,GAAGf,UAAU,CAACvG,MAAM,GAAG,CAAC,GAC1CuG,UAAU,CAACO,MAAM,CAAC,CAACK,GAAG,EAAEV,IAAI,KAAKU,GAAG,GAAGV,IAAI,CAACR,WAAW,EAAE,CAAC,CAAC,GAAGM,UAAU,CAACvG,MAAM,GAAG,CAAC;;EAErF;EACA,MAAMuH,gBAAgB,GAAGpB,gBAAgB,CAACN,KAAK,CAACrC,IAAI,GAAGE,WAAW,EAAEF,IAAI,GAAGE,WAAW,GAAGA,WAAW,CAAC;EAErG,MAAM1H,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EAExB,oBACEc,OAAA,CAACvC,GAAG;IAACiO,EAAE,EAAE;MAAEC,CAAC,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAAtG,QAAA,gBAC/BvF,OAAA,CAAC8B,UAAU;MAAAyD,QAAA,gBACTvF,OAAA,CAACvC,GAAG;QAAA8H,QAAA,gBACFvF,OAAA,CAACtC,UAAU;UAACoJ,OAAO,EAAC,IAAI;UAACgF,SAAS,EAAC,IAAI;UAACrL,UAAU,EAAC,MAAM;UAACC,KAAK,EAAC,SAAS;UAAA6E,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3F,OAAA,CAACtC,UAAU;UAACoJ,OAAO,EAAC,OAAO;UAACpG,KAAK,EAAC,gBAAgB;UAAA6E,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN3F,OAAA,CAACyC,aAAa;QACZqE,OAAO,EAAC,WAAW;QACnBiF,SAAS,eAAE/L,OAAA,CAACX,OAAO;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBkB,OAAO,EAAEiB,eAAgB;QACzBf,QAAQ,EAAE7D,QAAQ,CAACgB,MAAM,KAAK,CAAE;QAChCwH,EAAE,EAAE;UACF5K,UAAU,EAAE,0BAA0BZ,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACO,IAAI,QAAQjB,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACoL,IAAI;QACpG,CAAE;QAAAzG,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGb3F,OAAA,CAACvC,GAAG;MAACsE,OAAO,EAAC,MAAM;MAACQ,GAAG,EAAE,CAAE;MAAC0J,EAAE,EAAE,CAAE;MAACC,QAAQ,EAAC,MAAM;MAAA3G,QAAA,gBAChDvF,OAAA,CAACqB,QAAQ;QAAAkE,QAAA,eACPvF,OAAA,CAACpC,WAAW;UAAC8N,EAAE,EAAE;YAAES,SAAS,EAAE,QAAQ;YAAER,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,gBAC7CvF,OAAA,CAACf,MAAM;YAACyM,EAAE,EAAE;cACVU,OAAO,EAAE,eAAe;cACxB1L,KAAK,EAAE,sBAAsB;cAC7B2L,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,MAAM,EAAE;YACV,CAAE;YAAAhH,QAAA,eACAvF,OAAA,CAACL,OAAO;cAAC6M,QAAQ,EAAC;YAAO;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACT3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACpG,KAAK,EAAC,SAAS;YAACD,UAAU,EAAC,MAAM;YAAA8E,QAAA,EACvDwD,YAAY,CAACoC,YAAY;UAAC;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACb3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAACpG,KAAK,EAAC,gBAAgB;YAAA6E,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEX3F,OAAA,CAACqB,QAAQ;QAACqK,EAAE,EAAE;UAAEe,eAAe,EAAE;QAAe,CAAE;QAAAlH,QAAA,eAChDvF,OAAA,CAACpC,WAAW;UAAC8N,EAAE,EAAE;YAAES,SAAS,EAAE,QAAQ;YAAER,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,gBAC7CvF,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACpG,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAA8E,QAAA,GAC5D0D,aAAa,CAACmC,WAAW,EAAE,CAAC,CAAC,EAAC,GAAC,eAAApL,OAAA;cAAM0I,KAAK,EAAE;gBAAE8D,QAAQ,EAAE,OAAO;gBAAEE,OAAO,EAAE;cAAI,CAAE;cAAAnH,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACb3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAACpG,KAAK,EAAC,gBAAgB;YAAA6E,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjF3F,OAAA,CAACvC,GAAG;YAACiO,EAAE,EAAE;cAAEiB,EAAE,EAAE,CAAC;cAAEL,MAAM,EAAE,CAAC;cAAExL,UAAU,EAAEZ,KAAK,CAACE,OAAO,CAACC,OAAO;cAAEsC,YAAY,EAAE,CAAC;cAAEiK,QAAQ,EAAE;YAAS,CAAE;YAAArH,QAAA,eACpGvF,OAAA,CAACvC,GAAG;cAACiO,EAAE,EAAE;gBACPW,KAAK,EAAE,GAAG/C,IAAI,CAAC/C,GAAG,CAAC,GAAG,EAAG6E,WAAW,GAAG,GAAG,GAAI,GAAG,CAAC,GAAG;gBACrDkB,MAAM,EAAE,MAAM;gBACdxL,UAAU,EAAEZ,KAAK,CAACE,OAAO,CAACyM,OAAO,CAAC1L,IAAI;gBACtCK,UAAU,EAAE;cACd;YAAE;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEX3F,OAAA,CAACqB,QAAQ;QAACqK,EAAE,EAAE;UAAEe,eAAe,EAAE;QAAe,CAAE;QAAAlH,QAAA,eAChDvF,OAAA,CAACpC,WAAW;UAAC8N,EAAE,EAAE;YAAES,SAAS,EAAE,QAAQ;YAAER,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,gBAC7CvF,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACpG,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAA8E,QAAA,EAC5D+C,cAAc,CAACgD,SAAS;UAAC;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACb3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAACpG,KAAK,EAAC,gBAAgB;YAAA6E,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3E3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,SAAS;YAACpG,KAAK,EAAC,gBAAgB;YAAA6E,QAAA,GACjD+C,cAAc,CAACiD,gBAAgB,CAAC,EAAC,IACpC;UAAA;YAAA/F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEX3F,OAAA,CAACqB,QAAQ;QAACqK,EAAE,EAAE;UAAEe,eAAe,EAAE;QAAY,CAAE;QAAAlH,QAAA,eAC7CvF,OAAA,CAACpC,WAAW;UAAC8N,EAAE,EAAE;YAAES,SAAS,EAAE,QAAQ;YAAER,CAAC,EAAE;UAAE,CAAE;UAAApG,QAAA,gBAC7CvF,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACpG,KAAK,EAAC,WAAW;YAACD,UAAU,EAAC,MAAM;YAAA8E,QAAA,GACzDkF,UAAU,CAACvG,MAAM,GAAG,CAAC,GAAG+E,aAAa,CAACuC,cAAc,EAAE,CAAC,CAAC,GAAG,GAAG,EAAC,GAClE;UAAA;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAACpG,KAAK,EAAC,gBAAgB;YAAA6E,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtF3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,SAAS;YAACpG,KAAK,EAAC,gBAAgB;YAAA6E,QAAA,GACjDkF,UAAU,CAACvG,MAAM,EAAC,oBACrB;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAELwB,OAAO,CAACjD,MAAM,KAAK,CAAC,gBACrBlE,OAAA,CAACrC,IAAI;MAAA4H,QAAA,eACHvF,OAAA,CAACpC,WAAW;QAAA2H,QAAA,eACVvF,OAAA,CAACvC,GAAG;UAAC0O,SAAS,EAAC,QAAQ;UAACW,EAAE,EAAE,CAAE;UAAAvH,QAAA,gBAC5BvF,OAAA,CAACL,OAAO;YAAC+L,EAAE,EAAE;cAAEc,QAAQ,EAAE,EAAE;cAAE9L,KAAK,EAAE,gBAAgB;cAAEuL,EAAE,EAAE;YAAE;UAAE;YAAAzG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjE3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACpG,KAAK,EAAC,eAAe;YAACqM,YAAY;YAAAxH,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3F,OAAA,CAACtC,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAACpG,KAAK,EAAC,eAAe;YAACuL,EAAE,EAAE,CAAE;YAAA1G,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3F,OAAA,CAACnC,MAAM;YACLiJ,OAAO,EAAC,WAAW;YACnBiF,SAAS,eAAE/L,OAAA,CAACX,OAAO;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBkB,OAAO,EAAEiB,eAAgB;YACzBf,QAAQ,EAAE7D,QAAQ,CAACgB,MAAM,KAAK,CAAE;YAAAqB,QAAA,EAE/BrC,QAAQ,CAACgB,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;UAA4B;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEP3F,OAAA,CAACrC,IAAI;MAAC+N,EAAE,EAAE;QAAE/I,YAAY,EAAE,CAAC;QAAEiK,QAAQ,EAAE,QAAQ;QAAElL,SAAS,EAAE;MAAE,CAAE;MAAA6D,QAAA,gBAC9DvF,OAAA,CAAC/B,cAAc;QAAAsH,QAAA,eACbvF,OAAA,CAAClC,KAAK;UAAAyH,QAAA,gBACJvF,OAAA,CAAC9B,SAAS;YAAAqH,QAAA,eACRvF,OAAA,CAAC7B,QAAQ;cAAAoH,QAAA,gBACPvF,OAAA,CAACC,eAAe;gBAAAsF,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACxC3F,OAAA,CAACC,eAAe;gBAAAsF,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC3C3F,OAAA,CAACC,eAAe;gBAAC+M,KAAK,EAAC,OAAO;gBAAAzH,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC5D3F,OAAA,CAACC,eAAe;gBAAC+M,KAAK,EAAC,OAAO;gBAAAzH,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC9D3F,OAAA,CAACC,eAAe;gBAAC+M,KAAK,EAAC,OAAO;gBAAAzH,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACvD3F,OAAA,CAACC,eAAe;gBAAC+M,KAAK,EAAC,OAAO;gBAAAzH,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACzD3F,OAAA,CAACC,eAAe;gBAAC+M,KAAK,EAAC,OAAO;gBAAAzH,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACtD3F,OAAA,CAACC,eAAe;gBAAC+M,KAAK,EAAC,OAAO;gBAAAzH,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACxD3F,OAAA,CAACC,eAAe;gBAAAsF,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC7C3F,OAAA,CAACC,eAAe;gBAAAsF,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC3C3F,OAAA,CAACC,eAAe;gBAAC+M,KAAK,EAAC,QAAQ;gBAAAzH,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ3F,OAAA,CAACjC,SAAS;YAAAwH,QAAA,EACPkG,gBAAgB,CAACxF,GAAG,CAAC,CAACjD,MAAM,EAAE6G,KAAK,KAAK;cACvC,MAAMS,QAAQ,GAAGV,iBAAiB,CAAC5G,MAAM,EAAE6G,KAAK,CAAC;cACjD,MAAMoD,UAAU,GAAGjK,MAAM,CAACgB,cAAc;cAExC,oBACEhE,OAAA,CAACiB,cAAc;gBAAAsE,QAAA,gBACbvF,OAAA,CAACC,eAAe;kBAAAsF,QAAA,eACdvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,EACzCqD,UAAU,CAAC5F,MAAM,CAACO,KAAK;kBAAC;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAAsF,QAAA,eACdvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,EACzCvC,MAAM,CAACkK;kBAAe;oBAAA1H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAC+M,KAAK,EAAC,OAAO;kBAAAzH,QAAA,eAC5BvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,EACzCwD,YAAY,CAAC/F,MAAM,CAACW,mBAAmB;kBAAC;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAC+M,KAAK,EAAC,OAAO;kBAAAzH,QAAA,eAC5BvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,EACzCvC,MAAM,CAACkH,UAAU,GAAGnB,YAAY,CAACO,IAAI,CAACG,KAAK,CAACzG,MAAM,CAACkH,UAAU,CAAC,CAAC,GAAG;kBAAG;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAC+M,KAAK,EAAC,OAAO;kBAAAzH,QAAA,eAC5BvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,GACzC0D,aAAa,CAACjG,MAAM,CAACY,MAAM,EAAE,CAAC,CAAC,EAAC,GACnC;kBAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAC+M,KAAK,EAAC,OAAO;kBAAAzH,QAAA,eAC5BvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,GACzC0D,aAAa,CAACjG,MAAM,CAACa,YAAY,EAAE,CAAC,CAAC,EAAC,QACzC;kBAAA;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAC+M,KAAK,EAAC,OAAO;kBAAAzH,QAAA,eAC5BvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,EACzC+C,cAAc,CAACtF,MAAM,CAACc,WAAW;kBAAC;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAC+M,KAAK,EAAC,OAAO;kBAAAzH,QAAA,eAC5BvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,EACzCvC,MAAM,CAACmH,WAAW,GAAGlB,aAAa,CAACjG,MAAM,CAACmH,WAAW,EAAE,CAAC,CAAC,GAAG,GAAG,GAC/DnH,MAAM,CAACkH,UAAU,GAAG,SAAS,GAAG;kBAAG;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAAsF,QAAA,eACdvF,OAAA,CAACtC,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACrG,UAAU,EAAE,GAAI;oBAAA8E,QAAA,EACzCvC,MAAM,CAACe,UAAU,IAAI;kBAAG;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAAsF,QAAA,eACdvF,OAAA,CAAC3B,IAAI;oBACHwH,KAAK,EAAE7C,MAAM,CAACgB,cAAc,GAAG,IAAI,GAAG,IAAK;oBAC3CtD,KAAK,EAAEsC,MAAM,CAACgB,cAAc,GAAG,SAAS,GAAG,SAAU;oBACrDmJ,IAAI,EAAC;kBAAO;oBAAA3H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACa,CAAC,eAClB3F,OAAA,CAACC,eAAe;kBAAC+M,KAAK,EAAC,QAAQ;kBAAAzH,QAAA,gBAC7BvF,OAAA,CAACjB,OAAO;oBAACqO,KAAK,EAAC,QAAQ;oBAAA7H,QAAA,eACrBvF,OAAA,CAAC1B,UAAU;sBAAC6O,IAAI,EAAC,OAAO;sBAACtG,OAAO,EAAEA,CAAA,KAAMkB,gBAAgB,CAAC/E,MAAM,CAAE;sBAAAuC,QAAA,eAC/DvF,OAAA,CAACT,QAAQ;wBAAAiG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV3F,OAAA,CAACjB,OAAO;oBAACqO,KAAK,EAAC,UAAU;oBAAA7H,QAAA,eACvBvF,OAAA,CAAC1B,UAAU;sBAAC6O,IAAI,EAAC,OAAO;sBAACzM,KAAK,EAAC,OAAO;sBAACmG,OAAO,EAAEA,CAAA,KAAMmB,kBAAkB,CAAChF,MAAM,CAAE;sBAAAuC,QAAA,eAC/EvF,OAAA,CAACP,UAAU;wBAAA+F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA,GAjEC3C,MAAM,CAACmB,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkEd,CAAC;YAErB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEjB3F,OAAA,CAAChB,eAAe;QACd8M,SAAS,EAAC,KAAK;QACfuB,KAAK,EAAElG,OAAO,CAACjD,MAAO;QACtBwD,IAAI,EAAEA,IAAK;QACX4F,YAAY,EAAEA,CAAChJ,KAAK,EAAEiJ,OAAO,KAAK5F,OAAO,CAAC4F,OAAO,CAAE;QACnD3F,WAAW,EAAEA,WAAY;QACzB4F,mBAAmB,EAAGlJ,KAAK,IAAK;UAC9BuD,cAAc,CAAC1C,QAAQ,CAACb,KAAK,CAACE,MAAM,CAACD,KAAK,EAAE,EAAE,CAAC,CAAC;UAChDoD,OAAO,CAAC,CAAC,CAAC;QACZ,CAAE;QACF8F,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QACtCC,gBAAgB,EAAC,sBAAmB;QACpCC,kBAAkB,EAAEA,CAAC;UAAEC,IAAI;UAAEC,EAAE;UAAER;QAAM,CAAC,KAAK,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK;MAAG;QAAA7H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eACC3F,OAAA,CAAC6C,YAAY;MACXC,IAAI,EAAEwE,UAAW;MACjBvE,OAAO,EAAEA,CAAA,KAAMwE,aAAa,CAAC,KAAK,CAAE;MACpCvE,MAAM,EAAEwE,cAAe;MACvBvE,MAAM,EAAEkF,gBAAiB;MACzBjF,QAAQ,EAAEA;IAAS;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACuB,GAAA,CAzWID,WAAW;EAAA,QACuCrH,MAAM,EA+H9CV,QAAQ;AAAA;AAAA4O,GAAA,GAhIlB7G,WAAW;AA2WjB,eAAeA,WAAW;AAAC,IAAAjG,EAAA,EAAAI,GAAA,EAAAS,GAAA,EAAAW,GAAA,EAAAI,GAAA,EAAAoE,GAAA,EAAA8G,GAAA;AAAAC,YAAA,CAAA/M,EAAA;AAAA+M,YAAA,CAAA3M,GAAA;AAAA2M,YAAA,CAAAlM,GAAA;AAAAkM,YAAA,CAAAvL,GAAA;AAAAuL,YAAA,CAAAnL,GAAA;AAAAmL,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}