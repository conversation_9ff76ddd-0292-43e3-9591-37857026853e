{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { Box, CircularProgress, Alert, Snackbar } from '@mui/material';\n\n// Componentes\nimport Layout from './components/Layout/Layout';\nimport Dashboard from './components/Dashboard/Dashboard';\nimport VehiclesList from './components/Vehicles/VehiclesList';\nimport RefuelsList from './components/Refuels/RefuelsList';\nimport ExpensesList from './components/Expenses/ExpensesList';\nimport Statistics from './components/Statistics/Statistics';\nimport Settings from './components/Settings/Settings';\n\n// Context\nimport { AppProvider } from './context/AppContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  useEffect(() => {\n    // Verificar si estamos en Electron\n    if (window.electronAPI) {\n      console.log('✅ Aplicación ejecutándose en Electron');\n      setLoading(false);\n    } else {\n      console.log('🌐 Aplicación ejecutándose en navegador (modo desarrollo)');\n      // En modo desarrollo web, simular datos\n      setLoading(false);\n    }\n  }, []);\n  const showNotification = (message, severity = 'info') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"100vh\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"100vh\",\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          maxWidth: 500\n        },\n        children: [\"Error al inicializar la aplicaci\\xF3n: \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AppProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      future: {\n        v7_startTransition: true,\n        v7_relativeSplatPath: true\n      },\n      children: [/*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/vehicles\",\n            element: /*#__PURE__*/_jsxDEV(VehiclesList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/refuels\",\n            element: /*#__PURE__*/_jsxDEV(RefuelsList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 45\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Route, {\n              path: \"new\",\n              element: /*#__PURE__*/_jsxDEV(RefuelsList, {\n                showFormOnLoad: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/expenses\",\n            element: /*#__PURE__*/_jsxDEV(ExpensesList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 46\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Route, {\n              path: \"new\",\n              element: /*#__PURE__*/_jsxDEV(ExpensesList, {\n                showFormOnLoad: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/statistics\",\n            element: /*#__PURE__*/_jsxDEV(Statistics, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/settings\",\n            element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 6000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'right'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"xw5GP9aspIJfTnrjr4g8tyEWolE=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Box", "CircularProgress", "<PERSON><PERSON>", "Snackbar", "Layout", "Dashboard", "VehiclesList", "RefuelsList", "ExpensesList", "Statistics", "Settings", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "window", "electronAPI", "console", "log", "showNotification", "handleCloseNotification", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "sx", "max<PERSON><PERSON><PERSON>", "future", "v7_startTransition", "v7_relativeSplatPath", "path", "element", "showFormOnLoad", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { Box, CircularProgress, Alert, Snackbar } from '@mui/material';\n\n// Componentes\nimport Layout from './components/Layout/Layout';\nimport Dashboard from './components/Dashboard/Dashboard';\nimport VehiclesList from './components/Vehicles/VehiclesList';\nimport RefuelsList from './components/Refuels/RefuelsList';\nimport ExpensesList from './components/Expenses/ExpensesList';\nimport Statistics from './components/Statistics/Statistics';\nimport Settings from './components/Settings/Settings';\n\n// Context\nimport { AppProvider } from './context/AppContext';\n\nfunction App() {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });\n\n  useEffect(() => {\n    // Verificar si estamos en Electron\n    if (window.electronAPI) {\n      console.log('✅ Aplicación ejecutándose en Electron');\n      setLoading(false);\n    } else {\n      console.log('🌐 Aplicación ejecutándose en navegador (modo desarrollo)');\n      // En modo desarrollo web, simular datos\n      setLoading(false);\n    }\n  }, []);\n\n  const showNotification = (message, severity = 'info') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  const handleCloseNotification = () => {\n    setNotification({ ...notification, open: false });\n  };\n\n  if (loading) {\n    return (\n      <Box \n        display=\"flex\" \n        justifyContent=\"center\" \n        alignItems=\"center\" \n        minHeight=\"100vh\"\n      >\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box \n        display=\"flex\" \n        justifyContent=\"center\" \n        alignItems=\"center\" \n        minHeight=\"100vh\"\n        p={3}\n      >\n        <Alert severity=\"error\" sx={{ maxWidth: 500 }}>\n          Error al inicializar la aplicación: {error}\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <AppProvider>\n      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>\n        <Layout>\n          <Routes>\n            <Route path=\"/\" element={<Dashboard />} />\n            <Route path=\"/vehicles\" element={<VehiclesList />} />\n            <Route path=\"/refuels\" element={<RefuelsList />}>\n              <Route path=\"new\" element={<RefuelsList showFormOnLoad={true} />} />\n            </Route>\n            <Route path=\"/expenses\" element={<ExpensesList />}>\n              <Route path=\"new\" element={<ExpensesList showFormOnLoad={true} />} />\n            </Route>\n            <Route path=\"/statistics\" element={<Statistics />} />\n            <Route path=\"/settings\" element={<Settings />} />\n          </Routes>\n        </Layout>\n        \n        <Snackbar\n          open={notification.open}\n          autoHideDuration={6000}\n          onClose={handleCloseNotification}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n        >\n          <Alert \n            onClose={handleCloseNotification} \n            severity={notification.severity}\n            sx={{ width: '100%' }}\n          >\n            {notification.message}\n          </Alert>\n        </Snackbar>\n      </Router>\n    </AppProvider>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;;AAEtE;AACA,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,QAAQ,MAAM,gCAAgC;;AAErD;AACA,SAASC,WAAW,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC;IAAE4B,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAO,CAAC,CAAC;EAEhG7B,SAAS,CAAC,MAAM;IACd;IACA,IAAI8B,MAAM,CAACC,WAAW,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpDX,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,MAAM;MACLU,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE;MACAX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,gBAAgB,GAAGA,CAACN,OAAO,EAAEC,QAAQ,GAAG,MAAM,KAAK;IACvDH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;EAED,MAAMM,uBAAuB,GAAGA,CAAA,KAAM;IACpCT,eAAe,CAAC;MAAE,GAAGD,YAAY;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EACnD,CAAC;EAED,IAAIN,OAAO,EAAE;IACX,oBACEH,OAAA,CAACb,GAAG;MACF+B,OAAO,EAAC,MAAM;MACdC,cAAc,EAAC,QAAQ;MACvBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAEjBtB,OAAA,CAACZ,gBAAgB;QAACmC,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAItB,KAAK,EAAE;IACT,oBACEL,OAAA,CAACb,GAAG;MACF+B,OAAO,EAAC,MAAM;MACdC,cAAc,EAAC,QAAQ;MACvBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAC,OAAO;MACjBO,CAAC,EAAE,CAAE;MAAAN,QAAA,eAELtB,OAAA,CAACX,KAAK;QAACsB,QAAQ,EAAC,OAAO;QAACkB,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAR,QAAA,GAAC,yCACT,EAACjB,KAAK;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACE3B,OAAA,CAACF,WAAW;IAAAwB,QAAA,eACVtB,OAAA,CAAChB,MAAM;MAAC+C,MAAM,EAAE;QAAEC,kBAAkB,EAAE,IAAI;QAAEC,oBAAoB,EAAE;MAAK,CAAE;MAAAX,QAAA,gBACvEtB,OAAA,CAACT,MAAM;QAAA+B,QAAA,eACLtB,OAAA,CAACf,MAAM;UAAAqC,QAAA,gBACLtB,OAAA,CAACd,KAAK;YAACgD,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEnC,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C3B,OAAA,CAACd,KAAK;YAACgD,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEnC,OAAA,CAACP,YAAY;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrD3B,OAAA,CAACd,KAAK;YAACgD,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEnC,OAAA,CAACN,WAAW;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,eAC9CtB,OAAA,CAACd,KAAK;cAACgD,IAAI,EAAC,KAAK;cAACC,OAAO,eAAEnC,OAAA,CAACN,WAAW;gBAAC0C,cAAc,EAAE;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACR3B,OAAA,CAACd,KAAK;YAACgD,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEnC,OAAA,CAACL,YAAY;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,eAChDtB,OAAA,CAACd,KAAK;cAACgD,IAAI,EAAC,KAAK;cAACC,OAAO,eAAEnC,OAAA,CAACL,YAAY;gBAACyC,cAAc,EAAE;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACR3B,OAAA,CAACd,KAAK;YAACgD,IAAI,EAAC,aAAa;YAACC,OAAO,eAAEnC,OAAA,CAACJ,UAAU;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrD3B,OAAA,CAACd,KAAK;YAACgD,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEnC,OAAA,CAACH,QAAQ;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAET3B,OAAA,CAACV,QAAQ;QACPmB,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxB4B,gBAAgB,EAAE,IAAK;QACvBC,OAAO,EAAErB,uBAAwB;QACjCsB,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAAnB,QAAA,eAE1DtB,OAAA,CAACX,KAAK;UACJiD,OAAO,EAAErB,uBAAwB;UACjCN,QAAQ,EAAEJ,YAAY,CAACI,QAAS;UAChCkB,EAAE,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAE;UAAApB,QAAA,EAErBf,YAAY,CAACG;QAAO;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAElB;AAACzB,EAAA,CAzFQD,GAAG;AAAA0C,EAAA,GAAH1C,GAAG;AA2FZ,eAAeA,GAAG;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}