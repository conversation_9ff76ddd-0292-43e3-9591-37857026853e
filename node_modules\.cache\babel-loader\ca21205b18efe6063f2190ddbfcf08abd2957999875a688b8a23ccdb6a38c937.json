{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useNow = exports.useDefaultDates = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _usePickerAdapter = require(\"../../hooks/usePickerAdapter\");\nconst useDefaultDates = () => (0, _usePickerAdapter.useLocalizationContext)().defaultDates;\nexports.useDefaultDates = useDefaultDates;\nconst useNow = timezone => {\n  const adapter = (0, _usePickerAdapter.usePickerAdapter)();\n  const now = React.useRef(undefined);\n  if (now.current === undefined) {\n    now.current = adapter.date(undefined, timezone);\n  }\n  return now.current;\n};\nexports.useNow = useNow;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "Object", "defineProperty", "exports", "value", "useNow", "useDefaultDates", "React", "_usePickerAdapter", "useLocalizationContext", "defaultDates", "timezone", "adapter", "usePickerAdapter", "now", "useRef", "undefined", "current", "date"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/useUtils.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useNow = exports.useDefaultDates = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _usePickerAdapter = require(\"../../hooks/usePickerAdapter\");\nconst useDefaultDates = () => (0, _usePickerAdapter.useLocalizationContext)().defaultDates;\nexports.useDefaultDates = useDefaultDates;\nconst useNow = timezone => {\n  const adapter = (0, _usePickerAdapter.usePickerAdapter)();\n  const now = React.useRef(undefined);\n  if (now.current === undefined) {\n    now.current = adapter.date(undefined, timezone);\n  }\n  return now.current;\n};\nexports.useNow = useNow;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,MAAM,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AACjD,IAAIC,KAAK,GAAGT,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,iBAAiB,GAAGT,OAAO,CAAC,8BAA8B,CAAC;AAC/D,MAAMO,eAAe,GAAGA,CAAA,KAAM,CAAC,CAAC,EAAEE,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,CAACC,YAAY;AAC1FP,OAAO,CAACG,eAAe,GAAGA,eAAe;AACzC,MAAMD,MAAM,GAAGM,QAAQ,IAAI;EACzB,MAAMC,OAAO,GAAG,CAAC,CAAC,EAAEJ,iBAAiB,CAACK,gBAAgB,EAAE,CAAC;EACzD,MAAMC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAACC,SAAS,CAAC;EACnC,IAAIF,GAAG,CAACG,OAAO,KAAKD,SAAS,EAAE;IAC7BF,GAAG,CAACG,OAAO,GAAGL,OAAO,CAACM,IAAI,CAACF,SAAS,EAAEL,QAAQ,CAAC;EACjD;EACA,OAAOG,GAAG,CAACG,OAAO;AACpB,CAAC;AACDd,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}