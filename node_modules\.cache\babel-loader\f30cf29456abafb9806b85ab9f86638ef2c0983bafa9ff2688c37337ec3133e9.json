{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useParsedFormat = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _usePickerAdapter = require(\"./usePickerAdapter\");\nvar _buildSectionsFromFormat = require(\"../internals/hooks/useField/buildSectionsFromFormat\");\nvar _useField = require(\"../internals/hooks/useField/useField.utils\");\nvar _usePickerTranslations = require(\"./usePickerTranslations\");\nvar _useNullablePickerContext = require(\"../internals/hooks/useNullablePickerContext\");\n/**\n * Returns the parsed format to be rendered in the field when there is no value or in other parts of the Picker.\n * This format is localized (for example `AAAA` for the year with the French locale) and cannot be parsed by your date library.\n * @param {object} The parameters needed to build the placeholder.\n * @param {string} params.format Format to parse.\n * @returns\n */\nconst useParsedFormat = (parameters = {}) => {\n  const pickerContext = (0, _useNullablePickerContext.useNullablePickerContext)();\n  const adapter = (0, _usePickerAdapter.usePickerAdapter)();\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const translations = (0, _usePickerTranslations.usePickerTranslations)();\n  const localizedDigits = React.useMemo(() => (0, _useField.getLocalizedDigits)(adapter), [adapter]);\n  const {\n    format = pickerContext?.fieldFormat ?? adapter.formats.fullDate\n  } = parameters;\n  return React.useMemo(() => {\n    const sections = (0, _buildSectionsFromFormat.buildSectionsFromFormat)({\n      adapter,\n      format,\n      formatDensity: 'dense',\n      isRtl,\n      shouldRespectLeadingZeros: true,\n      localeText: translations,\n      localizedDigits,\n      date: null,\n      // TODO v9: Make sure we still don't reverse in `buildSectionsFromFormat` when using `useParsedFormat`.\n      enableAccessibleFieldDOMStructure: false\n    });\n    return sections.map(section => `${section.startSeparator}${section.placeholder}${section.endSeparator}`).join('');\n  }, [adapter, isRtl, translations, localizedDigits, format]);\n};\nexports.useParsedFormat = useParsedFormat;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "Object", "defineProperty", "exports", "value", "useParsedFormat", "React", "_RtlProvider", "_usePickerAdapter", "_buildSectionsFromFormat", "_useField", "_usePickerTranslations", "_useNullablePickerContext", "parameters", "picker<PERSON>ontext", "useNullablePickerContext", "adapter", "usePickerAdapter", "isRtl", "useRtl", "translations", "usePickerTranslations", "localizedDigits", "useMemo", "getLocalizedDigits", "format", "fieldFormat", "formats", "fullDate", "sections", "buildSectionsFromFormat", "formatDensity", "shouldRespectLeadingZeros", "localeText", "date", "enableAccessibleFieldDOMStructure", "map", "section", "startSeparator", "placeholder", "endSeparator", "join"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/hooks/useParsedFormat.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useParsedFormat = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _usePickerAdapter = require(\"./usePickerAdapter\");\nvar _buildSectionsFromFormat = require(\"../internals/hooks/useField/buildSectionsFromFormat\");\nvar _useField = require(\"../internals/hooks/useField/useField.utils\");\nvar _usePickerTranslations = require(\"./usePickerTranslations\");\nvar _useNullablePickerContext = require(\"../internals/hooks/useNullablePickerContext\");\n/**\n * Returns the parsed format to be rendered in the field when there is no value or in other parts of the Picker.\n * This format is localized (for example `AAAA` for the year with the French locale) and cannot be parsed by your date library.\n * @param {object} The parameters needed to build the placeholder.\n * @param {string} params.format Format to parse.\n * @returns\n */\nconst useParsedFormat = (parameters = {}) => {\n  const pickerContext = (0, _useNullablePickerContext.useNullablePickerContext)();\n  const adapter = (0, _usePickerAdapter.usePickerAdapter)();\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const translations = (0, _usePickerTranslations.usePickerTranslations)();\n  const localizedDigits = React.useMemo(() => (0, _useField.getLocalizedDigits)(adapter), [adapter]);\n  const {\n    format = pickerContext?.fieldFormat ?? adapter.formats.fullDate\n  } = parameters;\n  return React.useMemo(() => {\n    const sections = (0, _buildSectionsFromFormat.buildSectionsFromFormat)({\n      adapter,\n      format,\n      formatDensity: 'dense',\n      isRtl,\n      shouldRespectLeadingZeros: true,\n      localeText: translations,\n      localizedDigits,\n      date: null,\n      // TODO v9: Make sure we still don't reverse in `buildSectionsFromFormat` when using `useParsedFormat`.\n      enableAccessibleFieldDOMStructure: false\n    });\n    return sections.map(section => `${section.startSeparator}${section.placeholder}${section.endSeparator}`).join('');\n  }, [adapter, isRtl, translations, localizedDigits, format]);\n};\nexports.useParsedFormat = useParsedFormat;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChC,IAAIC,KAAK,GAAGR,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,YAAY,GAAGR,OAAO,CAAC,yBAAyB,CAAC;AACrD,IAAIS,iBAAiB,GAAGT,OAAO,CAAC,oBAAoB,CAAC;AACrD,IAAIU,wBAAwB,GAAGV,OAAO,CAAC,qDAAqD,CAAC;AAC7F,IAAIW,SAAS,GAAGX,OAAO,CAAC,4CAA4C,CAAC;AACrE,IAAIY,sBAAsB,GAAGZ,OAAO,CAAC,yBAAyB,CAAC;AAC/D,IAAIa,yBAAyB,GAAGb,OAAO,CAAC,6CAA6C,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,eAAe,GAAGA,CAACQ,UAAU,GAAG,CAAC,CAAC,KAAK;EAC3C,MAAMC,aAAa,GAAG,CAAC,CAAC,EAAEF,yBAAyB,CAACG,wBAAwB,EAAE,CAAC;EAC/E,MAAMC,OAAO,GAAG,CAAC,CAAC,EAAER,iBAAiB,CAACS,gBAAgB,EAAE,CAAC;EACzD,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEX,YAAY,CAACY,MAAM,EAAE,CAAC;EACxC,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAET,sBAAsB,CAACU,qBAAqB,EAAE,CAAC;EACxE,MAAMC,eAAe,GAAGhB,KAAK,CAACiB,OAAO,CAAC,MAAM,CAAC,CAAC,EAAEb,SAAS,CAACc,kBAAkB,EAAER,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAClG,MAAM;IACJS,MAAM,GAAGX,aAAa,EAAEY,WAAW,IAAIV,OAAO,CAACW,OAAO,CAACC;EACzD,CAAC,GAAGf,UAAU;EACd,OAAOP,KAAK,CAACiB,OAAO,CAAC,MAAM;IACzB,MAAMM,QAAQ,GAAG,CAAC,CAAC,EAAEpB,wBAAwB,CAACqB,uBAAuB,EAAE;MACrEd,OAAO;MACPS,MAAM;MACNM,aAAa,EAAE,OAAO;MACtBb,KAAK;MACLc,yBAAyB,EAAE,IAAI;MAC/BC,UAAU,EAAEb,YAAY;MACxBE,eAAe;MACfY,IAAI,EAAE,IAAI;MACV;MACAC,iCAAiC,EAAE;IACrC,CAAC,CAAC;IACF,OAAON,QAAQ,CAACO,GAAG,CAACC,OAAO,IAAI,GAAGA,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,YAAY,EAAE,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACnH,CAAC,EAAE,CAACzB,OAAO,EAAEE,KAAK,EAAEE,YAAY,EAAEE,eAAe,EAAEG,MAAM,CAAC,CAAC;AAC7D,CAAC;AACDtB,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}