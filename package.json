{"name": "re<PERSON><PERSON><PERSON>-manager", "version": "1.0.0", "description": "Sistema de gestión de repostajes y gastos vehiculares", "main": "public/electron.js", "homepage": "./", "scripts": {"start": "react-scripts start", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "build": "react-scripts build", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "build-portable": "node build-portable.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "build": {"appId": "com.repostajes.manager", "productName": "Repostajes Manager", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "server/**/*", "node_modules/**/*"], "extraResources": [{"from": "server", "to": "server"}], "win": {"target": {"target": "portable", "arch": ["x64"]}, "icon": "public/favicon.ico"}, "portable": {"artifactName": "${productName}-${version}-portable.exe"}}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.0", "@mui/material": "^5.15.0", "@mui/x-date-pickers": "^8.10.0", "cors": "^2.8.5", "date-fns": "^2.30.0", "express": "^5.1.0", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "path": "^0.12.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "recharts": "^2.15.4", "sqlite3": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.3.3", "electron-builder": "^24.13.3", "electron-is-dev": "^3.0.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "wait-on": "^7.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}