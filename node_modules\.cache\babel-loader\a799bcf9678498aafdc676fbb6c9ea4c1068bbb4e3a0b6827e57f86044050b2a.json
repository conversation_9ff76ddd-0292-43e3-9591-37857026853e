{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = capitalize;\nvar _formatMuiErrorMessage = _interopRequireDefault(require(\"@mui/utils/formatMuiErrorMessage\"));\n// It should to be noted that this function isn't equivalent to `text-transform: capitalize`.\n//\n// A strict capitalization should uppercase the first letter of each word in the sentence.\n// We only handle the first word.\nfunction capitalize(string) {\n  if (typeof string !== 'string') {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `capitalize(string)` expects a string argument.' : (0, _formatMuiErrorMessage.default)(7));\n  }\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "capitalize", "_formatMuiErrorMessage", "string", "Error", "process", "env", "NODE_ENV", "char<PERSON>t", "toUpperCase", "slice"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/capitalize/capitalize.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = capitalize;\nvar _formatMuiErrorMessage = _interopRequireDefault(require(\"@mui/utils/formatMuiErrorMessage\"));\n// It should to be noted that this function isn't equivalent to `text-transform: capitalize`.\n//\n// A strict capitalization should uppercase the first letter of each word in the sentence.\n// We only handle the first word.\nfunction capitalize(string) {\n  if (typeof string !== 'string') {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `capitalize(string)` expects a string argument.' : (0, _formatMuiErrorMessage.default)(7));\n  }\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAGK,UAAU;AAC5B,IAAIC,sBAAsB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,kCAAkC,CAAC,CAAC;AAChG;AACA;AACA;AACA;AACA,SAASM,UAAUA,CAACE,MAAM,EAAE;EAC1B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,sDAAsD,GAAG,CAAC,CAAC,EAAEL,sBAAsB,CAACN,OAAO,EAAE,CAAC,CAAC,CAAC;EAC1J;EACA,OAAOO,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}