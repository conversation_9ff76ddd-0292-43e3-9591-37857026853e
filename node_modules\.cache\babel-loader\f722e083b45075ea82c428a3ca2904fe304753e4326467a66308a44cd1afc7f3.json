{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Expenses\\\\ExpensesList.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { styled } from '@mui/material/styles';\nimport { Box, Typography, Card, CardContent, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Paper, Chip, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Receipt as ReceiptIcon } from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\n\n// Styled components for better visual hierarchy\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledTableCell = styled(TableCell)(({\n  theme\n}) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '12px 16px',\n  '&:first-of-type': {\n    paddingLeft: 24\n  },\n  '&:last-child': {\n    paddingRight: 24\n  }\n}));\n_c = StyledTableCell;\nconst StyledTableRow = styled(TableRow)(({\n  theme\n}) => ({\n  '&:nth-of-type(even)': {\n    backgroundColor: theme.palette.action.hover\n  },\n  '&:hover': {\n    backgroundColor: theme.palette.action.selected\n  },\n  '&:last-child td': {\n    borderBottom: 0\n  }\n}));\n_c2 = StyledTableRow;\nconst PageHeader = styled(Box)(({\n  theme\n}) => ({\n  background: theme.palette.background.paper,\n  padding: theme.spacing(3, 4),\n  borderRadius: theme.shape.borderRadius,\n  boxShadow: theme.shadows[1],\n  marginBottom: theme.spacing(3),\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center'\n}));\n_c3 = PageHeader;\nconst PrimaryButton = styled(Button)(({\n  theme\n}) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-1px)',\n    boxShadow: theme.shadows[2]\n  },\n  transition: 'all 0.2s ease'\n}));\n_c4 = PrimaryButton;\nconst ExpenseDialog = ({\n  open,\n  onClose,\n  expense,\n  onSave,\n  vehicles\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: ''\n  });\n  const expenseTypes = ['Mantenimiento', 'Reparación', 'Seguro', 'ITV', 'Impuestos', 'Neumáticos', 'Aceite', 'Filtros', 'Frenos', 'Batería', 'Otros'];\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0] // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: ''\n      });\n    }\n  }, [expense, vehicles, open]);\n  const handleChange = field => event => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value\n    });\n  };\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && formData.coste && formData.descripcion;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: expense ? 'Editar Gasto' : 'Nuevo Gasto'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: 2,\n        pt: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Veh\\xEDculo\",\n          select: true,\n          value: formData.vehiculo_id,\n          onChange: handleChange('vehiculo_id'),\n          fullWidth: true,\n          required: true,\n          children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: vehicle.id,\n            children: vehicle.nombre\n          }, vehicle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Fecha\",\n            type: \"date\",\n            value: formData.fecha,\n            onChange: handleChange('fecha'),\n            fullWidth: true,\n            required: true,\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Kilometraje actual\",\n            type: \"number\",\n            value: formData.kilometros_actuales,\n            onChange: handleChange('kilometros_actuales'),\n            fullWidth: true,\n            inputProps: {\n              min: 0\n            },\n            helperText: \"Opcional\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Tipo de gasto\",\n            select: true,\n            value: formData.tipo_gasto,\n            onChange: handleChange('tipo_gasto'),\n            fullWidth: true,\n            required: true,\n            children: expenseTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: type,\n              children: type\n            }, type, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Coste (\\u20AC)\",\n            type: \"number\",\n            value: formData.coste,\n            onChange: handleChange('coste'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Descripci\\xF3n\",\n          value: formData.descripcion,\n          onChange: handleChange('descripcion'),\n          fullWidth: true,\n          required: true,\n          multiline: true,\n          rows: 2,\n          placeholder: \"Describe el gasto realizado...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Proveedor/Taller\",\n          value: formData.proveedor,\n          onChange: handleChange('proveedor'),\n          fullWidth: true,\n          placeholder: \"Nombre del taller, tienda, etc.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: !isValid,\n        children: expense ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpenseDialog, \"hAHDk6I+hYE0GG43vBPHqnsZ1HE=\");\n_c5 = ExpenseDialog;\nexport default function ExpensesList() {\n  _s2();\n  const {\n    vehicles,\n    expenses,\n    loadExpenses,\n    addExpense\n  } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n\n  // Color mapping for different expense types\n  const expenseTypeColors = {\n    'Mantenimiento': 'primary.main',\n    'Reparación': 'error.main',\n    'Seguro': 'info.main',\n    'ITV': 'secondary.main',\n    'Impuestos': 'warning.main',\n    'Neumáticos': 'success.main',\n    'Aceite': 'info.dark',\n    'Filtros': 'info.light',\n    'Frenos': 'error.dark',\n    'Batería': 'warning.dark',\n    'Otros': 'text.secondary'\n  };\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    setDialogOpen(true);\n  };\n  const handleEditExpense = expense => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n  const handleDeleteExpense = expense => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n  const handleSaveExpense = async expenseData => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\n      } else {\n        await addExpense(expenseData);\n        // Recargar la lista\n        loadExpenses(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving expense:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n  const getExpenseTypeColor = type => {\n    const colors = {\n      'Mantenimiento': 'primary',\n      'Reparación': 'error',\n      'Seguro': 'info',\n      'ITV': 'warning',\n      'Impuestos': 'secondary',\n      'Neumáticos': 'success'\n    };\n    return colors[type] || 'default';\n  };\n\n  // Calcular estadísticas rápidas\n  const totalExpenses = expenses.length;\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\n\n  // Agrupar por tipo\n  const expensesByType = expenses.reduce((acc, expense) => {\n    const type = expense.tipo_gasto || 'Otros';\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\n    return acc;\n  }, {});\n  const topExpenseType = Object.entries(expensesByType).sort((a, b) => b[1] - a[1]).shift();\n\n  // Pagination\n  const paginatedExpenses = expenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: {\n        xs: 2,\n        md: 3\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          fontWeight: \"bold\",\n          color: \"primary\",\n          children: \"Gastos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Gesti\\xF3n de gastos del veh\\xEDculo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddExpense,\n        disabled: vehicles.length === 0,\n        children: \"Nuevo Gasto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary\",\n            children: totalExpenses\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Total Gastos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"error.main\",\n            children: formatCurrency(totalCost)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Coste Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"warning.main\",\n            children: formatCurrency(avgExpense)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Gasto Promedio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"info.main\",\n            children: topExpenseType ? topExpenseType[0] : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Tipo M\\xE1s Frecuente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 5\n    }, this), expenses.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        bgcolor: 'background.default',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.05)',\n        borderRadius: 2,\n        overflow: 'hidden',\n        textAlign: 'center',\n        p: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n        sx: {\n          fontSize: 64,\n          color: 'primary.light',\n          mb: 2,\n          opacity: 0.8\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        gutterBottom: true,\n        children: \"No hay gastos registrados\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        mb: 3,\n        children: \"Comienza registrando tu primer gasto para llevar un control de los costes.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddExpense,\n        disabled: vehicles.length === 0,\n        children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Gasto'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        borderRadius: 2,\n        overflow: 'hidden',\n        boxShadow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: 'background.paper',\n                '& th': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                  py: 2,\n                  '&:first-of-type': {\n                    pl: 3\n                  },\n                  '&:last-child': {\n                    pr: 3\n                  }\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Fecha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Tipo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: \"Coste\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: \"Proveedor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"center\",\n                children: \"Acciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: paginatedExpenses.map(expense => /*#__PURE__*/_jsxDEV(StyledTableRow, {\n              children: [/*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: formatDate(expense.fecha)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: 500,\n                  children: expense.vehiculo_nombre\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: expense.tipo_gasto,\n                  size: \"small\",\n                  sx: {\n                    bgcolor: `${expenseTypeColors[expense.tipo_gasto] || 'grey.200'}20`,\n                    color: expenseTypeColors[expense.tipo_gasto] || 'text.primary',\n                    fontWeight: 500,\n                    border: `1px solid ${expenseTypeColors[expense.tipo_gasto] || 'grey.300'}40`,\n                    borderRadius: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  noWrap: true,\n                  children: expense.descripcion\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  color: \"primary\",\n                  children: formatCurrency(expense.coste)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: expense.proveedor || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StyledTableCell, {\n                align: \"center\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1,\n                    justifyContent: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Editar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleEditExpense(expense),\n                      sx: {\n                        color: 'primary.main',\n                        '&:hover': {\n                          bgcolor: 'primary.light',\n                          color: 'primary.contrastText'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 514,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Eliminar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleDeleteExpense(expense),\n                      sx: {\n                        color: 'error.main',\n                        '&:hover': {\n                          bgcolor: 'error.light',\n                          color: 'error.contrastText'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 526,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this)]\n            }, expense.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          p: 2,\n          borderTop: '1px solid',\n          borderColor: 'divider',\n          bgcolor: 'background.paper'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Mostrando \", paginatedExpenses.length, \" de \", expenses.length, \" gastos\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n          rowsPerPageOptions: [10, 25, 50],\n          component: \"div\",\n          count: expenses.length,\n          rowsPerPage: rowsPerPage,\n          page: page,\n          onPageChange: (e, newPage) => setPage(newPage),\n          onRowsPerPageChange: e => {\n            setRowsPerPage(parseInt(e.target.value, 10));\n            setPage(0);\n          },\n          labelRowsPerPage: \"Filas por p\\xE1gina:\",\n          labelDisplayedRows: ({\n            from,\n            to,\n            count\n          }) => `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`,\n          sx: {\n            '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n              marginBottom: 0\n            },\n            '& .MuiTablePagination-toolbar': {\n              padding: 0\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ExpenseDialog, {\n      open: dialogOpen,\n      onClose: () => setDialogOpen(false),\n      expense: selectedExpense,\n      onSave: handleSaveExpense,\n      vehicles: vehicles\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 360,\n    columnNumber: 5\n  }, this);\n}\n_s2(ExpensesList, \"XX6fvOdJiW/NptiQILzK1bySeUw=\", false, function () {\n  return [useApp];\n});\n_c6 = ExpensesList;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StyledTableCell\");\n$RefreshReg$(_c2, \"StyledTableRow\");\n$RefreshReg$(_c3, \"PageHeader\");\n$RefreshReg$(_c4, \"PrimaryButton\");\n$RefreshReg$(_c5, \"ExpenseDialog\");\n$RefreshReg$(_c6, \"ExpensesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "format", "es", "styled", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "Paper", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Receipt", "ReceiptIcon", "useApp", "jsxDEV", "_jsxDEV", "StyledTableCell", "theme", "borderBottom", "palette", "divider", "padding", "paddingLeft", "paddingRight", "_c", "StyledTableRow", "backgroundColor", "action", "hover", "selected", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "background", "paper", "spacing", "borderRadius", "shape", "boxShadow", "shadows", "marginBottom", "display", "justifyContent", "alignItems", "_c3", "PrimaryButton", "textTransform", "fontWeight", "transform", "transition", "_c4", "ExpenseDialog", "open", "onClose", "expense", "onSave", "vehicles", "_s", "formData", "setFormData", "vehiculo_id", "fecha", "Date", "toISOString", "split", "kilometros_actuales", "tipo_gasto", "coste", "descripcion", "proveedor", "expenseTypes", "length", "id", "handleChange", "field", "event", "target", "value", "handleSubmit", "dataToSave", "parseFloat", "parseInt", "categoria", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "gap", "pt", "label", "select", "onChange", "required", "map", "vehicle", "nombre", "type", "InputLabelProps", "shrink", "inputProps", "min", "helperText", "step", "multiline", "rows", "placeholder", "onClick", "variant", "disabled", "_c5", "ExpensesList", "_s2", "expenses", "loadExpenses", "addExpense", "dialogOpen", "setDialogOpen", "selectedExpense", "setSelectedExpense", "page", "setPage", "rowsPerPage", "setRowsPerPage", "expenseTypeColors", "handleAddExpense", "handleEditExpense", "handleDeleteExpense", "console", "log", "handleSaveExpense", "expenseData", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "locale", "formatNumber", "num", "getExpenseTypeColor", "colors", "totalExpenses", "totalCost", "reduce", "sum", "avgExpense", "expensesByType", "acc", "topExpenseType", "Object", "entries", "sort", "a", "b", "shift", "paginatedExpenses", "slice", "sx", "p", "xs", "md", "component", "color", "startIcon", "mb", "flex", "textAlign", "bgcolor", "overflow", "fontSize", "opacity", "gutterBottom", "borderColor", "py", "pl", "pr", "align", "vehiculo_nombre", "size", "border", "noWrap", "title", "borderTop", "rowsPerPageOptions", "count", "onPageChange", "e", "newPage", "onRowsPerPageChange", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c6", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Expenses/ExpensesList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { styled } from '@mui/material/styles';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  Paper,\n  Chip,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Receipt as ReceiptIcon,\n} from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\n\n// Styled components for better visual hierarchy\nconst StyledTableCell = styled(TableCell)(({ theme }) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '12px 16px',\n  '&:first-of-type': { paddingLeft: 24 },\n  '&:last-child': { paddingRight: 24 }\n}));\n\nconst StyledTableRow = styled(TableRow)(({ theme }) => ({\n  '&:nth-of-type(even)': {\n    backgroundColor: theme.palette.action.hover,\n  },\n  '&:hover': {\n    backgroundColor: theme.palette.action.selected,\n  },\n  '&:last-child td': {\n    borderBottom: 0,\n  },\n}));\n\nconst PageHeader = styled(Box)(({ theme }) => ({\n  background: theme.palette.background.paper,\n  padding: theme.spacing(3, 4),\n  borderRadius: theme.shape.borderRadius,\n  boxShadow: theme.shadows[1],\n  marginBottom: theme.spacing(3),\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n}));\n\nconst PrimaryButton = styled(Button)(({ theme }) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-1px)',\n    boxShadow: theme.shadows[2],\n  },\n  transition: 'all 0.2s ease',\n}));\n\nconst ExpenseDialog = ({ open, onClose, expense, onSave, vehicles }) => {\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: '',\n  });\n\n  const expenseTypes = [\n    'Mantenimiento',\n    'Reparación',\n    'Seguro',\n    'ITV',\n    'Impuestos',\n    'Neumáticos',\n    'Aceite',\n    'Filtros',\n    'Frenos',\n    'Batería',\n    'Otros'\n  ];\n\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0], // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: '',\n      });\n    }\n  }, [expense, vehicles, open]);\n\n  const handleChange = (field) => (event) => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value,\n    });\n  };\n\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto, // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && \n                  formData.coste && formData.descripcion;\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        {expense ? 'Editar Gasto' : 'Nuevo Gasto'}\n      </DialogTitle>\n      <DialogContent>\n        <Box display=\"flex\" flexDirection=\"column\" gap={2} pt={1}>\n          <TextField\n            label=\"Vehículo\"\n            select\n            value={formData.vehiculo_id}\n            onChange={handleChange('vehiculo_id')}\n            fullWidth\n            required\n          >\n            {vehicles.map((vehicle) => (\n              <MenuItem key={vehicle.id} value={vehicle.id}>\n                {vehicle.nombre}\n              </MenuItem>\n            ))}\n          </TextField>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Fecha\"\n              type=\"date\"\n              value={formData.fecha}\n              onChange={handleChange('fecha')}\n              fullWidth\n              required\n              InputLabelProps={{ shrink: true }}\n            />\n            <TextField\n              label=\"Kilometraje actual\"\n              type=\"number\"\n              value={formData.kilometros_actuales}\n              onChange={handleChange('kilometros_actuales')}\n              fullWidth\n              inputProps={{ min: 0 }}\n              helperText=\"Opcional\"\n            />\n          </Box>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Tipo de gasto\"\n              select\n              value={formData.tipo_gasto}\n              onChange={handleChange('tipo_gasto')}\n              fullWidth\n              required\n            >\n              {expenseTypes.map((type) => (\n                <MenuItem key={type} value={type}>\n                  {type}\n                </MenuItem>\n              ))}\n            </TextField>\n            <TextField\n              label=\"Coste (€)\"\n              type=\"number\"\n              value={formData.coste}\n              onChange={handleChange('coste')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n          </Box>\n\n          <TextField\n            label=\"Descripción\"\n            value={formData.descripcion}\n            onChange={handleChange('descripcion')}\n            fullWidth\n            required\n            multiline\n            rows={2}\n            placeholder=\"Describe el gasto realizado...\"\n          />\n\n          <TextField\n            label=\"Proveedor/Taller\"\n            value={formData.proveedor}\n            onChange={handleChange('proveedor')}\n            fullWidth\n            placeholder=\"Nombre del taller, tienda, etc.\"\n          />\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose}>Cancelar</Button>\n        <Button \n          onClick={handleSubmit} \n          variant=\"contained\"\n          disabled={!isValid}\n        >\n          {expense ? 'Actualizar' : 'Guardar'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default function ExpensesList() {\n  const { vehicles, expenses, loadExpenses, addExpense } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n\n  // Color mapping for different expense types\n  const expenseTypeColors = {\n    'Mantenimiento': 'primary.main',\n    'Reparación': 'error.main',\n    'Seguro': 'info.main',\n    'ITV': 'secondary.main',\n    'Impuestos': 'warning.main',\n    'Neumáticos': 'success.main',\n    'Aceite': 'info.dark',\n    'Filtros': 'info.light',\n    'Frenos': 'error.dark',\n    'Batería': 'warning.dark',\n    'Otros': 'text.secondary'\n  };\n\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    setDialogOpen(true);\n  };\n\n  const handleEditExpense = (expense) => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n\n  const handleDeleteExpense = (expense) => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n\n  const handleSaveExpense = async (expenseData) => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\n      } else {\n        await addExpense(expenseData);\n        // Recargar la lista\n        loadExpenses(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving expense:', error);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  const formatDate = (dateString) => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\n    } catch {\n      return dateString;\n    }\n  };\n\n  const formatNumber = (num) => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  const getExpenseTypeColor = (type) => {\n    const colors = {\n      'Mantenimiento': 'primary',\n      'Reparación': 'error',\n      'Seguro': 'info',\n      'ITV': 'warning',\n      'Impuestos': 'secondary',\n      'Neumáticos': 'success',\n    };\n    return colors[type] || 'default';\n  };\n\n  // Calcular estadísticas rápidas\n  const totalExpenses = expenses.length;\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\n  \n  // Agrupar por tipo\n  const expensesByType = expenses.reduce((acc, expense) => {\n    const type = expense.tipo_gasto || 'Otros';\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\n    return acc;\n  }, {});\n  \n  const topExpenseType = Object.entries(expensesByType)\n    .sort((a, b) => b[1] - a[1])\n    .shift();\n\n  // Pagination\n  const paginatedExpenses = expenses.slice(\n    page * rowsPerPage,\n    page * rowsPerPage + rowsPerPage\n  );\n\n  return (\n    <Box sx={{ p: { xs: 2, md: 3 } }}>\n      <PageHeader>\n        <Box>\n          <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\" color=\"primary\">\n            Gastos\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Gestión de gastos del vehículo\n          </Typography>\n        </Box>\n        <PrimaryButton\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleAddExpense}\n          disabled={vehicles.length === 0}\n        >\n          Nuevo Gasto\n        </PrimaryButton>\n      </PageHeader>\n\n    {/* Estadísticas rápidas */}\n    <Box display=\"flex\" gap={2} mb={3}>\n      <Card sx={{ flex: 1 }}>\n        <CardContent sx={{ textAlign: 'center' }}>\n          <Typography variant=\"h4\" color=\"primary\">{totalExpenses}</Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">Total Gastos</Typography>\n        </CardContent>\n      </Card>\n      <Card sx={{ flex: 1 }}>\n        <CardContent sx={{ textAlign: 'center' }}>\n          <Typography variant=\"h4\" color=\"error.main\">{formatCurrency(totalCost)}</Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">Coste Total</Typography>\n        </CardContent>\n      </Card>\n      <Card sx={{ flex: 1 }}>\n        <CardContent sx={{ textAlign: 'center' }}>\n          <Typography variant=\"h4\" color=\"warning.main\">{formatCurrency(avgExpense)}</Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">Gasto Promedio</Typography>\n        </CardContent>\n      </Card>\n      <Card sx={{ flex: 1 }}>\n        <CardContent sx={{ textAlign: 'center' }}>\n          <Typography variant=\"h6\" color=\"info.main\">\n            {topExpenseType ? topExpenseType[0] : 'N/A'}\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">Tipo Más Frecuente</Typography>\n        </CardContent>\n      </Card>\n    </Box>\n\n    {expenses.length === 0 ? (\n      <Card sx={{\n        bgcolor: 'background.default',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.05)',\n        borderRadius: 2,\n        overflow: 'hidden',\n        textAlign: 'center',\n        p: 4\n      }}>\n        <ReceiptIcon sx={{\n          fontSize: 64,\n          color: 'primary.light',\n          mb: 2,\n          opacity: 0.8\n        }} />\n        <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n          No hay gastos registrados\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" mb={3}>\n          Comienza registrando tu primer gasto para llevar un control de los costes.\n        </Typography>\n        <PrimaryButton\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleAddExpense}\n          disabled={vehicles.length === 0}\n        >\n          {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Gasto'}\n        </PrimaryButton>\n      </Card>\n    ) : (\n      <Card sx={{ borderRadius: 2, overflow: 'hidden', boxShadow: 1 }}>\n        <TableContainer>\n          <Table>\n            <TableHead>\n              <TableRow sx={{\n                bgcolor: 'background.paper',\n                '& th': {\n                  fontWeight: 'bold',\n                  color: 'text.primary',\n                  borderBottom: '2px solid',\n                  borderColor: 'divider',\n                  py: 2,\n                  '&:first-of-type': { pl: 3 },\n                  '&:last-child': { pr: 3 }\n                }\n              }}>\n                <StyledTableCell>Fecha</StyledTableCell>\n                <StyledTableCell>Vehículo</StyledTableCell>\n                <StyledTableCell>Tipo</StyledTableCell>\n                <StyledTableCell>Descripción</StyledTableCell>\n                <StyledTableCell align=\"right\">Coste</StyledTableCell>\n                <StyledTableCell>Proveedor</StyledTableCell>\n                <StyledTableCell align=\"center\">Acciones</StyledTableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {paginatedExpenses.map((expense) => (\n                <StyledTableRow key={expense.id}>\n                  <StyledTableCell>{formatDate(expense.fecha)}</StyledTableCell>\n                  <StyledTableCell>\n                    <Typography variant=\"body2\" fontWeight={500}>\n                      {expense.vehiculo_nombre}\n                    </Typography>\n                  </StyledTableCell>\n                  <StyledTableCell>\n                    <Chip\n                      label={expense.tipo_gasto}\n                      size=\"small\"\n                      sx={{\n                        bgcolor: `${expenseTypeColors[expense.tipo_gasto] || 'grey.200'}20`,\n                        color: expenseTypeColors[expense.tipo_gasto] || 'text.primary',\n                        fontWeight: 500,\n                        border: `1px solid ${expenseTypeColors[expense.tipo_gasto] || 'grey.300'}40`,\n                        borderRadius: 1\n                      }}\n                    />\n                  </StyledTableCell>\n                  <StyledTableCell>\n                    <Typography variant=\"body2\" noWrap>\n                      {expense.descripcion}\n                    </Typography>\n                  </StyledTableCell>\n                  <StyledTableCell align=\"right\">\n                    <Typography variant=\"body2\" fontWeight=\"bold\" color=\"primary\">\n                      {formatCurrency(expense.coste)}\n                    </Typography>\n                  </StyledTableCell>\n                  <StyledTableCell>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {expense.proveedor || '-'}\n                    </Typography>\n                  </StyledTableCell>\n                  <StyledTableCell align=\"center\">\n                    <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>\n                      <Tooltip title=\"Editar\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleEditExpense(expense)}\n                          sx={{\n                            color: 'primary.main',\n                            '&:hover': { bgcolor: 'primary.light', color: 'primary.contrastText' }\n                          }}\n                        >\n                          <EditIcon fontSize=\"small\" />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Eliminar\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleDeleteExpense(expense)}\n                          sx={{\n                            color: 'error.main',\n                            '&:hover': { bgcolor: 'error.light', color: 'error.contrastText' }\n                          }}\n                        >\n                          <DeleteIcon fontSize=\"small\" />\n                        </IconButton>\n                      </Tooltip>\n                    </Box>\n                  </StyledTableCell>\n                </StyledTableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n        <Box sx={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          p: 2,\n          borderTop: '1px solid',\n          borderColor: 'divider',\n          bgcolor: 'background.paper'\n        }}>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Mostrando {paginatedExpenses.length} de {expenses.length} gastos\n          </Typography>\n          <TablePagination\n            rowsPerPageOptions={[10, 25, 50]}\n            component=\"div\"\n            count={expenses.length}\n            rowsPerPage={rowsPerPage}\n            page={page}\n            onPageChange={(e, newPage) => setPage(newPage)}\n            onRowsPerPageChange={(e) => {\n              setRowsPerPage(parseInt(e.target.value, 10));\n              setPage(0);\n            }}\n            labelRowsPerPage=\"Filas por página:\"\n            labelDisplayedRows={({ from, to, count }) =>\n              `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`\n            }\n            sx={{\n              '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {\n                marginBottom: 0\n              },\n              '& .MuiTablePagination-toolbar': {\n                padding: 0\n              }\n            }}\n          />\n        </Box>\n      </Card>\n    )}\n\n    <ExpenseDialog\n      open={dialogOpen}\n      onClose={() => setDialogOpen(false)}\n      expense={selectedExpense}\n      onSave={handleSaveExpense}\n      vehicles={vehicles}\n    />\n  </Box>\n  );\n}"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,eAAe,EACfC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,0BAA0B;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAGlC,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;EAAE2B;AAAM,CAAC,MAAM;EACxDC,YAAY,EAAE,aAAaD,KAAK,CAACE,OAAO,CAACC,OAAO,EAAE;EAClDC,OAAO,EAAE,WAAW;EACpB,iBAAiB,EAAE;IAAEC,WAAW,EAAE;EAAG,CAAC;EACtC,cAAc,EAAE;IAAEC,YAAY,EAAE;EAAG;AACrC,CAAC,CAAC,CAAC;AAACC,EAAA,GALER,eAAe;AAOrB,MAAMS,cAAc,GAAG3C,MAAM,CAACW,QAAQ,CAAC,CAAC,CAAC;EAAEwB;AAAM,CAAC,MAAM;EACtD,qBAAqB,EAAE;IACrBS,eAAe,EAAET,KAAK,CAACE,OAAO,CAACQ,MAAM,CAACC;EACxC,CAAC;EACD,SAAS,EAAE;IACTF,eAAe,EAAET,KAAK,CAACE,OAAO,CAACQ,MAAM,CAACE;EACxC,CAAC;EACD,iBAAiB,EAAE;IACjBX,YAAY,EAAE;EAChB;AACF,CAAC,CAAC,CAAC;AAACY,GAAA,GAVEL,cAAc;AAYpB,MAAMM,UAAU,GAAGjD,MAAM,CAACC,GAAG,CAAC,CAAC,CAAC;EAAEkC;AAAM,CAAC,MAAM;EAC7Ce,UAAU,EAAEf,KAAK,CAACE,OAAO,CAACa,UAAU,CAACC,KAAK;EAC1CZ,OAAO,EAAEJ,KAAK,CAACiB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5BC,YAAY,EAAElB,KAAK,CAACmB,KAAK,CAACD,YAAY;EACtCE,SAAS,EAAEpB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;EAC3BC,YAAY,EAAEtB,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC;EAC9BM,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,eAAe;EAC/BC,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAACC,GAAA,GATEZ,UAAU;AAWhB,MAAMa,aAAa,GAAG9D,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC;EAAE8B;AAAM,CAAC,MAAM;EACnD4B,aAAa,EAAE,MAAM;EACrBC,UAAU,EAAE,GAAG;EACfzB,OAAO,EAAE,UAAU;EACnBc,YAAY,EAAE,CAAC;EACfE,SAAS,EAAE,MAAM;EACjB,SAAS,EAAE;IACTU,SAAS,EAAE,kBAAkB;IAC7BV,SAAS,EAAEpB,KAAK,CAACqB,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDU,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAACC,GAAA,GAXEL,aAAa;AAanB,MAAMM,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC;IACvCiF,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,mBAAmB,EAAE,EAAE;IACvBC,UAAU,EAAE,eAAe;IAC3BC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,CACnB,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,CACR;EAED1F,SAAS,CAAC,MAAM;IACd,IAAI0E,OAAO,EAAE;MACXK,WAAW,CAAC;QACV,GAAGL,OAAO;QACVO,KAAK,EAAEP,OAAO,CAACO,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE;MACtC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,WAAW,CAAC;QACVC,WAAW,EAAEJ,QAAQ,CAACe,MAAM,GAAG,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAACgB,EAAE,GAAG,EAAE;QACtDX,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7CC,mBAAmB,EAAE,EAAE;QACvBC,UAAU,EAAE,eAAe;QAC3BC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACf,OAAO,EAAEE,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EAE7B,MAAMqB,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzChB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgB,KAAK,GAAGC,KAAK,CAACC,MAAM,CAACC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjB,GAAGrB,QAAQ;MACXS,KAAK,EAAEa,UAAU,CAACtB,QAAQ,CAACS,KAAK,CAAC;MACjCF,mBAAmB,EAAEgB,QAAQ,CAACvB,QAAQ,CAACO,mBAAmB,CAAC,IAAI,CAAC;MAChEiB,SAAS,EAAExB,QAAQ,CAACQ,UAAU,CAAE;IAClC,CAAC;IACDX,MAAM,CAACwB,UAAU,CAAC;IAClB1B,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM8B,OAAO,GAAGzB,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACQ,UAAU,IAC7DR,QAAQ,CAACS,KAAK,IAAIT,QAAQ,CAACU,WAAW;EAEtD,oBACEpD,OAAA,CAAChB,MAAM;IAACoD,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC+B,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DtE,OAAA,CAACf,WAAW;MAAAqF,QAAA,EACThC,OAAO,GAAG,cAAc,GAAG;IAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACd1E,OAAA,CAACd,aAAa;MAAAoF,QAAA,eACZtE,OAAA,CAAChC,GAAG;QAACyD,OAAO,EAAC,MAAM;QAACkD,aAAa,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACvDtE,OAAA,CAACZ,SAAS;UACR0F,KAAK,EAAC,aAAU;UAChBC,MAAM;UACNlB,KAAK,EAAEnB,QAAQ,CAACE,WAAY;UAC5BoC,QAAQ,EAAEvB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTY,QAAQ;UAAAX,QAAA,EAEP9B,QAAQ,CAAC0C,GAAG,CAAEC,OAAO,iBACpBnF,OAAA,CAACX,QAAQ;YAAkBwE,KAAK,EAAEsB,OAAO,CAAC3B,EAAG;YAAAc,QAAA,EAC1Ca,OAAO,CAACC;UAAM,GADFD,OAAO,CAAC3B,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1E,OAAA,CAAChC,GAAG;UAACyD,OAAO,EAAC,MAAM;UAACmD,GAAG,EAAE,CAAE;UAAAN,QAAA,gBACzBtE,OAAA,CAACZ,SAAS;YACR0F,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,MAAM;YACXxB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;YACtBmC,QAAQ,EAAEvB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTY,QAAQ;YACRK,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACF1E,OAAA,CAACZ,SAAS;YACR0F,KAAK,EAAC,oBAAoB;YAC1BO,IAAI,EAAC,QAAQ;YACbxB,KAAK,EAAEnB,QAAQ,CAACO,mBAAoB;YACpC+B,QAAQ,EAAEvB,YAAY,CAAC,qBAAqB,CAAE;YAC9CY,SAAS;YACTmB,UAAU,EAAE;cAAEC,GAAG,EAAE;YAAE,CAAE;YACvBC,UAAU,EAAC;UAAU;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1E,OAAA,CAAChC,GAAG;UAACyD,OAAO,EAAC,MAAM;UAACmD,GAAG,EAAE,CAAE;UAAAN,QAAA,gBACzBtE,OAAA,CAACZ,SAAS;YACR0F,KAAK,EAAC,eAAe;YACrBC,MAAM;YACNlB,KAAK,EAAEnB,QAAQ,CAACQ,UAAW;YAC3B8B,QAAQ,EAAEvB,YAAY,CAAC,YAAY,CAAE;YACrCY,SAAS;YACTY,QAAQ;YAAAX,QAAA,EAEPhB,YAAY,CAAC4B,GAAG,CAAEG,IAAI,iBACrBrF,OAAA,CAACX,QAAQ;cAAYwE,KAAK,EAAEwB,IAAK;cAAAf,QAAA,EAC9Be;YAAI,GADQA,IAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZ1E,OAAA,CAACZ,SAAS;YACR0F,KAAK,EAAC,gBAAW;YACjBO,IAAI,EAAC,QAAQ;YACbxB,KAAK,EAAEnB,QAAQ,CAACS,KAAM;YACtB6B,QAAQ,EAAEvB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTY,QAAQ;YACRO,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEE,IAAI,EAAE;YAAK;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1E,OAAA,CAACZ,SAAS;UACR0F,KAAK,EAAC,gBAAa;UACnBjB,KAAK,EAAEnB,QAAQ,CAACU,WAAY;UAC5B4B,QAAQ,EAAEvB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTY,QAAQ;UACRW,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,WAAW,EAAC;QAAgC;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEF1E,OAAA,CAACZ,SAAS;UACR0F,KAAK,EAAC,kBAAkB;UACxBjB,KAAK,EAAEnB,QAAQ,CAACW,SAAU;UAC1B2B,QAAQ,EAAEvB,YAAY,CAAC,WAAW,CAAE;UACpCY,SAAS;UACTyB,WAAW,EAAC;QAAiC;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChB1E,OAAA,CAACb,aAAa;MAAAmF,QAAA,gBACZtE,OAAA,CAAC5B,MAAM;QAAC2H,OAAO,EAAE1D,OAAQ;QAAAiC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3C1E,OAAA,CAAC5B,MAAM;QACL2H,OAAO,EAAEjC,YAAa;QACtBkC,OAAO,EAAC,WAAW;QACnBC,QAAQ,EAAE,CAAC9B,OAAQ;QAAAG,QAAA,EAElBhC,OAAO,GAAG,YAAY,GAAG;MAAS;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACjC,EAAA,CAtKIN,aAAa;AAAA+D,GAAA,GAAb/D,aAAa;AAwKnB,eAAe,SAASgE,YAAYA,CAAA,EAAG;EAAAC,GAAA;EACrC,MAAM;IAAE5D,QAAQ;IAAE6D,QAAQ;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAGzG,MAAM,CAAC,CAAC;EACjE,MAAM,CAAC0G,UAAU,EAAEC,aAAa,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+I,eAAe,EAAEC,kBAAkB,CAAC,GAAGhJ,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiJ,IAAI,EAAEC,OAAO,CAAC,GAAGlJ,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACmJ,WAAW,EAAEC,cAAc,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAMqJ,iBAAiB,GAAG;IACxB,eAAe,EAAE,cAAc;IAC/B,YAAY,EAAE,YAAY;IAC1B,QAAQ,EAAE,WAAW;IACrB,KAAK,EAAE,gBAAgB;IACvB,WAAW,EAAE,cAAc;IAC3B,YAAY,EAAE,cAAc;IAC5B,QAAQ,EAAE,WAAW;IACrB,SAAS,EAAE,YAAY;IACvB,QAAQ,EAAE,YAAY;IACtB,SAAS,EAAE,cAAc;IACzB,OAAO,EAAE;EACX,CAAC;EAEDpJ,SAAS,CAAC,MAAM;IACd;IACA0I,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7BN,kBAAkB,CAAC,IAAI,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMS,iBAAiB,GAAI5E,OAAO,IAAK;IACrCqE,kBAAkB,CAACrE,OAAO,CAAC;IAC3BmE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMU,mBAAmB,GAAI7E,OAAO,IAAK;IACvC;IACA8E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE/E,OAAO,CAAC;EACzC,CAAC;EAED,MAAMgF,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C,IAAI;MACF,IAAIb,eAAe,EAAE;QACnB;QACAU,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,WAAW,CAAC;MAC7C,CAAC,MAAM;QACL,MAAMhB,UAAU,CAACgB,WAAW,CAAC;QAC7B;QACAjB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACjK,MAAM,CAAC6J,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOnK,MAAM,CAAC,IAAIiF,IAAI,CAACkF,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEC,MAAM,EAAEnK;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAOkK,UAAU;IACnB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,GAAG,IAAK;IAC5B,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAAC/J,MAAM,CAACsK,GAAG,IAAI,CAAC,CAAC;EACxD,CAAC;EAED,MAAMC,mBAAmB,GAAI/C,IAAI,IAAK;IACpC,MAAMgD,MAAM,GAAG;MACb,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE,OAAO;MACrB,QAAQ,EAAE,MAAM;MAChB,KAAK,EAAE,SAAS;MAChB,WAAW,EAAE,WAAW;MACxB,YAAY,EAAE;IAChB,CAAC;IACD,OAAOA,MAAM,CAAChD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;;EAED;EACA,MAAMiD,aAAa,GAAGjC,QAAQ,CAAC9C,MAAM;EACrC,MAAMgF,SAAS,GAAGlC,QAAQ,CAACmC,MAAM,CAAC,CAACC,GAAG,EAAEnG,OAAO,KAAKmG,GAAG,IAAInG,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAMuF,UAAU,GAAGJ,aAAa,GAAG,CAAC,GAAGC,SAAS,GAAGD,aAAa,GAAG,CAAC;;EAEpE;EACA,MAAMK,cAAc,GAAGtC,QAAQ,CAACmC,MAAM,CAAC,CAACI,GAAG,EAAEtG,OAAO,KAAK;IACvD,MAAM+C,IAAI,GAAG/C,OAAO,CAACY,UAAU,IAAI,OAAO;IAC1C0F,GAAG,CAACvD,IAAI,CAAC,GAAG,CAACuD,GAAG,CAACvD,IAAI,CAAC,IAAI,CAAC,KAAK/C,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC;IACnD,OAAOyF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO,CAACJ,cAAc,CAAC,CAClDK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAC3BE,KAAK,CAAC,CAAC;;EAEV;EACA,MAAMC,iBAAiB,GAAG/C,QAAQ,CAACgD,KAAK,CACtCzC,IAAI,GAAGE,WAAW,EAClBF,IAAI,GAAGE,WAAW,GAAGA,WACvB,CAAC;EAED,oBACE9G,OAAA,CAAChC,GAAG;IAACsL,EAAE,EAAE;MAAEC,CAAC,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAAnF,QAAA,gBAC/BtE,OAAA,CAACgB,UAAU;MAAAsD,QAAA,gBACTtE,OAAA,CAAChC,GAAG;QAAAsG,QAAA,gBACFtE,OAAA,CAAC/B,UAAU;UAAC+H,OAAO,EAAC,IAAI;UAAC0D,SAAS,EAAC,IAAI;UAAC3H,UAAU,EAAC,MAAM;UAAC4H,KAAK,EAAC,SAAS;UAAArF,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1E,OAAA,CAAC/B,UAAU;UAAC+H,OAAO,EAAC,OAAO;UAAC2D,KAAK,EAAC,gBAAgB;UAAArF,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN1E,OAAA,CAAC6B,aAAa;QACZmE,OAAO,EAAC,WAAW;QACnB4D,SAAS,eAAE5J,OAAA,CAACT,OAAO;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBqB,OAAO,EAAEkB,gBAAiB;QAC1BhB,QAAQ,EAAEzD,QAAQ,CAACe,MAAM,KAAK,CAAE;QAAAe,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGf1E,OAAA,CAAChC,GAAG;MAACyD,OAAO,EAAC,MAAM;MAACmD,GAAG,EAAE,CAAE;MAACiF,EAAE,EAAE,CAAE;MAAAvF,QAAA,gBAChCtE,OAAA,CAAC9B,IAAI;QAACoL,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAE,CAAE;QAAAxF,QAAA,eACpBtE,OAAA,CAAC7B,WAAW;UAACmL,EAAE,EAAE;YAAES,SAAS,EAAE;UAAS,CAAE;UAAAzF,QAAA,gBACvCtE,OAAA,CAAC/B,UAAU;YAAC+H,OAAO,EAAC,IAAI;YAAC2D,KAAK,EAAC,SAAS;YAAArF,QAAA,EAAEgE;UAAa;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrE1E,OAAA,CAAC/B,UAAU;YAAC+H,OAAO,EAAC,OAAO;YAAC2D,KAAK,EAAC,eAAe;YAAArF,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACP1E,OAAA,CAAC9B,IAAI;QAACoL,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAE,CAAE;QAAAxF,QAAA,eACpBtE,OAAA,CAAC7B,WAAW;UAACmL,EAAE,EAAE;YAAES,SAAS,EAAE;UAAS,CAAE;UAAAzF,QAAA,gBACvCtE,OAAA,CAAC/B,UAAU;YAAC+H,OAAO,EAAC,IAAI;YAAC2D,KAAK,EAAC,YAAY;YAAArF,QAAA,EAAEmD,cAAc,CAACc,SAAS;UAAC;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACpF1E,OAAA,CAAC/B,UAAU;YAAC+H,OAAO,EAAC,OAAO;YAAC2D,KAAK,EAAC,eAAe;YAAArF,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACP1E,OAAA,CAAC9B,IAAI;QAACoL,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAE,CAAE;QAAAxF,QAAA,eACpBtE,OAAA,CAAC7B,WAAW;UAACmL,EAAE,EAAE;YAAES,SAAS,EAAE;UAAS,CAAE;UAAAzF,QAAA,gBACvCtE,OAAA,CAAC/B,UAAU;YAAC+H,OAAO,EAAC,IAAI;YAAC2D,KAAK,EAAC,cAAc;YAAArF,QAAA,EAAEmD,cAAc,CAACiB,UAAU;UAAC;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACvF1E,OAAA,CAAC/B,UAAU;YAAC+H,OAAO,EAAC,OAAO;YAAC2D,KAAK,EAAC,eAAe;YAAArF,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACP1E,OAAA,CAAC9B,IAAI;QAACoL,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAE,CAAE;QAAAxF,QAAA,eACpBtE,OAAA,CAAC7B,WAAW;UAACmL,EAAE,EAAE;YAAES,SAAS,EAAE;UAAS,CAAE;UAAAzF,QAAA,gBACvCtE,OAAA,CAAC/B,UAAU;YAAC+H,OAAO,EAAC,IAAI;YAAC2D,KAAK,EAAC,WAAW;YAAArF,QAAA,EACvCuE,cAAc,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAG;UAAK;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACb1E,OAAA,CAAC/B,UAAU;YAAC+H,OAAO,EAAC,OAAO;YAAC2D,KAAK,EAAC,eAAe;YAAArF,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEL2B,QAAQ,CAAC9C,MAAM,KAAK,CAAC,gBACpBvD,OAAA,CAAC9B,IAAI;MAACoL,EAAE,EAAE;QACRU,OAAO,EAAE,oBAAoB;QAC7B1I,SAAS,EAAE,6BAA6B;QACxCF,YAAY,EAAE,CAAC;QACf6I,QAAQ,EAAE,QAAQ;QAClBF,SAAS,EAAE,QAAQ;QACnBR,CAAC,EAAE;MACL,CAAE;MAAAjF,QAAA,gBACAtE,OAAA,CAACH,WAAW;QAACyJ,EAAE,EAAE;UACfY,QAAQ,EAAE,EAAE;UACZP,KAAK,EAAE,eAAe;UACtBE,EAAE,EAAE,CAAC;UACLM,OAAO,EAAE;QACX;MAAE;QAAA5F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACL1E,OAAA,CAAC/B,UAAU;QAAC+H,OAAO,EAAC,IAAI;QAAC2D,KAAK,EAAC,gBAAgB;QAACS,YAAY;QAAA9F,QAAA,EAAC;MAE7D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1E,OAAA,CAAC/B,UAAU;QAAC+H,OAAO,EAAC,OAAO;QAAC2D,KAAK,EAAC,gBAAgB;QAACE,EAAE,EAAE,CAAE;QAAAvF,QAAA,EAAC;MAE1D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1E,OAAA,CAAC6B,aAAa;QACZmE,OAAO,EAAC,WAAW;QACnB4D,SAAS,eAAE5J,OAAA,CAACT,OAAO;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBqB,OAAO,EAAEkB,gBAAiB;QAC1BhB,QAAQ,EAAEzD,QAAQ,CAACe,MAAM,KAAK,CAAE;QAAAe,QAAA,EAE/B9B,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;MAAwB;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,gBAEP1E,OAAA,CAAC9B,IAAI;MAACoL,EAAE,EAAE;QAAElI,YAAY,EAAE,CAAC;QAAE6I,QAAQ,EAAE,QAAQ;QAAE3I,SAAS,EAAE;MAAE,CAAE;MAAAgD,QAAA,gBAC9DtE,OAAA,CAACxB,cAAc;QAAA8F,QAAA,eACbtE,OAAA,CAAC3B,KAAK;UAAAiG,QAAA,gBACJtE,OAAA,CAACvB,SAAS;YAAA6F,QAAA,eACRtE,OAAA,CAACtB,QAAQ;cAAC4K,EAAE,EAAE;gBACZU,OAAO,EAAE,kBAAkB;gBAC3B,MAAM,EAAE;kBACNjI,UAAU,EAAE,MAAM;kBAClB4H,KAAK,EAAE,cAAc;kBACrBxJ,YAAY,EAAE,WAAW;kBACzBkK,WAAW,EAAE,SAAS;kBACtBC,EAAE,EAAE,CAAC;kBACL,iBAAiB,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAC;kBAC5B,cAAc,EAAE;oBAAEC,EAAE,EAAE;kBAAE;gBAC1B;cACF,CAAE;cAAAlG,QAAA,gBACAtE,OAAA,CAACC,eAAe;gBAAAqE,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACxC1E,OAAA,CAACC,eAAe;gBAAAqE,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC3C1E,OAAA,CAACC,eAAe;gBAAAqE,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACvC1E,OAAA,CAACC,eAAe;gBAAAqE,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC9C1E,OAAA,CAACC,eAAe;gBAACwK,KAAK,EAAC,OAAO;gBAAAnG,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eACtD1E,OAAA,CAACC,eAAe;gBAAAqE,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAC5C1E,OAAA,CAACC,eAAe;gBAACwK,KAAK,EAAC,QAAQ;gBAAAnG,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ1E,OAAA,CAAC1B,SAAS;YAAAgG,QAAA,EACP8E,iBAAiB,CAAClE,GAAG,CAAE5C,OAAO,iBAC7BtC,OAAA,CAACU,cAAc;cAAA4D,QAAA,gBACbtE,OAAA,CAACC,eAAe;gBAAAqE,QAAA,EAAEyD,UAAU,CAACzF,OAAO,CAACO,KAAK;cAAC;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eAC9D1E,OAAA,CAACC,eAAe;gBAAAqE,QAAA,eACdtE,OAAA,CAAC/B,UAAU;kBAAC+H,OAAO,EAAC,OAAO;kBAACjE,UAAU,EAAE,GAAI;kBAAAuC,QAAA,EACzChC,OAAO,CAACoI;gBAAe;kBAAAnG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAClB1E,OAAA,CAACC,eAAe;gBAAAqE,QAAA,eACdtE,OAAA,CAACnB,IAAI;kBACHiG,KAAK,EAAExC,OAAO,CAACY,UAAW;kBAC1ByH,IAAI,EAAC,OAAO;kBACZrB,EAAE,EAAE;oBACFU,OAAO,EAAE,GAAGhD,iBAAiB,CAAC1E,OAAO,CAACY,UAAU,CAAC,IAAI,UAAU,IAAI;oBACnEyG,KAAK,EAAE3C,iBAAiB,CAAC1E,OAAO,CAACY,UAAU,CAAC,IAAI,cAAc;oBAC9DnB,UAAU,EAAE,GAAG;oBACf6I,MAAM,EAAE,aAAa5D,iBAAiB,CAAC1E,OAAO,CAACY,UAAU,CAAC,IAAI,UAAU,IAAI;oBAC5E9B,YAAY,EAAE;kBAChB;gBAAE;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACa,CAAC,eAClB1E,OAAA,CAACC,eAAe;gBAAAqE,QAAA,eACdtE,OAAA,CAAC/B,UAAU;kBAAC+H,OAAO,EAAC,OAAO;kBAAC6E,MAAM;kBAAAvG,QAAA,EAC/BhC,OAAO,CAACc;gBAAW;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAClB1E,OAAA,CAACC,eAAe;gBAACwK,KAAK,EAAC,OAAO;gBAAAnG,QAAA,eAC5BtE,OAAA,CAAC/B,UAAU;kBAAC+H,OAAO,EAAC,OAAO;kBAACjE,UAAU,EAAC,MAAM;kBAAC4H,KAAK,EAAC,SAAS;kBAAArF,QAAA,EAC1DmD,cAAc,CAACnF,OAAO,CAACa,KAAK;gBAAC;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAClB1E,OAAA,CAACC,eAAe;gBAAAqE,QAAA,eACdtE,OAAA,CAAC/B,UAAU;kBAAC+H,OAAO,EAAC,OAAO;kBAAC2D,KAAK,EAAC,gBAAgB;kBAAArF,QAAA,EAC/ChC,OAAO,CAACe,SAAS,IAAI;gBAAG;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAClB1E,OAAA,CAACC,eAAe;gBAACwK,KAAK,EAAC,QAAQ;gBAAAnG,QAAA,eAC7BtE,OAAA,CAAChC,GAAG;kBAACsL,EAAE,EAAE;oBAAE7H,OAAO,EAAE,MAAM;oBAAEmD,GAAG,EAAE,CAAC;oBAAElD,cAAc,EAAE;kBAAS,CAAE;kBAAA4C,QAAA,gBAC7DtE,OAAA,CAACjB,OAAO;oBAAC+L,KAAK,EAAC,QAAQ;oBAAAxG,QAAA,eACrBtE,OAAA,CAAClB,UAAU;sBACT6L,IAAI,EAAC,OAAO;sBACZ5E,OAAO,EAAEA,CAAA,KAAMmB,iBAAiB,CAAC5E,OAAO,CAAE;sBAC1CgH,EAAE,EAAE;wBACFK,KAAK,EAAE,cAAc;wBACrB,SAAS,EAAE;0BAAEK,OAAO,EAAE,eAAe;0BAAEL,KAAK,EAAE;wBAAuB;sBACvE,CAAE;sBAAArF,QAAA,eAEFtE,OAAA,CAACP,QAAQ;wBAACyK,QAAQ,EAAC;sBAAO;wBAAA3F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV1E,OAAA,CAACjB,OAAO;oBAAC+L,KAAK,EAAC,UAAU;oBAAAxG,QAAA,eACvBtE,OAAA,CAAClB,UAAU;sBACT6L,IAAI,EAAC,OAAO;sBACZ5E,OAAO,EAAEA,CAAA,KAAMoB,mBAAmB,CAAC7E,OAAO,CAAE;sBAC5CgH,EAAE,EAAE;wBACFK,KAAK,EAAE,YAAY;wBACnB,SAAS,EAAE;0BAAEK,OAAO,EAAE,aAAa;0BAAEL,KAAK,EAAE;wBAAqB;sBACnE,CAAE;sBAAArF,QAAA,eAEFtE,OAAA,CAACL,UAAU;wBAACuK,QAAQ,EAAC;sBAAO;wBAAA3F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA,GA9DCpC,OAAO,CAACkB,EAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+Df,CACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eACjB1E,OAAA,CAAChC,GAAG;QAACsL,EAAE,EAAE;UACP7H,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpB4H,CAAC,EAAE,CAAC;UACJwB,SAAS,EAAE,WAAW;UACtBV,WAAW,EAAE,SAAS;UACtBL,OAAO,EAAE;QACX,CAAE;QAAA1F,QAAA,gBACAtE,OAAA,CAAC/B,UAAU;UAAC+H,OAAO,EAAC,OAAO;UAAC2D,KAAK,EAAC,gBAAgB;UAAArF,QAAA,GAAC,YACvC,EAAC8E,iBAAiB,CAAC7F,MAAM,EAAC,MAAI,EAAC8C,QAAQ,CAAC9C,MAAM,EAAC,SAC3D;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1E,OAAA,CAACrB,eAAe;UACdqM,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UACjCtB,SAAS,EAAC,KAAK;UACfuB,KAAK,EAAE5E,QAAQ,CAAC9C,MAAO;UACvBuD,WAAW,EAAEA,WAAY;UACzBF,IAAI,EAAEA,IAAK;UACXsE,YAAY,EAAEA,CAACC,CAAC,EAAEC,OAAO,KAAKvE,OAAO,CAACuE,OAAO,CAAE;UAC/CC,mBAAmB,EAAGF,CAAC,IAAK;YAC1BpE,cAAc,CAAC9C,QAAQ,CAACkH,CAAC,CAACvH,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC5CgD,OAAO,CAAC,CAAC,CAAC;UACZ,CAAE;UACFyE,gBAAgB,EAAC,sBAAmB;UACpCC,kBAAkB,EAAEA,CAAC;YAAEC,IAAI;YAAEC,EAAE;YAAER;UAAM,CAAC,KACtC,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAG,UAAUQ,EAAE,EAAE,EAC1D;UACDnC,EAAE,EAAE;YACF,wEAAwE,EAAE;cACxE9H,YAAY,EAAE;YAChB,CAAC;YACD,+BAA+B,EAAE;cAC/BlB,OAAO,EAAE;YACX;UACF;QAAE;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,eAED1E,OAAA,CAACmC,aAAa;MACZC,IAAI,EAAEoE,UAAW;MACjBnE,OAAO,EAAEA,CAAA,KAAMoE,aAAa,CAAC,KAAK,CAAE;MACpCnE,OAAO,EAAEoE,eAAgB;MACzBnE,MAAM,EAAE+E,iBAAkB;MAC1B9E,QAAQ,EAAEA;IAAS;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAER;AAAC0B,GAAA,CAhVuBD,YAAY;EAAA,QACuBrG,MAAM;AAAA;AAAA4L,GAAA,GADzCvF,YAAY;AAAA,IAAA1F,EAAA,EAAAM,GAAA,EAAAa,GAAA,EAAAM,GAAA,EAAAgE,GAAA,EAAAwF,GAAA;AAAAC,YAAA,CAAAlL,EAAA;AAAAkL,YAAA,CAAA5K,GAAA;AAAA4K,YAAA,CAAA/J,GAAA;AAAA+J,YAAA,CAAAzJ,GAAA;AAAAyJ,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}