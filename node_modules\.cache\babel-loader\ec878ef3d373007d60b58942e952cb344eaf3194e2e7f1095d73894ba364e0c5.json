{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateSections = exports.removeLocalizedDigits = exports.parseSelectedSections = exports.mergeDateIntoReferenceDate = exports.isStringNumber = exports.isAndroid = exports.getSectionsBoundaries = exports.getSectionVisibleValue = exports.getSectionOrder = exports.getLocalizedDigits = exports.getLetterEditingOptions = exports.getDaysInWeekStr = exports.getDateSectionConfigFromFormatToken = exports.getDateFromDateSections = exports.doesSectionFormatHaveLeadingZeros = exports.createDateStrForV7HiddenInputFromSections = exports.createDateStrForV6InputFromSections = exports.cleanLeadingZeros = exports.cleanDigitSectionValue = exports.changeSectionValueFormat = exports.applyLocalizedDigits = exports.FORMAT_SECONDS_NO_LEADING_ZEROS = void 0;\nvar _dateUtils = require(\"../../utils/date-utils\");\nconst getDateSectionConfigFromFormatToken = (adapter, formatToken) => {\n  const config = adapter.formatTokenMap[formatToken];\n  if (config == null) {\n    throw new Error([`MUI X: The token \"${formatToken}\" is not supported by the Date and Time Pickers.`, 'Please try using another token or open an issue on https://github.com/mui/mui-x/issues/new/choose if you think it should be supported.'].join('\\n'));\n  }\n  if (typeof config === 'string') {\n    return {\n      type: config,\n      contentType: config === 'meridiem' ? 'letter' : 'digit',\n      maxLength: undefined\n    };\n  }\n  return {\n    type: config.sectionType,\n    contentType: config.contentType,\n    maxLength: config.maxLength\n  };\n};\nexports.getDateSectionConfigFromFormatToken = getDateSectionConfigFromFormatToken;\nconst getDaysInWeekStr = (adapter, format) => {\n  const elements = [];\n  const now = adapter.date(undefined, 'default');\n  const startDate = adapter.startOfWeek(now);\n  const endDate = adapter.endOfWeek(now);\n  let current = startDate;\n  while (adapter.isBefore(current, endDate)) {\n    elements.push(current);\n    current = adapter.addDays(current, 1);\n  }\n  return elements.map(weekDay => adapter.formatByString(weekDay, format));\n};\nexports.getDaysInWeekStr = getDaysInWeekStr;\nconst getLetterEditingOptions = (adapter, timezone, sectionType, format) => {\n  switch (sectionType) {\n    case 'month':\n      {\n        return (0, _dateUtils.getMonthsInYear)(adapter, adapter.date(undefined, timezone)).map(month => adapter.formatByString(month, format));\n      }\n    case 'weekDay':\n      {\n        return getDaysInWeekStr(adapter, format);\n      }\n    case 'meridiem':\n      {\n        const now = adapter.date(undefined, timezone);\n        return [adapter.startOfDay(now), adapter.endOfDay(now)].map(date => adapter.formatByString(date, format));\n      }\n    default:\n      {\n        return [];\n      }\n  }\n};\n\n// This format should be the same on all the adapters\n// If some adapter does not respect this convention, then we will need to hardcode the format on each adapter.\nexports.getLetterEditingOptions = getLetterEditingOptions;\nconst FORMAT_SECONDS_NO_LEADING_ZEROS = exports.FORMAT_SECONDS_NO_LEADING_ZEROS = 's';\nconst NON_LOCALIZED_DIGITS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];\nconst getLocalizedDigits = adapter => {\n  const today = adapter.date(undefined);\n  const formattedZero = adapter.formatByString(adapter.setSeconds(today, 0), FORMAT_SECONDS_NO_LEADING_ZEROS);\n  if (formattedZero === '0') {\n    return NON_LOCALIZED_DIGITS;\n  }\n  return Array.from({\n    length: 10\n  }).map((_, index) => adapter.formatByString(adapter.setSeconds(today, index), FORMAT_SECONDS_NO_LEADING_ZEROS));\n};\nexports.getLocalizedDigits = getLocalizedDigits;\nconst removeLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  const digits = [];\n  let currentFormattedDigit = '';\n  for (let i = 0; i < valueStr.length; i += 1) {\n    currentFormattedDigit += valueStr[i];\n    const matchingDigitIndex = localizedDigits.indexOf(currentFormattedDigit);\n    if (matchingDigitIndex > -1) {\n      digits.push(matchingDigitIndex.toString());\n      currentFormattedDigit = '';\n    }\n  }\n  return digits.join('');\n};\nexports.removeLocalizedDigits = removeLocalizedDigits;\nconst applyLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  return valueStr.split('').map(char => localizedDigits[Number(char)]).join('');\n};\nexports.applyLocalizedDigits = applyLocalizedDigits;\nconst isStringNumber = (valueStr, localizedDigits) => {\n  const nonLocalizedValueStr = removeLocalizedDigits(valueStr, localizedDigits);\n  // `Number(' ')` returns `0` even if ' ' is not a valid number.\n  return nonLocalizedValueStr !== ' ' && !Number.isNaN(Number(nonLocalizedValueStr));\n};\n\n/**\n * Make sure the value of a digit section have the right amount of leading zeros.\n * E.g.: `03` => `3`\n * Warning: Should only be called with non-localized digits. Call `removeLocalizedDigits` with your value if needed.\n */\nexports.isStringNumber = isStringNumber;\nconst cleanLeadingZeros = (valueStr, size) => {\n  // Remove the leading zeros and then add back as many as needed.\n  return Number(valueStr).toString().padStart(size, '0');\n};\nexports.cleanLeadingZeros = cleanLeadingZeros;\nconst cleanDigitSectionValue = (adapter, value, sectionBoundaries, localizedDigits, section) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (section.type !== 'day' && section.contentType === 'digit-with-letter') {\n      throw new Error([`MUI X: The token \"${section.format}\" is a digit format with letter in it.'\n             This type of format is only supported for 'day' sections`].join('\\n'));\n    }\n  }\n  if (section.type === 'day' && section.contentType === 'digit-with-letter') {\n    const date = adapter.setDate(sectionBoundaries.longestMonth, value);\n    return adapter.formatByString(date, section.format);\n  }\n\n  // queryValue without leading `0` (`01` => `1`)\n  let valueStr = value.toString();\n  if (section.hasLeadingZerosInInput) {\n    valueStr = cleanLeadingZeros(valueStr, section.maxLength);\n  }\n  return applyLocalizedDigits(valueStr, localizedDigits);\n};\nexports.cleanDigitSectionValue = cleanDigitSectionValue;\nconst getSectionVisibleValue = (section, target, localizedDigits) => {\n  let value = section.value || section.placeholder;\n  const hasLeadingZeros = target === 'non-input' ? section.hasLeadingZerosInFormat : section.hasLeadingZerosInInput;\n  if (target === 'non-input' && section.hasLeadingZerosInInput && !section.hasLeadingZerosInFormat) {\n    value = Number(removeLocalizedDigits(value, localizedDigits)).toString();\n  }\n\n  // In the input, we add an empty character at the end of each section without leading zeros.\n  // This makes sure that `onChange` will always be fired.\n  // Otherwise, when your input value equals `1/dd/yyyy` (format `M/DD/YYYY` on DayJs),\n  // If you press `1`, on the first section, the new value is also `1/dd/yyyy`,\n  // So the browser will not fire the input `onChange`.\n  const shouldAddInvisibleSpace = ['input-rtl', 'input-ltr'].includes(target) && section.contentType === 'digit' && !hasLeadingZeros && value.length === 1;\n  if (shouldAddInvisibleSpace) {\n    value = `${value}\\u200e`;\n  }\n  if (target === 'input-rtl') {\n    value = `\\u2068${value}\\u2069`;\n  }\n  return value;\n};\nexports.getSectionVisibleValue = getSectionVisibleValue;\nconst changeSectionValueFormat = (adapter, valueStr, currentFormat, newFormat) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (getDateSectionConfigFromFormatToken(adapter, currentFormat).type === 'weekDay') {\n      throw new Error(\"changeSectionValueFormat doesn't support week day formats\");\n    }\n  }\n  return adapter.formatByString(adapter.parse(valueStr, currentFormat), newFormat);\n};\nexports.changeSectionValueFormat = changeSectionValueFormat;\nconst isFourDigitYearFormat = (adapter, format) => adapter.formatByString(adapter.date(undefined, 'system'), format).length === 4;\nconst doesSectionFormatHaveLeadingZeros = (adapter, contentType, sectionType, format) => {\n  if (contentType !== 'digit') {\n    return false;\n  }\n  const now = adapter.date(undefined, 'default');\n  switch (sectionType) {\n    // We can't use `changeSectionValueFormat`, because  `adapter.parse('1', 'YYYY')` returns `1971` instead of `1`.\n    case 'year':\n      {\n        // Remove once https://github.com/iamkun/dayjs/pull/2847 is merged and bump dayjs version\n        if (adapter.lib === 'dayjs' && format === 'YY') {\n          return true;\n        }\n        return adapter.formatByString(adapter.setYear(now, 1), format).startsWith('0');\n      }\n    case 'month':\n      {\n        return adapter.formatByString(adapter.startOfYear(now), format).length > 1;\n      }\n    case 'day':\n      {\n        return adapter.formatByString(adapter.startOfMonth(now), format).length > 1;\n      }\n    case 'weekDay':\n      {\n        return adapter.formatByString(adapter.startOfWeek(now), format).length > 1;\n      }\n    case 'hours':\n      {\n        return adapter.formatByString(adapter.setHours(now, 1), format).length > 1;\n      }\n    case 'minutes':\n      {\n        return adapter.formatByString(adapter.setMinutes(now, 1), format).length > 1;\n      }\n    case 'seconds':\n      {\n        return adapter.formatByString(adapter.setSeconds(now, 1), format).length > 1;\n      }\n    default:\n      {\n        throw new Error('Invalid section type');\n      }\n  }\n};\n\n/**\n * Some date libraries like `dayjs` don't support parsing from date with escaped characters.\n * To make sure that the parsing works, we are building a format and a date without any separator.\n */\nexports.doesSectionFormatHaveLeadingZeros = doesSectionFormatHaveLeadingZeros;\nconst getDateFromDateSections = (adapter, sections, localizedDigits) => {\n  // If we have both a day and a weekDay section,\n  // Then we skip the weekDay in the parsing because libraries like dayjs can't parse complicated formats containing a weekDay.\n  // dayjs(dayjs().format('dddd MMMM D YYYY'), 'dddd MMMM D YYYY')) // returns `Invalid Date` even if the format is valid.\n  const shouldSkipWeekDays = sections.some(section => section.type === 'day');\n  const sectionFormats = [];\n  const sectionValues = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const shouldSkip = shouldSkipWeekDays && section.type === 'weekDay';\n    if (!shouldSkip) {\n      sectionFormats.push(section.format);\n      sectionValues.push(getSectionVisibleValue(section, 'non-input', localizedDigits));\n    }\n  }\n  const formatWithoutSeparator = sectionFormats.join(' ');\n  const dateWithoutSeparatorStr = sectionValues.join(' ');\n  return adapter.parse(dateWithoutSeparatorStr, formatWithoutSeparator);\n};\nexports.getDateFromDateSections = getDateFromDateSections;\nconst createDateStrForV7HiddenInputFromSections = sections => sections.map(section => {\n  return `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`;\n}).join('');\nexports.createDateStrForV7HiddenInputFromSections = createDateStrForV7HiddenInputFromSections;\nconst createDateStrForV6InputFromSections = (sections, localizedDigits, isRtl) => {\n  const formattedSections = sections.map(section => {\n    const dateValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    return `${section.startSeparator}${dateValue}${section.endSeparator}`;\n  });\n  const dateStr = formattedSections.join('');\n  if (!isRtl) {\n    return dateStr;\n  }\n\n  // \\u2066: start left-to-right isolation\n  // \\u2067: start right-to-left isolation\n  // \\u2068: start first strong character isolation\n  // \\u2069: pop isolation\n  // wrap into an isolated group such that separators can split the string in smaller ones by adding \\u2069\\u2068\n  return `\\u2066${dateStr}\\u2069`;\n};\nexports.createDateStrForV6InputFromSections = createDateStrForV6InputFromSections;\nconst getSectionsBoundaries = (adapter, localizedDigits, timezone) => {\n  const today = adapter.date(undefined, timezone);\n  const endOfYear = adapter.endOfYear(today);\n  const endOfDay = adapter.endOfDay(today);\n  const {\n    maxDaysInMonth,\n    longestMonth\n  } = (0, _dateUtils.getMonthsInYear)(adapter, today).reduce((acc, month) => {\n    const daysInMonth = adapter.getDaysInMonth(month);\n    if (daysInMonth > acc.maxDaysInMonth) {\n      return {\n        maxDaysInMonth: daysInMonth,\n        longestMonth: month\n      };\n    }\n    return acc;\n  }, {\n    maxDaysInMonth: 0,\n    longestMonth: null\n  });\n  return {\n    year: ({\n      format\n    }) => ({\n      minimum: 0,\n      maximum: isFourDigitYearFormat(adapter, format) ? 9999 : 99\n    }),\n    month: () => ({\n      minimum: 1,\n      // Assumption: All years have the same amount of months\n      maximum: adapter.getMonth(endOfYear) + 1\n    }),\n    day: ({\n      currentDate\n    }) => ({\n      minimum: 1,\n      maximum: adapter.isValid(currentDate) ? adapter.getDaysInMonth(currentDate) : maxDaysInMonth,\n      longestMonth: longestMonth\n    }),\n    weekDay: ({\n      format,\n      contentType\n    }) => {\n      if (contentType === 'digit') {\n        const daysInWeek = getDaysInWeekStr(adapter, format).map(Number);\n        return {\n          minimum: Math.min(...daysInWeek),\n          maximum: Math.max(...daysInWeek)\n        };\n      }\n      return {\n        minimum: 1,\n        maximum: 7\n      };\n    },\n    hours: ({\n      format\n    }) => {\n      const lastHourInDay = adapter.getHours(endOfDay);\n      const hasMeridiem = removeLocalizedDigits(adapter.formatByString(adapter.endOfDay(today), format), localizedDigits) !== lastHourInDay.toString();\n      if (hasMeridiem) {\n        return {\n          minimum: 1,\n          maximum: Number(removeLocalizedDigits(adapter.formatByString(adapter.startOfDay(today), format), localizedDigits))\n        };\n      }\n      return {\n        minimum: 0,\n        maximum: lastHourInDay\n      };\n    },\n    minutes: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of minutes\n      maximum: adapter.getMinutes(endOfDay)\n    }),\n    seconds: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of seconds\n      maximum: adapter.getSeconds(endOfDay)\n    }),\n    meridiem: () => ({\n      minimum: 0,\n      maximum: 1\n    }),\n    empty: () => ({\n      minimum: 0,\n      maximum: 0\n    })\n  };\n};\nexports.getSectionsBoundaries = getSectionsBoundaries;\nlet warnedOnceInvalidSection = false;\nconst validateSections = (sections, valueType) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceInvalidSection) {\n      const supportedSections = ['empty'];\n      if (['date', 'date-time'].includes(valueType)) {\n        supportedSections.push('weekDay', 'day', 'month', 'year');\n      }\n      if (['time', 'date-time'].includes(valueType)) {\n        supportedSections.push('hours', 'minutes', 'seconds', 'meridiem');\n      }\n      const invalidSection = sections.find(section => !supportedSections.includes(section.type));\n      if (invalidSection) {\n        console.warn(`MUI X: The field component you are using is not compatible with the \"${invalidSection.type}\" date section.`, `The supported date sections are [\"${supportedSections.join('\", \"')}\"]\\`.`);\n        warnedOnceInvalidSection = true;\n      }\n    }\n  }\n};\nexports.validateSections = validateSections;\nconst transferDateSectionValue = (adapter, section, dateToTransferFrom, dateToTransferTo) => {\n  switch (section.type) {\n    case 'year':\n      {\n        return adapter.setYear(dateToTransferTo, adapter.getYear(dateToTransferFrom));\n      }\n    case 'month':\n      {\n        return adapter.setMonth(dateToTransferTo, adapter.getMonth(dateToTransferFrom));\n      }\n    case 'weekDay':\n      {\n        let dayInWeekStrOfActiveDate = adapter.formatByString(dateToTransferFrom, section.format);\n        if (section.hasLeadingZerosInInput) {\n          dayInWeekStrOfActiveDate = cleanLeadingZeros(dayInWeekStrOfActiveDate, section.maxLength);\n        }\n        const formattedDaysInWeek = getDaysInWeekStr(adapter, section.format);\n        const dayInWeekOfActiveDate = formattedDaysInWeek.indexOf(dayInWeekStrOfActiveDate);\n        const dayInWeekOfNewSectionValue = formattedDaysInWeek.indexOf(section.value);\n        const diff = dayInWeekOfNewSectionValue - dayInWeekOfActiveDate;\n        return adapter.addDays(dateToTransferFrom, diff);\n      }\n    case 'day':\n      {\n        return adapter.setDate(dateToTransferTo, adapter.getDate(dateToTransferFrom));\n      }\n    case 'meridiem':\n      {\n        const isAM = adapter.getHours(dateToTransferFrom) < 12;\n        const mergedDateHours = adapter.getHours(dateToTransferTo);\n        if (isAM && mergedDateHours >= 12) {\n          return adapter.addHours(dateToTransferTo, -12);\n        }\n        if (!isAM && mergedDateHours < 12) {\n          return adapter.addHours(dateToTransferTo, 12);\n        }\n        return dateToTransferTo;\n      }\n    case 'hours':\n      {\n        return adapter.setHours(dateToTransferTo, adapter.getHours(dateToTransferFrom));\n      }\n    case 'minutes':\n      {\n        return adapter.setMinutes(dateToTransferTo, adapter.getMinutes(dateToTransferFrom));\n      }\n    case 'seconds':\n      {\n        return adapter.setSeconds(dateToTransferTo, adapter.getSeconds(dateToTransferFrom));\n      }\n    default:\n      {\n        return dateToTransferTo;\n      }\n  }\n};\nconst reliableSectionModificationOrder = {\n  year: 1,\n  month: 2,\n  day: 3,\n  weekDay: 4,\n  hours: 5,\n  minutes: 6,\n  seconds: 7,\n  meridiem: 8,\n  empty: 9\n};\nconst mergeDateIntoReferenceDate = (adapter, dateToTransferFrom, sections, referenceDate, shouldLimitToEditedSections) =>\n// cloning sections before sort to avoid mutating it\n[...sections].sort((a, b) => reliableSectionModificationOrder[a.type] - reliableSectionModificationOrder[b.type]).reduce((mergedDate, section) => {\n  if (!shouldLimitToEditedSections || section.modified) {\n    return transferDateSectionValue(adapter, section, dateToTransferFrom, mergedDate);\n  }\n  return mergedDate;\n}, referenceDate);\nexports.mergeDateIntoReferenceDate = mergeDateIntoReferenceDate;\nconst isAndroid = () => navigator.userAgent.toLowerCase().includes('android');\n\n// TODO v9: Remove\nexports.isAndroid = isAndroid;\nconst getSectionOrder = (sections, shouldApplyRTL) => {\n  const neighbors = {};\n  if (!shouldApplyRTL) {\n    sections.forEach((_, index) => {\n      const leftIndex = index === 0 ? null : index - 1;\n      const rightIndex = index === sections.length - 1 ? null : index + 1;\n      neighbors[index] = {\n        leftIndex,\n        rightIndex\n      };\n    });\n    return {\n      neighbors,\n      startIndex: 0,\n      endIndex: sections.length - 1\n    };\n  }\n  const rtl2ltr = {};\n  const ltr2rtl = {};\n  let groupedSectionsStart = 0;\n  let groupedSectionsEnd = 0;\n  let RTLIndex = sections.length - 1;\n  while (RTLIndex >= 0) {\n    groupedSectionsEnd = sections.findIndex(\n    // eslint-disable-next-line @typescript-eslint/no-loop-func\n    (section, index) => index >= groupedSectionsStart && section.endSeparator?.includes(' ') &&\n    // Special case where the spaces were not there in the initial input\n    section.endSeparator !== ' / ');\n    if (groupedSectionsEnd === -1) {\n      groupedSectionsEnd = sections.length - 1;\n    }\n    for (let i = groupedSectionsEnd; i >= groupedSectionsStart; i -= 1) {\n      ltr2rtl[i] = RTLIndex;\n      rtl2ltr[RTLIndex] = i;\n      RTLIndex -= 1;\n    }\n    groupedSectionsStart = groupedSectionsEnd + 1;\n  }\n  sections.forEach((_, index) => {\n    const rtlIndex = ltr2rtl[index];\n    const leftIndex = rtlIndex === 0 ? null : rtl2ltr[rtlIndex - 1];\n    const rightIndex = rtlIndex === sections.length - 1 ? null : rtl2ltr[rtlIndex + 1];\n    neighbors[index] = {\n      leftIndex,\n      rightIndex\n    };\n  });\n  return {\n    neighbors,\n    startIndex: rtl2ltr[0],\n    endIndex: rtl2ltr[sections.length - 1]\n  };\n};\nexports.getSectionOrder = getSectionOrder;\nconst parseSelectedSections = (selectedSections, sections) => {\n  if (selectedSections == null) {\n    return null;\n  }\n  if (selectedSections === 'all') {\n    return 'all';\n  }\n  if (typeof selectedSections === 'string') {\n    const index = sections.findIndex(section => section.type === selectedSections);\n    return index === -1 ? null : index;\n  }\n  return selectedSections;\n};\nexports.parseSelectedSections = parseSelectedSections;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "validateSections", "removeLocalizedDigits", "parseSelectedSections", "mergeDateIntoReferenceDate", "isStringNumber", "isAndroid", "getSectionsBoundaries", "getSectionVisibleValue", "getSectionOrder", "getLocalizedDigits", "getLetterEditingOptions", "getDaysInWeekStr", "getDateSectionConfigFromFormatToken", "getDateFromDateSections", "doesSectionFormatHaveLeadingZeros", "createDateStrForV7HiddenInputFromSections", "createDateStrForV6InputFromSections", "cleanLeadingZeros", "cleanDigitSectionValue", "changeSectionValueFormat", "applyLocalizedDigits", "FORMAT_SECONDS_NO_LEADING_ZEROS", "_dateUtils", "require", "adapter", "formatToken", "config", "formatTokenMap", "Error", "join", "type", "contentType", "max<PERSON><PERSON><PERSON>", "undefined", "sectionType", "format", "elements", "now", "date", "startDate", "startOfWeek", "endDate", "endOfWeek", "current", "isBefore", "push", "addDays", "map", "weekDay", "formatByString", "timezone", "getMonthsInYear", "month", "startOfDay", "endOfDay", "NON_LOCALIZED_DIGITS", "today", "formattedZero", "setSeconds", "Array", "from", "length", "_", "index", "valueStr", "localizedDigits", "digits", "currentFormattedDigit", "i", "matchingDigitIndex", "indexOf", "toString", "split", "char", "Number", "nonLocalizedValueStr", "isNaN", "size", "padStart", "sectionBoundaries", "section", "process", "env", "NODE_ENV", "setDate", "longestMonth", "hasLeadingZerosInInput", "target", "placeholder", "hasLeadingZeros", "hasLeadingZerosInFormat", "shouldAddInvisibleSpace", "includes", "currentFormat", "newFormat", "parse", "isFourDigitYearFormat", "lib", "setYear", "startsWith", "startOfYear", "startOfMonth", "setHours", "setMinutes", "sections", "shouldSkipWeekDays", "some", "sectionFormats", "sectionValues", "shouldSkip", "formatWithoutSeparator", "dateWithoutSeparatorStr", "startSeparator", "endSeparator", "isRtl", "formattedSections", "dateValue", "dateStr", "endOfYear", "maxDaysInMonth", "reduce", "acc", "daysInMonth", "getDaysInMonth", "year", "minimum", "maximum", "getMonth", "day", "currentDate", "<PERSON><PERSON><PERSON><PERSON>", "daysInWeek", "Math", "min", "max", "hours", "lastHourInDay", "getHours", "hasMeridiem", "minutes", "getMinutes", "seconds", "getSeconds", "meridiem", "empty", "warnedOnceInvalidSection", "valueType", "supportedSections", "invalidSection", "find", "console", "warn", "transferDateSectionValue", "dateToTransferFrom", "dateToTransferTo", "getYear", "setMonth", "dayInWeekStrOfActiveDate", "formattedDaysInWeek", "dayInWeekOfActiveDate", "dayInWeekOfNewSectionValue", "diff", "getDate", "isAM", "mergedDateHours", "addHours", "reliableSectionModificationOrder", "referenceDate", "shouldLimitToEditedSections", "sort", "a", "b", "mergedDate", "modified", "navigator", "userAgent", "toLowerCase", "shouldApplyRTL", "neighbors", "for<PERSON>ach", "leftIndex", "rightIndex", "startIndex", "endIndex", "rtl2ltr", "ltr2rtl", "groupedSectionsStart", "groupedSectionsEnd", "RTLIndex", "findIndex", "rtlIndex", "selectedSections"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/useField/useField.utils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateSections = exports.removeLocalizedDigits = exports.parseSelectedSections = exports.mergeDateIntoReferenceDate = exports.isStringNumber = exports.isAndroid = exports.getSectionsBoundaries = exports.getSectionVisibleValue = exports.getSectionOrder = exports.getLocalizedDigits = exports.getLetterEditingOptions = exports.getDaysInWeekStr = exports.getDateSectionConfigFromFormatToken = exports.getDateFromDateSections = exports.doesSectionFormatHaveLeadingZeros = exports.createDateStrForV7HiddenInputFromSections = exports.createDateStrForV6InputFromSections = exports.cleanLeadingZeros = exports.cleanDigitSectionValue = exports.changeSectionValueFormat = exports.applyLocalizedDigits = exports.FORMAT_SECONDS_NO_LEADING_ZEROS = void 0;\nvar _dateUtils = require(\"../../utils/date-utils\");\nconst getDateSectionConfigFromFormatToken = (adapter, formatToken) => {\n  const config = adapter.formatTokenMap[formatToken];\n  if (config == null) {\n    throw new Error([`MUI X: The token \"${formatToken}\" is not supported by the Date and Time Pickers.`, 'Please try using another token or open an issue on https://github.com/mui/mui-x/issues/new/choose if you think it should be supported.'].join('\\n'));\n  }\n  if (typeof config === 'string') {\n    return {\n      type: config,\n      contentType: config === 'meridiem' ? 'letter' : 'digit',\n      maxLength: undefined\n    };\n  }\n  return {\n    type: config.sectionType,\n    contentType: config.contentType,\n    maxLength: config.maxLength\n  };\n};\nexports.getDateSectionConfigFromFormatToken = getDateSectionConfigFromFormatToken;\nconst getDaysInWeekStr = (adapter, format) => {\n  const elements = [];\n  const now = adapter.date(undefined, 'default');\n  const startDate = adapter.startOfWeek(now);\n  const endDate = adapter.endOfWeek(now);\n  let current = startDate;\n  while (adapter.isBefore(current, endDate)) {\n    elements.push(current);\n    current = adapter.addDays(current, 1);\n  }\n  return elements.map(weekDay => adapter.formatByString(weekDay, format));\n};\nexports.getDaysInWeekStr = getDaysInWeekStr;\nconst getLetterEditingOptions = (adapter, timezone, sectionType, format) => {\n  switch (sectionType) {\n    case 'month':\n      {\n        return (0, _dateUtils.getMonthsInYear)(adapter, adapter.date(undefined, timezone)).map(month => adapter.formatByString(month, format));\n      }\n    case 'weekDay':\n      {\n        return getDaysInWeekStr(adapter, format);\n      }\n    case 'meridiem':\n      {\n        const now = adapter.date(undefined, timezone);\n        return [adapter.startOfDay(now), adapter.endOfDay(now)].map(date => adapter.formatByString(date, format));\n      }\n    default:\n      {\n        return [];\n      }\n  }\n};\n\n// This format should be the same on all the adapters\n// If some adapter does not respect this convention, then we will need to hardcode the format on each adapter.\nexports.getLetterEditingOptions = getLetterEditingOptions;\nconst FORMAT_SECONDS_NO_LEADING_ZEROS = exports.FORMAT_SECONDS_NO_LEADING_ZEROS = 's';\nconst NON_LOCALIZED_DIGITS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];\nconst getLocalizedDigits = adapter => {\n  const today = adapter.date(undefined);\n  const formattedZero = adapter.formatByString(adapter.setSeconds(today, 0), FORMAT_SECONDS_NO_LEADING_ZEROS);\n  if (formattedZero === '0') {\n    return NON_LOCALIZED_DIGITS;\n  }\n  return Array.from({\n    length: 10\n  }).map((_, index) => adapter.formatByString(adapter.setSeconds(today, index), FORMAT_SECONDS_NO_LEADING_ZEROS));\n};\nexports.getLocalizedDigits = getLocalizedDigits;\nconst removeLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  const digits = [];\n  let currentFormattedDigit = '';\n  for (let i = 0; i < valueStr.length; i += 1) {\n    currentFormattedDigit += valueStr[i];\n    const matchingDigitIndex = localizedDigits.indexOf(currentFormattedDigit);\n    if (matchingDigitIndex > -1) {\n      digits.push(matchingDigitIndex.toString());\n      currentFormattedDigit = '';\n    }\n  }\n  return digits.join('');\n};\nexports.removeLocalizedDigits = removeLocalizedDigits;\nconst applyLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  return valueStr.split('').map(char => localizedDigits[Number(char)]).join('');\n};\nexports.applyLocalizedDigits = applyLocalizedDigits;\nconst isStringNumber = (valueStr, localizedDigits) => {\n  const nonLocalizedValueStr = removeLocalizedDigits(valueStr, localizedDigits);\n  // `Number(' ')` returns `0` even if ' ' is not a valid number.\n  return nonLocalizedValueStr !== ' ' && !Number.isNaN(Number(nonLocalizedValueStr));\n};\n\n/**\n * Make sure the value of a digit section have the right amount of leading zeros.\n * E.g.: `03` => `3`\n * Warning: Should only be called with non-localized digits. Call `removeLocalizedDigits` with your value if needed.\n */\nexports.isStringNumber = isStringNumber;\nconst cleanLeadingZeros = (valueStr, size) => {\n  // Remove the leading zeros and then add back as many as needed.\n  return Number(valueStr).toString().padStart(size, '0');\n};\nexports.cleanLeadingZeros = cleanLeadingZeros;\nconst cleanDigitSectionValue = (adapter, value, sectionBoundaries, localizedDigits, section) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (section.type !== 'day' && section.contentType === 'digit-with-letter') {\n      throw new Error([`MUI X: The token \"${section.format}\" is a digit format with letter in it.'\n             This type of format is only supported for 'day' sections`].join('\\n'));\n    }\n  }\n  if (section.type === 'day' && section.contentType === 'digit-with-letter') {\n    const date = adapter.setDate(sectionBoundaries.longestMonth, value);\n    return adapter.formatByString(date, section.format);\n  }\n\n  // queryValue without leading `0` (`01` => `1`)\n  let valueStr = value.toString();\n  if (section.hasLeadingZerosInInput) {\n    valueStr = cleanLeadingZeros(valueStr, section.maxLength);\n  }\n  return applyLocalizedDigits(valueStr, localizedDigits);\n};\nexports.cleanDigitSectionValue = cleanDigitSectionValue;\nconst getSectionVisibleValue = (section, target, localizedDigits) => {\n  let value = section.value || section.placeholder;\n  const hasLeadingZeros = target === 'non-input' ? section.hasLeadingZerosInFormat : section.hasLeadingZerosInInput;\n  if (target === 'non-input' && section.hasLeadingZerosInInput && !section.hasLeadingZerosInFormat) {\n    value = Number(removeLocalizedDigits(value, localizedDigits)).toString();\n  }\n\n  // In the input, we add an empty character at the end of each section without leading zeros.\n  // This makes sure that `onChange` will always be fired.\n  // Otherwise, when your input value equals `1/dd/yyyy` (format `M/DD/YYYY` on DayJs),\n  // If you press `1`, on the first section, the new value is also `1/dd/yyyy`,\n  // So the browser will not fire the input `onChange`.\n  const shouldAddInvisibleSpace = ['input-rtl', 'input-ltr'].includes(target) && section.contentType === 'digit' && !hasLeadingZeros && value.length === 1;\n  if (shouldAddInvisibleSpace) {\n    value = `${value}\\u200e`;\n  }\n  if (target === 'input-rtl') {\n    value = `\\u2068${value}\\u2069`;\n  }\n  return value;\n};\nexports.getSectionVisibleValue = getSectionVisibleValue;\nconst changeSectionValueFormat = (adapter, valueStr, currentFormat, newFormat) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (getDateSectionConfigFromFormatToken(adapter, currentFormat).type === 'weekDay') {\n      throw new Error(\"changeSectionValueFormat doesn't support week day formats\");\n    }\n  }\n  return adapter.formatByString(adapter.parse(valueStr, currentFormat), newFormat);\n};\nexports.changeSectionValueFormat = changeSectionValueFormat;\nconst isFourDigitYearFormat = (adapter, format) => adapter.formatByString(adapter.date(undefined, 'system'), format).length === 4;\nconst doesSectionFormatHaveLeadingZeros = (adapter, contentType, sectionType, format) => {\n  if (contentType !== 'digit') {\n    return false;\n  }\n  const now = adapter.date(undefined, 'default');\n  switch (sectionType) {\n    // We can't use `changeSectionValueFormat`, because  `adapter.parse('1', 'YYYY')` returns `1971` instead of `1`.\n    case 'year':\n      {\n        // Remove once https://github.com/iamkun/dayjs/pull/2847 is merged and bump dayjs version\n        if (adapter.lib === 'dayjs' && format === 'YY') {\n          return true;\n        }\n        return adapter.formatByString(adapter.setYear(now, 1), format).startsWith('0');\n      }\n    case 'month':\n      {\n        return adapter.formatByString(adapter.startOfYear(now), format).length > 1;\n      }\n    case 'day':\n      {\n        return adapter.formatByString(adapter.startOfMonth(now), format).length > 1;\n      }\n    case 'weekDay':\n      {\n        return adapter.formatByString(adapter.startOfWeek(now), format).length > 1;\n      }\n    case 'hours':\n      {\n        return adapter.formatByString(adapter.setHours(now, 1), format).length > 1;\n      }\n    case 'minutes':\n      {\n        return adapter.formatByString(adapter.setMinutes(now, 1), format).length > 1;\n      }\n    case 'seconds':\n      {\n        return adapter.formatByString(adapter.setSeconds(now, 1), format).length > 1;\n      }\n    default:\n      {\n        throw new Error('Invalid section type');\n      }\n  }\n};\n\n/**\n * Some date libraries like `dayjs` don't support parsing from date with escaped characters.\n * To make sure that the parsing works, we are building a format and a date without any separator.\n */\nexports.doesSectionFormatHaveLeadingZeros = doesSectionFormatHaveLeadingZeros;\nconst getDateFromDateSections = (adapter, sections, localizedDigits) => {\n  // If we have both a day and a weekDay section,\n  // Then we skip the weekDay in the parsing because libraries like dayjs can't parse complicated formats containing a weekDay.\n  // dayjs(dayjs().format('dddd MMMM D YYYY'), 'dddd MMMM D YYYY')) // returns `Invalid Date` even if the format is valid.\n  const shouldSkipWeekDays = sections.some(section => section.type === 'day');\n  const sectionFormats = [];\n  const sectionValues = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const shouldSkip = shouldSkipWeekDays && section.type === 'weekDay';\n    if (!shouldSkip) {\n      sectionFormats.push(section.format);\n      sectionValues.push(getSectionVisibleValue(section, 'non-input', localizedDigits));\n    }\n  }\n  const formatWithoutSeparator = sectionFormats.join(' ');\n  const dateWithoutSeparatorStr = sectionValues.join(' ');\n  return adapter.parse(dateWithoutSeparatorStr, formatWithoutSeparator);\n};\nexports.getDateFromDateSections = getDateFromDateSections;\nconst createDateStrForV7HiddenInputFromSections = sections => sections.map(section => {\n  return `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`;\n}).join('');\nexports.createDateStrForV7HiddenInputFromSections = createDateStrForV7HiddenInputFromSections;\nconst createDateStrForV6InputFromSections = (sections, localizedDigits, isRtl) => {\n  const formattedSections = sections.map(section => {\n    const dateValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    return `${section.startSeparator}${dateValue}${section.endSeparator}`;\n  });\n  const dateStr = formattedSections.join('');\n  if (!isRtl) {\n    return dateStr;\n  }\n\n  // \\u2066: start left-to-right isolation\n  // \\u2067: start right-to-left isolation\n  // \\u2068: start first strong character isolation\n  // \\u2069: pop isolation\n  // wrap into an isolated group such that separators can split the string in smaller ones by adding \\u2069\\u2068\n  return `\\u2066${dateStr}\\u2069`;\n};\nexports.createDateStrForV6InputFromSections = createDateStrForV6InputFromSections;\nconst getSectionsBoundaries = (adapter, localizedDigits, timezone) => {\n  const today = adapter.date(undefined, timezone);\n  const endOfYear = adapter.endOfYear(today);\n  const endOfDay = adapter.endOfDay(today);\n  const {\n    maxDaysInMonth,\n    longestMonth\n  } = (0, _dateUtils.getMonthsInYear)(adapter, today).reduce((acc, month) => {\n    const daysInMonth = adapter.getDaysInMonth(month);\n    if (daysInMonth > acc.maxDaysInMonth) {\n      return {\n        maxDaysInMonth: daysInMonth,\n        longestMonth: month\n      };\n    }\n    return acc;\n  }, {\n    maxDaysInMonth: 0,\n    longestMonth: null\n  });\n  return {\n    year: ({\n      format\n    }) => ({\n      minimum: 0,\n      maximum: isFourDigitYearFormat(adapter, format) ? 9999 : 99\n    }),\n    month: () => ({\n      minimum: 1,\n      // Assumption: All years have the same amount of months\n      maximum: adapter.getMonth(endOfYear) + 1\n    }),\n    day: ({\n      currentDate\n    }) => ({\n      minimum: 1,\n      maximum: adapter.isValid(currentDate) ? adapter.getDaysInMonth(currentDate) : maxDaysInMonth,\n      longestMonth: longestMonth\n    }),\n    weekDay: ({\n      format,\n      contentType\n    }) => {\n      if (contentType === 'digit') {\n        const daysInWeek = getDaysInWeekStr(adapter, format).map(Number);\n        return {\n          minimum: Math.min(...daysInWeek),\n          maximum: Math.max(...daysInWeek)\n        };\n      }\n      return {\n        minimum: 1,\n        maximum: 7\n      };\n    },\n    hours: ({\n      format\n    }) => {\n      const lastHourInDay = adapter.getHours(endOfDay);\n      const hasMeridiem = removeLocalizedDigits(adapter.formatByString(adapter.endOfDay(today), format), localizedDigits) !== lastHourInDay.toString();\n      if (hasMeridiem) {\n        return {\n          minimum: 1,\n          maximum: Number(removeLocalizedDigits(adapter.formatByString(adapter.startOfDay(today), format), localizedDigits))\n        };\n      }\n      return {\n        minimum: 0,\n        maximum: lastHourInDay\n      };\n    },\n    minutes: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of minutes\n      maximum: adapter.getMinutes(endOfDay)\n    }),\n    seconds: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of seconds\n      maximum: adapter.getSeconds(endOfDay)\n    }),\n    meridiem: () => ({\n      minimum: 0,\n      maximum: 1\n    }),\n    empty: () => ({\n      minimum: 0,\n      maximum: 0\n    })\n  };\n};\nexports.getSectionsBoundaries = getSectionsBoundaries;\nlet warnedOnceInvalidSection = false;\nconst validateSections = (sections, valueType) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceInvalidSection) {\n      const supportedSections = ['empty'];\n      if (['date', 'date-time'].includes(valueType)) {\n        supportedSections.push('weekDay', 'day', 'month', 'year');\n      }\n      if (['time', 'date-time'].includes(valueType)) {\n        supportedSections.push('hours', 'minutes', 'seconds', 'meridiem');\n      }\n      const invalidSection = sections.find(section => !supportedSections.includes(section.type));\n      if (invalidSection) {\n        console.warn(`MUI X: The field component you are using is not compatible with the \"${invalidSection.type}\" date section.`, `The supported date sections are [\"${supportedSections.join('\", \"')}\"]\\`.`);\n        warnedOnceInvalidSection = true;\n      }\n    }\n  }\n};\nexports.validateSections = validateSections;\nconst transferDateSectionValue = (adapter, section, dateToTransferFrom, dateToTransferTo) => {\n  switch (section.type) {\n    case 'year':\n      {\n        return adapter.setYear(dateToTransferTo, adapter.getYear(dateToTransferFrom));\n      }\n    case 'month':\n      {\n        return adapter.setMonth(dateToTransferTo, adapter.getMonth(dateToTransferFrom));\n      }\n    case 'weekDay':\n      {\n        let dayInWeekStrOfActiveDate = adapter.formatByString(dateToTransferFrom, section.format);\n        if (section.hasLeadingZerosInInput) {\n          dayInWeekStrOfActiveDate = cleanLeadingZeros(dayInWeekStrOfActiveDate, section.maxLength);\n        }\n        const formattedDaysInWeek = getDaysInWeekStr(adapter, section.format);\n        const dayInWeekOfActiveDate = formattedDaysInWeek.indexOf(dayInWeekStrOfActiveDate);\n        const dayInWeekOfNewSectionValue = formattedDaysInWeek.indexOf(section.value);\n        const diff = dayInWeekOfNewSectionValue - dayInWeekOfActiveDate;\n        return adapter.addDays(dateToTransferFrom, diff);\n      }\n    case 'day':\n      {\n        return adapter.setDate(dateToTransferTo, adapter.getDate(dateToTransferFrom));\n      }\n    case 'meridiem':\n      {\n        const isAM = adapter.getHours(dateToTransferFrom) < 12;\n        const mergedDateHours = adapter.getHours(dateToTransferTo);\n        if (isAM && mergedDateHours >= 12) {\n          return adapter.addHours(dateToTransferTo, -12);\n        }\n        if (!isAM && mergedDateHours < 12) {\n          return adapter.addHours(dateToTransferTo, 12);\n        }\n        return dateToTransferTo;\n      }\n    case 'hours':\n      {\n        return adapter.setHours(dateToTransferTo, adapter.getHours(dateToTransferFrom));\n      }\n    case 'minutes':\n      {\n        return adapter.setMinutes(dateToTransferTo, adapter.getMinutes(dateToTransferFrom));\n      }\n    case 'seconds':\n      {\n        return adapter.setSeconds(dateToTransferTo, adapter.getSeconds(dateToTransferFrom));\n      }\n    default:\n      {\n        return dateToTransferTo;\n      }\n  }\n};\nconst reliableSectionModificationOrder = {\n  year: 1,\n  month: 2,\n  day: 3,\n  weekDay: 4,\n  hours: 5,\n  minutes: 6,\n  seconds: 7,\n  meridiem: 8,\n  empty: 9\n};\nconst mergeDateIntoReferenceDate = (adapter, dateToTransferFrom, sections, referenceDate, shouldLimitToEditedSections) =>\n// cloning sections before sort to avoid mutating it\n[...sections].sort((a, b) => reliableSectionModificationOrder[a.type] - reliableSectionModificationOrder[b.type]).reduce((mergedDate, section) => {\n  if (!shouldLimitToEditedSections || section.modified) {\n    return transferDateSectionValue(adapter, section, dateToTransferFrom, mergedDate);\n  }\n  return mergedDate;\n}, referenceDate);\nexports.mergeDateIntoReferenceDate = mergeDateIntoReferenceDate;\nconst isAndroid = () => navigator.userAgent.toLowerCase().includes('android');\n\n// TODO v9: Remove\nexports.isAndroid = isAndroid;\nconst getSectionOrder = (sections, shouldApplyRTL) => {\n  const neighbors = {};\n  if (!shouldApplyRTL) {\n    sections.forEach((_, index) => {\n      const leftIndex = index === 0 ? null : index - 1;\n      const rightIndex = index === sections.length - 1 ? null : index + 1;\n      neighbors[index] = {\n        leftIndex,\n        rightIndex\n      };\n    });\n    return {\n      neighbors,\n      startIndex: 0,\n      endIndex: sections.length - 1\n    };\n  }\n  const rtl2ltr = {};\n  const ltr2rtl = {};\n  let groupedSectionsStart = 0;\n  let groupedSectionsEnd = 0;\n  let RTLIndex = sections.length - 1;\n  while (RTLIndex >= 0) {\n    groupedSectionsEnd = sections.findIndex(\n    // eslint-disable-next-line @typescript-eslint/no-loop-func\n    (section, index) => index >= groupedSectionsStart && section.endSeparator?.includes(' ') &&\n    // Special case where the spaces were not there in the initial input\n    section.endSeparator !== ' / ');\n    if (groupedSectionsEnd === -1) {\n      groupedSectionsEnd = sections.length - 1;\n    }\n    for (let i = groupedSectionsEnd; i >= groupedSectionsStart; i -= 1) {\n      ltr2rtl[i] = RTLIndex;\n      rtl2ltr[RTLIndex] = i;\n      RTLIndex -= 1;\n    }\n    groupedSectionsStart = groupedSectionsEnd + 1;\n  }\n  sections.forEach((_, index) => {\n    const rtlIndex = ltr2rtl[index];\n    const leftIndex = rtlIndex === 0 ? null : rtl2ltr[rtlIndex - 1];\n    const rightIndex = rtlIndex === sections.length - 1 ? null : rtl2ltr[rtlIndex + 1];\n    neighbors[index] = {\n      leftIndex,\n      rightIndex\n    };\n  });\n  return {\n    neighbors,\n    startIndex: rtl2ltr[0],\n    endIndex: rtl2ltr[sections.length - 1]\n  };\n};\nexports.getSectionOrder = getSectionOrder;\nconst parseSelectedSections = (selectedSections, sections) => {\n  if (selectedSections == null) {\n    return null;\n  }\n  if (selectedSections === 'all') {\n    return 'all';\n  }\n  if (typeof selectedSections === 'string') {\n    const index = sections.findIndex(section => section.type === selectedSections);\n    return index === -1 ? null : index;\n  }\n  return selectedSections;\n};\nexports.parseSelectedSections = parseSelectedSections;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gBAAgB,GAAGF,OAAO,CAACG,qBAAqB,GAAGH,OAAO,CAACI,qBAAqB,GAAGJ,OAAO,CAACK,0BAA0B,GAAGL,OAAO,CAACM,cAAc,GAAGN,OAAO,CAACO,SAAS,GAAGP,OAAO,CAACQ,qBAAqB,GAAGR,OAAO,CAACS,sBAAsB,GAAGT,OAAO,CAACU,eAAe,GAAGV,OAAO,CAACW,kBAAkB,GAAGX,OAAO,CAACY,uBAAuB,GAAGZ,OAAO,CAACa,gBAAgB,GAAGb,OAAO,CAACc,mCAAmC,GAAGd,OAAO,CAACe,uBAAuB,GAAGf,OAAO,CAACgB,iCAAiC,GAAGhB,OAAO,CAACiB,yCAAyC,GAAGjB,OAAO,CAACkB,mCAAmC,GAAGlB,OAAO,CAACmB,iBAAiB,GAAGnB,OAAO,CAACoB,sBAAsB,GAAGpB,OAAO,CAACqB,wBAAwB,GAAGrB,OAAO,CAACsB,oBAAoB,GAAGtB,OAAO,CAACuB,+BAA+B,GAAG,KAAK,CAAC;AAC/uB,IAAIC,UAAU,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAClD,MAAMX,mCAAmC,GAAGA,CAACY,OAAO,EAAEC,WAAW,KAAK;EACpE,MAAMC,MAAM,GAAGF,OAAO,CAACG,cAAc,CAACF,WAAW,CAAC;EAClD,IAAIC,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIE,KAAK,CAAC,CAAC,qBAAqBH,WAAW,kDAAkD,EAAE,wIAAwI,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5P;EACA,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO;MACLI,IAAI,EAAEJ,MAAM;MACZK,WAAW,EAAEL,MAAM,KAAK,UAAU,GAAG,QAAQ,GAAG,OAAO;MACvDM,SAAS,EAAEC;IACb,CAAC;EACH;EACA,OAAO;IACLH,IAAI,EAAEJ,MAAM,CAACQ,WAAW;IACxBH,WAAW,EAAEL,MAAM,CAACK,WAAW;IAC/BC,SAAS,EAAEN,MAAM,CAACM;EACpB,CAAC;AACH,CAAC;AACDlC,OAAO,CAACc,mCAAmC,GAAGA,mCAAmC;AACjF,MAAMD,gBAAgB,GAAGA,CAACa,OAAO,EAAEW,MAAM,KAAK;EAC5C,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,GAAG,GAAGb,OAAO,CAACc,IAAI,CAACL,SAAS,EAAE,SAAS,CAAC;EAC9C,MAAMM,SAAS,GAAGf,OAAO,CAACgB,WAAW,CAACH,GAAG,CAAC;EAC1C,MAAMI,OAAO,GAAGjB,OAAO,CAACkB,SAAS,CAACL,GAAG,CAAC;EACtC,IAAIM,OAAO,GAAGJ,SAAS;EACvB,OAAOf,OAAO,CAACoB,QAAQ,CAACD,OAAO,EAAEF,OAAO,CAAC,EAAE;IACzCL,QAAQ,CAACS,IAAI,CAACF,OAAO,CAAC;IACtBA,OAAO,GAAGnB,OAAO,CAACsB,OAAO,CAACH,OAAO,EAAE,CAAC,CAAC;EACvC;EACA,OAAOP,QAAQ,CAACW,GAAG,CAACC,OAAO,IAAIxB,OAAO,CAACyB,cAAc,CAACD,OAAO,EAAEb,MAAM,CAAC,CAAC;AACzE,CAAC;AACDrC,OAAO,CAACa,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMD,uBAAuB,GAAGA,CAACc,OAAO,EAAE0B,QAAQ,EAAEhB,WAAW,EAAEC,MAAM,KAAK;EAC1E,QAAQD,WAAW;IACjB,KAAK,OAAO;MACV;QACE,OAAO,CAAC,CAAC,EAAEZ,UAAU,CAAC6B,eAAe,EAAE3B,OAAO,EAAEA,OAAO,CAACc,IAAI,CAACL,SAAS,EAAEiB,QAAQ,CAAC,CAAC,CAACH,GAAG,CAACK,KAAK,IAAI5B,OAAO,CAACyB,cAAc,CAACG,KAAK,EAAEjB,MAAM,CAAC,CAAC;MACxI;IACF,KAAK,SAAS;MACZ;QACE,OAAOxB,gBAAgB,CAACa,OAAO,EAAEW,MAAM,CAAC;MAC1C;IACF,KAAK,UAAU;MACb;QACE,MAAME,GAAG,GAAGb,OAAO,CAACc,IAAI,CAACL,SAAS,EAAEiB,QAAQ,CAAC;QAC7C,OAAO,CAAC1B,OAAO,CAAC6B,UAAU,CAAChB,GAAG,CAAC,EAAEb,OAAO,CAAC8B,QAAQ,CAACjB,GAAG,CAAC,CAAC,CAACU,GAAG,CAACT,IAAI,IAAId,OAAO,CAACyB,cAAc,CAACX,IAAI,EAAEH,MAAM,CAAC,CAAC;MAC3G;IACF;MACE;QACE,OAAO,EAAE;MACX;EACJ;AACF,CAAC;;AAED;AACA;AACArC,OAAO,CAACY,uBAAuB,GAAGA,uBAAuB;AACzD,MAAMW,+BAA+B,GAAGvB,OAAO,CAACuB,+BAA+B,GAAG,GAAG;AACrF,MAAMkC,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/E,MAAM9C,kBAAkB,GAAGe,OAAO,IAAI;EACpC,MAAMgC,KAAK,GAAGhC,OAAO,CAACc,IAAI,CAACL,SAAS,CAAC;EACrC,MAAMwB,aAAa,GAAGjC,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAACkC,UAAU,CAACF,KAAK,EAAE,CAAC,CAAC,EAAEnC,+BAA+B,CAAC;EAC3G,IAAIoC,aAAa,KAAK,GAAG,EAAE;IACzB,OAAOF,oBAAoB;EAC7B;EACA,OAAOI,KAAK,CAACC,IAAI,CAAC;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC,CAACd,GAAG,CAAC,CAACe,CAAC,EAAEC,KAAK,KAAKvC,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAACkC,UAAU,CAACF,KAAK,EAAEO,KAAK,CAAC,EAAE1C,+BAA+B,CAAC,CAAC;AACjH,CAAC;AACDvB,OAAO,CAACW,kBAAkB,GAAGA,kBAAkB;AAC/C,MAAMR,qBAAqB,GAAGA,CAAC+D,QAAQ,EAAEC,eAAe,KAAK;EAC3D,IAAIA,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC9B,OAAOD,QAAQ;EACjB;EACA,MAAME,MAAM,GAAG,EAAE;EACjB,IAAIC,qBAAqB,GAAG,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACH,MAAM,EAAEO,CAAC,IAAI,CAAC,EAAE;IAC3CD,qBAAqB,IAAIH,QAAQ,CAACI,CAAC,CAAC;IACpC,MAAMC,kBAAkB,GAAGJ,eAAe,CAACK,OAAO,CAACH,qBAAqB,CAAC;IACzE,IAAIE,kBAAkB,GAAG,CAAC,CAAC,EAAE;MAC3BH,MAAM,CAACrB,IAAI,CAACwB,kBAAkB,CAACE,QAAQ,CAAC,CAAC,CAAC;MAC1CJ,qBAAqB,GAAG,EAAE;IAC5B;EACF;EACA,OAAOD,MAAM,CAACrC,IAAI,CAAC,EAAE,CAAC;AACxB,CAAC;AACD/B,OAAO,CAACG,qBAAqB,GAAGA,qBAAqB;AACrD,MAAMmB,oBAAoB,GAAGA,CAAC4C,QAAQ,EAAEC,eAAe,KAAK;EAC1D,IAAIA,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC9B,OAAOD,QAAQ;EACjB;EACA,OAAOA,QAAQ,CAACQ,KAAK,CAAC,EAAE,CAAC,CAACzB,GAAG,CAAC0B,IAAI,IAAIR,eAAe,CAACS,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC5C,IAAI,CAAC,EAAE,CAAC;AAC/E,CAAC;AACD/B,OAAO,CAACsB,oBAAoB,GAAGA,oBAAoB;AACnD,MAAMhB,cAAc,GAAGA,CAAC4D,QAAQ,EAAEC,eAAe,KAAK;EACpD,MAAMU,oBAAoB,GAAG1E,qBAAqB,CAAC+D,QAAQ,EAAEC,eAAe,CAAC;EAC7E;EACA,OAAOU,oBAAoB,KAAK,GAAG,IAAI,CAACD,MAAM,CAACE,KAAK,CAACF,MAAM,CAACC,oBAAoB,CAAC,CAAC;AACpF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA7E,OAAO,CAACM,cAAc,GAAGA,cAAc;AACvC,MAAMa,iBAAiB,GAAGA,CAAC+C,QAAQ,EAAEa,IAAI,KAAK;EAC5C;EACA,OAAOH,MAAM,CAACV,QAAQ,CAAC,CAACO,QAAQ,CAAC,CAAC,CAACO,QAAQ,CAACD,IAAI,EAAE,GAAG,CAAC;AACxD,CAAC;AACD/E,OAAO,CAACmB,iBAAiB,GAAGA,iBAAiB;AAC7C,MAAMC,sBAAsB,GAAGA,CAACM,OAAO,EAAEzB,KAAK,EAAEgF,iBAAiB,EAAEd,eAAe,EAAEe,OAAO,KAAK;EAC9F,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIH,OAAO,CAAClD,IAAI,KAAK,KAAK,IAAIkD,OAAO,CAACjD,WAAW,KAAK,mBAAmB,EAAE;MACzE,MAAM,IAAIH,KAAK,CAAC,CAAC,qBAAqBoD,OAAO,CAAC7C,MAAM;AAC1D,sEAAsE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/E;EACF;EACA,IAAImD,OAAO,CAAClD,IAAI,KAAK,KAAK,IAAIkD,OAAO,CAACjD,WAAW,KAAK,mBAAmB,EAAE;IACzE,MAAMO,IAAI,GAAGd,OAAO,CAAC4D,OAAO,CAACL,iBAAiB,CAACM,YAAY,EAAEtF,KAAK,CAAC;IACnE,OAAOyB,OAAO,CAACyB,cAAc,CAACX,IAAI,EAAE0C,OAAO,CAAC7C,MAAM,CAAC;EACrD;;EAEA;EACA,IAAI6B,QAAQ,GAAGjE,KAAK,CAACwE,QAAQ,CAAC,CAAC;EAC/B,IAAIS,OAAO,CAACM,sBAAsB,EAAE;IAClCtB,QAAQ,GAAG/C,iBAAiB,CAAC+C,QAAQ,EAAEgB,OAAO,CAAChD,SAAS,CAAC;EAC3D;EACA,OAAOZ,oBAAoB,CAAC4C,QAAQ,EAAEC,eAAe,CAAC;AACxD,CAAC;AACDnE,OAAO,CAACoB,sBAAsB,GAAGA,sBAAsB;AACvD,MAAMX,sBAAsB,GAAGA,CAACyE,OAAO,EAAEO,MAAM,EAAEtB,eAAe,KAAK;EACnE,IAAIlE,KAAK,GAAGiF,OAAO,CAACjF,KAAK,IAAIiF,OAAO,CAACQ,WAAW;EAChD,MAAMC,eAAe,GAAGF,MAAM,KAAK,WAAW,GAAGP,OAAO,CAACU,uBAAuB,GAAGV,OAAO,CAACM,sBAAsB;EACjH,IAAIC,MAAM,KAAK,WAAW,IAAIP,OAAO,CAACM,sBAAsB,IAAI,CAACN,OAAO,CAACU,uBAAuB,EAAE;IAChG3F,KAAK,GAAG2E,MAAM,CAACzE,qBAAqB,CAACF,KAAK,EAAEkE,eAAe,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC;EAC1E;;EAEA;EACA;EACA;EACA;EACA;EACA,MAAMoB,uBAAuB,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACL,MAAM,CAAC,IAAIP,OAAO,CAACjD,WAAW,KAAK,OAAO,IAAI,CAAC0D,eAAe,IAAI1F,KAAK,CAAC8D,MAAM,KAAK,CAAC;EACxJ,IAAI8B,uBAAuB,EAAE;IAC3B5F,KAAK,GAAG,GAAGA,KAAK,QAAQ;EAC1B;EACA,IAAIwF,MAAM,KAAK,WAAW,EAAE;IAC1BxF,KAAK,GAAG,SAASA,KAAK,QAAQ;EAChC;EACA,OAAOA,KAAK;AACd,CAAC;AACDD,OAAO,CAACS,sBAAsB,GAAGA,sBAAsB;AACvD,MAAMY,wBAAwB,GAAGA,CAACK,OAAO,EAAEwC,QAAQ,EAAE6B,aAAa,EAAEC,SAAS,KAAK;EAChF,IAAIb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIvE,mCAAmC,CAACY,OAAO,EAAEqE,aAAa,CAAC,CAAC/D,IAAI,KAAK,SAAS,EAAE;MAClF,MAAM,IAAIF,KAAK,CAAC,2DAA2D,CAAC;IAC9E;EACF;EACA,OAAOJ,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAACuE,KAAK,CAAC/B,QAAQ,EAAE6B,aAAa,CAAC,EAAEC,SAAS,CAAC;AAClF,CAAC;AACDhG,OAAO,CAACqB,wBAAwB,GAAGA,wBAAwB;AAC3D,MAAM6E,qBAAqB,GAAGA,CAACxE,OAAO,EAAEW,MAAM,KAAKX,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAACc,IAAI,CAACL,SAAS,EAAE,QAAQ,CAAC,EAAEE,MAAM,CAAC,CAAC0B,MAAM,KAAK,CAAC;AACjI,MAAM/C,iCAAiC,GAAGA,CAACU,OAAO,EAAEO,WAAW,EAAEG,WAAW,EAAEC,MAAM,KAAK;EACvF,IAAIJ,WAAW,KAAK,OAAO,EAAE;IAC3B,OAAO,KAAK;EACd;EACA,MAAMM,GAAG,GAAGb,OAAO,CAACc,IAAI,CAACL,SAAS,EAAE,SAAS,CAAC;EAC9C,QAAQC,WAAW;IACjB;IACA,KAAK,MAAM;MACT;QACE;QACA,IAAIV,OAAO,CAACyE,GAAG,KAAK,OAAO,IAAI9D,MAAM,KAAK,IAAI,EAAE;UAC9C,OAAO,IAAI;QACb;QACA,OAAOX,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC0E,OAAO,CAAC7D,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAACgE,UAAU,CAAC,GAAG,CAAC;MAChF;IACF,KAAK,OAAO;MACV;QACE,OAAO3E,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC4E,WAAW,CAAC/D,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MAC5E;IACF,KAAK,KAAK;MACR;QACE,OAAOrC,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC6E,YAAY,CAAChE,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MAC7E;IACF,KAAK,SAAS;MACZ;QACE,OAAOrC,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAACgB,WAAW,CAACH,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MAC5E;IACF,KAAK,OAAO;MACV;QACE,OAAOrC,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC8E,QAAQ,CAACjE,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MAC5E;IACF,KAAK,SAAS;MACZ;QACE,OAAOrC,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC+E,UAAU,CAAClE,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MAC9E;IACF,KAAK,SAAS;MACZ;QACE,OAAOrC,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAACkC,UAAU,CAACrB,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MAC9E;IACF;MACE;QACE,MAAM,IAAIjC,KAAK,CAAC,sBAAsB,CAAC;MACzC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA9B,OAAO,CAACgB,iCAAiC,GAAGA,iCAAiC;AAC7E,MAAMD,uBAAuB,GAAGA,CAACW,OAAO,EAAEgF,QAAQ,EAAEvC,eAAe,KAAK;EACtE;EACA;EACA;EACA,MAAMwC,kBAAkB,GAAGD,QAAQ,CAACE,IAAI,CAAC1B,OAAO,IAAIA,OAAO,CAAClD,IAAI,KAAK,KAAK,CAAC;EAC3E,MAAM6E,cAAc,GAAG,EAAE;EACzB,MAAMC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,QAAQ,CAAC3C,MAAM,EAAEO,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAMY,OAAO,GAAGwB,QAAQ,CAACpC,CAAC,CAAC;IAC3B,MAAMyC,UAAU,GAAGJ,kBAAkB,IAAIzB,OAAO,CAAClD,IAAI,KAAK,SAAS;IACnE,IAAI,CAAC+E,UAAU,EAAE;MACfF,cAAc,CAAC9D,IAAI,CAACmC,OAAO,CAAC7C,MAAM,CAAC;MACnCyE,aAAa,CAAC/D,IAAI,CAACtC,sBAAsB,CAACyE,OAAO,EAAE,WAAW,EAAEf,eAAe,CAAC,CAAC;IACnF;EACF;EACA,MAAM6C,sBAAsB,GAAGH,cAAc,CAAC9E,IAAI,CAAC,GAAG,CAAC;EACvD,MAAMkF,uBAAuB,GAAGH,aAAa,CAAC/E,IAAI,CAAC,GAAG,CAAC;EACvD,OAAOL,OAAO,CAACuE,KAAK,CAACgB,uBAAuB,EAAED,sBAAsB,CAAC;AACvE,CAAC;AACDhH,OAAO,CAACe,uBAAuB,GAAGA,uBAAuB;AACzD,MAAME,yCAAyC,GAAGyF,QAAQ,IAAIA,QAAQ,CAACzD,GAAG,CAACiC,OAAO,IAAI;EACpF,OAAO,GAAGA,OAAO,CAACgC,cAAc,GAAGhC,OAAO,CAACjF,KAAK,IAAIiF,OAAO,CAACQ,WAAW,GAAGR,OAAO,CAACiC,YAAY,EAAE;AAClG,CAAC,CAAC,CAACpF,IAAI,CAAC,EAAE,CAAC;AACX/B,OAAO,CAACiB,yCAAyC,GAAGA,yCAAyC;AAC7F,MAAMC,mCAAmC,GAAGA,CAACwF,QAAQ,EAAEvC,eAAe,EAAEiD,KAAK,KAAK;EAChF,MAAMC,iBAAiB,GAAGX,QAAQ,CAACzD,GAAG,CAACiC,OAAO,IAAI;IAChD,MAAMoC,SAAS,GAAG7G,sBAAsB,CAACyE,OAAO,EAAEkC,KAAK,GAAG,WAAW,GAAG,WAAW,EAAEjD,eAAe,CAAC;IACrG,OAAO,GAAGe,OAAO,CAACgC,cAAc,GAAGI,SAAS,GAAGpC,OAAO,CAACiC,YAAY,EAAE;EACvE,CAAC,CAAC;EACF,MAAMI,OAAO,GAAGF,iBAAiB,CAACtF,IAAI,CAAC,EAAE,CAAC;EAC1C,IAAI,CAACqF,KAAK,EAAE;IACV,OAAOG,OAAO;EAChB;;EAEA;EACA;EACA;EACA;EACA;EACA,OAAO,SAASA,OAAO,QAAQ;AACjC,CAAC;AACDvH,OAAO,CAACkB,mCAAmC,GAAGA,mCAAmC;AACjF,MAAMV,qBAAqB,GAAGA,CAACkB,OAAO,EAAEyC,eAAe,EAAEf,QAAQ,KAAK;EACpE,MAAMM,KAAK,GAAGhC,OAAO,CAACc,IAAI,CAACL,SAAS,EAAEiB,QAAQ,CAAC;EAC/C,MAAMoE,SAAS,GAAG9F,OAAO,CAAC8F,SAAS,CAAC9D,KAAK,CAAC;EAC1C,MAAMF,QAAQ,GAAG9B,OAAO,CAAC8B,QAAQ,CAACE,KAAK,CAAC;EACxC,MAAM;IACJ+D,cAAc;IACdlC;EACF,CAAC,GAAG,CAAC,CAAC,EAAE/D,UAAU,CAAC6B,eAAe,EAAE3B,OAAO,EAAEgC,KAAK,CAAC,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAErE,KAAK,KAAK;IACzE,MAAMsE,WAAW,GAAGlG,OAAO,CAACmG,cAAc,CAACvE,KAAK,CAAC;IACjD,IAAIsE,WAAW,GAAGD,GAAG,CAACF,cAAc,EAAE;MACpC,OAAO;QACLA,cAAc,EAAEG,WAAW;QAC3BrC,YAAY,EAAEjC;MAChB,CAAC;IACH;IACA,OAAOqE,GAAG;EACZ,CAAC,EAAE;IACDF,cAAc,EAAE,CAAC;IACjBlC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,OAAO;IACLuC,IAAI,EAAEA,CAAC;MACLzF;IACF,CAAC,MAAM;MACL0F,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE9B,qBAAqB,CAACxE,OAAO,EAAEW,MAAM,CAAC,GAAG,IAAI,GAAG;IAC3D,CAAC,CAAC;IACFiB,KAAK,EAAEA,CAAA,MAAO;MACZyE,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAEtG,OAAO,CAACuG,QAAQ,CAACT,SAAS,CAAC,GAAG;IACzC,CAAC,CAAC;IACFU,GAAG,EAAEA,CAAC;MACJC;IACF,CAAC,MAAM;MACLJ,OAAO,EAAE,CAAC;MACVC,OAAO,EAAEtG,OAAO,CAAC0G,OAAO,CAACD,WAAW,CAAC,GAAGzG,OAAO,CAACmG,cAAc,CAACM,WAAW,CAAC,GAAGV,cAAc;MAC5FlC,YAAY,EAAEA;IAChB,CAAC,CAAC;IACFrC,OAAO,EAAEA,CAAC;MACRb,MAAM;MACNJ;IACF,CAAC,KAAK;MACJ,IAAIA,WAAW,KAAK,OAAO,EAAE;QAC3B,MAAMoG,UAAU,GAAGxH,gBAAgB,CAACa,OAAO,EAAEW,MAAM,CAAC,CAACY,GAAG,CAAC2B,MAAM,CAAC;QAChE,OAAO;UACLmD,OAAO,EAAEO,IAAI,CAACC,GAAG,CAAC,GAAGF,UAAU,CAAC;UAChCL,OAAO,EAAEM,IAAI,CAACE,GAAG,CAAC,GAAGH,UAAU;QACjC,CAAC;MACH;MACA,OAAO;QACLN,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IACDS,KAAK,EAAEA,CAAC;MACNpG;IACF,CAAC,KAAK;MACJ,MAAMqG,aAAa,GAAGhH,OAAO,CAACiH,QAAQ,CAACnF,QAAQ,CAAC;MAChD,MAAMoF,WAAW,GAAGzI,qBAAqB,CAACuB,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC8B,QAAQ,CAACE,KAAK,CAAC,EAAErB,MAAM,CAAC,EAAE8B,eAAe,CAAC,KAAKuE,aAAa,CAACjE,QAAQ,CAAC,CAAC;MAChJ,IAAImE,WAAW,EAAE;QACf,OAAO;UACLb,OAAO,EAAE,CAAC;UACVC,OAAO,EAAEpD,MAAM,CAACzE,qBAAqB,CAACuB,OAAO,CAACyB,cAAc,CAACzB,OAAO,CAAC6B,UAAU,CAACG,KAAK,CAAC,EAAErB,MAAM,CAAC,EAAE8B,eAAe,CAAC;QACnH,CAAC;MACH;MACA,OAAO;QACL4D,OAAO,EAAE,CAAC;QACVC,OAAO,EAAEU;MACX,CAAC;IACH,CAAC;IACDG,OAAO,EAAEA,CAAA,MAAO;MACdd,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAEtG,OAAO,CAACoH,UAAU,CAACtF,QAAQ;IACtC,CAAC,CAAC;IACFuF,OAAO,EAAEA,CAAA,MAAO;MACdhB,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAEtG,OAAO,CAACsH,UAAU,CAACxF,QAAQ;IACtC,CAAC,CAAC;IACFyF,QAAQ,EAAEA,CAAA,MAAO;MACflB,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC,CAAC;IACFkB,KAAK,EAAEA,CAAA,MAAO;MACZnB,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;AACH,CAAC;AACDhI,OAAO,CAACQ,qBAAqB,GAAGA,qBAAqB;AACrD,IAAI2I,wBAAwB,GAAG,KAAK;AACpC,MAAMjJ,gBAAgB,GAAGA,CAACwG,QAAQ,EAAE0C,SAAS,KAAK;EAChD,IAAIjE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAAC8D,wBAAwB,EAAE;MAC7B,MAAME,iBAAiB,GAAG,CAAC,OAAO,CAAC;MACnC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAACvD,QAAQ,CAACsD,SAAS,CAAC,EAAE;QAC7CC,iBAAiB,CAACtG,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;MAC3D;MACA,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC+C,QAAQ,CAACsD,SAAS,CAAC,EAAE;QAC7CC,iBAAiB,CAACtG,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;MACnE;MACA,MAAMuG,cAAc,GAAG5C,QAAQ,CAAC6C,IAAI,CAACrE,OAAO,IAAI,CAACmE,iBAAiB,CAACvD,QAAQ,CAACZ,OAAO,CAAClD,IAAI,CAAC,CAAC;MAC1F,IAAIsH,cAAc,EAAE;QAClBE,OAAO,CAACC,IAAI,CAAC,wEAAwEH,cAAc,CAACtH,IAAI,iBAAiB,EAAE,qCAAqCqH,iBAAiB,CAACtH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACtMoH,wBAAwB,GAAG,IAAI;MACjC;IACF;EACF;AACF,CAAC;AACDnJ,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMwJ,wBAAwB,GAAGA,CAAChI,OAAO,EAAEwD,OAAO,EAAEyE,kBAAkB,EAAEC,gBAAgB,KAAK;EAC3F,QAAQ1E,OAAO,CAAClD,IAAI;IAClB,KAAK,MAAM;MACT;QACE,OAAON,OAAO,CAAC0E,OAAO,CAACwD,gBAAgB,EAAElI,OAAO,CAACmI,OAAO,CAACF,kBAAkB,CAAC,CAAC;MAC/E;IACF,KAAK,OAAO;MACV;QACE,OAAOjI,OAAO,CAACoI,QAAQ,CAACF,gBAAgB,EAAElI,OAAO,CAACuG,QAAQ,CAAC0B,kBAAkB,CAAC,CAAC;MACjF;IACF,KAAK,SAAS;MACZ;QACE,IAAII,wBAAwB,GAAGrI,OAAO,CAACyB,cAAc,CAACwG,kBAAkB,EAAEzE,OAAO,CAAC7C,MAAM,CAAC;QACzF,IAAI6C,OAAO,CAACM,sBAAsB,EAAE;UAClCuE,wBAAwB,GAAG5I,iBAAiB,CAAC4I,wBAAwB,EAAE7E,OAAO,CAAChD,SAAS,CAAC;QAC3F;QACA,MAAM8H,mBAAmB,GAAGnJ,gBAAgB,CAACa,OAAO,EAAEwD,OAAO,CAAC7C,MAAM,CAAC;QACrE,MAAM4H,qBAAqB,GAAGD,mBAAmB,CAACxF,OAAO,CAACuF,wBAAwB,CAAC;QACnF,MAAMG,0BAA0B,GAAGF,mBAAmB,CAACxF,OAAO,CAACU,OAAO,CAACjF,KAAK,CAAC;QAC7E,MAAMkK,IAAI,GAAGD,0BAA0B,GAAGD,qBAAqB;QAC/D,OAAOvI,OAAO,CAACsB,OAAO,CAAC2G,kBAAkB,EAAEQ,IAAI,CAAC;MAClD;IACF,KAAK,KAAK;MACR;QACE,OAAOzI,OAAO,CAAC4D,OAAO,CAACsE,gBAAgB,EAAElI,OAAO,CAAC0I,OAAO,CAACT,kBAAkB,CAAC,CAAC;MAC/E;IACF,KAAK,UAAU;MACb;QACE,MAAMU,IAAI,GAAG3I,OAAO,CAACiH,QAAQ,CAACgB,kBAAkB,CAAC,GAAG,EAAE;QACtD,MAAMW,eAAe,GAAG5I,OAAO,CAACiH,QAAQ,CAACiB,gBAAgB,CAAC;QAC1D,IAAIS,IAAI,IAAIC,eAAe,IAAI,EAAE,EAAE;UACjC,OAAO5I,OAAO,CAAC6I,QAAQ,CAACX,gBAAgB,EAAE,CAAC,EAAE,CAAC;QAChD;QACA,IAAI,CAACS,IAAI,IAAIC,eAAe,GAAG,EAAE,EAAE;UACjC,OAAO5I,OAAO,CAAC6I,QAAQ,CAACX,gBAAgB,EAAE,EAAE,CAAC;QAC/C;QACA,OAAOA,gBAAgB;MACzB;IACF,KAAK,OAAO;MACV;QACE,OAAOlI,OAAO,CAAC8E,QAAQ,CAACoD,gBAAgB,EAAElI,OAAO,CAACiH,QAAQ,CAACgB,kBAAkB,CAAC,CAAC;MACjF;IACF,KAAK,SAAS;MACZ;QACE,OAAOjI,OAAO,CAAC+E,UAAU,CAACmD,gBAAgB,EAAElI,OAAO,CAACoH,UAAU,CAACa,kBAAkB,CAAC,CAAC;MACrF;IACF,KAAK,SAAS;MACZ;QACE,OAAOjI,OAAO,CAACkC,UAAU,CAACgG,gBAAgB,EAAElI,OAAO,CAACsH,UAAU,CAACW,kBAAkB,CAAC,CAAC;MACrF;IACF;MACE;QACE,OAAOC,gBAAgB;MACzB;EACJ;AACF,CAAC;AACD,MAAMY,gCAAgC,GAAG;EACvC1C,IAAI,EAAE,CAAC;EACPxE,KAAK,EAAE,CAAC;EACR4E,GAAG,EAAE,CAAC;EACNhF,OAAO,EAAE,CAAC;EACVuF,KAAK,EAAE,CAAC;EACRI,OAAO,EAAE,CAAC;EACVE,OAAO,EAAE,CAAC;EACVE,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE;AACT,CAAC;AACD,MAAM7I,0BAA0B,GAAGA,CAACqB,OAAO,EAAEiI,kBAAkB,EAAEjD,QAAQ,EAAE+D,aAAa,EAAEC,2BAA2B;AACrH;AACA,CAAC,GAAGhE,QAAQ,CAAC,CAACiE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKL,gCAAgC,CAACI,CAAC,CAAC5I,IAAI,CAAC,GAAGwI,gCAAgC,CAACK,CAAC,CAAC7I,IAAI,CAAC,CAAC,CAAC0F,MAAM,CAAC,CAACoD,UAAU,EAAE5F,OAAO,KAAK;EAChJ,IAAI,CAACwF,2BAA2B,IAAIxF,OAAO,CAAC6F,QAAQ,EAAE;IACpD,OAAOrB,wBAAwB,CAAChI,OAAO,EAAEwD,OAAO,EAAEyE,kBAAkB,EAAEmB,UAAU,CAAC;EACnF;EACA,OAAOA,UAAU;AACnB,CAAC,EAAEL,aAAa,CAAC;AACjBzK,OAAO,CAACK,0BAA0B,GAAGA,0BAA0B;AAC/D,MAAME,SAAS,GAAGA,CAAA,KAAMyK,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACpF,QAAQ,CAAC,SAAS,CAAC;;AAE7E;AACA9F,OAAO,CAACO,SAAS,GAAGA,SAAS;AAC7B,MAAMG,eAAe,GAAGA,CAACgG,QAAQ,EAAEyE,cAAc,KAAK;EACpD,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,IAAI,CAACD,cAAc,EAAE;IACnBzE,QAAQ,CAAC2E,OAAO,CAAC,CAACrH,CAAC,EAAEC,KAAK,KAAK;MAC7B,MAAMqH,SAAS,GAAGrH,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK,GAAG,CAAC;MAChD,MAAMsH,UAAU,GAAGtH,KAAK,KAAKyC,QAAQ,CAAC3C,MAAM,GAAG,CAAC,GAAG,IAAI,GAAGE,KAAK,GAAG,CAAC;MACnEmH,SAAS,CAACnH,KAAK,CAAC,GAAG;QACjBqH,SAAS;QACTC;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAO;MACLH,SAAS;MACTI,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE/E,QAAQ,CAAC3C,MAAM,GAAG;IAC9B,CAAC;EACH;EACA,MAAM2H,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,CAAC,CAAC;EAClB,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,IAAIC,QAAQ,GAAGpF,QAAQ,CAAC3C,MAAM,GAAG,CAAC;EAClC,OAAO+H,QAAQ,IAAI,CAAC,EAAE;IACpBD,kBAAkB,GAAGnF,QAAQ,CAACqF,SAAS;IACvC;IACA,CAAC7G,OAAO,EAAEjB,KAAK,KAAKA,KAAK,IAAI2H,oBAAoB,IAAI1G,OAAO,CAACiC,YAAY,EAAErB,QAAQ,CAAC,GAAG,CAAC;IACxF;IACAZ,OAAO,CAACiC,YAAY,KAAK,KAAK,CAAC;IAC/B,IAAI0E,kBAAkB,KAAK,CAAC,CAAC,EAAE;MAC7BA,kBAAkB,GAAGnF,QAAQ,CAAC3C,MAAM,GAAG,CAAC;IAC1C;IACA,KAAK,IAAIO,CAAC,GAAGuH,kBAAkB,EAAEvH,CAAC,IAAIsH,oBAAoB,EAAEtH,CAAC,IAAI,CAAC,EAAE;MAClEqH,OAAO,CAACrH,CAAC,CAAC,GAAGwH,QAAQ;MACrBJ,OAAO,CAACI,QAAQ,CAAC,GAAGxH,CAAC;MACrBwH,QAAQ,IAAI,CAAC;IACf;IACAF,oBAAoB,GAAGC,kBAAkB,GAAG,CAAC;EAC/C;EACAnF,QAAQ,CAAC2E,OAAO,CAAC,CAACrH,CAAC,EAAEC,KAAK,KAAK;IAC7B,MAAM+H,QAAQ,GAAGL,OAAO,CAAC1H,KAAK,CAAC;IAC/B,MAAMqH,SAAS,GAAGU,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGN,OAAO,CAACM,QAAQ,GAAG,CAAC,CAAC;IAC/D,MAAMT,UAAU,GAAGS,QAAQ,KAAKtF,QAAQ,CAAC3C,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG2H,OAAO,CAACM,QAAQ,GAAG,CAAC,CAAC;IAClFZ,SAAS,CAACnH,KAAK,CAAC,GAAG;MACjBqH,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO;IACLH,SAAS;IACTI,UAAU,EAAEE,OAAO,CAAC,CAAC,CAAC;IACtBD,QAAQ,EAAEC,OAAO,CAAChF,QAAQ,CAAC3C,MAAM,GAAG,CAAC;EACvC,CAAC;AACH,CAAC;AACD/D,OAAO,CAACU,eAAe,GAAGA,eAAe;AACzC,MAAMN,qBAAqB,GAAGA,CAAC6L,gBAAgB,EAAEvF,QAAQ,KAAK;EAC5D,IAAIuF,gBAAgB,IAAI,IAAI,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,IAAIA,gBAAgB,KAAK,KAAK,EAAE;IAC9B,OAAO,KAAK;EACd;EACA,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;IACxC,MAAMhI,KAAK,GAAGyC,QAAQ,CAACqF,SAAS,CAAC7G,OAAO,IAAIA,OAAO,CAAClD,IAAI,KAAKiK,gBAAgB,CAAC;IAC9E,OAAOhI,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGA,KAAK;EACpC;EACA,OAAOgI,gBAAgB;AACzB,CAAC;AACDjM,OAAO,CAACI,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}