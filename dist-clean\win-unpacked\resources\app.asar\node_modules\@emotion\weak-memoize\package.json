{"name": "@emotion/weak-memoize", "version": "0.4.0", "description": "A memoization function that uses a WeakMap", "main": "dist/emotion-weak-memoize.cjs.js", "module": "dist/emotion-weak-memoize.esm.js", "types": "dist/emotion-weak-memoize.cjs.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/weak-memoize", "publishConfig": {"access": "public"}, "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^5.4.5"}, "files": ["src", "dist"], "exports": {".": {"module": "./dist/emotion-weak-memoize.esm.js", "import": "./dist/emotion-weak-memoize.cjs.mjs", "default": "./dist/emotion-weak-memoize.cjs.js"}, "./package.json": "./package.json"}}