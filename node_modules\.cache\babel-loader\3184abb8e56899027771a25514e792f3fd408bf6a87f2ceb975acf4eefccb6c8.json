{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldState = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _useTimeout = _interopRequireDefault(require(\"@mui/utils/useTimeout\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _hooks = require(\"../../../hooks\");\nvar _useField = require(\"./useField.utils\");\nvar _buildSectionsFromFormat = require(\"./buildSectionsFromFormat\");\nvar _validation = require(\"../../../validation\");\nvar _useControlledValue = require(\"../useControlledValue\");\nvar _getDefaultReferenceDate = require(\"../../utils/getDefaultReferenceDate\");\nconst QUERY_LIFE_DURATION_MS = 5000;\nconst useFieldState = parameters => {\n  const adapter = (0, _hooks.usePickerAdapter)();\n  const translations = (0, _hooks.usePickerTranslations)();\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const {\n    manager: {\n      validator,\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager\n    },\n    internalPropsWithDefaults,\n    internalPropsWithDefaults: {\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      onChange,\n      format,\n      formatDensity = 'dense',\n      selectedSections: selectedSectionsProp,\n      onSelectedSectionsChange,\n      shouldRespectLeadingZeros = false,\n      timezone: timezoneProp,\n      enableAccessibleFieldDOMStructure = true\n    },\n    forwardedProps: {\n      error: errorProp\n    }\n  } = parameters;\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = (0, _useControlledValue.useControlledValue)({\n    name: 'a field component',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager\n  });\n  const valueRef = React.useRef(value);\n  React.useEffect(() => {\n    valueRef.current = value;\n  }, [value]);\n  const {\n    hasValidationError\n  } = (0, _validation.useValidation)({\n    props: internalPropsWithDefaults,\n    validator,\n    timezone,\n    value,\n    onError: internalPropsWithDefaults.onError\n  });\n  const error = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (errorProp !== undefined) {\n      return errorProp;\n    }\n    return hasValidationError;\n  }, [hasValidationError, errorProp]);\n  const localizedDigits = React.useMemo(() => (0, _useField.getLocalizedDigits)(adapter), [adapter]);\n  const sectionsValueBoundaries = React.useMemo(() => (0, _useField.getSectionsBoundaries)(adapter, localizedDigits, timezone), [adapter, localizedDigits, timezone]);\n  const getSectionsFromValue = React.useCallback(valueToAnalyze => fieldValueManager.getSectionsFromValue(valueToAnalyze, date => (0, _buildSectionsFromFormat.buildSectionsFromFormat)({\n    adapter,\n    localeText: translations,\n    localizedDigits,\n    format,\n    date,\n    formatDensity,\n    shouldRespectLeadingZeros,\n    enableAccessibleFieldDOMStructure,\n    isRtl\n  })), [fieldValueManager, format, translations, localizedDigits, isRtl, shouldRespectLeadingZeros, adapter, formatDensity, enableAccessibleFieldDOMStructure]);\n  const [state, setState] = React.useState(() => {\n    const sections = getSectionsFromValue(value);\n    (0, _useField.validateSections)(sections, valueType);\n    const stateWithoutReferenceDate = {\n      sections,\n      lastExternalValue: value,\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      tempValueStrAndroid: null,\n      characterQuery: null\n    };\n    const granularity = (0, _getDefaultReferenceDate.getSectionTypeGranularity)(sections);\n    const referenceValue = valueManager.getInitialReferenceValue({\n      referenceDate: referenceDateProp,\n      value,\n      adapter,\n      props: internalPropsWithDefaults,\n      granularity,\n      timezone\n    });\n    return (0, _extends2.default)({}, stateWithoutReferenceDate, {\n      referenceValue\n    });\n  });\n  const [selectedSections, innerSetSelectedSections] = (0, _useControlled.default)({\n    controlled: selectedSectionsProp,\n    default: null,\n    name: 'useField',\n    state: 'selectedSections'\n  });\n  const setSelectedSections = newSelectedSections => {\n    innerSetSelectedSections(newSelectedSections);\n    onSelectedSectionsChange?.(newSelectedSections);\n  };\n  const parsedSelectedSections = React.useMemo(() => (0, _useField.parseSelectedSections)(selectedSections, state.sections), [selectedSections, state.sections]);\n  const activeSectionIndex = parsedSelectedSections === 'all' ? 0 : parsedSelectedSections;\n  const sectionOrder = React.useMemo(() => (0, _useField.getSectionOrder)(state.sections, isRtl && !enableAccessibleFieldDOMStructure), [state.sections, isRtl, enableAccessibleFieldDOMStructure]);\n  const areAllSectionsEmpty = React.useMemo(() => state.sections.every(section => section.value === ''), [state.sections]);\n  const publishValue = newValue => {\n    const context = {\n      validationError: validator({\n        adapter,\n        value: newValue,\n        timezone,\n        props: internalPropsWithDefaults\n      })\n    };\n    handleValueChange(newValue, context);\n  };\n  const setSectionValue = (sectionIndex, newSectionValue) => {\n    const newSections = [...state.sections];\n    newSections[sectionIndex] = (0, _extends2.default)({}, newSections[sectionIndex], {\n      value: newSectionValue,\n      modified: true\n    });\n    return newSections;\n  };\n  const sectionToUpdateOnNextInvalidDateRef = React.useRef(null);\n  const updateSectionValueOnNextInvalidDateTimeout = (0, _useTimeout.default)();\n  const setSectionUpdateToApplyOnNextInvalidDate = newSectionValue => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    sectionToUpdateOnNextInvalidDateRef.current = {\n      sectionIndex: activeSectionIndex,\n      value: newSectionValue\n    };\n    updateSectionValueOnNextInvalidDateTimeout.start(0, () => {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    });\n  };\n  const clearValue = () => {\n    if (valueManager.areValuesEqual(adapter, value, valueManager.emptyValue)) {\n      setState(prevState => (0, _extends2.default)({}, prevState, {\n        sections: prevState.sections.map(section => (0, _extends2.default)({}, section, {\n          value: ''\n        })),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => (0, _extends2.default)({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(valueManager.emptyValue);\n    }\n  };\n  const clearActiveSection = () => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    const activeSection = state.sections[activeSectionIndex];\n    if (activeSection.value === '') {\n      return;\n    }\n    setSectionUpdateToApplyOnNextInvalidDate('');\n    if (fieldValueManager.getDateFromSection(value, activeSection) === null) {\n      setState(prevState => (0, _extends2.default)({}, prevState, {\n        sections: setSectionValue(activeSectionIndex, ''),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => (0, _extends2.default)({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(fieldValueManager.updateDateInValue(value, activeSection, null));\n    }\n  };\n  const updateValueFromValueStr = valueStr => {\n    const parseDateStr = (dateStr, referenceDate) => {\n      const date = adapter.parse(dateStr, format);\n      if (!adapter.isValid(date)) {\n        return null;\n      }\n      const sections = (0, _buildSectionsFromFormat.buildSectionsFromFormat)({\n        adapter,\n        localeText: translations,\n        localizedDigits,\n        format,\n        date,\n        formatDensity,\n        shouldRespectLeadingZeros,\n        enableAccessibleFieldDOMStructure,\n        isRtl\n      });\n      return (0, _useField.mergeDateIntoReferenceDate)(adapter, date, sections, referenceDate, false);\n    };\n    const newValue = fieldValueManager.parseValueStr(valueStr, state.referenceValue, parseDateStr);\n    publishValue(newValue);\n  };\n  const cleanActiveDateSectionsIfValueNullTimeout = (0, _useTimeout.default)();\n  const updateSectionValue = ({\n    section,\n    newSectionValue,\n    shouldGoToNextSection\n  }) => {\n    updateSectionValueOnNextInvalidDateTimeout.clear();\n    cleanActiveDateSectionsIfValueNullTimeout.clear();\n    const activeDate = fieldValueManager.getDateFromSection(value, section);\n\n    /**\n     * Decide which section should be focused\n     */\n    if (shouldGoToNextSection && activeSectionIndex < state.sections.length - 1) {\n      setSelectedSections(activeSectionIndex + 1);\n    }\n\n    /**\n     * Try to build a valid date from the new section value\n     */\n    const newSections = setSectionValue(activeSectionIndex, newSectionValue);\n    const newActiveDateSections = fieldValueManager.getDateSectionsFromValue(newSections, section);\n    const newActiveDate = (0, _useField.getDateFromDateSections)(adapter, newActiveDateSections, localizedDigits);\n\n    /**\n     * If the new date is valid,\n     * Then we merge the value of the modified sections into the reference date.\n     * This makes sure that we don't lose some information of the initial date (like the time on a date field).\n     */\n    if (adapter.isValid(newActiveDate)) {\n      const mergedDate = (0, _useField.mergeDateIntoReferenceDate)(adapter, newActiveDate, newActiveDateSections, fieldValueManager.getDateFromSection(state.referenceValue, section), true);\n      if (activeDate == null) {\n        cleanActiveDateSectionsIfValueNullTimeout.start(0, () => {\n          if (valueRef.current === value) {\n            setState(prevState => (0, _extends2.default)({}, prevState, {\n              sections: fieldValueManager.clearDateSections(state.sections, section),\n              tempValueStrAndroid: null\n            }));\n          }\n        });\n      }\n      return publishValue(fieldValueManager.updateDateInValue(value, section, mergedDate));\n    }\n\n    /**\n     * If all the sections are filled but the date is invalid and the previous date is valid or null,\n     * Then we publish an invalid date.\n     */\n    if (newActiveDateSections.every(sectionBis => sectionBis.value !== '') && (activeDate == null || adapter.isValid(activeDate))) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, newActiveDate));\n    }\n\n    /**\n     * If the previous date is not null,\n     * Then we publish the date as `null`.\n     */\n    if (activeDate != null) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, null));\n    }\n\n    /**\n     * If the previous date is already null,\n     * Then we don't publish the date and we update the sections.\n     */\n    return setState(prevState => (0, _extends2.default)({}, prevState, {\n      sections: newSections,\n      tempValueStrAndroid: null\n    }));\n  };\n  const setTempAndroidValueStr = tempValueStrAndroid => setState(prevState => (0, _extends2.default)({}, prevState, {\n    tempValueStrAndroid\n  }));\n  const setCharacterQuery = (0, _useEventCallback.default)(newCharacterQuery => {\n    setState(prevState => (0, _extends2.default)({}, prevState, {\n      characterQuery: newCharacterQuery\n    }));\n  });\n\n  // If `prop.value` changes, we update the state to reflect the new value\n  if (value !== state.lastExternalValue) {\n    let sections;\n    if (sectionToUpdateOnNextInvalidDateRef.current != null && !adapter.isValid(fieldValueManager.getDateFromSection(value, state.sections[sectionToUpdateOnNextInvalidDateRef.current.sectionIndex]))) {\n      sections = setSectionValue(sectionToUpdateOnNextInvalidDateRef.current.sectionIndex, sectionToUpdateOnNextInvalidDateRef.current.value);\n    } else {\n      sections = getSectionsFromValue(value);\n    }\n    setState(prevState => (0, _extends2.default)({}, prevState, {\n      lastExternalValue: value,\n      sections,\n      sectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      referenceValue: fieldValueManager.updateReferenceValue(adapter, value, prevState.referenceValue),\n      tempValueStrAndroid: null\n    }));\n  }\n  if (isRtl !== state.lastSectionsDependencies.isRtl || format !== state.lastSectionsDependencies.format || adapter.locale !== state.lastSectionsDependencies.locale) {\n    const sections = getSectionsFromValue(value);\n    (0, _useField.validateSections)(sections, valueType);\n    setState(prevState => (0, _extends2.default)({}, prevState, {\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      sections,\n      tempValueStrAndroid: null,\n      characterQuery: null\n    }));\n  }\n  if (state.characterQuery != null && !error && activeSectionIndex == null) {\n    setCharacterQuery(null);\n  }\n  if (state.characterQuery != null && state.sections[state.characterQuery.sectionIndex]?.type !== state.characterQuery.sectionType) {\n    setCharacterQuery(null);\n  }\n  React.useEffect(() => {\n    if (sectionToUpdateOnNextInvalidDateRef.current != null) {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    }\n  });\n  const cleanCharacterQueryTimeout = (0, _useTimeout.default)();\n  React.useEffect(() => {\n    if (state.characterQuery != null) {\n      cleanCharacterQueryTimeout.start(QUERY_LIFE_DURATION_MS, () => setCharacterQuery(null));\n    }\n    return () => {};\n  }, [state.characterQuery, setCharacterQuery, cleanCharacterQueryTimeout]);\n\n  // If `tempValueStrAndroid` is still defined for some section when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && activeSectionIndex != null) {\n      clearActiveSection();\n    }\n  }, [state.sections]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  return {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    sectionsValueBoundaries,\n    state,\n    timezone,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  };\n};\nexports.useFieldState = useFieldState;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "useFieldState", "_extends2", "React", "_useControlled", "_useTimeout", "_useEventCallback", "_RtlProvider", "_hooks", "_useField", "_buildSectionsFromFormat", "_validation", "_useControlledValue", "_getDefaultReferenceDate", "QUERY_LIFE_DURATION_MS", "parameters", "adapter", "usePickerAdapter", "translations", "usePickerTranslations", "isRtl", "useRtl", "manager", "validator", "valueType", "internal_valueManager", "valueManager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "internalPropsWithDefaults", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "onChange", "format", "formatDensity", "selectedSections", "selectedSectionsProp", "onSelectedSectionsChange", "shouldRespectLeadingZeros", "timezone", "timezoneProp", "enableAccessibleFieldDOMStructure", "forwardedProps", "error", "errorProp", "handleValueChange", "useControlledValue", "name", "valueRef", "useRef", "useEffect", "current", "hasValidationError", "useValidation", "props", "onError", "useMemo", "undefined", "localizedDigits", "getLocalizedDigits", "sectionsValueBoundaries", "getSectionsBoundaries", "getSectionsFromValue", "useCallback", "valueToAnalyze", "date", "buildSectionsFromFormat", "localeText", "state", "setState", "useState", "sections", "validateSections", "stateWithoutReferenceDate", "lastExternalValue", "lastSectionsDependencies", "locale", "tempValueStrAndroid", "<PERSON><PERSON><PERSON><PERSON>", "granularity", "getSectionTypeGranularity", "referenceValue", "getInitialReferenceValue", "innerSetSelectedSections", "controlled", "setSelectedSections", "newSelectedSections", "parsedSelectedSections", "parseSelectedSections", "activeSectionIndex", "sectionOrder", "getSectionOrder", "areAllSectionsEmpty", "every", "section", "publishValue", "newValue", "context", "validationError", "setSectionValue", "sectionIndex", "newSectionValue", "newSections", "modified", "sectionToUpdateOnNextInvalidDateRef", "updateSectionValueOnNextInvalidDateTimeout", "setSectionUpdateToApplyOnNextInvalidDate", "start", "clearValue", "areValuesEqual", "emptyValue", "prevState", "map", "clearActiveSection", "activeSection", "getDateFromSection", "updateDateInValue", "updateValueFromValueStr", "valueStr", "parseDateStr", "dateStr", "parse", "<PERSON><PERSON><PERSON><PERSON>", "mergeDateIntoReferenceDate", "parseValueStr", "cleanActiveDateSectionsIfValueNullTimeout", "updateSectionValue", "shouldGoToNextSection", "clear", "activeDate", "length", "newActiveDateSections", "getDateSectionsFromValue", "newActiveDate", "getDateFromDateSections", "mergedDate", "clearDateSections", "sectionBis", "setTempAndroidValueStr", "setCharacterQuery", "newCharacterQuery", "sectionsDependencies", "updateReferenceValue", "type", "sectionType", "cleanCharacterQueryTimeout"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldState.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldState = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _useTimeout = _interopRequireDefault(require(\"@mui/utils/useTimeout\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _hooks = require(\"../../../hooks\");\nvar _useField = require(\"./useField.utils\");\nvar _buildSectionsFromFormat = require(\"./buildSectionsFromFormat\");\nvar _validation = require(\"../../../validation\");\nvar _useControlledValue = require(\"../useControlledValue\");\nvar _getDefaultReferenceDate = require(\"../../utils/getDefaultReferenceDate\");\nconst QUERY_LIFE_DURATION_MS = 5000;\nconst useFieldState = parameters => {\n  const adapter = (0, _hooks.usePickerAdapter)();\n  const translations = (0, _hooks.usePickerTranslations)();\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const {\n    manager: {\n      validator,\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager\n    },\n    internalPropsWithDefaults,\n    internalPropsWithDefaults: {\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      onChange,\n      format,\n      formatDensity = 'dense',\n      selectedSections: selectedSectionsProp,\n      onSelectedSectionsChange,\n      shouldRespectLeadingZeros = false,\n      timezone: timezoneProp,\n      enableAccessibleFieldDOMStructure = true\n    },\n    forwardedProps: {\n      error: errorProp\n    }\n  } = parameters;\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = (0, _useControlledValue.useControlledValue)({\n    name: 'a field component',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager\n  });\n  const valueRef = React.useRef(value);\n  React.useEffect(() => {\n    valueRef.current = value;\n  }, [value]);\n  const {\n    hasValidationError\n  } = (0, _validation.useValidation)({\n    props: internalPropsWithDefaults,\n    validator,\n    timezone,\n    value,\n    onError: internalPropsWithDefaults.onError\n  });\n  const error = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (errorProp !== undefined) {\n      return errorProp;\n    }\n    return hasValidationError;\n  }, [hasValidationError, errorProp]);\n  const localizedDigits = React.useMemo(() => (0, _useField.getLocalizedDigits)(adapter), [adapter]);\n  const sectionsValueBoundaries = React.useMemo(() => (0, _useField.getSectionsBoundaries)(adapter, localizedDigits, timezone), [adapter, localizedDigits, timezone]);\n  const getSectionsFromValue = React.useCallback(valueToAnalyze => fieldValueManager.getSectionsFromValue(valueToAnalyze, date => (0, _buildSectionsFromFormat.buildSectionsFromFormat)({\n    adapter,\n    localeText: translations,\n    localizedDigits,\n    format,\n    date,\n    formatDensity,\n    shouldRespectLeadingZeros,\n    enableAccessibleFieldDOMStructure,\n    isRtl\n  })), [fieldValueManager, format, translations, localizedDigits, isRtl, shouldRespectLeadingZeros, adapter, formatDensity, enableAccessibleFieldDOMStructure]);\n  const [state, setState] = React.useState(() => {\n    const sections = getSectionsFromValue(value);\n    (0, _useField.validateSections)(sections, valueType);\n    const stateWithoutReferenceDate = {\n      sections,\n      lastExternalValue: value,\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      tempValueStrAndroid: null,\n      characterQuery: null\n    };\n    const granularity = (0, _getDefaultReferenceDate.getSectionTypeGranularity)(sections);\n    const referenceValue = valueManager.getInitialReferenceValue({\n      referenceDate: referenceDateProp,\n      value,\n      adapter,\n      props: internalPropsWithDefaults,\n      granularity,\n      timezone\n    });\n    return (0, _extends2.default)({}, stateWithoutReferenceDate, {\n      referenceValue\n    });\n  });\n  const [selectedSections, innerSetSelectedSections] = (0, _useControlled.default)({\n    controlled: selectedSectionsProp,\n    default: null,\n    name: 'useField',\n    state: 'selectedSections'\n  });\n  const setSelectedSections = newSelectedSections => {\n    innerSetSelectedSections(newSelectedSections);\n    onSelectedSectionsChange?.(newSelectedSections);\n  };\n  const parsedSelectedSections = React.useMemo(() => (0, _useField.parseSelectedSections)(selectedSections, state.sections), [selectedSections, state.sections]);\n  const activeSectionIndex = parsedSelectedSections === 'all' ? 0 : parsedSelectedSections;\n  const sectionOrder = React.useMemo(() => (0, _useField.getSectionOrder)(state.sections, isRtl && !enableAccessibleFieldDOMStructure), [state.sections, isRtl, enableAccessibleFieldDOMStructure]);\n  const areAllSectionsEmpty = React.useMemo(() => state.sections.every(section => section.value === ''), [state.sections]);\n  const publishValue = newValue => {\n    const context = {\n      validationError: validator({\n        adapter,\n        value: newValue,\n        timezone,\n        props: internalPropsWithDefaults\n      })\n    };\n    handleValueChange(newValue, context);\n  };\n  const setSectionValue = (sectionIndex, newSectionValue) => {\n    const newSections = [...state.sections];\n    newSections[sectionIndex] = (0, _extends2.default)({}, newSections[sectionIndex], {\n      value: newSectionValue,\n      modified: true\n    });\n    return newSections;\n  };\n  const sectionToUpdateOnNextInvalidDateRef = React.useRef(null);\n  const updateSectionValueOnNextInvalidDateTimeout = (0, _useTimeout.default)();\n  const setSectionUpdateToApplyOnNextInvalidDate = newSectionValue => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    sectionToUpdateOnNextInvalidDateRef.current = {\n      sectionIndex: activeSectionIndex,\n      value: newSectionValue\n    };\n    updateSectionValueOnNextInvalidDateTimeout.start(0, () => {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    });\n  };\n  const clearValue = () => {\n    if (valueManager.areValuesEqual(adapter, value, valueManager.emptyValue)) {\n      setState(prevState => (0, _extends2.default)({}, prevState, {\n        sections: prevState.sections.map(section => (0, _extends2.default)({}, section, {\n          value: ''\n        })),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => (0, _extends2.default)({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(valueManager.emptyValue);\n    }\n  };\n  const clearActiveSection = () => {\n    if (activeSectionIndex == null) {\n      return;\n    }\n    const activeSection = state.sections[activeSectionIndex];\n    if (activeSection.value === '') {\n      return;\n    }\n    setSectionUpdateToApplyOnNextInvalidDate('');\n    if (fieldValueManager.getDateFromSection(value, activeSection) === null) {\n      setState(prevState => (0, _extends2.default)({}, prevState, {\n        sections: setSectionValue(activeSectionIndex, ''),\n        tempValueStrAndroid: null,\n        characterQuery: null\n      }));\n    } else {\n      setState(prevState => (0, _extends2.default)({}, prevState, {\n        characterQuery: null\n      }));\n      publishValue(fieldValueManager.updateDateInValue(value, activeSection, null));\n    }\n  };\n  const updateValueFromValueStr = valueStr => {\n    const parseDateStr = (dateStr, referenceDate) => {\n      const date = adapter.parse(dateStr, format);\n      if (!adapter.isValid(date)) {\n        return null;\n      }\n      const sections = (0, _buildSectionsFromFormat.buildSectionsFromFormat)({\n        adapter,\n        localeText: translations,\n        localizedDigits,\n        format,\n        date,\n        formatDensity,\n        shouldRespectLeadingZeros,\n        enableAccessibleFieldDOMStructure,\n        isRtl\n      });\n      return (0, _useField.mergeDateIntoReferenceDate)(adapter, date, sections, referenceDate, false);\n    };\n    const newValue = fieldValueManager.parseValueStr(valueStr, state.referenceValue, parseDateStr);\n    publishValue(newValue);\n  };\n  const cleanActiveDateSectionsIfValueNullTimeout = (0, _useTimeout.default)();\n  const updateSectionValue = ({\n    section,\n    newSectionValue,\n    shouldGoToNextSection\n  }) => {\n    updateSectionValueOnNextInvalidDateTimeout.clear();\n    cleanActiveDateSectionsIfValueNullTimeout.clear();\n    const activeDate = fieldValueManager.getDateFromSection(value, section);\n\n    /**\n     * Decide which section should be focused\n     */\n    if (shouldGoToNextSection && activeSectionIndex < state.sections.length - 1) {\n      setSelectedSections(activeSectionIndex + 1);\n    }\n\n    /**\n     * Try to build a valid date from the new section value\n     */\n    const newSections = setSectionValue(activeSectionIndex, newSectionValue);\n    const newActiveDateSections = fieldValueManager.getDateSectionsFromValue(newSections, section);\n    const newActiveDate = (0, _useField.getDateFromDateSections)(adapter, newActiveDateSections, localizedDigits);\n\n    /**\n     * If the new date is valid,\n     * Then we merge the value of the modified sections into the reference date.\n     * This makes sure that we don't lose some information of the initial date (like the time on a date field).\n     */\n    if (adapter.isValid(newActiveDate)) {\n      const mergedDate = (0, _useField.mergeDateIntoReferenceDate)(adapter, newActiveDate, newActiveDateSections, fieldValueManager.getDateFromSection(state.referenceValue, section), true);\n      if (activeDate == null) {\n        cleanActiveDateSectionsIfValueNullTimeout.start(0, () => {\n          if (valueRef.current === value) {\n            setState(prevState => (0, _extends2.default)({}, prevState, {\n              sections: fieldValueManager.clearDateSections(state.sections, section),\n              tempValueStrAndroid: null\n            }));\n          }\n        });\n      }\n      return publishValue(fieldValueManager.updateDateInValue(value, section, mergedDate));\n    }\n\n    /**\n     * If all the sections are filled but the date is invalid and the previous date is valid or null,\n     * Then we publish an invalid date.\n     */\n    if (newActiveDateSections.every(sectionBis => sectionBis.value !== '') && (activeDate == null || adapter.isValid(activeDate))) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, newActiveDate));\n    }\n\n    /**\n     * If the previous date is not null,\n     * Then we publish the date as `null`.\n     */\n    if (activeDate != null) {\n      setSectionUpdateToApplyOnNextInvalidDate(newSectionValue);\n      return publishValue(fieldValueManager.updateDateInValue(value, section, null));\n    }\n\n    /**\n     * If the previous date is already null,\n     * Then we don't publish the date and we update the sections.\n     */\n    return setState(prevState => (0, _extends2.default)({}, prevState, {\n      sections: newSections,\n      tempValueStrAndroid: null\n    }));\n  };\n  const setTempAndroidValueStr = tempValueStrAndroid => setState(prevState => (0, _extends2.default)({}, prevState, {\n    tempValueStrAndroid\n  }));\n  const setCharacterQuery = (0, _useEventCallback.default)(newCharacterQuery => {\n    setState(prevState => (0, _extends2.default)({}, prevState, {\n      characterQuery: newCharacterQuery\n    }));\n  });\n\n  // If `prop.value` changes, we update the state to reflect the new value\n  if (value !== state.lastExternalValue) {\n    let sections;\n    if (sectionToUpdateOnNextInvalidDateRef.current != null && !adapter.isValid(fieldValueManager.getDateFromSection(value, state.sections[sectionToUpdateOnNextInvalidDateRef.current.sectionIndex]))) {\n      sections = setSectionValue(sectionToUpdateOnNextInvalidDateRef.current.sectionIndex, sectionToUpdateOnNextInvalidDateRef.current.value);\n    } else {\n      sections = getSectionsFromValue(value);\n    }\n    setState(prevState => (0, _extends2.default)({}, prevState, {\n      lastExternalValue: value,\n      sections,\n      sectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      referenceValue: fieldValueManager.updateReferenceValue(adapter, value, prevState.referenceValue),\n      tempValueStrAndroid: null\n    }));\n  }\n  if (isRtl !== state.lastSectionsDependencies.isRtl || format !== state.lastSectionsDependencies.format || adapter.locale !== state.lastSectionsDependencies.locale) {\n    const sections = getSectionsFromValue(value);\n    (0, _useField.validateSections)(sections, valueType);\n    setState(prevState => (0, _extends2.default)({}, prevState, {\n      lastSectionsDependencies: {\n        format,\n        isRtl,\n        locale: adapter.locale\n      },\n      sections,\n      tempValueStrAndroid: null,\n      characterQuery: null\n    }));\n  }\n  if (state.characterQuery != null && !error && activeSectionIndex == null) {\n    setCharacterQuery(null);\n  }\n  if (state.characterQuery != null && state.sections[state.characterQuery.sectionIndex]?.type !== state.characterQuery.sectionType) {\n    setCharacterQuery(null);\n  }\n  React.useEffect(() => {\n    if (sectionToUpdateOnNextInvalidDateRef.current != null) {\n      sectionToUpdateOnNextInvalidDateRef.current = null;\n    }\n  });\n  const cleanCharacterQueryTimeout = (0, _useTimeout.default)();\n  React.useEffect(() => {\n    if (state.characterQuery != null) {\n      cleanCharacterQueryTimeout.start(QUERY_LIFE_DURATION_MS, () => setCharacterQuery(null));\n    }\n    return () => {};\n  }, [state.characterQuery, setCharacterQuery, cleanCharacterQueryTimeout]);\n\n  // If `tempValueStrAndroid` is still defined for some section when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && activeSectionIndex != null) {\n      clearActiveSection();\n    }\n  }, [state.sections]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  return {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    sectionsValueBoundaries,\n    state,\n    timezone,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  };\n};\nexports.useFieldState = useFieldState;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAIC,SAAS,GAAGN,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIS,KAAK,GAAGV,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIU,cAAc,GAAGR,sBAAsB,CAACF,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF,IAAIW,WAAW,GAAGT,sBAAsB,CAACF,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIY,iBAAiB,GAAGV,sBAAsB,CAACF,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIa,YAAY,GAAGb,OAAO,CAAC,yBAAyB,CAAC;AACrD,IAAIc,MAAM,GAAGd,OAAO,CAAC,gBAAgB,CAAC;AACtC,IAAIe,SAAS,GAAGf,OAAO,CAAC,kBAAkB,CAAC;AAC3C,IAAIgB,wBAAwB,GAAGhB,OAAO,CAAC,2BAA2B,CAAC;AACnE,IAAIiB,WAAW,GAAGjB,OAAO,CAAC,qBAAqB,CAAC;AAChD,IAAIkB,mBAAmB,GAAGlB,OAAO,CAAC,uBAAuB,CAAC;AAC1D,IAAImB,wBAAwB,GAAGnB,OAAO,CAAC,qCAAqC,CAAC;AAC7E,MAAMoB,sBAAsB,GAAG,IAAI;AACnC,MAAMb,aAAa,GAAGc,UAAU,IAAI;EAClC,MAAMC,OAAO,GAAG,CAAC,CAAC,EAAER,MAAM,CAACS,gBAAgB,EAAE,CAAC;EAC9C,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAEV,MAAM,CAACW,qBAAqB,EAAE,CAAC;EACxD,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEb,YAAY,CAACc,MAAM,EAAE,CAAC;EACxC,MAAM;IACJC,OAAO,EAAE;MACPC,SAAS;MACTC,SAAS;MACTC,qBAAqB,EAAEC,YAAY;MACnCC,0BAA0B,EAAEC;IAC9B,CAAC;IACDC,yBAAyB;IACzBA,yBAAyB,EAAE;MACzB7B,KAAK,EAAE8B,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,QAAQ;MACRC,MAAM;MACNC,aAAa,GAAG,OAAO;MACvBC,gBAAgB,EAAEC,oBAAoB;MACtCC,wBAAwB;MACxBC,yBAAyB,GAAG,KAAK;MACjCC,QAAQ,EAAEC,YAAY;MACtBC,iCAAiC,GAAG;IACtC,CAAC;IACDC,cAAc,EAAE;MACdC,KAAK,EAAEC;IACT;EACF,CAAC,GAAG/B,UAAU;EACd,MAAM;IACJf,KAAK;IACL+C,iBAAiB;IACjBN;EACF,CAAC,GAAG,CAAC,CAAC,EAAE7B,mBAAmB,CAACoC,kBAAkB,EAAE;IAC9CC,IAAI,EAAE,mBAAmB;IACzBR,QAAQ,EAAEC,YAAY;IACtB1C,KAAK,EAAE8B,SAAS;IAChBC,YAAY;IACZC,aAAa,EAAEC,iBAAiB;IAChCC,QAAQ;IACRR;EACF,CAAC,CAAC;EACF,MAAMwB,QAAQ,GAAG/C,KAAK,CAACgD,MAAM,CAACnD,KAAK,CAAC;EACpCG,KAAK,CAACiD,SAAS,CAAC,MAAM;IACpBF,QAAQ,CAACG,OAAO,GAAGrD,KAAK;EAC1B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,MAAM;IACJsD;EACF,CAAC,GAAG,CAAC,CAAC,EAAE3C,WAAW,CAAC4C,aAAa,EAAE;IACjCC,KAAK,EAAE3B,yBAAyB;IAChCN,SAAS;IACTkB,QAAQ;IACRzC,KAAK;IACLyD,OAAO,EAAE5B,yBAAyB,CAAC4B;EACrC,CAAC,CAAC;EACF,MAAMZ,KAAK,GAAG1C,KAAK,CAACuD,OAAO,CAAC,MAAM;IAChC;IACA;IACA,IAAIZ,SAAS,KAAKa,SAAS,EAAE;MAC3B,OAAOb,SAAS;IAClB;IACA,OAAOQ,kBAAkB;EAC3B,CAAC,EAAE,CAACA,kBAAkB,EAAER,SAAS,CAAC,CAAC;EACnC,MAAMc,eAAe,GAAGzD,KAAK,CAACuD,OAAO,CAAC,MAAM,CAAC,CAAC,EAAEjD,SAAS,CAACoD,kBAAkB,EAAE7C,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAClG,MAAM8C,uBAAuB,GAAG3D,KAAK,CAACuD,OAAO,CAAC,MAAM,CAAC,CAAC,EAAEjD,SAAS,CAACsD,qBAAqB,EAAE/C,OAAO,EAAE4C,eAAe,EAAEnB,QAAQ,CAAC,EAAE,CAACzB,OAAO,EAAE4C,eAAe,EAAEnB,QAAQ,CAAC,CAAC;EACnK,MAAMuB,oBAAoB,GAAG7D,KAAK,CAAC8D,WAAW,CAACC,cAAc,IAAItC,iBAAiB,CAACoC,oBAAoB,CAACE,cAAc,EAAEC,IAAI,IAAI,CAAC,CAAC,EAAEzD,wBAAwB,CAAC0D,uBAAuB,EAAE;IACpLpD,OAAO;IACPqD,UAAU,EAAEnD,YAAY;IACxB0C,eAAe;IACfzB,MAAM;IACNgC,IAAI;IACJ/B,aAAa;IACbI,yBAAyB;IACzBG,iCAAiC;IACjCvB;EACF,CAAC,CAAC,CAAC,EAAE,CAACQ,iBAAiB,EAAEO,MAAM,EAAEjB,YAAY,EAAE0C,eAAe,EAAExC,KAAK,EAAEoB,yBAAyB,EAAExB,OAAO,EAAEoB,aAAa,EAAEO,iCAAiC,CAAC,CAAC;EAC7J,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,KAAK,CAACqE,QAAQ,CAAC,MAAM;IAC7C,MAAMC,QAAQ,GAAGT,oBAAoB,CAAChE,KAAK,CAAC;IAC5C,CAAC,CAAC,EAAES,SAAS,CAACiE,gBAAgB,EAAED,QAAQ,EAAEjD,SAAS,CAAC;IACpD,MAAMmD,yBAAyB,GAAG;MAChCF,QAAQ;MACRG,iBAAiB,EAAE5E,KAAK;MACxB6E,wBAAwB,EAAE;QACxB1C,MAAM;QACNf,KAAK;QACL0D,MAAM,EAAE9D,OAAO,CAAC8D;MAClB,CAAC;MACDC,mBAAmB,EAAE,IAAI;MACzBC,cAAc,EAAE;IAClB,CAAC;IACD,MAAMC,WAAW,GAAG,CAAC,CAAC,EAAEpE,wBAAwB,CAACqE,yBAAyB,EAAET,QAAQ,CAAC;IACrF,MAAMU,cAAc,GAAGzD,YAAY,CAAC0D,wBAAwB,CAAC;MAC3DpD,aAAa,EAAEC,iBAAiB;MAChCjC,KAAK;MACLgB,OAAO;MACPwC,KAAK,EAAE3B,yBAAyB;MAChCoD,WAAW;MACXxC;IACF,CAAC,CAAC;IACF,OAAO,CAAC,CAAC,EAAEvC,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEgF,yBAAyB,EAAE;MAC3DQ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM,CAAC9C,gBAAgB,EAAEgD,wBAAwB,CAAC,GAAG,CAAC,CAAC,EAAEjF,cAAc,CAACT,OAAO,EAAE;IAC/E2F,UAAU,EAAEhD,oBAAoB;IAChC3C,OAAO,EAAE,IAAI;IACbsD,IAAI,EAAE,UAAU;IAChBqB,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMiB,mBAAmB,GAAGC,mBAAmB,IAAI;IACjDH,wBAAwB,CAACG,mBAAmB,CAAC;IAC7CjD,wBAAwB,GAAGiD,mBAAmB,CAAC;EACjD,CAAC;EACD,MAAMC,sBAAsB,GAAGtF,KAAK,CAACuD,OAAO,CAAC,MAAM,CAAC,CAAC,EAAEjD,SAAS,CAACiF,qBAAqB,EAAErD,gBAAgB,EAAEiC,KAAK,CAACG,QAAQ,CAAC,EAAE,CAACpC,gBAAgB,EAAEiC,KAAK,CAACG,QAAQ,CAAC,CAAC;EAC9J,MAAMkB,kBAAkB,GAAGF,sBAAsB,KAAK,KAAK,GAAG,CAAC,GAAGA,sBAAsB;EACxF,MAAMG,YAAY,GAAGzF,KAAK,CAACuD,OAAO,CAAC,MAAM,CAAC,CAAC,EAAEjD,SAAS,CAACoF,eAAe,EAAEvB,KAAK,CAACG,QAAQ,EAAErD,KAAK,IAAI,CAACuB,iCAAiC,CAAC,EAAE,CAAC2B,KAAK,CAACG,QAAQ,EAAErD,KAAK,EAAEuB,iCAAiC,CAAC,CAAC;EACjM,MAAMmD,mBAAmB,GAAG3F,KAAK,CAACuD,OAAO,CAAC,MAAMY,KAAK,CAACG,QAAQ,CAACsB,KAAK,CAACC,OAAO,IAAIA,OAAO,CAAChG,KAAK,KAAK,EAAE,CAAC,EAAE,CAACsE,KAAK,CAACG,QAAQ,CAAC,CAAC;EACxH,MAAMwB,YAAY,GAAGC,QAAQ,IAAI;IAC/B,MAAMC,OAAO,GAAG;MACdC,eAAe,EAAE7E,SAAS,CAAC;QACzBP,OAAO;QACPhB,KAAK,EAAEkG,QAAQ;QACfzD,QAAQ;QACRe,KAAK,EAAE3B;MACT,CAAC;IACH,CAAC;IACDkB,iBAAiB,CAACmD,QAAQ,EAAEC,OAAO,CAAC;EACtC,CAAC;EACD,MAAME,eAAe,GAAGA,CAACC,YAAY,EAAEC,eAAe,KAAK;IACzD,MAAMC,WAAW,GAAG,CAAC,GAAGlC,KAAK,CAACG,QAAQ,CAAC;IACvC+B,WAAW,CAACF,YAAY,CAAC,GAAG,CAAC,CAAC,EAAEpG,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAE6G,WAAW,CAACF,YAAY,CAAC,EAAE;MAChFtG,KAAK,EAAEuG,eAAe;MACtBE,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,OAAOD,WAAW;EACpB,CAAC;EACD,MAAME,mCAAmC,GAAGvG,KAAK,CAACgD,MAAM,CAAC,IAAI,CAAC;EAC9D,MAAMwD,0CAA0C,GAAG,CAAC,CAAC,EAAEtG,WAAW,CAACV,OAAO,EAAE,CAAC;EAC7E,MAAMiH,wCAAwC,GAAGL,eAAe,IAAI;IAClE,IAAIZ,kBAAkB,IAAI,IAAI,EAAE;MAC9B;IACF;IACAe,mCAAmC,CAACrD,OAAO,GAAG;MAC5CiD,YAAY,EAAEX,kBAAkB;MAChC3F,KAAK,EAAEuG;IACT,CAAC;IACDI,0CAA0C,CAACE,KAAK,CAAC,CAAC,EAAE,MAAM;MACxDH,mCAAmC,CAACrD,OAAO,GAAG,IAAI;IACpD,CAAC,CAAC;EACJ,CAAC;EACD,MAAMyD,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIpF,YAAY,CAACqF,cAAc,CAAC/F,OAAO,EAAEhB,KAAK,EAAE0B,YAAY,CAACsF,UAAU,CAAC,EAAE;MACxEzC,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;QAC1DxC,QAAQ,EAAEwC,SAAS,CAACxC,QAAQ,CAACyC,GAAG,CAAClB,OAAO,IAAI,CAAC,CAAC,EAAE9F,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEqG,OAAO,EAAE;UAC9EhG,KAAK,EAAE;QACT,CAAC,CAAC,CAAC;QACH+E,mBAAmB,EAAE,IAAI;QACzBC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLT,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;QAC1DjC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;MACHiB,YAAY,CAACvE,YAAY,CAACsF,UAAU,CAAC;IACvC;EACF,CAAC;EACD,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIxB,kBAAkB,IAAI,IAAI,EAAE;MAC9B;IACF;IACA,MAAMyB,aAAa,GAAG9C,KAAK,CAACG,QAAQ,CAACkB,kBAAkB,CAAC;IACxD,IAAIyB,aAAa,CAACpH,KAAK,KAAK,EAAE,EAAE;MAC9B;IACF;IACA4G,wCAAwC,CAAC,EAAE,CAAC;IAC5C,IAAIhF,iBAAiB,CAACyF,kBAAkB,CAACrH,KAAK,EAAEoH,aAAa,CAAC,KAAK,IAAI,EAAE;MACvE7C,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;QAC1DxC,QAAQ,EAAE4B,eAAe,CAACV,kBAAkB,EAAE,EAAE,CAAC;QACjDZ,mBAAmB,EAAE,IAAI;QACzBC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLT,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;QAC1DjC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;MACHiB,YAAY,CAACrE,iBAAiB,CAAC0F,iBAAiB,CAACtH,KAAK,EAAEoH,aAAa,EAAE,IAAI,CAAC,CAAC;IAC/E;EACF,CAAC;EACD,MAAMG,uBAAuB,GAAGC,QAAQ,IAAI;IAC1C,MAAMC,YAAY,GAAGA,CAACC,OAAO,EAAE1F,aAAa,KAAK;MAC/C,MAAMmC,IAAI,GAAGnD,OAAO,CAAC2G,KAAK,CAACD,OAAO,EAAEvF,MAAM,CAAC;MAC3C,IAAI,CAACnB,OAAO,CAAC4G,OAAO,CAACzD,IAAI,CAAC,EAAE;QAC1B,OAAO,IAAI;MACb;MACA,MAAMM,QAAQ,GAAG,CAAC,CAAC,EAAE/D,wBAAwB,CAAC0D,uBAAuB,EAAE;QACrEpD,OAAO;QACPqD,UAAU,EAAEnD,YAAY;QACxB0C,eAAe;QACfzB,MAAM;QACNgC,IAAI;QACJ/B,aAAa;QACbI,yBAAyB;QACzBG,iCAAiC;QACjCvB;MACF,CAAC,CAAC;MACF,OAAO,CAAC,CAAC,EAAEX,SAAS,CAACoH,0BAA0B,EAAE7G,OAAO,EAAEmD,IAAI,EAAEM,QAAQ,EAAEzC,aAAa,EAAE,KAAK,CAAC;IACjG,CAAC;IACD,MAAMkE,QAAQ,GAAGtE,iBAAiB,CAACkG,aAAa,CAACN,QAAQ,EAAElD,KAAK,CAACa,cAAc,EAAEsC,YAAY,CAAC;IAC9FxB,YAAY,CAACC,QAAQ,CAAC;EACxB,CAAC;EACD,MAAM6B,yCAAyC,GAAG,CAAC,CAAC,EAAE1H,WAAW,CAACV,OAAO,EAAE,CAAC;EAC5E,MAAMqI,kBAAkB,GAAGA,CAAC;IAC1BhC,OAAO;IACPO,eAAe;IACf0B;EACF,CAAC,KAAK;IACJtB,0CAA0C,CAACuB,KAAK,CAAC,CAAC;IAClDH,yCAAyC,CAACG,KAAK,CAAC,CAAC;IACjD,MAAMC,UAAU,GAAGvG,iBAAiB,CAACyF,kBAAkB,CAACrH,KAAK,EAAEgG,OAAO,CAAC;;IAEvE;AACJ;AACA;IACI,IAAIiC,qBAAqB,IAAItC,kBAAkB,GAAGrB,KAAK,CAACG,QAAQ,CAAC2D,MAAM,GAAG,CAAC,EAAE;MAC3E7C,mBAAmB,CAACI,kBAAkB,GAAG,CAAC,CAAC;IAC7C;;IAEA;AACJ;AACA;IACI,MAAMa,WAAW,GAAGH,eAAe,CAACV,kBAAkB,EAAEY,eAAe,CAAC;IACxE,MAAM8B,qBAAqB,GAAGzG,iBAAiB,CAAC0G,wBAAwB,CAAC9B,WAAW,EAAER,OAAO,CAAC;IAC9F,MAAMuC,aAAa,GAAG,CAAC,CAAC,EAAE9H,SAAS,CAAC+H,uBAAuB,EAAExH,OAAO,EAAEqH,qBAAqB,EAAEzE,eAAe,CAAC;;IAE7G;AACJ;AACA;AACA;AACA;IACI,IAAI5C,OAAO,CAAC4G,OAAO,CAACW,aAAa,CAAC,EAAE;MAClC,MAAME,UAAU,GAAG,CAAC,CAAC,EAAEhI,SAAS,CAACoH,0BAA0B,EAAE7G,OAAO,EAAEuH,aAAa,EAAEF,qBAAqB,EAAEzG,iBAAiB,CAACyF,kBAAkB,CAAC/C,KAAK,CAACa,cAAc,EAAEa,OAAO,CAAC,EAAE,IAAI,CAAC;MACtL,IAAImC,UAAU,IAAI,IAAI,EAAE;QACtBJ,yCAAyC,CAAClB,KAAK,CAAC,CAAC,EAAE,MAAM;UACvD,IAAI3D,QAAQ,CAACG,OAAO,KAAKrD,KAAK,EAAE;YAC9BuE,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;cAC1DxC,QAAQ,EAAE7C,iBAAiB,CAAC8G,iBAAiB,CAACpE,KAAK,CAACG,QAAQ,EAAEuB,OAAO,CAAC;cACtEjB,mBAAmB,EAAE;YACvB,CAAC,CAAC,CAAC;UACL;QACF,CAAC,CAAC;MACJ;MACA,OAAOkB,YAAY,CAACrE,iBAAiB,CAAC0F,iBAAiB,CAACtH,KAAK,EAAEgG,OAAO,EAAEyC,UAAU,CAAC,CAAC;IACtF;;IAEA;AACJ;AACA;AACA;IACI,IAAIJ,qBAAqB,CAACtC,KAAK,CAAC4C,UAAU,IAAIA,UAAU,CAAC3I,KAAK,KAAK,EAAE,CAAC,KAAKmI,UAAU,IAAI,IAAI,IAAInH,OAAO,CAAC4G,OAAO,CAACO,UAAU,CAAC,CAAC,EAAE;MAC7HvB,wCAAwC,CAACL,eAAe,CAAC;MACzD,OAAON,YAAY,CAACrE,iBAAiB,CAAC0F,iBAAiB,CAACtH,KAAK,EAAEgG,OAAO,EAAEuC,aAAa,CAAC,CAAC;IACzF;;IAEA;AACJ;AACA;AACA;IACI,IAAIJ,UAAU,IAAI,IAAI,EAAE;MACtBvB,wCAAwC,CAACL,eAAe,CAAC;MACzD,OAAON,YAAY,CAACrE,iBAAiB,CAAC0F,iBAAiB,CAACtH,KAAK,EAAEgG,OAAO,EAAE,IAAI,CAAC,CAAC;IAChF;;IAEA;AACJ;AACA;AACA;IACI,OAAOzB,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;MACjExC,QAAQ,EAAE+B,WAAW;MACrBzB,mBAAmB,EAAE;IACvB,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAM6D,sBAAsB,GAAG7D,mBAAmB,IAAIR,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;IAChHlC;EACF,CAAC,CAAC,CAAC;EACH,MAAM8D,iBAAiB,GAAG,CAAC,CAAC,EAAEvI,iBAAiB,CAACX,OAAO,EAAEmJ,iBAAiB,IAAI;IAC5EvE,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;MAC1DjC,cAAc,EAAE8D;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;;EAEF;EACA,IAAI9I,KAAK,KAAKsE,KAAK,CAACM,iBAAiB,EAAE;IACrC,IAAIH,QAAQ;IACZ,IAAIiC,mCAAmC,CAACrD,OAAO,IAAI,IAAI,IAAI,CAACrC,OAAO,CAAC4G,OAAO,CAAChG,iBAAiB,CAACyF,kBAAkB,CAACrH,KAAK,EAAEsE,KAAK,CAACG,QAAQ,CAACiC,mCAAmC,CAACrD,OAAO,CAACiD,YAAY,CAAC,CAAC,CAAC,EAAE;MAClM7B,QAAQ,GAAG4B,eAAe,CAACK,mCAAmC,CAACrD,OAAO,CAACiD,YAAY,EAAEI,mCAAmC,CAACrD,OAAO,CAACrD,KAAK,CAAC;IACzI,CAAC,MAAM;MACLyE,QAAQ,GAAGT,oBAAoB,CAAChE,KAAK,CAAC;IACxC;IACAuE,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;MAC1DrC,iBAAiB,EAAE5E,KAAK;MACxByE,QAAQ;MACRsE,oBAAoB,EAAE;QACpB5G,MAAM;QACNf,KAAK;QACL0D,MAAM,EAAE9D,OAAO,CAAC8D;MAClB,CAAC;MACDK,cAAc,EAAEvD,iBAAiB,CAACoH,oBAAoB,CAAChI,OAAO,EAAEhB,KAAK,EAAEiH,SAAS,CAAC9B,cAAc,CAAC;MAChGJ,mBAAmB,EAAE;IACvB,CAAC,CAAC,CAAC;EACL;EACA,IAAI3D,KAAK,KAAKkD,KAAK,CAACO,wBAAwB,CAACzD,KAAK,IAAIe,MAAM,KAAKmC,KAAK,CAACO,wBAAwB,CAAC1C,MAAM,IAAInB,OAAO,CAAC8D,MAAM,KAAKR,KAAK,CAACO,wBAAwB,CAACC,MAAM,EAAE;IAClK,MAAML,QAAQ,GAAGT,oBAAoB,CAAChE,KAAK,CAAC;IAC5C,CAAC,CAAC,EAAES,SAAS,CAACiE,gBAAgB,EAAED,QAAQ,EAAEjD,SAAS,CAAC;IACpD+C,QAAQ,CAAC0C,SAAS,IAAI,CAAC,CAAC,EAAE/G,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEsH,SAAS,EAAE;MAC1DpC,wBAAwB,EAAE;QACxB1C,MAAM;QACNf,KAAK;QACL0D,MAAM,EAAE9D,OAAO,CAAC8D;MAClB,CAAC;MACDL,QAAQ;MACRM,mBAAmB,EAAE,IAAI;MACzBC,cAAc,EAAE;IAClB,CAAC,CAAC,CAAC;EACL;EACA,IAAIV,KAAK,CAACU,cAAc,IAAI,IAAI,IAAI,CAACnC,KAAK,IAAI8C,kBAAkB,IAAI,IAAI,EAAE;IACxEkD,iBAAiB,CAAC,IAAI,CAAC;EACzB;EACA,IAAIvE,KAAK,CAACU,cAAc,IAAI,IAAI,IAAIV,KAAK,CAACG,QAAQ,CAACH,KAAK,CAACU,cAAc,CAACsB,YAAY,CAAC,EAAE2C,IAAI,KAAK3E,KAAK,CAACU,cAAc,CAACkE,WAAW,EAAE;IAChIL,iBAAiB,CAAC,IAAI,CAAC;EACzB;EACA1I,KAAK,CAACiD,SAAS,CAAC,MAAM;IACpB,IAAIsD,mCAAmC,CAACrD,OAAO,IAAI,IAAI,EAAE;MACvDqD,mCAAmC,CAACrD,OAAO,GAAG,IAAI;IACpD;EACF,CAAC,CAAC;EACF,MAAM8F,0BAA0B,GAAG,CAAC,CAAC,EAAE9I,WAAW,CAACV,OAAO,EAAE,CAAC;EAC7DQ,KAAK,CAACiD,SAAS,CAAC,MAAM;IACpB,IAAIkB,KAAK,CAACU,cAAc,IAAI,IAAI,EAAE;MAChCmE,0BAA0B,CAACtC,KAAK,CAAC/F,sBAAsB,EAAE,MAAM+H,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACzF;IACA,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,CAACvE,KAAK,CAACU,cAAc,EAAE6D,iBAAiB,EAAEM,0BAA0B,CAAC,CAAC;;EAEzE;EACA;EACA;EACA;EACAhJ,KAAK,CAACiD,SAAS,CAAC,MAAM;IACpB,IAAIkB,KAAK,CAACS,mBAAmB,IAAI,IAAI,IAAIY,kBAAkB,IAAI,IAAI,EAAE;MACnEwB,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAAC7C,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtB,OAAO;IACL;IACAkB,kBAAkB;IAClBG,mBAAmB;IACnBjD,KAAK;IACLe,eAAe;IACf6B,sBAAsB;IACtBG,YAAY;IACZ9B,uBAAuB;IACvBQ,KAAK;IACL7B,QAAQ;IACRzC,KAAK;IACL;IACA8G,UAAU;IACVK,kBAAkB;IAClB0B,iBAAiB;IACjBtD,mBAAmB;IACnBqD,sBAAsB;IACtBZ,kBAAkB;IAClBT,uBAAuB;IACvB;IACAvD;EACF,CAAC;AACH,CAAC;AACDjE,OAAO,CAACE,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}