{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useControlledValue = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _usePickerAdapter = require(\"../../hooks/usePickerAdapter\");\n/**\n * Hooks controlling the value while making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nconst useControlledValue = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const adapter = (0, _usePickerAdapter.usePickerAdapter)();\n  const [valueWithInputTimezone, setValue] = (0, _useControlled.default)({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(adapter, valueWithInputTimezone), [adapter, valueManager, valueWithInputTimezone]);\n  const setInputTimezone = (0, _useEventCallback.default)(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(adapter, inputTimezone, newValue);\n  });\n  const timezoneToRender = React.useMemo(() => {\n    if (timezoneProp) {\n      return timezoneProp;\n    }\n    if (inputTimezone) {\n      return inputTimezone;\n    }\n    if (referenceDate) {\n      return adapter.getTimezone(Array.isArray(referenceDate) ? referenceDate[0] : referenceDate);\n    }\n    return 'default';\n  }, [timezoneProp, inputTimezone, referenceDate, adapter]);\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(adapter, timezoneToRender, valueWithInputTimezone), [valueManager, adapter, timezoneToRender, valueWithInputTimezone]);\n  const handleValueChange = (0, _useEventCallback.default)((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    setValue(newValueWithInputTimezone);\n    onChangeProp?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\nexports.useControlledValue = useControlledValue;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "useControlledValue", "React", "_useEventCallback", "_useControlled", "_usePickerAdapter", "name", "timezone", "timezoneProp", "valueProp", "defaultValue", "referenceDate", "onChange", "onChangeProp", "valueManager", "adapter", "usePickerAdapter", "valueWithInputTimezone", "setValue", "state", "controlled", "emptyValue", "inputTimezone", "useMemo", "getTimezone", "setInputTimezone", "newValue", "setTimezone", "timezoneToRender", "Array", "isArray", "valueWithTimezoneToRender", "handleValueChange", "otherParams", "newValueWithInputTimezone"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/useControlledValue.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useControlledValue = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _usePickerAdapter = require(\"../../hooks/usePickerAdapter\");\n/**\n * Hooks controlling the value while making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nconst useControlledValue = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const adapter = (0, _usePickerAdapter.usePickerAdapter)();\n  const [valueWithInputTimezone, setValue] = (0, _useControlled.default)({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(adapter, valueWithInputTimezone), [adapter, valueManager, valueWithInputTimezone]);\n  const setInputTimezone = (0, _useEventCallback.default)(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(adapter, inputTimezone, newValue);\n  });\n  const timezoneToRender = React.useMemo(() => {\n    if (timezoneProp) {\n      return timezoneProp;\n    }\n    if (inputTimezone) {\n      return inputTimezone;\n    }\n    if (referenceDate) {\n      return adapter.getTimezone(Array.isArray(referenceDate) ? referenceDate[0] : referenceDate);\n    }\n    return 'default';\n  }, [timezoneProp, inputTimezone, referenceDate, adapter]);\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(adapter, timezoneToRender, valueWithInputTimezone), [valueManager, adapter, timezoneToRender, valueWithInputTimezone]);\n  const handleValueChange = (0, _useEventCallback.default)((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    setValue(newValueWithInputTimezone);\n    onChangeProp?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\nexports.useControlledValue = useControlledValue;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,kBAAkB,GAAG,KAAK,CAAC;AACnC,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,iBAAiB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIU,cAAc,GAAGX,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF,IAAIW,iBAAiB,GAAGX,OAAO,CAAC,8BAA8B,CAAC;AAC/D;AACA;AACA;AACA;AACA;AACA,MAAMO,kBAAkB,GAAGA,CAAC;EAC1BK,IAAI;EACJC,QAAQ,EAAEC,YAAY;EACtBR,KAAK,EAAES,SAAS;EAChBC,YAAY;EACZC,aAAa;EACbC,QAAQ,EAAEC,YAAY;EACtBC;AACF,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAG,CAAC,CAAC,EAAEV,iBAAiB,CAACW,gBAAgB,EAAE,CAAC;EACzD,MAAM,CAACC,sBAAsB,EAAEC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAEd,cAAc,CAACT,OAAO,EAAE;IACrEW,IAAI;IACJa,KAAK,EAAE,OAAO;IACdC,UAAU,EAAEX,SAAS;IACrBd,OAAO,EAAEe,YAAY,IAAII,YAAY,CAACO;EACxC,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGpB,KAAK,CAACqB,OAAO,CAAC,MAAMT,YAAY,CAACU,WAAW,CAACT,OAAO,EAAEE,sBAAsB,CAAC,EAAE,CAACF,OAAO,EAAED,YAAY,EAAEG,sBAAsB,CAAC,CAAC;EACrJ,MAAMQ,gBAAgB,GAAG,CAAC,CAAC,EAAEtB,iBAAiB,CAACR,OAAO,EAAE+B,QAAQ,IAAI;IAClE,IAAIJ,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOI,QAAQ;IACjB;IACA,OAAOZ,YAAY,CAACa,WAAW,CAACZ,OAAO,EAAEO,aAAa,EAAEI,QAAQ,CAAC;EACnE,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAG1B,KAAK,CAACqB,OAAO,CAAC,MAAM;IAC3C,IAAIf,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;IACA,IAAIc,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB;IACA,IAAIX,aAAa,EAAE;MACjB,OAAOI,OAAO,CAACS,WAAW,CAACK,KAAK,CAACC,OAAO,CAACnB,aAAa,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAGA,aAAa,CAAC;IAC7F;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAACH,YAAY,EAAEc,aAAa,EAAEX,aAAa,EAAEI,OAAO,CAAC,CAAC;EACzD,MAAMgB,yBAAyB,GAAG7B,KAAK,CAACqB,OAAO,CAAC,MAAMT,YAAY,CAACa,WAAW,CAACZ,OAAO,EAAEa,gBAAgB,EAAEX,sBAAsB,CAAC,EAAE,CAACH,YAAY,EAAEC,OAAO,EAAEa,gBAAgB,EAAEX,sBAAsB,CAAC,CAAC;EACrM,MAAMe,iBAAiB,GAAG,CAAC,CAAC,EAAE7B,iBAAiB,CAACR,OAAO,EAAE,CAAC+B,QAAQ,EAAE,GAAGO,WAAW,KAAK;IACrF,MAAMC,yBAAyB,GAAGT,gBAAgB,CAACC,QAAQ,CAAC;IAC5DR,QAAQ,CAACgB,yBAAyB,CAAC;IACnCrB,YAAY,GAAGqB,yBAAyB,EAAE,GAAGD,WAAW,CAAC;EAC3D,CAAC,CAAC;EACF,OAAO;IACLjC,KAAK,EAAE+B,yBAAyB;IAChCC,iBAAiB;IACjBzB,QAAQ,EAAEqB;EACZ,CAAC;AACH,CAAC;AACD7B,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}