{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldRootProps = useFieldRootProps;\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useTimeout = _interopRequireDefault(require(\"@mui/utils/useTimeout\"));\nvar _useFieldRootHandleKeyDown = require(\"./useFieldRootHandleKeyDown\");\nvar _utils = require(\"../../utils/utils\");\nvar _syncSelectionToDOM = require(\"./syncSelectionToDOM\");\n/**\n * Generate the props to pass to the root element of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the root element of the field.\n */\nfunction useFieldRootProps(parameters) {\n  const {\n    manager,\n    focused,\n    setFocused,\n    domGetters,\n    stateResponse,\n    applyCharacterEditing,\n    internalPropsWithDefaults,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      sectionOrder,\n      state,\n      // Methods to update the states\n      clearValue,\n      setCharacterQuery,\n      setSelectedSections,\n      updateValueFromValueStr\n    },\n    internalPropsWithDefaults: {\n      disabled = false,\n      readOnly = false\n    }\n  } = parameters;\n\n  // TODO: Inline onContainerKeyDown once the old DOM structure is removed\n  const handleKeyDown = (0, _useFieldRootHandleKeyDown.useFieldRootHandleKeyDown)({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const containerClickTimeout = (0, _useTimeout.default)();\n  const handleClick = (0, _useEventCallback.default)(event => {\n    if (disabled || !domGetters.isReady()) {\n      return;\n    }\n    setFocused(true);\n    if (parsedSelectedSections === 'all') {\n      containerClickTimeout.start(0, () => {\n        const cursorPosition = document.getSelection().getRangeAt(0).startOffset;\n        if (cursorPosition === 0) {\n          setSelectedSections(sectionOrder.startIndex);\n          return;\n        }\n        let sectionIndex = 0;\n        let cursorOnStartOfSection = 0;\n        while (cursorOnStartOfSection < cursorPosition && sectionIndex < state.sections.length) {\n          const section = state.sections[sectionIndex];\n          sectionIndex += 1;\n          cursorOnStartOfSection += `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`.length;\n        }\n        setSelectedSections(sectionIndex - 1);\n      });\n    } else if (!focused) {\n      setFocused(true);\n      setSelectedSections(sectionOrder.startIndex);\n    } else {\n      const hasClickedOnASection = domGetters.getRoot().contains(event.target);\n      if (!hasClickedOnASection) {\n        setSelectedSections(sectionOrder.startIndex);\n      }\n    }\n  });\n  const handleInput = (0, _useEventCallback.default)(event => {\n    if (!domGetters.isReady() || parsedSelectedSections !== 'all') {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    domGetters.getRoot().innerHTML = state.sections.map(section => `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`).join('');\n    (0, _syncSelectionToDOM.syncSelectionToDOM)({\n      focused,\n      domGetters,\n      stateResponse\n    });\n    if (keyPressed.length === 0 || keyPressed.charCodeAt(0) === 10) {\n      clearValue();\n      setSelectedSections('all');\n    } else if (keyPressed.length > 1) {\n      updateValueFromValueStr(keyPressed);\n    } else {\n      if (parsedSelectedSections === 'all') {\n        setSelectedSections(0);\n      }\n      applyCharacterEditing({\n        keyPressed,\n        sectionIndex: 0\n      });\n    }\n  });\n  const handlePaste = (0, _useEventCallback.default)(event => {\n    if (readOnly || parsedSelectedSections !== 'all') {\n      event.preventDefault();\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    event.preventDefault();\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleFocus = (0, _useEventCallback.default)(() => {\n    if (focused || disabled || !domGetters.isReady()) {\n      return;\n    }\n    const activeElement = (0, _utils.getActiveElement)(domGetters.getRoot());\n    setFocused(true);\n    const isFocusInsideASection = domGetters.getSectionIndexFromDOMElement(activeElement) != null;\n    if (!isFocusInsideASection) {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleBlur = (0, _useEventCallback.default)(() => {\n    setTimeout(() => {\n      if (!domGetters.isReady()) {\n        return;\n      }\n      const activeElement = (0, _utils.getActiveElement)(domGetters.getRoot());\n      const shouldBlur = !domGetters.getRoot().contains(activeElement);\n      if (shouldBlur) {\n        setFocused(false);\n        setSelectedSections(null);\n      }\n    });\n  });\n  return {\n    // Event handlers\n    onKeyDown: handleKeyDown,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    onClick: handleClick,\n    onPaste: handlePaste,\n    onInput: handleInput,\n    // Other\n    contentEditable: parsedSelectedSections === 'all',\n    tabIndex: parsedSelectedSections === 0 ? -1 : 0 // TODO: Try to set to undefined when there is a section selected.\n  };\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "useFieldRootProps", "_useEventCallback", "_useTimeout", "_useFieldRootHandleKeyDown", "_utils", "_syncSelectionToDOM", "parameters", "manager", "focused", "setFocused", "domGetters", "stateResponse", "applyCharacterEditing", "internalPropsWithDefaults", "parsedSelectedSections", "sectionOrder", "state", "clearValue", "setCharacterQuery", "setSelectedSections", "updateValueFromValueStr", "disabled", "readOnly", "handleKeyDown", "useFieldRootHandleKeyDown", "containerClickTimeout", "handleClick", "event", "isReady", "start", "cursorPosition", "document", "getSelection", "getRangeAt", "startOffset", "startIndex", "sectionIndex", "cursorOnStartOfSection", "sections", "length", "section", "startSeparator", "placeholder", "endSeparator", "hasClickedOnASection", "getRoot", "contains", "target", "handleInput", "keyPressed", "textContent", "innerHTML", "map", "join", "syncSelectionToDOM", "charCodeAt", "handlePaste", "preventDefault", "pastedValue", "clipboardData", "getData", "handleFocus", "activeElement", "getActiveElement", "isFocusInsideASection", "getSectionIndexFromDOMElement", "handleBlur", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "onKeyDown", "onBlur", "onFocus", "onClick", "onPaste", "onInput", "contentEditable", "tabIndex"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldRootProps.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldRootProps = useFieldRootProps;\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useTimeout = _interopRequireDefault(require(\"@mui/utils/useTimeout\"));\nvar _useFieldRootHandleKeyDown = require(\"./useFieldRootHandleKeyDown\");\nvar _utils = require(\"../../utils/utils\");\nvar _syncSelectionToDOM = require(\"./syncSelectionToDOM\");\n/**\n * Generate the props to pass to the root element of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the root element of the field.\n */\nfunction useFieldRootProps(parameters) {\n  const {\n    manager,\n    focused,\n    setFocused,\n    domGetters,\n    stateResponse,\n    applyCharacterEditing,\n    internalPropsWithDefaults,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      sectionOrder,\n      state,\n      // Methods to update the states\n      clearValue,\n      setCharacterQuery,\n      setSelectedSections,\n      updateValueFromValueStr\n    },\n    internalPropsWithDefaults: {\n      disabled = false,\n      readOnly = false\n    }\n  } = parameters;\n\n  // TODO: Inline onContainerKeyDown once the old DOM structure is removed\n  const handleKeyDown = (0, _useFieldRootHandleKeyDown.useFieldRootHandleKeyDown)({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const containerClickTimeout = (0, _useTimeout.default)();\n  const handleClick = (0, _useEventCallback.default)(event => {\n    if (disabled || !domGetters.isReady()) {\n      return;\n    }\n    setFocused(true);\n    if (parsedSelectedSections === 'all') {\n      containerClickTimeout.start(0, () => {\n        const cursorPosition = document.getSelection().getRangeAt(0).startOffset;\n        if (cursorPosition === 0) {\n          setSelectedSections(sectionOrder.startIndex);\n          return;\n        }\n        let sectionIndex = 0;\n        let cursorOnStartOfSection = 0;\n        while (cursorOnStartOfSection < cursorPosition && sectionIndex < state.sections.length) {\n          const section = state.sections[sectionIndex];\n          sectionIndex += 1;\n          cursorOnStartOfSection += `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`.length;\n        }\n        setSelectedSections(sectionIndex - 1);\n      });\n    } else if (!focused) {\n      setFocused(true);\n      setSelectedSections(sectionOrder.startIndex);\n    } else {\n      const hasClickedOnASection = domGetters.getRoot().contains(event.target);\n      if (!hasClickedOnASection) {\n        setSelectedSections(sectionOrder.startIndex);\n      }\n    }\n  });\n  const handleInput = (0, _useEventCallback.default)(event => {\n    if (!domGetters.isReady() || parsedSelectedSections !== 'all') {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    domGetters.getRoot().innerHTML = state.sections.map(section => `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`).join('');\n    (0, _syncSelectionToDOM.syncSelectionToDOM)({\n      focused,\n      domGetters,\n      stateResponse\n    });\n    if (keyPressed.length === 0 || keyPressed.charCodeAt(0) === 10) {\n      clearValue();\n      setSelectedSections('all');\n    } else if (keyPressed.length > 1) {\n      updateValueFromValueStr(keyPressed);\n    } else {\n      if (parsedSelectedSections === 'all') {\n        setSelectedSections(0);\n      }\n      applyCharacterEditing({\n        keyPressed,\n        sectionIndex: 0\n      });\n    }\n  });\n  const handlePaste = (0, _useEventCallback.default)(event => {\n    if (readOnly || parsedSelectedSections !== 'all') {\n      event.preventDefault();\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    event.preventDefault();\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleFocus = (0, _useEventCallback.default)(() => {\n    if (focused || disabled || !domGetters.isReady()) {\n      return;\n    }\n    const activeElement = (0, _utils.getActiveElement)(domGetters.getRoot());\n    setFocused(true);\n    const isFocusInsideASection = domGetters.getSectionIndexFromDOMElement(activeElement) != null;\n    if (!isFocusInsideASection) {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleBlur = (0, _useEventCallback.default)(() => {\n    setTimeout(() => {\n      if (!domGetters.isReady()) {\n        return;\n      }\n      const activeElement = (0, _utils.getActiveElement)(domGetters.getRoot());\n      const shouldBlur = !domGetters.getRoot().contains(activeElement);\n      if (shouldBlur) {\n        setFocused(false);\n        setSelectedSections(null);\n      }\n    });\n  });\n  return {\n    // Event handlers\n    onKeyDown: handleKeyDown,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    onClick: handleClick,\n    onPaste: handlePaste,\n    onInput: handleInput,\n    // Other\n    contentEditable: parsedSelectedSections === 'all',\n    tabIndex: parsedSelectedSections === 0 ? -1 : 0 // TODO: Try to set to undefined when there is a section selected.\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,iBAAiB,GAAGA,iBAAiB;AAC7C,IAAIC,iBAAiB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIS,0BAA0B,GAAGT,OAAO,CAAC,6BAA6B,CAAC;AACvE,IAAIU,MAAM,GAAGV,OAAO,CAAC,mBAAmB,CAAC;AACzC,IAAIW,mBAAmB,GAAGX,OAAO,CAAC,sBAAsB,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,iBAAiBA,CAACM,UAAU,EAAE;EACrC,MAAM;IACJC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,UAAU;IACVC,aAAa;IACbC,qBAAqB;IACrBC,yBAAyB;IACzBF,aAAa,EAAE;MACb;MACAG,sBAAsB;MACtBC,YAAY;MACZC,KAAK;MACL;MACAC,UAAU;MACVC,iBAAiB;MACjBC,mBAAmB;MACnBC;IACF,CAAC;IACDP,yBAAyB,EAAE;MACzBQ,QAAQ,GAAG,KAAK;MAChBC,QAAQ,GAAG;IACb;EACF,CAAC,GAAGhB,UAAU;;EAEd;EACA,MAAMiB,aAAa,GAAG,CAAC,CAAC,EAAEpB,0BAA0B,CAACqB,yBAAyB,EAAE;IAC9EjB,OAAO;IACPM,yBAAyB;IACzBF;EACF,CAAC,CAAC;EACF,MAAMc,qBAAqB,GAAG,CAAC,CAAC,EAAEvB,WAAW,CAACP,OAAO,EAAE,CAAC;EACxD,MAAM+B,WAAW,GAAG,CAAC,CAAC,EAAEzB,iBAAiB,CAACN,OAAO,EAAEgC,KAAK,IAAI;IAC1D,IAAIN,QAAQ,IAAI,CAACX,UAAU,CAACkB,OAAO,CAAC,CAAC,EAAE;MACrC;IACF;IACAnB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIK,sBAAsB,KAAK,KAAK,EAAE;MACpCW,qBAAqB,CAACI,KAAK,CAAC,CAAC,EAAE,MAAM;QACnC,MAAMC,cAAc,GAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,WAAW;QACxE,IAAIJ,cAAc,KAAK,CAAC,EAAE;UACxBX,mBAAmB,CAACJ,YAAY,CAACoB,UAAU,CAAC;UAC5C;QACF;QACA,IAAIC,YAAY,GAAG,CAAC;QACpB,IAAIC,sBAAsB,GAAG,CAAC;QAC9B,OAAOA,sBAAsB,GAAGP,cAAc,IAAIM,YAAY,GAAGpB,KAAK,CAACsB,QAAQ,CAACC,MAAM,EAAE;UACtF,MAAMC,OAAO,GAAGxB,KAAK,CAACsB,QAAQ,CAACF,YAAY,CAAC;UAC5CA,YAAY,IAAI,CAAC;UACjBC,sBAAsB,IAAI,GAAGG,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACzC,KAAK,IAAIyC,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,YAAY,EAAE,CAACJ,MAAM;QAC5H;QACApB,mBAAmB,CAACiB,YAAY,GAAG,CAAC,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAAC5B,OAAO,EAAE;MACnBC,UAAU,CAAC,IAAI,CAAC;MAChBU,mBAAmB,CAACJ,YAAY,CAACoB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMS,oBAAoB,GAAGlC,UAAU,CAACmC,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACnB,KAAK,CAACoB,MAAM,CAAC;MACxE,IAAI,CAACH,oBAAoB,EAAE;QACzBzB,mBAAmB,CAACJ,YAAY,CAACoB,UAAU,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;EACF,MAAMa,WAAW,GAAG,CAAC,CAAC,EAAE/C,iBAAiB,CAACN,OAAO,EAAEgC,KAAK,IAAI;IAC1D,IAAI,CAACjB,UAAU,CAACkB,OAAO,CAAC,CAAC,IAAId,sBAAsB,KAAK,KAAK,EAAE;MAC7D;IACF;IACA,MAAMiC,MAAM,GAAGpB,KAAK,CAACoB,MAAM;IAC3B,MAAME,UAAU,GAAGF,MAAM,CAACG,WAAW,IAAI,EAAE;IAC3CxC,UAAU,CAACmC,OAAO,CAAC,CAAC,CAACM,SAAS,GAAGnC,KAAK,CAACsB,QAAQ,CAACc,GAAG,CAACZ,OAAO,IAAI,GAAGA,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACzC,KAAK,IAAIyC,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,YAAY,EAAE,CAAC,CAACU,IAAI,CAAC,EAAE,CAAC;IAClK,CAAC,CAAC,EAAEhD,mBAAmB,CAACiD,kBAAkB,EAAE;MAC1C9C,OAAO;MACPE,UAAU;MACVC;IACF,CAAC,CAAC;IACF,IAAIsC,UAAU,CAACV,MAAM,KAAK,CAAC,IAAIU,UAAU,CAACM,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MAC9DtC,UAAU,CAAC,CAAC;MACZE,mBAAmB,CAAC,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAI8B,UAAU,CAACV,MAAM,GAAG,CAAC,EAAE;MAChCnB,uBAAuB,CAAC6B,UAAU,CAAC;IACrC,CAAC,MAAM;MACL,IAAInC,sBAAsB,KAAK,KAAK,EAAE;QACpCK,mBAAmB,CAAC,CAAC,CAAC;MACxB;MACAP,qBAAqB,CAAC;QACpBqC,UAAU;QACVb,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAMoB,WAAW,GAAG,CAAC,CAAC,EAAEvD,iBAAiB,CAACN,OAAO,EAAEgC,KAAK,IAAI;IAC1D,IAAIL,QAAQ,IAAIR,sBAAsB,KAAK,KAAK,EAAE;MAChDa,KAAK,CAAC8B,cAAc,CAAC,CAAC;MACtB;IACF;IACA,MAAMC,WAAW,GAAG/B,KAAK,CAACgC,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvDjC,KAAK,CAAC8B,cAAc,CAAC,CAAC;IACtBvC,iBAAiB,CAAC,IAAI,CAAC;IACvBE,uBAAuB,CAACsC,WAAW,CAAC;EACtC,CAAC,CAAC;EACF,MAAMG,WAAW,GAAG,CAAC,CAAC,EAAE5D,iBAAiB,CAACN,OAAO,EAAE,MAAM;IACvD,IAAIa,OAAO,IAAIa,QAAQ,IAAI,CAACX,UAAU,CAACkB,OAAO,CAAC,CAAC,EAAE;MAChD;IACF;IACA,MAAMkC,aAAa,GAAG,CAAC,CAAC,EAAE1D,MAAM,CAAC2D,gBAAgB,EAAErD,UAAU,CAACmC,OAAO,CAAC,CAAC,CAAC;IACxEpC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMuD,qBAAqB,GAAGtD,UAAU,CAACuD,6BAA6B,CAACH,aAAa,CAAC,IAAI,IAAI;IAC7F,IAAI,CAACE,qBAAqB,EAAE;MAC1B7C,mBAAmB,CAACJ,YAAY,CAACoB,UAAU,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,MAAM+B,UAAU,GAAG,CAAC,CAAC,EAAEjE,iBAAiB,CAACN,OAAO,EAAE,MAAM;IACtDwE,UAAU,CAAC,MAAM;MACf,IAAI,CAACzD,UAAU,CAACkB,OAAO,CAAC,CAAC,EAAE;QACzB;MACF;MACA,MAAMkC,aAAa,GAAG,CAAC,CAAC,EAAE1D,MAAM,CAAC2D,gBAAgB,EAAErD,UAAU,CAACmC,OAAO,CAAC,CAAC,CAAC;MACxE,MAAMuB,UAAU,GAAG,CAAC1D,UAAU,CAACmC,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACgB,aAAa,CAAC;MAChE,IAAIM,UAAU,EAAE;QACd3D,UAAU,CAAC,KAAK,CAAC;QACjBU,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO;IACL;IACAkD,SAAS,EAAE9C,aAAa;IACxB+C,MAAM,EAAEJ,UAAU;IAClBK,OAAO,EAAEV,WAAW;IACpBW,OAAO,EAAE9C,WAAW;IACpB+C,OAAO,EAAEjB,WAAW;IACpBkB,OAAO,EAAE1B,WAAW;IACpB;IACA2B,eAAe,EAAE7D,sBAAsB,KAAK,KAAK;IACjD8D,QAAQ,EAAE9D,sBAAsB,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAClD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}