{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Expenses\\\\ExpensesList.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Tooltip, TablePagination } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Receipt as ReceiptIcon } from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { styled } from '@mui/material/styles';\n\n// Styled components for better visual hierarchy\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledTableCell = styled(TableCell)(({\n  theme\n}) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '12px 16px',\n  '&:first-of-type': {\n    paddingLeft: 24\n  },\n  '&:last-child': {\n    paddingRight: 24\n  }\n}));\nconst StyledTableRow = styled(TableRow)(({\n  theme\n}) => ({\n  '&:nth-of-type(even)': {\n    backgroundColor: theme.palette.action.hover\n  },\n  '&:hover': {\n    backgroundColor: theme.palette.action.selected\n  },\n  '&:last-child td': {\n    borderBottom: 0\n  }\n}));\nconst PageHeader = styled(Box)(({\n  theme\n}) => ({\n  background: theme.palette.background.paper,\n  padding: theme.spacing(3, 4),\n  borderRadius: theme.shape.borderRadius,\n  boxShadow: theme.shadows[1],\n  marginBottom: theme.spacing(3),\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center'\n}));\nconst PrimaryButton = styled(Button)(({\n  theme\n}) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-1px)',\n    boxShadow: theme.shadows[2]\n  },\n  transition: 'all 0.2s ease'\n}));\nconst ExpenseDialog = ({\n  open,\n  onClose,\n  expense,\n  onSave,\n  vehicles\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: ''\n  });\n  const expenseTypes = ['Mantenimiento', 'Reparación', 'Seguro', 'ITV', 'Impuestos', 'Neumáticos', 'Aceite', 'Filtros', 'Frenos', 'Batería', 'Otros'];\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0] // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: ''\n      });\n    }\n  }, [expense, vehicles, open]);\n  const handleChange = field => event => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value\n    });\n  };\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && formData.coste && formData.descripcion;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: expense ? 'Editar Gasto' : 'Nuevo Gasto'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: 2,\n        pt: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Veh\\xEDculo\",\n          select: true,\n          value: formData.vehiculo_id,\n          onChange: handleChange('vehiculo_id'),\n          fullWidth: true,\n          required: true,\n          children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: vehicle.id,\n            children: vehicle.nombre\n          }, vehicle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Fecha\",\n            type: \"date\",\n            value: formData.fecha,\n            onChange: handleChange('fecha'),\n            fullWidth: true,\n            required: true,\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Kilometraje actual\",\n            type: \"number\",\n            value: formData.kilometros_actuales,\n            onChange: handleChange('kilometros_actuales'),\n            fullWidth: true,\n            inputProps: {\n              min: 0\n            },\n            helperText: \"Opcional\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Tipo de gasto\",\n            select: true,\n            value: formData.tipo_gasto,\n            onChange: handleChange('tipo_gasto'),\n            fullWidth: true,\n            required: true,\n            children: expenseTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: type,\n              children: type\n            }, type, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Coste (\\u20AC)\",\n            type: \"number\",\n            value: formData.coste,\n            onChange: handleChange('coste'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Descripci\\xF3n\",\n          value: formData.descripcion,\n          onChange: handleChange('descripcion'),\n          fullWidth: true,\n          required: true,\n          multiline: true,\n          rows: 2,\n          placeholder: \"Describe el gasto realizado...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Proveedor/Taller\",\n          value: formData.proveedor,\n          onChange: handleChange('proveedor'),\n          fullWidth: true,\n          placeholder: \"Nombre del taller, tienda, etc.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: !isValid,\n        children: expense ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpenseDialog, \"hAHDk6I+hYE0GG43vBPHqnsZ1HE=\");\n_c = ExpenseDialog;\nconst ExpensesList = () => {\n  _s2();\n  const {\n    vehicles,\n    expenses,\n    loadExpenses,\n    addExpense\n  } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    setDialogOpen(true);\n  };\n  const handleEditExpense = expense => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n  const handleDeleteExpense = expense => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n  const handleSaveExpense = async expenseData => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\n      } else {\n        await addExpense(expenseData);\n        // Recargar la lista\n        loadExpenses(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving expense:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n  const getExpenseTypeColor = type => {\n    const colors = {\n      'Mantenimiento': 'primary',\n      'Reparación': 'error',\n      'Seguro': 'info',\n      'ITV': 'warning',\n      'Impuestos': 'secondary',\n      'Neumáticos': 'success'\n    };\n    return colors[type] || 'default';\n  };\n\n  // Calcular estadísticas rápidas\n  const totalExpenses = expenses.length;\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\n\n  // Agrupar por tipo\n  const expensesByType = expenses.reduce((acc, expense) => {\n    const type = expense.tipo_gasto || 'Otros';\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\n    return acc;\n  }, {});\n  const topExpenseType = Object.entries(expensesByType).sort(([, a], [, b]) => b - a)[0];\n\n  // Paginación\n  const paginatedExpenses = expenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        fontWeight: \"bold\",\n        children: \"Gastos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddExpense,\n        disabled: vehicles.length === 0,\n        children: \"Nuevo Gasto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary\",\n            children: totalExpenses\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Total Gastos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"error.main\",\n            children: formatCurrency(totalCost)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Coste Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"warning.main\",\n            children: formatCurrency(avgExpense)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Gasto Promedio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"info.main\",\n            children: topExpenseType ? topExpenseType[0] : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Tipo M\\xE1s Frecuente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), expenses.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          py: 6,\n          children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n            sx: {\n              fontSize: 64,\n              color: 'text.secondary',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"textSecondary\",\n            gutterBottom: true,\n            children: \"No hay gastos registrados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            mb: 3,\n            children: \"Comienza registrando gastos de mantenimiento, reparaciones, seguros, etc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 28\n            }, this),\n            onClick: handleAddExpense,\n            disabled: vehicles.length === 0,\n            children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Gasto'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Fecha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Coste\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Proveedor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Km\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Acciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: paginatedExpenses.map(expense => {\n              var _expense$descripcion;\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                hover: true,\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatDate(expense.fecha)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: expense.vehiculo_nombre\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: expense.tipo_gasto,\n                    color: getExpenseTypeColor(expense.tipo_gasto),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: ((_expense$descripcion = expense.descripcion) === null || _expense$descripcion === void 0 ? void 0 : _expense$descripcion.length) > 50 ? `${expense.descripcion.substring(0, 50)}...` : expense.descripcion\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: formatCurrency(expense.coste)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: expense.proveedor || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: expense.kilometros_actuales ? formatNumber(expense.kilometros_actuales) : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Editar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleEditExpense(expense),\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 456,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Eliminar\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteExpense(expense),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 461,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n        component: \"div\",\n        count: expenses.length,\n        page: page,\n        onPageChange: (event, newPage) => setPage(newPage),\n        rowsPerPage: rowsPerPage,\n        onRowsPerPageChange: event => {\n          setRowsPerPage(parseInt(event.target.value, 10));\n          setPage(0);\n        },\n        rowsPerPageOptions: [10, 25, 50, 100],\n        labelRowsPerPage: \"Filas por p\\xE1gina:\",\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} de ${count}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ExpenseDialog, {\n      open: dialogOpen,\n      onClose: () => setDialogOpen(false),\n      expense: selectedExpense,\n      onSave: handleSaveExpense,\n      vehicles: vehicles\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 341,\n    columnNumber: 5\n  }, this);\n};\n_s2(ExpensesList, \"R+BSRn2Q9xHwwc49kaZdCKxThMA=\", false, function () {\n  return [useApp];\n});\n_c2 = ExpensesList;\nexport default ExpensesList;\nvar _c, _c2;\n$RefreshReg$(_c, \"ExpenseDialog\");\n$RefreshReg$(_c2, \"ExpensesList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON><PERSON>", "TablePagination", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Receipt", "ReceiptIcon", "useApp", "format", "es", "styled", "jsxDEV", "_jsxDEV", "StyledTableCell", "theme", "borderBottom", "palette", "divider", "padding", "paddingLeft", "paddingRight", "StyledTableRow", "backgroundColor", "action", "hover", "selected", "<PERSON><PERSON><PERSON><PERSON>", "background", "paper", "spacing", "borderRadius", "shape", "boxShadow", "shadows", "marginBottom", "display", "justifyContent", "alignItems", "PrimaryButton", "textTransform", "fontWeight", "transform", "transition", "ExpenseDialog", "open", "onClose", "expense", "onSave", "vehicles", "_s", "formData", "setFormData", "vehiculo_id", "fecha", "Date", "toISOString", "split", "kilometros_actuales", "tipo_gasto", "coste", "descripcion", "proveedor", "expenseTypes", "length", "id", "handleChange", "field", "event", "target", "value", "handleSubmit", "dataToSave", "parseFloat", "parseInt", "categoria", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "gap", "pt", "label", "select", "onChange", "required", "map", "vehicle", "nombre", "type", "InputLabelProps", "shrink", "inputProps", "min", "helperText", "step", "multiline", "rows", "placeholder", "onClick", "variant", "disabled", "_c", "ExpensesList", "_s2", "expenses", "loadExpenses", "addExpense", "dialogOpen", "setDialogOpen", "selectedExpense", "setSelectedExpense", "page", "setPage", "rowsPerPage", "setRowsPerPage", "handleAddExpense", "handleEditExpense", "handleDeleteExpense", "console", "log", "handleSaveExpense", "expenseData", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "locale", "formatNumber", "num", "getExpenseTypeColor", "colors", "totalExpenses", "totalCost", "reduce", "sum", "avgExpense", "expensesByType", "acc", "topExpenseType", "Object", "entries", "sort", "a", "b", "paginatedExpenses", "slice", "mb", "component", "startIcon", "sx", "flex", "textAlign", "color", "py", "fontSize", "gutterBottom", "align", "_expense$descripcion", "vehiculo_nombre", "size", "substring", "title", "count", "onPageChange", "newPage", "onRowsPerPageChange", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c2", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Expenses/ExpensesList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Tooltip,\n  TablePagination,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Receipt as ReceiptIcon,\n} from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { styled } from '@mui/material/styles';\n\n// Styled components for better visual hierarchy\nconst StyledTableCell = styled(TableCell)(({ theme }) => ({\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  padding: '12px 16px',\n  '&:first-of-type': { paddingLeft: 24 },\n  '&:last-child': { paddingRight: 24 }\n}));\n\nconst StyledTableRow = styled(TableRow)(({ theme }) => ({\n  '&:nth-of-type(even)': {\n    backgroundColor: theme.palette.action.hover,\n  },\n  '&:hover': {\n    backgroundColor: theme.palette.action.selected,\n  },\n  '&:last-child td': {\n    borderBottom: 0,\n  },\n}));\n\nconst PageHeader = styled(Box)(({ theme }) => ({\n  background: theme.palette.background.paper,\n  padding: theme.spacing(3, 4),\n  borderRadius: theme.shape.borderRadius,\n  boxShadow: theme.shadows[1],\n  marginBottom: theme.spacing(3),\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n}));\n\nconst PrimaryButton = styled(Button)(({ theme }) => ({\n  textTransform: 'none',\n  fontWeight: 600,\n  padding: '8px 20px',\n  borderRadius: 8,\n  boxShadow: 'none',\n  '&:hover': {\n    transform: 'translateY(-1px)',\n    boxShadow: theme.shadows[2],\n  },\n  transition: 'all 0.2s ease',\n}));\n\nconst ExpenseDialog = ({ open, onClose, expense, onSave, vehicles }) => {\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    tipo_gasto: 'Mantenimiento',\n    coste: '',\n    descripcion: '',\n    proveedor: '',\n  });\n\n  const expenseTypes = [\n    'Mantenimiento',\n    'Reparación',\n    'Seguro',\n    'ITV',\n    'Impuestos',\n    'Neumáticos',\n    'Aceite',\n    'Filtros',\n    'Frenos',\n    'Batería',\n    'Otros'\n  ];\n\n  useEffect(() => {\n    if (expense) {\n      setFormData({\n        ...expense,\n        fecha: expense.fecha.split('T')[0], // Formato para input date\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        tipo_gasto: 'Mantenimiento',\n        coste: '',\n        descripcion: '',\n        proveedor: '',\n      });\n    }\n  }, [expense, vehicles, open]);\n\n  const handleChange = (field) => (event) => {\n    setFormData({\n      ...formData,\n      [field]: event.target.value,\n    });\n  };\n\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      coste: parseFloat(formData.coste),\n      kilometros_actuales: parseInt(formData.kilometros_actuales) || 0,\n      categoria: formData.tipo_gasto, // Para compatibilidad\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n\n  const isValid = formData.vehiculo_id && formData.fecha && formData.tipo_gasto && \n                  formData.coste && formData.descripcion;\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        {expense ? 'Editar Gasto' : 'Nuevo Gasto'}\n      </DialogTitle>\n      <DialogContent>\n        <Box display=\"flex\" flexDirection=\"column\" gap={2} pt={1}>\n          <TextField\n            label=\"Vehículo\"\n            select\n            value={formData.vehiculo_id}\n            onChange={handleChange('vehiculo_id')}\n            fullWidth\n            required\n          >\n            {vehicles.map((vehicle) => (\n              <MenuItem key={vehicle.id} value={vehicle.id}>\n                {vehicle.nombre}\n              </MenuItem>\n            ))}\n          </TextField>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Fecha\"\n              type=\"date\"\n              value={formData.fecha}\n              onChange={handleChange('fecha')}\n              fullWidth\n              required\n              InputLabelProps={{ shrink: true }}\n            />\n            <TextField\n              label=\"Kilometraje actual\"\n              type=\"number\"\n              value={formData.kilometros_actuales}\n              onChange={handleChange('kilometros_actuales')}\n              fullWidth\n              inputProps={{ min: 0 }}\n              helperText=\"Opcional\"\n            />\n          </Box>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Tipo de gasto\"\n              select\n              value={formData.tipo_gasto}\n              onChange={handleChange('tipo_gasto')}\n              fullWidth\n              required\n            >\n              {expenseTypes.map((type) => (\n                <MenuItem key={type} value={type}>\n                  {type}\n                </MenuItem>\n              ))}\n            </TextField>\n            <TextField\n              label=\"Coste (€)\"\n              type=\"number\"\n              value={formData.coste}\n              onChange={handleChange('coste')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n          </Box>\n\n          <TextField\n            label=\"Descripción\"\n            value={formData.descripcion}\n            onChange={handleChange('descripcion')}\n            fullWidth\n            required\n            multiline\n            rows={2}\n            placeholder=\"Describe el gasto realizado...\"\n          />\n\n          <TextField\n            label=\"Proveedor/Taller\"\n            value={formData.proveedor}\n            onChange={handleChange('proveedor')}\n            fullWidth\n            placeholder=\"Nombre del taller, tienda, etc.\"\n          />\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose}>Cancelar</Button>\n        <Button \n          onClick={handleSubmit} \n          variant=\"contained\"\n          disabled={!isValid}\n        >\n          {expense ? 'Actualizar' : 'Guardar'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nconst ExpensesList = () => {\n  const { vehicles, expenses, loadExpenses, addExpense } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedExpense, setSelectedExpense] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n\n  useEffect(() => {\n    // Cargar todos los gastos (sin límite)\n    loadExpenses(null, null);\n  }, []);\n\n  const handleAddExpense = () => {\n    setSelectedExpense(null);\n    setDialogOpen(true);\n  };\n\n  const handleEditExpense = (expense) => {\n    setSelectedExpense(expense);\n    setDialogOpen(true);\n  };\n\n  const handleDeleteExpense = (expense) => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete expense:', expense);\n  };\n\n  const handleSaveExpense = async (expenseData) => {\n    try {\n      if (selectedExpense) {\n        // TODO: Implementar actualización\n        console.log('Update expense:', expenseData);\r\n      } else {\r\n        await addExpense(expenseData);\r\n        // Recargar la lista\r\n        loadExpenses(null, null);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving expense:', error);\r\n    }\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    return new Intl.NumberFormat('es-ES', {\r\n      style: 'currency',\r\n      currency: 'EUR'\r\n    }).format(amount || 0);\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    try {\r\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\r\n    } catch {\r\n      return dateString;\r\n    }\r\n  };\r\n\r\n  const formatNumber = (num) => {\r\n    return new Intl.NumberFormat('es-ES').format(num || 0);\r\n  };\r\n\r\n  const getExpenseTypeColor = (type) => {\r\n    const colors = {\r\n      'Mantenimiento': 'primary',\r\n      'Reparación': 'error',\r\n      'Seguro': 'info',\r\n      'ITV': 'warning',\r\n      'Impuestos': 'secondary',\r\n      'Neumáticos': 'success',\r\n    };\r\n    return colors[type] || 'default';\r\n  };\r\n\r\n  // Calcular estadísticas rápidas\r\n  const totalExpenses = expenses.length;\r\n  const totalCost = expenses.reduce((sum, expense) => sum + (expense.coste || 0), 0);\r\n  const avgExpense = totalExpenses > 0 ? totalCost / totalExpenses : 0;\r\n  \r\n  // Agrupar por tipo\r\n  const expensesByType = expenses.reduce((acc, expense) => {\r\n    const type = expense.tipo_gasto || 'Otros';\r\n    acc[type] = (acc[type] || 0) + (expense.coste || 0);\r\n    return acc;\r\n  }, {});\r\n  \r\n  const topExpenseType = Object.entries(expensesByType)\r\n    .sort(([,a], [,b]) => b - a)[0];\r\n\r\n  // Paginación\r\n  const paginatedExpenses = expenses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\r\n\r\n  return (\r\n    <Box>\r\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\r\n        <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\">\r\n          Gastos\r\n        </Typography>\r\n        <Button\r\n          variant=\"contained\"\r\n          startIcon={<AddIcon />}\r\n          onClick={handleAddExpense}\r\n          disabled={vehicles.length === 0}\r\n        >\r\n          Nuevo Gasto\r\n        </Button>\r\n      </Box>\r\n\r\n      {/* Estadísticas rápidas */}\r\n      <Box display=\"flex\" gap={2} mb={3}>\r\n        <Card sx={{ flex: 1 }}>\r\n          <CardContent sx={{ textAlign: 'center' }}>\r\n            <Typography variant=\"h4\" color=\"primary\">{totalExpenses}</Typography>\r\n            <Typography variant=\"body2\" color=\"textSecondary\">Total Gastos</Typography>\r\n          </CardContent>\r\n        </Card>\r\n        <Card sx={{ flex: 1 }}>\r\n          <CardContent sx={{ textAlign: 'center' }}>\r\n            <Typography variant=\"h4\" color=\"error.main\">{formatCurrency(totalCost)}</Typography>\r\n            <Typography variant=\"body2\" color=\"textSecondary\">Coste Total</Typography>\r\n          </CardContent>\r\n        </Card>\r\n        <Card sx={{ flex: 1 }}>\r\n          <CardContent sx={{ textAlign: 'center' }}>\r\n            <Typography variant=\"h4\" color=\"warning.main\">{formatCurrency(avgExpense)}</Typography>\r\n            <Typography variant=\"body2\" color=\"textSecondary\">Gasto Promedio</Typography>\r\n          </CardContent>\r\n        </Card>\r\n        <Card sx={{ flex: 1 }}>\r\n          <CardContent sx={{ textAlign: 'center' }}>\r\n            <Typography variant=\"h6\" color=\"info.main\">\r\n              {topExpenseType ? topExpenseType[0] : 'N/A'}\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"textSecondary\">Tipo Más Frecuente</Typography>\r\n          </CardContent>\r\n        </Card>\r\n      </Box>\r\n\r\n      {expenses.length === 0 ? (\r\n        <Card>\r\n          <CardContent>\r\n            <Box textAlign=\"center\" py={6}>\r\n              <ReceiptIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\r\n              <Typography variant=\"h6\" color=\"textSecondary\" gutterBottom>\r\n                No hay gastos registrados\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"textSecondary\" mb={3}>\r\n                Comienza registrando gastos de mantenimiento, reparaciones, seguros, etc.\r\n              </Typography>\r\n              <Button\r\n                variant=\"contained\"\r\n                startIcon={<AddIcon />}\r\n                onClick={handleAddExpense}\r\n                disabled={vehicles.length === 0}\r\n              >\r\n                {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Gasto'}\r\n              </Button>\r\n            </Box>\r\n          </CardContent>\r\n        </Card>\r\n      ) : (\r\n        <Card>\r\n          <TableContainer component={Paper}>\r\n            <Table>\r\n              <TableHead>\r\n                <TableRow>\r\n                  <TableCell>Fecha</TableCell>\r\n                  <TableCell>Vehículo</TableCell>\r\n                  <TableCell>Tipo</TableCell>\r\n                  <TableCell>Descripción</TableCell>\r\n                  <TableCell align=\"right\">Coste</TableCell>\r\n                  <TableCell>Proveedor</TableCell>\r\n                  <TableCell align=\"right\">Km</TableCell>\r\n                  <TableCell align=\"center\">Acciones</TableCell>\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                {paginatedExpenses.map((expense) => (\r\n                  <TableRow key={expense.id} hover>\r\n                    <TableCell>{formatDate(expense.fecha)}</TableCell>\r\n                    <TableCell>{expense.vehiculo_nombre}</TableCell>\r\n                    <TableCell>\r\n                      <Chip \r\n                        label={expense.tipo_gasto} \r\n                        color={getExpenseTypeColor(expense.tipo_gasto)}\r\n                        size=\"small\"\r\n                      />\r\n                    </TableCell>\r\n                    <TableCell>\r\n                      <Typography variant=\"body2\">\r\n                        {expense.descripcion?.length > 50 \r\n                          ? `${expense.descripcion.substring(0, 50)}...`\r\n                          : expense.descripcion\r\n                        }\r\n                      </Typography>\r\n                    </TableCell>\r\n                    <TableCell align=\"right\">\r\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\r\n                        {formatCurrency(expense.coste)}\r\n                      </Typography>\r\n                    </TableCell>\r\n                    <TableCell>{expense.proveedor || '-'}</TableCell>\r\n                    <TableCell align=\"right\">\r\n                      {expense.kilometros_actuales ? formatNumber(expense.kilometros_actuales) : '-'}\r\n                    </TableCell>\r\n                    <TableCell align=\"center\">\r\n                      <Tooltip title=\"Editar\">\r\n                        <IconButton size=\"small\" onClick={() => handleEditExpense(expense)}>\r\n                          <EditIcon />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                      <Tooltip title=\"Eliminar\">\r\n                        <IconButton size=\"small\" color=\"error\" onClick={() => handleDeleteExpense(expense)}>\r\n                          <DeleteIcon />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                    </TableCell>\r\n                  </TableRow>\r\n                ))}\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n          \r\n          <TablePagination\r\n            component=\"div\"\r\n            count={expenses.length}\r\n            page={page}\r\n            onPageChange={(event, newPage) => setPage(newPage)}\r\n            rowsPerPage={rowsPerPage}\r\n            onRowsPerPageChange={(event) => {\r\n              setRowsPerPage(parseInt(event.target.value, 10));\r\n              setPage(0);\r\n            }}\r\n            rowsPerPageOptions={[10, 25, 50, 100]}\r\n            labelRowsPerPage=\"Filas por página:\"\r\n            labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}\r\n          />\r\n        </Card>\r\n      )}\r\n\r\n      <ExpenseDialog\r\n        open={dialogOpen}\r\n        onClose={() => setDialogOpen(false)}\r\n        expense={selectedExpense}\r\n        onSave={handleSaveExpense}\r\n        vehicles={vehicles}\r\n      />\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ExpensesList;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,eAAe,QACV,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,MAAM,QAAQ,sBAAsB;;AAE7C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAGH,MAAM,CAAC1B,SAAS,CAAC,CAAC,CAAC;EAAE8B;AAAM,CAAC,MAAM;EACxDC,YAAY,EAAE,aAAaD,KAAK,CAACE,OAAO,CAACC,OAAO,EAAE;EAClDC,OAAO,EAAE,WAAW;EACpB,iBAAiB,EAAE;IAAEC,WAAW,EAAE;EAAG,CAAC;EACtC,cAAc,EAAE;IAAEC,YAAY,EAAE;EAAG;AACrC,CAAC,CAAC,CAAC;AAEH,MAAMC,cAAc,GAAGX,MAAM,CAACvB,QAAQ,CAAC,CAAC,CAAC;EAAE2B;AAAM,CAAC,MAAM;EACtD,qBAAqB,EAAE;IACrBQ,eAAe,EAAER,KAAK,CAACE,OAAO,CAACO,MAAM,CAACC;EACxC,CAAC;EACD,SAAS,EAAE;IACTF,eAAe,EAAER,KAAK,CAACE,OAAO,CAACO,MAAM,CAACE;EACxC,CAAC;EACD,iBAAiB,EAAE;IACjBV,YAAY,EAAE;EAChB;AACF,CAAC,CAAC,CAAC;AAEH,MAAMW,UAAU,GAAGhB,MAAM,CAACjC,GAAG,CAAC,CAAC,CAAC;EAAEqC;AAAM,CAAC,MAAM;EAC7Ca,UAAU,EAAEb,KAAK,CAACE,OAAO,CAACW,UAAU,CAACC,KAAK;EAC1CV,OAAO,EAAEJ,KAAK,CAACe,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5BC,YAAY,EAAEhB,KAAK,CAACiB,KAAK,CAACD,YAAY;EACtCE,SAAS,EAAElB,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC;EAC3BC,YAAY,EAAEpB,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;EAC9BM,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,eAAe;EAC/BC,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAEH,MAAMC,aAAa,GAAG5B,MAAM,CAAC7B,MAAM,CAAC,CAAC,CAAC;EAAEiC;AAAM,CAAC,MAAM;EACnDyB,aAAa,EAAE,MAAM;EACrBC,UAAU,EAAE,GAAG;EACftB,OAAO,EAAE,UAAU;EACnBY,YAAY,EAAE,CAAC;EACfE,SAAS,EAAE,MAAM;EACjB,SAAS,EAAE;IACTS,SAAS,EAAE,kBAAkB;IAC7BT,SAAS,EAAElB,KAAK,CAACmB,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDS,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AAEH,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,mBAAmB,EAAE,EAAE;IACvBC,UAAU,EAAE,eAAe;IAC3BC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,CACnB,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,CACR;EAEDtF,SAAS,CAAC,MAAM;IACd,IAAIsE,OAAO,EAAE;MACXK,WAAW,CAAC;QACV,GAAGL,OAAO;QACVO,KAAK,EAAEP,OAAO,CAACO,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE;MACtC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,WAAW,CAAC;QACVC,WAAW,EAAEJ,QAAQ,CAACe,MAAM,GAAG,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAACgB,EAAE,GAAG,EAAE;QACtDX,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7CC,mBAAmB,EAAE,EAAE;QACvBC,UAAU,EAAE,eAAe;QAC3BC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACf,OAAO,EAAEE,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EAE7B,MAAMqB,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzChB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgB,KAAK,GAAGC,KAAK,CAACC,MAAM,CAACC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjB,GAAGrB,QAAQ;MACXS,KAAK,EAAEa,UAAU,CAACtB,QAAQ,CAACS,KAAK,CAAC;MACjCF,mBAAmB,EAAEgB,QAAQ,CAACvB,QAAQ,CAACO,mBAAmB,CAAC,IAAI,CAAC;MAChEiB,SAAS,EAAExB,QAAQ,CAACQ,UAAU,CAAE;IAClC,CAAC;IACDX,MAAM,CAACwB,UAAU,CAAC;IAClB1B,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM8B,OAAO,GAAGzB,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACQ,UAAU,IAC7DR,QAAQ,CAACS,KAAK,IAAIT,QAAQ,CAACU,WAAW;EAEtD,oBACEhD,OAAA,CAACrB,MAAM;IAACqD,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC+B,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DlE,OAAA,CAACpB,WAAW;MAAAsF,QAAA,EACThC,OAAO,GAAG,cAAc,GAAG;IAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACdtE,OAAA,CAACnB,aAAa;MAAAqF,QAAA,eACZlE,OAAA,CAACnC,GAAG;QAAC0D,OAAO,EAAC,MAAM;QAACgD,aAAa,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACvDlE,OAAA,CAACjB,SAAS;UACR2F,KAAK,EAAC,aAAU;UAChBC,MAAM;UACNlB,KAAK,EAAEnB,QAAQ,CAACE,WAAY;UAC5BoC,QAAQ,EAAEvB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTY,QAAQ;UAAAX,QAAA,EAEP9B,QAAQ,CAAC0C,GAAG,CAAEC,OAAO,iBACpB/E,OAAA,CAAChB,QAAQ;YAAkByE,KAAK,EAAEsB,OAAO,CAAC3B,EAAG;YAAAc,QAAA,EAC1Ca,OAAO,CAACC;UAAM,GADFD,OAAO,CAAC3B,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZtE,OAAA,CAACnC,GAAG;UAAC0D,OAAO,EAAC,MAAM;UAACiD,GAAG,EAAE,CAAE;UAAAN,QAAA,gBACzBlE,OAAA,CAACjB,SAAS;YACR2F,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,MAAM;YACXxB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;YACtBmC,QAAQ,EAAEvB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTY,QAAQ;YACRK,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFtE,OAAA,CAACjB,SAAS;YACR2F,KAAK,EAAC,oBAAoB;YAC1BO,IAAI,EAAC,QAAQ;YACbxB,KAAK,EAAEnB,QAAQ,CAACO,mBAAoB;YACpC+B,QAAQ,EAAEvB,YAAY,CAAC,qBAAqB,CAAE;YAC9CY,SAAS;YACTmB,UAAU,EAAE;cAAEC,GAAG,EAAE;YAAE,CAAE;YACvBC,UAAU,EAAC;UAAU;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtE,OAAA,CAACnC,GAAG;UAAC0D,OAAO,EAAC,MAAM;UAACiD,GAAG,EAAE,CAAE;UAAAN,QAAA,gBACzBlE,OAAA,CAACjB,SAAS;YACR2F,KAAK,EAAC,eAAe;YACrBC,MAAM;YACNlB,KAAK,EAAEnB,QAAQ,CAACQ,UAAW;YAC3B8B,QAAQ,EAAEvB,YAAY,CAAC,YAAY,CAAE;YACrCY,SAAS;YACTY,QAAQ;YAAAX,QAAA,EAEPhB,YAAY,CAAC4B,GAAG,CAAEG,IAAI,iBACrBjF,OAAA,CAAChB,QAAQ;cAAYyE,KAAK,EAAEwB,IAAK;cAAAf,QAAA,EAC9Be;YAAI,GADQA,IAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZtE,OAAA,CAACjB,SAAS;YACR2F,KAAK,EAAC,gBAAW;YACjBO,IAAI,EAAC,QAAQ;YACbxB,KAAK,EAAEnB,QAAQ,CAACS,KAAM;YACtB6B,QAAQ,EAAEvB,YAAY,CAAC,OAAO,CAAE;YAChCY,SAAS;YACTY,QAAQ;YACRO,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEE,IAAI,EAAE;YAAK;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtE,OAAA,CAACjB,SAAS;UACR2F,KAAK,EAAC,gBAAa;UACnBjB,KAAK,EAAEnB,QAAQ,CAACU,WAAY;UAC5B4B,QAAQ,EAAEvB,YAAY,CAAC,aAAa,CAAE;UACtCY,SAAS;UACTY,QAAQ;UACRW,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,WAAW,EAAC;QAAgC;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEFtE,OAAA,CAACjB,SAAS;UACR2F,KAAK,EAAC,kBAAkB;UACxBjB,KAAK,EAAEnB,QAAQ,CAACW,SAAU;UAC1B2B,QAAQ,EAAEvB,YAAY,CAAC,WAAW,CAAE;UACpCY,SAAS;UACTyB,WAAW,EAAC;QAAiC;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChBtE,OAAA,CAAClB,aAAa;MAAAoF,QAAA,gBACZlE,OAAA,CAAC/B,MAAM;QAAC0H,OAAO,EAAE1D,OAAQ;QAAAiC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3CtE,OAAA,CAAC/B,MAAM;QACL0H,OAAO,EAAEjC,YAAa;QACtBkC,OAAO,EAAC,WAAW;QACnBC,QAAQ,EAAE,CAAC9B,OAAQ;QAAAG,QAAA,EAElBhC,OAAO,GAAG,YAAY,GAAG;MAAS;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACjC,EAAA,CAtKIN,aAAa;AAAA+D,EAAA,GAAb/D,aAAa;AAwKnB,MAAMgE,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAE5D,QAAQ;IAAE6D,QAAQ;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAGxG,MAAM,CAAC,CAAC;EACjE,MAAM,CAACyG,UAAU,EAAEC,aAAa,CAAC,GAAG1I,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2I,eAAe,EAAEC,kBAAkB,CAAC,GAAG5I,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6I,IAAI,EAAEC,OAAO,CAAC,GAAG9I,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC+I,WAAW,EAAEC,cAAc,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd;IACAsI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,gBAAgB,GAAGA,CAAA,KAAM;IAC7BL,kBAAkB,CAAC,IAAI,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMQ,iBAAiB,GAAI3E,OAAO,IAAK;IACrCqE,kBAAkB,CAACrE,OAAO,CAAC;IAC3BmE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMS,mBAAmB,GAAI5E,OAAO,IAAK;IACvC;IACA6E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE9E,OAAO,CAAC;EACzC,CAAC;EAED,MAAM+E,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C,IAAI;MACF,IAAIZ,eAAe,EAAE;QACnB;QACAS,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,WAAW,CAAC;MAC7C,CAAC,MAAM;QACL,MAAMf,UAAU,CAACe,WAAW,CAAC;QAC7B;QACAhB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC7H,MAAM,CAACyH,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAO/H,MAAM,CAAC,IAAI8C,IAAI,CAACiF,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEC,MAAM,EAAE/H;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAO8H,UAAU;IACnB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,GAAG,IAAK;IAC5B,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAAC3H,MAAM,CAACkI,GAAG,IAAI,CAAC,CAAC;EACxD,CAAC;EAED,MAAMC,mBAAmB,GAAI9C,IAAI,IAAK;IACpC,MAAM+C,MAAM,GAAG;MACb,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE,OAAO;MACrB,QAAQ,EAAE,MAAM;MAChB,KAAK,EAAE,SAAS;MAChB,WAAW,EAAE,WAAW;MACxB,YAAY,EAAE;IAChB,CAAC;IACD,OAAOA,MAAM,CAAC/C,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;;EAED;EACA,MAAMgD,aAAa,GAAGhC,QAAQ,CAAC9C,MAAM;EACrC,MAAM+E,SAAS,GAAGjC,QAAQ,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAElG,OAAO,KAAKkG,GAAG,IAAIlG,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAMsF,UAAU,GAAGJ,aAAa,GAAG,CAAC,GAAGC,SAAS,GAAGD,aAAa,GAAG,CAAC;;EAEpE;EACA,MAAMK,cAAc,GAAGrC,QAAQ,CAACkC,MAAM,CAAC,CAACI,GAAG,EAAErG,OAAO,KAAK;IACvD,MAAM+C,IAAI,GAAG/C,OAAO,CAACY,UAAU,IAAI,OAAO;IAC1CyF,GAAG,CAACtD,IAAI,CAAC,GAAG,CAACsD,GAAG,CAACtD,IAAI,CAAC,IAAI,CAAC,KAAK/C,OAAO,CAACa,KAAK,IAAI,CAAC,CAAC;IACnD,OAAOwF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO,CAACJ,cAAc,CAAC,CAClDK,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC;EACA,MAAME,iBAAiB,GAAG7C,QAAQ,CAAC8C,KAAK,CAACvC,IAAI,GAAGE,WAAW,EAAEF,IAAI,GAAGE,WAAW,GAAGA,WAAW,CAAC;EAE9F,oBACE1G,OAAA,CAACnC,GAAG;IAAAqG,QAAA,gBACFlE,OAAA,CAACnC,GAAG;MAAC0D,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACuH,EAAE,EAAE,CAAE;MAAA9E,QAAA,gBAC3ElE,OAAA,CAAClC,UAAU;QAAC8H,OAAO,EAAC,IAAI;QAACqD,SAAS,EAAC,IAAI;QAACrH,UAAU,EAAC,MAAM;QAAAsC,QAAA,EAAC;MAE1D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtE,OAAA,CAAC/B,MAAM;QACL2H,OAAO,EAAC,WAAW;QACnBsD,SAAS,eAAElJ,OAAA,CAACZ,OAAO;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBqB,OAAO,EAAEiB,gBAAiB;QAC1Bf,QAAQ,EAAEzD,QAAQ,CAACe,MAAM,KAAK,CAAE;QAAAe,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNtE,OAAA,CAACnC,GAAG;MAAC0D,OAAO,EAAC,MAAM;MAACiD,GAAG,EAAE,CAAE;MAACwE,EAAE,EAAE,CAAE;MAAA9E,QAAA,gBAChClE,OAAA,CAACjC,IAAI;QAACoL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAlF,QAAA,eACpBlE,OAAA,CAAChC,WAAW;UAACmL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAnF,QAAA,gBACvClE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,IAAI;YAAC0D,KAAK,EAAC,SAAS;YAAApF,QAAA,EAAE+D;UAAa;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrEtE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,OAAO;YAAC0D,KAAK,EAAC,eAAe;YAAApF,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPtE,OAAA,CAACjC,IAAI;QAACoL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAlF,QAAA,eACpBlE,OAAA,CAAChC,WAAW;UAACmL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAnF,QAAA,gBACvClE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,IAAI;YAAC0D,KAAK,EAAC,YAAY;YAAApF,QAAA,EAAEkD,cAAc,CAACc,SAAS;UAAC;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACpFtE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,OAAO;YAAC0D,KAAK,EAAC,eAAe;YAAApF,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPtE,OAAA,CAACjC,IAAI;QAACoL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAlF,QAAA,eACpBlE,OAAA,CAAChC,WAAW;UAACmL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAnF,QAAA,gBACvClE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,IAAI;YAAC0D,KAAK,EAAC,cAAc;YAAApF,QAAA,EAAEkD,cAAc,CAACiB,UAAU;UAAC;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACvFtE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,OAAO;YAAC0D,KAAK,EAAC,eAAe;YAAApF,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPtE,OAAA,CAACjC,IAAI;QAACoL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAlF,QAAA,eACpBlE,OAAA,CAAChC,WAAW;UAACmL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAnF,QAAA,gBACvClE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,IAAI;YAAC0D,KAAK,EAAC,WAAW;YAAApF,QAAA,EACvCsE,cAAc,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAG;UAAK;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACbtE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,OAAO;YAAC0D,KAAK,EAAC,eAAe;YAAApF,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEL2B,QAAQ,CAAC9C,MAAM,KAAK,CAAC,gBACpBnD,OAAA,CAACjC,IAAI;MAAAmG,QAAA,eACHlE,OAAA,CAAChC,WAAW;QAAAkG,QAAA,eACVlE,OAAA,CAACnC,GAAG;UAACwL,SAAS,EAAC,QAAQ;UAACE,EAAE,EAAE,CAAE;UAAArF,QAAA,gBAC5BlE,OAAA,CAACN,WAAW;YAACyJ,EAAE,EAAE;cAAEK,QAAQ,EAAE,EAAE;cAAEF,KAAK,EAAE,gBAAgB;cAAEN,EAAE,EAAE;YAAE;UAAE;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEtE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,IAAI;YAAC0D,KAAK,EAAC,eAAe;YAACG,YAAY;YAAAvF,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtE,OAAA,CAAClC,UAAU;YAAC8H,OAAO,EAAC,OAAO;YAAC0D,KAAK,EAAC,eAAe;YAACN,EAAE,EAAE,CAAE;YAAA9E,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtE,OAAA,CAAC/B,MAAM;YACL2H,OAAO,EAAC,WAAW;YACnBsD,SAAS,eAAElJ,OAAA,CAACZ,OAAO;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBqB,OAAO,EAAEiB,gBAAiB;YAC1Bf,QAAQ,EAAEzD,QAAQ,CAACe,MAAM,KAAK,CAAE;YAAAe,QAAA,EAE/B9B,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;UAAwB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPtE,OAAA,CAACjC,IAAI;MAAAmG,QAAA,gBACHlE,OAAA,CAAC3B,cAAc;QAAC4K,SAAS,EAAEzK,KAAM;QAAA0F,QAAA,eAC/BlE,OAAA,CAAC9B,KAAK;UAAAgG,QAAA,gBACJlE,OAAA,CAAC1B,SAAS;YAAA4F,QAAA,eACRlE,OAAA,CAACzB,QAAQ;cAAA2F,QAAA,gBACPlE,OAAA,CAAC5B,SAAS;gBAAA8F,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BtE,OAAA,CAAC5B,SAAS;gBAAA8F,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BtE,OAAA,CAAC5B,SAAS;gBAAA8F,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3BtE,OAAA,CAAC5B,SAAS;gBAAA8F,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCtE,OAAA,CAAC5B,SAAS;gBAACsL,KAAK,EAAC,OAAO;gBAAAxF,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1CtE,OAAA,CAAC5B,SAAS;gBAAA8F,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCtE,OAAA,CAAC5B,SAAS;gBAACsL,KAAK,EAAC,OAAO;gBAAAxF,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACvCtE,OAAA,CAAC5B,SAAS;gBAACsL,KAAK,EAAC,QAAQ;gBAAAxF,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZtE,OAAA,CAAC7B,SAAS;YAAA+F,QAAA,EACP4E,iBAAiB,CAAChE,GAAG,CAAE5C,OAAO;cAAA,IAAAyH,oBAAA;cAAA,oBAC7B3J,OAAA,CAACzB,QAAQ;gBAAkBqC,KAAK;gBAAAsD,QAAA,gBAC9BlE,OAAA,CAAC5B,SAAS;kBAAA8F,QAAA,EAAEwD,UAAU,CAACxF,OAAO,CAACO,KAAK;gBAAC;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDtE,OAAA,CAAC5B,SAAS;kBAAA8F,QAAA,EAAEhC,OAAO,CAAC0H;gBAAe;kBAAAzF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDtE,OAAA,CAAC5B,SAAS;kBAAA8F,QAAA,eACRlE,OAAA,CAACvB,IAAI;oBACHiG,KAAK,EAAExC,OAAO,CAACY,UAAW;oBAC1BwG,KAAK,EAAEvB,mBAAmB,CAAC7F,OAAO,CAACY,UAAU,CAAE;oBAC/C+G,IAAI,EAAC;kBAAO;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZtE,OAAA,CAAC5B,SAAS;kBAAA8F,QAAA,eACRlE,OAAA,CAAClC,UAAU;oBAAC8H,OAAO,EAAC,OAAO;oBAAA1B,QAAA,EACxB,EAAAyF,oBAAA,GAAAzH,OAAO,CAACc,WAAW,cAAA2G,oBAAA,uBAAnBA,oBAAA,CAAqBxG,MAAM,IAAG,EAAE,GAC7B,GAAGjB,OAAO,CAACc,WAAW,CAAC8G,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC5C5H,OAAO,CAACc;kBAAW;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZtE,OAAA,CAAC5B,SAAS;kBAACsL,KAAK,EAAC,OAAO;kBAAAxF,QAAA,eACtBlE,OAAA,CAAClC,UAAU;oBAAC8H,OAAO,EAAC,OAAO;oBAAChE,UAAU,EAAC,MAAM;oBAAAsC,QAAA,EAC1CkD,cAAc,CAAClF,OAAO,CAACa,KAAK;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZtE,OAAA,CAAC5B,SAAS;kBAAA8F,QAAA,EAAEhC,OAAO,CAACe,SAAS,IAAI;gBAAG;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDtE,OAAA,CAAC5B,SAAS;kBAACsL,KAAK,EAAC,OAAO;kBAAAxF,QAAA,EACrBhC,OAAO,CAACW,mBAAmB,GAAGgF,YAAY,CAAC3F,OAAO,CAACW,mBAAmB,CAAC,GAAG;gBAAG;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACZtE,OAAA,CAAC5B,SAAS;kBAACsL,KAAK,EAAC,QAAQ;kBAAAxF,QAAA,gBACvBlE,OAAA,CAACf,OAAO;oBAAC8K,KAAK,EAAC,QAAQ;oBAAA7F,QAAA,eACrBlE,OAAA,CAACtB,UAAU;sBAACmL,IAAI,EAAC,OAAO;sBAAClE,OAAO,EAAEA,CAAA,KAAMkB,iBAAiB,CAAC3E,OAAO,CAAE;sBAAAgC,QAAA,eACjElE,OAAA,CAACV,QAAQ;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVtE,OAAA,CAACf,OAAO;oBAAC8K,KAAK,EAAC,UAAU;oBAAA7F,QAAA,eACvBlE,OAAA,CAACtB,UAAU;sBAACmL,IAAI,EAAC,OAAO;sBAACP,KAAK,EAAC,OAAO;sBAAC3D,OAAO,EAAEA,CAAA,KAAMmB,mBAAmB,CAAC5E,OAAO,CAAE;sBAAAgC,QAAA,eACjFlE,OAAA,CAACR,UAAU;wBAAA2E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,GAtCCpC,OAAO,CAACkB,EAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCf,CAAC;YAAA,CACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEjBtE,OAAA,CAACd,eAAe;QACd+J,SAAS,EAAC,KAAK;QACfe,KAAK,EAAE/D,QAAQ,CAAC9C,MAAO;QACvBqD,IAAI,EAAEA,IAAK;QACXyD,YAAY,EAAEA,CAAC1G,KAAK,EAAE2G,OAAO,KAAKzD,OAAO,CAACyD,OAAO,CAAE;QACnDxD,WAAW,EAAEA,WAAY;QACzByD,mBAAmB,EAAG5G,KAAK,IAAK;UAC9BoD,cAAc,CAAC9C,QAAQ,CAACN,KAAK,CAACC,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;UAChDgD,OAAO,CAAC,CAAC,CAAC;QACZ,CAAE;QACF2D,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QACtCC,gBAAgB,EAAC,sBAAmB;QACpCC,kBAAkB,EAAEA,CAAC;UAAEC,IAAI;UAAEC,EAAE;UAAER;QAAM,CAAC,KAAK,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK;MAAG;QAAA7F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAEDtE,OAAA,CAAC+B,aAAa;MACZC,IAAI,EAAEoE,UAAW;MACjBnE,OAAO,EAAEA,CAAA,KAAMoE,aAAa,CAAC,KAAK,CAAE;MACpCnE,OAAO,EAAEoE,eAAgB;MACzBnE,MAAM,EAAE8E,iBAAkB;MAC1B7E,QAAQ,EAAEA;IAAS;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC0B,GAAA,CAxPID,YAAY;EAAA,QACyCpG,MAAM;AAAA;AAAA8K,GAAA,GAD3D1E,YAAY;AA0PlB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAA2E,GAAA;AAAAC,YAAA,CAAA5E,EAAA;AAAA4E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}