{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Statistics\\\\Statistics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, FormControl, InputLabel, Select, MenuItem, TextField, Chip, Divider, Paper } from '@mui/material';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip as ChartTooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { useApp } from '../../context/AppContext';\nimport { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport VehicleComparison from './VehicleComparison';\nimport AdvancedAnalytics from './AdvancedAnalytics';\n\n// Colores para los gráficos\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\n\n// Componente para métricas principales\nconst MetricCard = ({\n  title,\n  value,\n  subtitle,\n  color = 'primary',\n  trend\n}) => /*#__PURE__*/_jsxDEV(Card, {\n  children: /*#__PURE__*/_jsxDEV(CardContent, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      color: \"textSecondary\",\n      gutterBottom: true,\n      variant: \"body2\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"div\",\n      color: color,\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 9\n    }, this), trend && /*#__PURE__*/_jsxDEV(Box, {\n      mt: 1,\n      children: /*#__PURE__*/_jsxDEV(Chip, {\n        label: trend,\n        size: \"small\",\n        color: trend.includes('+') ? 'success' : trend.includes('-') ? 'error' : 'default'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 46,\n  columnNumber: 3\n}, this);\n_c = MetricCard;\nconst Statistics = () => {\n  _s();\n  const {\n    vehicles,\n    refuels,\n    expenses,\n    loadStatistics,\n    statistics\n  } = useApp();\n  const [selectedVehicle, setSelectedVehicle] = useState('all');\n  const [dateRange, setDateRange] = useState('6months');\n  const [chartData, setChartData] = useState({\n    consumption: [],\n    costs: [],\n    expensesByType: [],\n    monthlyTrends: []\n  });\n  useEffect(() => {\n    calculateStatistics();\n  }, [refuels, expenses, selectedVehicle, dateRange]);\n  const calculateStatistics = () => {\n    console.log('🔄 Recalculating statistics with filters:', {\n      selectedVehicle,\n      dateRange\n    });\n    console.log('📊 Total data available:', {\n      refuels: refuels.length,\n      expenses: expenses.length\n    });\n    let filteredRefuels = [...refuels]; // Crear copia para evitar mutación\n    let filteredExpenses = [...expenses];\n\n    // Filtrar por vehículo\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n      console.log('🚗 Filtered by vehicle:', vehicleId, {\n        refuels: filteredRefuels.length,\n        expenses: filteredExpenses.length\n      });\n    }\n\n    // Filtrar por fecha\n    const now = new Date();\n    let startDate;\n    switch (dateRange) {\n      case '3months':\n        startDate = subMonths(now, 3);\n        break;\n      case '6months':\n        startDate = subMonths(now, 6);\n        break;\n      case '1year':\n        startDate = subMonths(now, 12);\n        break;\n      case '2years':\n        startDate = subMonths(now, 24);\n        break;\n      default:\n        startDate = new Date(0);\n      // Todos los datos\n    }\n    if (dateRange !== 'all') {\n      filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);\n      filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);\n      console.log('📅 Filtered by date:', dateRange, {\n        refuels: filteredRefuels.length,\n        expenses: filteredExpenses.length\n      });\n    }\n\n    // Calcular datos para gráficos\n    calculateChartData(filteredRefuels, filteredExpenses);\n  };\n  const calculateChartData = (refuels, expenses) => {\n    // 1. Datos de consumo por mes\n    const consumptionByMonth = {};\n    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    for (let i = 1; i < sortedRefuels.length; i++) {\n      const current = sortedRefuels[i];\n      const previous = sortedRefuels[i - 1];\n      if (current.vehiculo_id === previous.vehiculo_id) {\n        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;\n        const liters = current.litros;\n        if (kmDiff > 0 && liters > 0) {\n          const consumption = liters / kmDiff * 100; // L/100km\n          const month = format(new Date(current.fecha), 'yyyy-MM');\n          if (!consumptionByMonth[month]) {\n            consumptionByMonth[month] = {\n              total: 0,\n              count: 0,\n              month\n            };\n          }\n          consumptionByMonth[month].total += consumption;\n          consumptionByMonth[month].count += 1;\n        }\n      }\n    }\n    const consumptionData = Object.values(consumptionByMonth).map(item => ({\n      month: format(new Date(item.month + '-01'), 'MMM yyyy', {\n        locale: es\n      }),\n      consumption: (item.total / item.count).toFixed(1),\n      date: item.month\n    })).sort((a, b) => a.date.localeCompare(b.date));\n\n    // 2. Costes por mes\n    const costsByMonth = {};\n    [...refuels, ...expenses].forEach(item => {\n      const month = format(new Date(item.fecha), 'yyyy-MM');\n      const cost = item.coste_total || item.coste || 0;\n      const type = item.litros ? 'Combustible' : 'Gastos';\n      if (!costsByMonth[month]) {\n        costsByMonth[month] = {\n          month,\n          Combustible: 0,\n          Gastos: 0,\n          date: month\n        };\n      }\n      costsByMonth[month][type] += cost;\n    });\n    const costsData = Object.values(costsByMonth).map(item => ({\n      ...item,\n      month: format(new Date(item.month + '-01'), 'MMM yyyy', {\n        locale: es\n      }),\n      Total: item.Combustible + item.Gastos\n    })).sort((a, b) => a.date.localeCompare(b.date));\n\n    // 3. Gastos por tipo\n    const expensesByType = {};\n    expenses.forEach(expense => {\n      const type = expense.tipo_gasto || 'Otros';\n      expensesByType[type] = (expensesByType[type] || 0) + (expense.coste || 0);\n    });\n    const expensesData = Object.entries(expensesByType).map(([name, value]) => ({\n      name,\n      value: parseFloat(value.toFixed(2))\n    })).sort((a, b) => b.value - a.value);\n\n    // 4. Tendencias mensuales\n    const monthlyTrends = costsData.map(item => {\n      var _consumptionData$find;\n      return {\n        month: item.month,\n        date: item.date,\n        totalCost: item.Total,\n        fuelCost: item.Combustible,\n        expenseCost: item.Gastos,\n        consumption: ((_consumptionData$find = consumptionData.find(c => c.date === item.date)) === null || _consumptionData$find === void 0 ? void 0 : _consumptionData$find.consumption) || 0\n      };\n    });\n    setChartData({\n      consumption: consumptionData,\n      costs: costsData,\n      expensesByType: expensesData,\n      monthlyTrends\n    });\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  // Aplicar filtros para métricas principales\n  const getFilteredData = () => {\n    let filteredRefuels = [...refuels];\n    let filteredExpenses = [...expenses];\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n    }\n    if (dateRange !== 'all') {\n      const now = new Date();\n      let startDate;\n      switch (dateRange) {\n        case '3months':\n          startDate = subMonths(now, 3);\n          break;\n        case '6months':\n          startDate = subMonths(now, 6);\n          break;\n        case '1year':\n          startDate = subMonths(now, 12);\n          break;\n        case '2years':\n          startDate = subMonths(now, 24);\n          break;\n        default:\n          startDate = new Date(0);\n      }\n      if (dateRange !== 'all') {\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);\n      }\n    }\n    return {\n      filteredRefuels,\n      filteredExpenses\n    };\n  };\n  const {\n    filteredRefuels,\n    filteredExpenses\n  } = getFilteredData();\n\n  // Calcular métricas principales con datos filtrados\n  const totalRefuels = filteredRefuels.length;\n  const totalExpenses = filteredExpenses.length;\n  const totalFuelCost = filteredRefuels.reduce((sum, r) => sum + (r.coste_total || 0), 0);\n  const totalExpenseCost = filteredExpenses.reduce((sum, e) => sum + (e.coste || 0), 0);\n  const totalCost = totalFuelCost + totalExpenseCost;\n  const totalLiters = filteredRefuels.reduce((sum, r) => sum + (r.litros || 0), 0);\n\n  // Calcular consumo promedio con datos filtrados\n  let avgConsumption = 0;\n  if (filteredRefuels.length >= 2) {\n    const sortedRefuels = [...filteredRefuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales - sortedRefuels[0].kilometros_actuales;\n    if (totalKm > 0) {\n      avgConsumption = totalLiters / totalKm * 100;\n    }\n  }\n  const avgFuelPrice = totalLiters > 0 ? totalFuelCost / totalLiters : 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      fontWeight: \"bold\",\n      mb: 3,\n      children: \"Estad\\xEDsticas y An\\xE1lisis\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedVehicle,\n                onChange: e => setSelectedVehicle(e.target.value),\n                label: \"Veh\\xEDculo\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"Todos los veh\\xEDculos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: vehicle.id,\n                  children: vehicle.nombre\n                }, vehicle.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Per\\xEDodo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: dateRange,\n                onChange: e => setDateRange(e.target.value),\n                label: \"Per\\xEDodo\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"3months\",\n                  children: \"\\xDAltimos 3 meses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"6months\",\n                  children: \"\\xDAltimos 6 meses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"1year\",\n                  children: \"\\xDAltimo a\\xF1o\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"2years\",\n                  children: \"\\xDAltimos 2 a\\xF1os\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"Todo el historial\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Consumo Promedio\",\n          value: `${avgConsumption.toFixed(1)} L`,\n          subtitle: \"Por 100 km\",\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Precio Promedio\",\n          value: formatCurrency(avgFuelPrice),\n          subtitle: \"Por litro\",\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Gasto Total\",\n          value: formatCurrency(totalCost),\n          subtitle: `${formatCurrency(totalFuelCost)} combustible + ${formatCurrency(totalExpenseCost)} gastos`,\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Total Litros\",\n          value: `${totalLiters.toFixed(1)} L`,\n          subtitle: `En ${totalRefuels} repostajes`,\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Evoluci\\xF3n del Consumo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: chartData.consumption,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [`${value} L/100km`, 'Consumo'],\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"consumption\",\n                    stroke: \"#8884d8\",\n                    strokeWidth: 2,\n                    name: \"L/100km\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Costes Mensuales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                  data: chartData.costs,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [formatCurrency(value)],\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"Combustible\",\n                    stackId: \"1\",\n                    stroke: \"#82ca9d\",\n                    fill: \"#82ca9d\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"Gastos\",\n                    stackId: \"1\",\n                    stroke: \"#ffc658\",\n                    fill: \"#ffc658\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Distribuci\\xF3n de Gastos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(PieChart, {\n                  children: [/*#__PURE__*/_jsxDEV(Pie, {\n                    data: chartData.expensesByType,\n                    cx: \"50%\",\n                    cy: \"50%\",\n                    labelLine: false,\n                    label: ({\n                      name,\n                      percent\n                    }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                    outerRadius: 80,\n                    fill: \"#8884d8\",\n                    dataKey: \"value\",\n                    children: chartData.expensesByType.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                      fill: COLORS[index % COLORS.length]\n                    }, `cell-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [formatCurrency(value)]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Tendencias Generales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(BarChart, {\n                  data: chartData.monthlyTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: (value, name) => {\n                      if (name === 'Coste Total') return [formatCurrency(value), name];\n                      return [value, name];\n                    },\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                    dataKey: \"totalCost\",\n                    fill: \"#8884d8\",\n                    name: \"Coste Total (\\u20AC)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Resumen Estad\\xEDstico\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: totalRefuels\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Repostajes totales\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"success.main\",\n                children: totalExpenses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Gastos registrados\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"warning.main\",\n                children: formatCurrency(totalCost / (totalRefuels + totalExpenses) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Gasto promedio por registro\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"info.main\",\n                children: vehicles.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Veh\\xEDculos activos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mt: 4,\n      children: /*#__PURE__*/_jsxDEV(AdvancedAnalytics, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 7\n    }, this), vehicles.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      mt: 4,\n      children: /*#__PURE__*/_jsxDEV(VehicleComparison, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 5\n  }, this);\n};\n_s(Statistics, \"YEY60JuLroUDVtAbWOoU+6bOybQ=\", false, function () {\n  return [useApp];\n});\n_c2 = Statistics;\nexport default Statistics;\nvar _c, _c2;\n$RefreshReg$(_c, \"MetricCard\");\n$RefreshReg$(_c2, \"Statistics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Chip", "Divider", "Paper", "DatePicker", "Line<PERSON>hart", "Line", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ChartTooltip", "Legend", "ResponsiveContainer", "useApp", "format", "subMonths", "startOfMonth", "endOfMonth", "es", "VehicleComparison", "AdvancedAnalytics", "jsxDEV", "_jsxDEV", "COLORS", "MetricCard", "title", "value", "subtitle", "color", "trend", "children", "gutterBottom", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "mt", "label", "size", "includes", "_c", "Statistics", "_s", "vehicles", "refuels", "expenses", "loadStatistics", "statistics", "selectedVehicle", "setSelectedVehicle", "date<PERSON><PERSON><PERSON>", "setDateRange", "chartData", "setChartData", "consumption", "costs", "expensesByType", "monthlyTrends", "calculateStatistics", "console", "log", "length", "filteredRefuels", "filteredExpenses", "vehicleId", "parseInt", "filter", "r", "vehiculo_id", "e", "now", "Date", "startDate", "fecha", "calculateChartData", "consumptionByMonth", "sortedRefuels", "sort", "a", "b", "i", "current", "previous", "kmDiff", "kilometros_actuales", "liters", "litros", "month", "total", "count", "consumptionData", "Object", "values", "map", "item", "locale", "toFixed", "date", "localeCompare", "costsByMonth", "for<PERSON>ach", "cost", "coste_total", "coste", "type", "Combustible", "Gastos", "costsData", "Total", "expense", "tipo_gasto", "expensesData", "entries", "name", "parseFloat", "_consumptionData$find", "totalCost", "fuelCost", "expenseCost", "find", "c", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "getFilteredData", "totalRefuels", "totalExpenses", "totalFuelCost", "reduce", "sum", "totalExpenseCost", "totalLiters", "avgConsumption", "totalKm", "avgFuelPrice", "fontWeight", "mb", "sx", "container", "spacing", "alignItems", "xs", "sm", "md", "fullWidth", "onChange", "target", "vehicle", "id", "nombre", "lg", "height", "width", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "formatter", "labelStyle", "stroke", "strokeWidth", "stackId", "fill", "cx", "cy", "labelLine", "percent", "outerRadius", "entry", "index", "textAlign", "_c2", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Statistics/Statistics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Chip,\n  Divider,\n  Paper,\n} from '@mui/material';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip as ChartTooltip,\n  Legend,\n  ResponsiveContainer,\n} from 'recharts';\nimport { useApp } from '../../context/AppContext';\nimport { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport VehicleComparison from './VehicleComparison';\nimport AdvancedAnalytics from './AdvancedAnalytics';\n\n// Colores para los gráficos\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\n\n// Componente para métricas principales\nconst MetricCard = ({ title, value, subtitle, color = 'primary', trend }) => (\n  <Card>\n    <CardContent>\n      <Typography color=\"textSecondary\" gutterBottom variant=\"body2\">\n        {title}\n      </Typography>\n      <Typography variant=\"h4\" component=\"div\" color={color}>\n        {value}\n      </Typography>\n      {subtitle && (\n        <Typography variant=\"body2\" color=\"textSecondary\">\n          {subtitle}\n        </Typography>\n      )}\n      {trend && (\n        <Box mt={1}>\n          <Chip \n            label={trend} \n            size=\"small\" \n            color={trend.includes('+') ? 'success' : trend.includes('-') ? 'error' : 'default'}\n          />\n        </Box>\n      )}\n    </CardContent>\n  </Card>\n);\n\nconst Statistics = () => {\n  const { vehicles, refuels, expenses, loadStatistics, statistics } = useApp();\n  const [selectedVehicle, setSelectedVehicle] = useState('all');\n  const [dateRange, setDateRange] = useState('6months');\n  const [chartData, setChartData] = useState({\n    consumption: [],\n    costs: [],\n    expensesByType: [],\n    monthlyTrends: []\n  });\n\n  useEffect(() => {\n    calculateStatistics();\n  }, [refuels, expenses, selectedVehicle, dateRange]);\n\n  const calculateStatistics = () => {\n    console.log('🔄 Recalculating statistics with filters:', { selectedVehicle, dateRange });\n    console.log('📊 Total data available:', { refuels: refuels.length, expenses: expenses.length });\n    \n    let filteredRefuels = [...refuels]; // Crear copia para evitar mutación\n    let filteredExpenses = [...expenses];\n\n    // Filtrar por vehículo\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n      console.log('🚗 Filtered by vehicle:', vehicleId, { refuels: filteredRefuels.length, expenses: filteredExpenses.length });\n    }\n\n    // Filtrar por fecha\n    const now = new Date();\n    let startDate;\n    switch (dateRange) {\n      case '3months':\n        startDate = subMonths(now, 3);\n        break;\n      case '6months':\n        startDate = subMonths(now, 6);\n        break;\n      case '1year':\n        startDate = subMonths(now, 12);\n        break;\n      case '2years':\n        startDate = subMonths(now, 24);\n        break;\n      default:\n        startDate = new Date(0); // Todos los datos\n    }\n\n    if (dateRange !== 'all') {\n      filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);\n      filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);\n      console.log('📅 Filtered by date:', dateRange, { refuels: filteredRefuels.length, expenses: filteredExpenses.length });\n    }\n\n    // Calcular datos para gráficos\n    calculateChartData(filteredRefuels, filteredExpenses);\n  };\n\n  const calculateChartData = (refuels, expenses) => {\n    // 1. Datos de consumo por mes\n    const consumptionByMonth = {};\n    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    \n    for (let i = 1; i < sortedRefuels.length; i++) {\n      const current = sortedRefuels[i];\n      const previous = sortedRefuels[i - 1];\n      \n      if (current.vehiculo_id === previous.vehiculo_id) {\n        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;\n        const liters = current.litros;\n        \n        if (kmDiff > 0 && liters > 0) {\n          const consumption = (liters / kmDiff) * 100; // L/100km\n          const month = format(new Date(current.fecha), 'yyyy-MM');\n          \n          if (!consumptionByMonth[month]) {\n            consumptionByMonth[month] = { total: 0, count: 0, month };\n          }\n          consumptionByMonth[month].total += consumption;\n          consumptionByMonth[month].count += 1;\n        }\n      }\n    }\n\n    const consumptionData = Object.values(consumptionByMonth)\n      .map(item => ({\n        month: format(new Date(item.month + '-01'), 'MMM yyyy', { locale: es }),\n        consumption: (item.total / item.count).toFixed(1),\n        date: item.month\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n\n    // 2. Costes por mes\n    const costsByMonth = {};\n    \n    [...refuels, ...expenses].forEach(item => {\n      const month = format(new Date(item.fecha), 'yyyy-MM');\n      const cost = item.coste_total || item.coste || 0;\n      const type = item.litros ? 'Combustible' : 'Gastos';\n      \n      if (!costsByMonth[month]) {\n        costsByMonth[month] = { month, Combustible: 0, Gastos: 0, date: month };\n      }\n      costsByMonth[month][type] += cost;\n    });\n\n    const costsData = Object.values(costsByMonth)\n      .map(item => ({\n        ...item,\n        month: format(new Date(item.month + '-01'), 'MMM yyyy', { locale: es }),\n        Total: item.Combustible + item.Gastos\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n\n    // 3. Gastos por tipo\n    const expensesByType = {};\n    expenses.forEach(expense => {\n      const type = expense.tipo_gasto || 'Otros';\n      expensesByType[type] = (expensesByType[type] || 0) + (expense.coste || 0);\n    });\n\n    const expensesData = Object.entries(expensesByType)\n      .map(([name, value]) => ({ name, value: parseFloat(value.toFixed(2)) }))\n      .sort((a, b) => b.value - a.value);\n\n    // 4. Tendencias mensuales\n    const monthlyTrends = costsData.map(item => ({\n      month: item.month,\n      date: item.date,\n      totalCost: item.Total,\n      fuelCost: item.Combustible,\n      expenseCost: item.Gastos,\n      consumption: consumptionData.find(c => c.date === item.date)?.consumption || 0\n    }));\n\n    setChartData({\n      consumption: consumptionData,\n      costs: costsData,\n      expensesByType: expensesData,\n      monthlyTrends\n    });\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\r\n    }).format(amount || 0);\r\n  };\r\n\r\n  // Aplicar filtros para métricas principales\r\n  const getFilteredData = () => {\r\n    let filteredRefuels = [...refuels];\r\n    let filteredExpenses = [...expenses];\r\n\r\n    if (selectedVehicle !== 'all') {\r\n      const vehicleId = parseInt(selectedVehicle);\r\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\r\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\r\n    }\r\n\r\n    if (dateRange !== 'all') {\r\n      const now = new Date();\r\n      let startDate;\r\n      switch (dateRange) {\r\n        case '3months': startDate = subMonths(now, 3); break;\r\n        case '6months': startDate = subMonths(now, 6); break;\r\n        case '1year': startDate = subMonths(now, 12); break;\r\n        case '2years': startDate = subMonths(now, 24); break;\r\n        default: startDate = new Date(0);\r\n      }\r\n      \r\n      if (dateRange !== 'all') {\r\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);\r\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);\r\n      }\r\n    }\r\n\r\n    return { filteredRefuels, filteredExpenses };\r\n  };\r\n\r\n  const { filteredRefuels, filteredExpenses } = getFilteredData();\r\n\r\n  // Calcular métricas principales con datos filtrados\r\n  const totalRefuels = filteredRefuels.length;\r\n  const totalExpenses = filteredExpenses.length;\r\n  const totalFuelCost = filteredRefuels.reduce((sum, r) => sum + (r.coste_total || 0), 0);\r\n  const totalExpenseCost = filteredExpenses.reduce((sum, e) => sum + (e.coste || 0), 0);\r\n  const totalCost = totalFuelCost + totalExpenseCost;\r\n  const totalLiters = filteredRefuels.reduce((sum, r) => sum + (r.litros || 0), 0);\r\n  \r\n  // Calcular consumo promedio con datos filtrados\r\n  let avgConsumption = 0;\r\n  if (filteredRefuels.length >= 2) {\r\n    const sortedRefuels = [...filteredRefuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\r\n    const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales - \r\n                   sortedRefuels[0].kilometros_actuales;\r\n    if (totalKm > 0) {\r\n      avgConsumption = (totalLiters / totalKm * 100);\r\n    }\r\n  }\r\n\r\n  const avgFuelPrice = totalLiters > 0 ? totalFuelCost / totalLiters : 0;\r\n\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\" mb={3}>\r\n        Estadísticas y Análisis\r\n      </Typography>\r\n\r\n      {/* Filtros */}\r\n      <Card sx={{ mb: 3 }}>\r\n        <CardContent>\r\n          <Grid container spacing={2} alignItems=\"center\">\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <FormControl fullWidth>\r\n                <InputLabel>Vehículo</InputLabel>\r\n                <Select\r\n                  value={selectedVehicle}\r\n                  onChange={(e) => setSelectedVehicle(e.target.value)}\r\n                  label=\"Vehículo\"\r\n                >\r\n                  <MenuItem value=\"all\">Todos los vehículos</MenuItem>\r\n                  {vehicles.map((vehicle) => (\r\n                    <MenuItem key={vehicle.id} value={vehicle.id}>\r\n                      {vehicle.nombre}\r\n                    </MenuItem>\r\n                  ))}\r\n                </Select>\r\n              </FormControl>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <FormControl fullWidth>\r\n                <InputLabel>Período</InputLabel>\r\n                <Select\r\n                  value={dateRange}\r\n                  onChange={(e) => setDateRange(e.target.value)}\r\n                  label=\"Período\"\r\n                >\r\n                  <MenuItem value=\"3months\">Últimos 3 meses</MenuItem>\r\n                  <MenuItem value=\"6months\">Últimos 6 meses</MenuItem>\r\n                  <MenuItem value=\"1year\">Último año</MenuItem>\r\n                  <MenuItem value=\"2years\">Últimos 2 años</MenuItem>\r\n                  <MenuItem value=\"all\">Todo el historial</MenuItem>\r\n                </Select>\r\n              </FormControl>\r\n            </Grid>\r\n          </Grid>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Métricas principales */}\r\n      <Grid container spacing={3} mb={4}>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <MetricCard\r\n            title=\"Consumo Promedio\"\r\n            value={`${avgConsumption.toFixed(1)} L`}\r\n            subtitle=\"Por 100 km\"\r\n            color=\"primary\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <MetricCard\r\n            title=\"Precio Promedio\"\r\n            value={formatCurrency(avgFuelPrice)}\r\n            subtitle=\"Por litro\"\r\n            color=\"success\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <MetricCard\r\n            title=\"Gasto Total\"\r\n            value={formatCurrency(totalCost)}\r\n            subtitle={`${formatCurrency(totalFuelCost)} combustible + ${formatCurrency(totalExpenseCost)} gastos`}\r\n            color=\"warning\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <MetricCard\r\n            title=\"Total Litros\"\r\n            value={`${totalLiters.toFixed(1)} L`}\r\n            subtitle={`En ${totalRefuels} repostajes`}\r\n            color=\"info\"\r\n          />\r\n        </Grid>\r\n      </Grid>\r\n\r\n      {/* Gráficos */}\r\n      <Grid container spacing={3}>\r\n        {/* Gráfico de consumo */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Evolución del Consumo\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <LineChart data={chartData.consumption}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"month\" />\r\n                    <YAxis />\r\n                    <ChartTooltip \r\n                      formatter={(value) => [`${value} L/100km`, 'Consumo']}\r\n                      labelStyle={{ color: '#666' }}\r\n                    />\r\n                    <Legend />\r\n                    <Line \r\n                      type=\"monotone\" \r\n                      dataKey=\"consumption\" \r\n                      stroke=\"#8884d8\" \r\n                      strokeWidth={2}\r\n                      name=\"L/100km\"\r\n                    />\r\n                  </LineChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Gráfico de costes mensuales */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Costes Mensuales\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <AreaChart data={chartData.costs}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"month\" />\r\n                    <YAxis />\r\n                    <ChartTooltip \r\n                      formatter={(value) => [formatCurrency(value)]}\r\n                      labelStyle={{ color: '#666' }}\r\n                    />\r\n                    <Legend />\r\n                    <Area \r\n                      type=\"monotone\" \r\n                      dataKey=\"Combustible\" \r\n                      stackId=\"1\"\r\n                      stroke=\"#82ca9d\" \r\n                      fill=\"#82ca9d\" \r\n                    />\r\n                    <Area \r\n                      type=\"monotone\" \r\n                      dataKey=\"Gastos\" \r\n                      stackId=\"1\"\r\n                      stroke=\"#ffc658\" \r\n                      fill=\"#ffc658\" \r\n                    />\r\n                  </AreaChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Gráfico de gastos por tipo */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Distribución de Gastos\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <PieChart>\r\n                    <Pie\r\n                      data={chartData.expensesByType}\r\n                      cx=\"50%\"\r\n                      cy=\"50%\"\r\n                      labelLine={false}\r\n                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\r\n                      outerRadius={80}\r\n                      fill=\"#8884d8\"\r\n                      dataKey=\"value\"\r\n                    >\r\n                      {chartData.expensesByType.map((entry, index) => (\r\n                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\r\n                      ))}\r\n                    </Pie>\r\n                    <ChartTooltip formatter={(value) => [formatCurrency(value)]} />\r\n                  </PieChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Tendencias combinadas */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Tendencias Generales\r\n              </Typography>\r\n              <Box height={300}>\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <BarChart data={chartData.monthlyTrends}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"month\" />\r\n                    <YAxis />\r\n                    <ChartTooltip \r\n                      formatter={(value, name) => {\r\n                        if (name === 'Coste Total') return [formatCurrency(value), name];\r\n                        return [value, name];\r\n                      }}\r\n                      labelStyle={{ color: '#666' }}\r\n                    />\r\n                    <Legend />\r\n                    <Bar dataKey=\"totalCost\" fill=\"#8884d8\" name=\"Coste Total (€)\" />\r\n                  </BarChart>\r\n                </ResponsiveContainer>\r\n              </Box>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n\r\n      {/* Resumen estadístico */}\r\n      <Card sx={{ mt: 3 }}>\r\n        <CardContent>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            Resumen Estadístico\r\n          </Typography>\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"primary\">\r\n                  {totalRefuels}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Repostajes totales\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"success.main\">\r\n                  {totalExpenses}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Gastos registrados\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"warning.main\">\r\n                  {formatCurrency(totalCost / (totalRefuels + totalExpenses) || 0)}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Gasto promedio por registro\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"info.main\">\r\n                  {vehicles.length}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Vehículos activos\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Análisis avanzado */}\r\n      <Box mt={4}>\r\n        <AdvancedAnalytics />\r\n      </Box>\r\n\r\n      {/* Comparación de vehículos */}\r\n      {vehicles.length > 1 && (\r\n        <Box mt={4}>\r\n          <VehicleComparison />\r\n        </Box>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default Statistics;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,OAAO,EACPC,KAAK,QACA,eAAe;AACtB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SACEC,SAAS,EACTC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,IAAIC,YAAY,EACvBC,MAAM,EACNC,mBAAmB,QACd,UAAU;AACjB,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,UAAU,QAAQ,UAAU;AACtE,SAASC,EAAE,QAAQ,iBAAiB;AACpC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,iBAAiB,MAAM,qBAAqB;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;;AAEjF;AACA,MAAMC,UAAU,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,KAAK,GAAG,SAAS;EAAEC;AAAM,CAAC,kBACtEP,OAAA,CAACrC,IAAI;EAAA6C,QAAA,eACHR,OAAA,CAACpC,WAAW;IAAA4C,QAAA,gBACVR,OAAA,CAACtC,UAAU;MAAC4C,KAAK,EAAC,eAAe;MAACG,YAAY;MAACC,OAAO,EAAC,OAAO;MAAAF,QAAA,EAC3DL;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACbd,OAAA,CAACtC,UAAU;MAACgD,OAAO,EAAC,IAAI;MAACK,SAAS,EAAC,KAAK;MAACT,KAAK,EAAEA,KAAM;MAAAE,QAAA,EACnDJ;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EACZT,QAAQ,iBACPL,OAAA,CAACtC,UAAU;MAACgD,OAAO,EAAC,OAAO;MAACJ,KAAK,EAAC,eAAe;MAAAE,QAAA,EAC9CH;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACb,EACAP,KAAK,iBACJP,OAAA,CAACvC,GAAG;MAACuD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTR,OAAA,CAAC7B,IAAI;QACH8C,KAAK,EAAEV,KAAM;QACbW,IAAI,EAAC,OAAO;QACZZ,KAAK,EAAEC,KAAK,CAACY,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGZ,KAAK,CAACY,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG;MAAU;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACP;AAACM,EAAA,GAzBIlB,UAAU;AA2BhB,MAAMmB,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,cAAc;IAAEC;EAAW,CAAC,GAAGpC,MAAM,CAAC,CAAC;EAC5E,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuE,SAAS,EAAEC,YAAY,CAAC,GAAGxE,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC;IACzC2E,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF7E,SAAS,CAAC,MAAM;IACd8E,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACd,OAAO,EAAEC,QAAQ,EAAEG,eAAe,EAAEE,SAAS,CAAC,CAAC;EAEnD,MAAMQ,mBAAmB,GAAGA,CAAA,KAAM;IAChCC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MAAEZ,eAAe;MAAEE;IAAU,CAAC,CAAC;IACxFS,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MAAEhB,OAAO,EAAEA,OAAO,CAACiB,MAAM;MAAEhB,QAAQ,EAAEA,QAAQ,CAACgB;IAAO,CAAC,CAAC;IAE/F,IAAIC,eAAe,GAAG,CAAC,GAAGlB,OAAO,CAAC,CAAC,CAAC;IACpC,IAAImB,gBAAgB,GAAG,CAAC,GAAGlB,QAAQ,CAAC;;IAEpC;IACA,IAAIG,eAAe,KAAK,KAAK,EAAE;MAC7B,MAAMgB,SAAS,GAAGC,QAAQ,CAACjB,eAAe,CAAC;MAC3Cc,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,SAAS,CAAC;MAC1ED,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACD,WAAW,KAAKJ,SAAS,CAAC;MAC5EL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,SAAS,EAAE;QAAEpB,OAAO,EAAEkB,eAAe,CAACD,MAAM;QAAEhB,QAAQ,EAAEkB,gBAAgB,CAACF;MAAO,CAAC,CAAC;IAC3H;;IAEA;IACA,MAAMS,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAIC,SAAS;IACb,QAAQtB,SAAS;MACf,KAAK,SAAS;QACZsB,SAAS,GAAG3D,SAAS,CAACyD,GAAG,EAAE,CAAC,CAAC;QAC7B;MACF,KAAK,SAAS;QACZE,SAAS,GAAG3D,SAAS,CAACyD,GAAG,EAAE,CAAC,CAAC;QAC7B;MACF,KAAK,OAAO;QACVE,SAAS,GAAG3D,SAAS,CAACyD,GAAG,EAAE,EAAE,CAAC;QAC9B;MACF,KAAK,QAAQ;QACXE,SAAS,GAAG3D,SAAS,CAACyD,GAAG,EAAE,EAAE,CAAC;QAC9B;MACF;QACEE,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC,CAAC;MAAE;IAC7B;IAEA,IAAIrB,SAAS,KAAK,KAAK,EAAE;MACvBY,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAI,IAAII,IAAI,CAACJ,CAAC,CAACM,KAAK,CAAC,IAAID,SAAS,CAAC;MAC7ET,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAI,IAAIE,IAAI,CAACF,CAAC,CAACI,KAAK,CAAC,IAAID,SAAS,CAAC;MAC/Eb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEV,SAAS,EAAE;QAAEN,OAAO,EAAEkB,eAAe,CAACD,MAAM;QAAEhB,QAAQ,EAAEkB,gBAAgB,CAACF;MAAO,CAAC,CAAC;IACxH;;IAEA;IACAa,kBAAkB,CAACZ,eAAe,EAAEC,gBAAgB,CAAC;EACvD,CAAC;EAED,MAAMW,kBAAkB,GAAGA,CAAC9B,OAAO,EAAEC,QAAQ,KAAK;IAChD;IACA,MAAM8B,kBAAkB,GAAG,CAAC,CAAC;IAC7B,MAAMC,aAAa,GAAG,CAAC,GAAGhC,OAAO,CAAC,CAACiC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIR,IAAI,CAACO,CAAC,CAACL,KAAK,CAAC,GAAG,IAAIF,IAAI,CAACQ,CAAC,CAACN,KAAK,CAAC,CAAC;IAExF,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,aAAa,CAACf,MAAM,EAAEmB,CAAC,EAAE,EAAE;MAC7C,MAAMC,OAAO,GAAGL,aAAa,CAACI,CAAC,CAAC;MAChC,MAAME,QAAQ,GAAGN,aAAa,CAACI,CAAC,GAAG,CAAC,CAAC;MAErC,IAAIC,OAAO,CAACb,WAAW,KAAKc,QAAQ,CAACd,WAAW,EAAE;QAChD,MAAMe,MAAM,GAAGF,OAAO,CAACG,mBAAmB,GAAGF,QAAQ,CAACE,mBAAmB;QACzE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,MAAM;QAE7B,IAAIH,MAAM,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM/B,WAAW,GAAI+B,MAAM,GAAGF,MAAM,GAAI,GAAG,CAAC,CAAC;UAC7C,MAAMI,KAAK,GAAG3E,MAAM,CAAC,IAAI2D,IAAI,CAACU,OAAO,CAACR,KAAK,CAAC,EAAE,SAAS,CAAC;UAExD,IAAI,CAACE,kBAAkB,CAACY,KAAK,CAAC,EAAE;YAC9BZ,kBAAkB,CAACY,KAAK,CAAC,GAAG;cAAEC,KAAK,EAAE,CAAC;cAAEC,KAAK,EAAE,CAAC;cAAEF;YAAM,CAAC;UAC3D;UACAZ,kBAAkB,CAACY,KAAK,CAAC,CAACC,KAAK,IAAIlC,WAAW;UAC9CqB,kBAAkB,CAACY,KAAK,CAAC,CAACE,KAAK,IAAI,CAAC;QACtC;MACF;IACF;IAEA,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAACjB,kBAAkB,CAAC,CACtDkB,GAAG,CAACC,IAAI,KAAK;MACZP,KAAK,EAAE3E,MAAM,CAAC,IAAI2D,IAAI,CAACuB,IAAI,CAACP,KAAK,GAAG,KAAK,CAAC,EAAE,UAAU,EAAE;QAAEQ,MAAM,EAAE/E;MAAG,CAAC,CAAC;MACvEsC,WAAW,EAAE,CAACwC,IAAI,CAACN,KAAK,GAAGM,IAAI,CAACL,KAAK,EAAEO,OAAO,CAAC,CAAC,CAAC;MACjDC,IAAI,EAAEH,IAAI,CAACP;IACb,CAAC,CAAC,CAAC,CACFV,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACmB,IAAI,CAACC,aAAa,CAACnB,CAAC,CAACkB,IAAI,CAAC,CAAC;;IAE/C;IACA,MAAME,YAAY,GAAG,CAAC,CAAC;IAEvB,CAAC,GAAGvD,OAAO,EAAE,GAAGC,QAAQ,CAAC,CAACuD,OAAO,CAACN,IAAI,IAAI;MACxC,MAAMP,KAAK,GAAG3E,MAAM,CAAC,IAAI2D,IAAI,CAACuB,IAAI,CAACrB,KAAK,CAAC,EAAE,SAAS,CAAC;MACrD,MAAM4B,IAAI,GAAGP,IAAI,CAACQ,WAAW,IAAIR,IAAI,CAACS,KAAK,IAAI,CAAC;MAChD,MAAMC,IAAI,GAAGV,IAAI,CAACR,MAAM,GAAG,aAAa,GAAG,QAAQ;MAEnD,IAAI,CAACa,YAAY,CAACZ,KAAK,CAAC,EAAE;QACxBY,YAAY,CAACZ,KAAK,CAAC,GAAG;UAAEA,KAAK;UAAEkB,WAAW,EAAE,CAAC;UAAEC,MAAM,EAAE,CAAC;UAAET,IAAI,EAAEV;QAAM,CAAC;MACzE;MACAY,YAAY,CAACZ,KAAK,CAAC,CAACiB,IAAI,CAAC,IAAIH,IAAI;IACnC,CAAC,CAAC;IAEF,MAAMM,SAAS,GAAGhB,MAAM,CAACC,MAAM,CAACO,YAAY,CAAC,CAC1CN,GAAG,CAACC,IAAI,KAAK;MACZ,GAAGA,IAAI;MACPP,KAAK,EAAE3E,MAAM,CAAC,IAAI2D,IAAI,CAACuB,IAAI,CAACP,KAAK,GAAG,KAAK,CAAC,EAAE,UAAU,EAAE;QAAEQ,MAAM,EAAE/E;MAAG,CAAC,CAAC;MACvE4F,KAAK,EAAEd,IAAI,CAACW,WAAW,GAAGX,IAAI,CAACY;IACjC,CAAC,CAAC,CAAC,CACF7B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACmB,IAAI,CAACC,aAAa,CAACnB,CAAC,CAACkB,IAAI,CAAC,CAAC;;IAE/C;IACA,MAAMzC,cAAc,GAAG,CAAC,CAAC;IACzBX,QAAQ,CAACuD,OAAO,CAACS,OAAO,IAAI;MAC1B,MAAML,IAAI,GAAGK,OAAO,CAACC,UAAU,IAAI,OAAO;MAC1CtD,cAAc,CAACgD,IAAI,CAAC,GAAG,CAAChD,cAAc,CAACgD,IAAI,CAAC,IAAI,CAAC,KAAKK,OAAO,CAACN,KAAK,IAAI,CAAC,CAAC;IAC3E,CAAC,CAAC;IAEF,MAAMQ,YAAY,GAAGpB,MAAM,CAACqB,OAAO,CAACxD,cAAc,CAAC,CAChDqC,GAAG,CAAC,CAAC,CAACoB,IAAI,EAAEzF,KAAK,CAAC,MAAM;MAAEyF,IAAI;MAAEzF,KAAK,EAAE0F,UAAU,CAAC1F,KAAK,CAACwE,OAAO,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC,CACvEnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACvD,KAAK,GAAGsD,CAAC,CAACtD,KAAK,CAAC;;IAEpC;IACA,MAAMiC,aAAa,GAAGkD,SAAS,CAACd,GAAG,CAACC,IAAI;MAAA,IAAAqB,qBAAA;MAAA,OAAK;QAC3C5B,KAAK,EAAEO,IAAI,CAACP,KAAK;QACjBU,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfmB,SAAS,EAAEtB,IAAI,CAACc,KAAK;QACrBS,QAAQ,EAAEvB,IAAI,CAACW,WAAW;QAC1Ba,WAAW,EAAExB,IAAI,CAACY,MAAM;QACxBpD,WAAW,EAAE,EAAA6D,qBAAA,GAAAzB,eAAe,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,KAAKH,IAAI,CAACG,IAAI,CAAC,cAAAkB,qBAAA,uBAA/CA,qBAAA,CAAiD7D,WAAW,KAAI;MAC/E,CAAC;IAAA,CAAC,CAAC;IAEHD,YAAY,CAAC;MACXC,WAAW,EAAEoC,eAAe;MAC5BnC,KAAK,EAAEoD,SAAS;MAChBnD,cAAc,EAAEuD,YAAY;MAC5BtD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgE,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAClH,MAAM,CAAC8G,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIjE,eAAe,GAAG,CAAC,GAAGlB,OAAO,CAAC;IAClC,IAAImB,gBAAgB,GAAG,CAAC,GAAGlB,QAAQ,CAAC;IAEpC,IAAIG,eAAe,KAAK,KAAK,EAAE;MAC7B,MAAMgB,SAAS,GAAGC,QAAQ,CAACjB,eAAe,CAAC;MAC3Cc,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,SAAS,CAAC;MAC1ED,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACD,WAAW,KAAKJ,SAAS,CAAC;IAC9E;IAEA,IAAId,SAAS,KAAK,KAAK,EAAE;MACvB,MAAMoB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtB,IAAIC,SAAS;MACb,QAAQtB,SAAS;QACf,KAAK,SAAS;UAAEsB,SAAS,GAAG3D,SAAS,CAACyD,GAAG,EAAE,CAAC,CAAC;UAAE;QAC/C,KAAK,SAAS;UAAEE,SAAS,GAAG3D,SAAS,CAACyD,GAAG,EAAE,CAAC,CAAC;UAAE;QAC/C,KAAK,OAAO;UAAEE,SAAS,GAAG3D,SAAS,CAACyD,GAAG,EAAE,EAAE,CAAC;UAAE;QAC9C,KAAK,QAAQ;UAAEE,SAAS,GAAG3D,SAAS,CAACyD,GAAG,EAAE,EAAE,CAAC;UAAE;QAC/C;UAASE,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC,CAAC;MAClC;MAEA,IAAIrB,SAAS,KAAK,KAAK,EAAE;QACvBY,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAI,IAAII,IAAI,CAACJ,CAAC,CAACM,KAAK,CAAC,IAAID,SAAS,CAAC;QAC7ET,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAI,IAAIE,IAAI,CAACF,CAAC,CAACI,KAAK,CAAC,IAAID,SAAS,CAAC;MACjF;IACF;IAEA,OAAO;MAAEV,eAAe;MAAEC;IAAiB,CAAC;EAC9C,CAAC;EAED,MAAM;IAAED,eAAe;IAAEC;EAAiB,CAAC,GAAGgE,eAAe,CAAC,CAAC;;EAE/D;EACA,MAAMC,YAAY,GAAGlE,eAAe,CAACD,MAAM;EAC3C,MAAMoE,aAAa,GAAGlE,gBAAgB,CAACF,MAAM;EAC7C,MAAMqE,aAAa,GAAGpE,eAAe,CAACqE,MAAM,CAAC,CAACC,GAAG,EAAEjE,CAAC,KAAKiE,GAAG,IAAIjE,CAAC,CAACmC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACvF,MAAM+B,gBAAgB,GAAGtE,gBAAgB,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAE/D,CAAC,KAAK+D,GAAG,IAAI/D,CAAC,CAACkC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF,MAAMa,SAAS,GAAGc,aAAa,GAAGG,gBAAgB;EAClD,MAAMC,WAAW,GAAGxE,eAAe,CAACqE,MAAM,CAAC,CAACC,GAAG,EAAEjE,CAAC,KAAKiE,GAAG,IAAIjE,CAAC,CAACmB,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEhF;EACA,IAAIiD,cAAc,GAAG,CAAC;EACtB,IAAIzE,eAAe,CAACD,MAAM,IAAI,CAAC,EAAE;IAC/B,MAAMe,aAAa,GAAG,CAAC,GAAGd,eAAe,CAAC,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIR,IAAI,CAACO,CAAC,CAACL,KAAK,CAAC,GAAG,IAAIF,IAAI,CAACQ,CAAC,CAACN,KAAK,CAAC,CAAC;IAChG,MAAM+D,OAAO,GAAG5D,aAAa,CAACA,aAAa,CAACf,MAAM,GAAG,CAAC,CAAC,CAACuB,mBAAmB,GAC5DR,aAAa,CAAC,CAAC,CAAC,CAACQ,mBAAmB;IACnD,IAAIoD,OAAO,GAAG,CAAC,EAAE;MACfD,cAAc,GAAID,WAAW,GAAGE,OAAO,GAAG,GAAI;IAChD;EACF;EAEA,MAAMC,YAAY,GAAGH,WAAW,GAAG,CAAC,GAAGJ,aAAa,GAAGI,WAAW,GAAG,CAAC;EAEtE,oBACElH,OAAA,CAACvC,GAAG;IAAA+C,QAAA,gBACFR,OAAA,CAACtC,UAAU;MAACgD,OAAO,EAAC,IAAI;MAACK,SAAS,EAAC,IAAI;MAACuG,UAAU,EAAC,MAAM;MAACC,EAAE,EAAE,CAAE;MAAA/G,QAAA,EAAC;IAEjE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbd,OAAA,CAACrC,IAAI;MAAC6J,EAAE,EAAE;QAAED,EAAE,EAAE;MAAE,CAAE;MAAA/G,QAAA,eAClBR,OAAA,CAACpC,WAAW;QAAA4C,QAAA,eACVR,OAAA,CAACnC,IAAI;UAAC4J,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAnH,QAAA,gBAC7CR,OAAA,CAACnC,IAAI;YAAC6G,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAtH,QAAA,eAC9BR,OAAA,CAAClC,WAAW;cAACiK,SAAS;cAAAvH,QAAA,gBACpBR,OAAA,CAACjC,UAAU;gBAAAyC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCd,OAAA,CAAChC,MAAM;gBACLoC,KAAK,EAAEwB,eAAgB;gBACvBoG,QAAQ,EAAG/E,CAAC,IAAKpB,kBAAkB,CAACoB,CAAC,CAACgF,MAAM,CAAC7H,KAAK,CAAE;gBACpDa,KAAK,EAAC,aAAU;gBAAAT,QAAA,gBAEhBR,OAAA,CAAC/B,QAAQ;kBAACmC,KAAK,EAAC,KAAK;kBAAAI,QAAA,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EACnDS,QAAQ,CAACkD,GAAG,CAAEyD,OAAO,iBACpBlI,OAAA,CAAC/B,QAAQ;kBAAkBmC,KAAK,EAAE8H,OAAO,CAACC,EAAG;kBAAA3H,QAAA,EAC1C0H,OAAO,CAACE;gBAAM,GADFF,OAAO,CAACC,EAAE;kBAAAxH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPd,OAAA,CAACnC,IAAI;YAAC6G,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAtH,QAAA,eAC9BR,OAAA,CAAClC,WAAW;cAACiK,SAAS;cAAAvH,QAAA,gBACpBR,OAAA,CAACjC,UAAU;gBAAAyC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChCd,OAAA,CAAChC,MAAM;gBACLoC,KAAK,EAAE0B,SAAU;gBACjBkG,QAAQ,EAAG/E,CAAC,IAAKlB,YAAY,CAACkB,CAAC,CAACgF,MAAM,CAAC7H,KAAK,CAAE;gBAC9Ca,KAAK,EAAC,YAAS;gBAAAT,QAAA,gBAEfR,OAAA,CAAC/B,QAAQ;kBAACmC,KAAK,EAAC,SAAS;kBAAAI,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDd,OAAA,CAAC/B,QAAQ;kBAACmC,KAAK,EAAC,SAAS;kBAAAI,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDd,OAAA,CAAC/B,QAAQ;kBAACmC,KAAK,EAAC,OAAO;kBAAAI,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC7Cd,OAAA,CAAC/B,QAAQ;kBAACmC,KAAK,EAAC,QAAQ;kBAAAI,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClDd,OAAA,CAAC/B,QAAQ;kBAACmC,KAAK,EAAC,KAAK;kBAAAI,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPd,OAAA,CAACnC,IAAI;MAAC4J,SAAS;MAACC,OAAO,EAAE,CAAE;MAACH,EAAE,EAAE,CAAE;MAAA/G,QAAA,gBAChCR,OAAA,CAACnC,IAAI;QAAC6G,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAtH,QAAA,eAC9BR,OAAA,CAACE,UAAU;UACTC,KAAK,EAAC,kBAAkB;UACxBC,KAAK,EAAE,GAAG+G,cAAc,CAACvC,OAAO,CAAC,CAAC,CAAC,IAAK;UACxCvE,QAAQ,EAAC,YAAY;UACrBC,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPd,OAAA,CAACnC,IAAI;QAAC6G,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAtH,QAAA,eAC9BR,OAAA,CAACE,UAAU;UACTC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEiG,cAAc,CAACgB,YAAY,CAAE;UACpChH,QAAQ,EAAC,WAAW;UACpBC,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPd,OAAA,CAACnC,IAAI;QAAC6G,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAtH,QAAA,eAC9BR,OAAA,CAACE,UAAU;UACTC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAEiG,cAAc,CAACL,SAAS,CAAE;UACjC3F,QAAQ,EAAE,GAAGgG,cAAc,CAACS,aAAa,CAAC,kBAAkBT,cAAc,CAACY,gBAAgB,CAAC,SAAU;UACtG3G,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPd,OAAA,CAACnC,IAAI;QAAC6G,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAtH,QAAA,eAC9BR,OAAA,CAACE,UAAU;UACTC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAE,GAAG8G,WAAW,CAACtC,OAAO,CAAC,CAAC,CAAC,IAAK;UACrCvE,QAAQ,EAAE,MAAMuG,YAAY,aAAc;UAC1CtG,KAAK,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPd,OAAA,CAACnC,IAAI;MAAC4J,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlH,QAAA,gBAEzBR,OAAA,CAACnC,IAAI;QAAC6G,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAA7H,QAAA,eACvBR,OAAA,CAACrC,IAAI;UAAA6C,QAAA,eACHR,OAAA,CAACpC,WAAW;YAAA4C,QAAA,gBACVR,OAAA,CAACtC,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbd,OAAA,CAACvC,GAAG;cAAC6K,MAAM,EAAE,GAAI;cAAA9H,QAAA,eACfR,OAAA,CAACV,mBAAmB;gBAACiJ,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAA9H,QAAA,eAC7CR,OAAA,CAACzB,SAAS;kBAACiK,IAAI,EAAExG,SAAS,CAACE,WAAY;kBAAA1B,QAAA,gBACrCR,OAAA,CAACd,aAAa;oBAACuJ,eAAe,EAAC;kBAAK;oBAAA9H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCd,OAAA,CAAChB,KAAK;oBAAC0J,OAAO,EAAC;kBAAO;oBAAA/H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBd,OAAA,CAACf,KAAK;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTd,OAAA,CAACZ,YAAY;oBACXuJ,SAAS,EAAGvI,KAAK,IAAK,CAAC,GAAGA,KAAK,UAAU,EAAE,SAAS,CAAE;oBACtDwI,UAAU,EAAE;sBAAEtI,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFd,OAAA,CAACX,MAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVd,OAAA,CAACxB,IAAI;oBACH4G,IAAI,EAAC,UAAU;oBACfsD,OAAO,EAAC,aAAa;oBACrBG,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfjD,IAAI,EAAC;kBAAS;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPd,OAAA,CAACnC,IAAI;QAAC6G,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAA7H,QAAA,eACvBR,OAAA,CAACrC,IAAI;UAAA6C,QAAA,eACHR,OAAA,CAACpC,WAAW;YAAA4C,QAAA,gBACVR,OAAA,CAACtC,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbd,OAAA,CAACvC,GAAG;cAAC6K,MAAM,EAAE,GAAI;cAAA9H,QAAA,eACfR,OAAA,CAACV,mBAAmB;gBAACiJ,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAA9H,QAAA,eAC7CR,OAAA,CAACvB,SAAS;kBAAC+J,IAAI,EAAExG,SAAS,CAACG,KAAM;kBAAA3B,QAAA,gBAC/BR,OAAA,CAACd,aAAa;oBAACuJ,eAAe,EAAC;kBAAK;oBAAA9H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCd,OAAA,CAAChB,KAAK;oBAAC0J,OAAO,EAAC;kBAAO;oBAAA/H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBd,OAAA,CAACf,KAAK;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTd,OAAA,CAACZ,YAAY;oBACXuJ,SAAS,EAAGvI,KAAK,IAAK,CAACiG,cAAc,CAACjG,KAAK,CAAC,CAAE;oBAC9CwI,UAAU,EAAE;sBAAEtI,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFd,OAAA,CAACX,MAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVd,OAAA,CAACtB,IAAI;oBACH0G,IAAI,EAAC,UAAU;oBACfsD,OAAO,EAAC,aAAa;oBACrBK,OAAO,EAAC,GAAG;oBACXF,MAAM,EAAC,SAAS;oBAChBG,IAAI,EAAC;kBAAS;oBAAArI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFd,OAAA,CAACtB,IAAI;oBACH0G,IAAI,EAAC,UAAU;oBACfsD,OAAO,EAAC,QAAQ;oBAChBK,OAAO,EAAC,GAAG;oBACXF,MAAM,EAAC,SAAS;oBAChBG,IAAI,EAAC;kBAAS;oBAAArI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPd,OAAA,CAACnC,IAAI;QAAC6G,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAA7H,QAAA,eACvBR,OAAA,CAACrC,IAAI;UAAA6C,QAAA,eACHR,OAAA,CAACpC,WAAW;YAAA4C,QAAA,gBACVR,OAAA,CAACtC,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbd,OAAA,CAACvC,GAAG;cAAC6K,MAAM,EAAE,GAAI;cAAA9H,QAAA,eACfR,OAAA,CAACV,mBAAmB;gBAACiJ,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAA9H,QAAA,eAC7CR,OAAA,CAACnB,QAAQ;kBAAA2B,QAAA,gBACPR,OAAA,CAAClB,GAAG;oBACF0J,IAAI,EAAExG,SAAS,CAACI,cAAe;oBAC/B6G,EAAE,EAAC,KAAK;oBACRC,EAAE,EAAC,KAAK;oBACRC,SAAS,EAAE,KAAM;oBACjBlI,KAAK,EAAEA,CAAC;sBAAE4E,IAAI;sBAAEuD;oBAAQ,CAAC,KAAK,GAAGvD,IAAI,IAAI,CAACuD,OAAO,GAAG,GAAG,EAAExE,OAAO,CAAC,CAAC,CAAC,GAAI;oBACvEyE,WAAW,EAAE,EAAG;oBAChBL,IAAI,EAAC,SAAS;oBACdN,OAAO,EAAC,OAAO;oBAAAlI,QAAA,EAEdwB,SAAS,CAACI,cAAc,CAACqC,GAAG,CAAC,CAAC6E,KAAK,EAAEC,KAAK,kBACzCvJ,OAAA,CAACjB,IAAI;sBAAuBiK,IAAI,EAAE/I,MAAM,CAACsJ,KAAK,GAAGtJ,MAAM,CAACwC,MAAM;oBAAE,GAArD,QAAQ8G,KAAK,EAAE;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAwC,CACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNd,OAAA,CAACZ,YAAY;oBAACuJ,SAAS,EAAGvI,KAAK,IAAK,CAACiG,cAAc,CAACjG,KAAK,CAAC;kBAAE;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPd,OAAA,CAACnC,IAAI;QAAC6G,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAA7H,QAAA,eACvBR,OAAA,CAACrC,IAAI;UAAA6C,QAAA,eACHR,OAAA,CAACpC,WAAW;YAAA4C,QAAA,gBACVR,OAAA,CAACtC,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbd,OAAA,CAACvC,GAAG;cAAC6K,MAAM,EAAE,GAAI;cAAA9H,QAAA,eACfR,OAAA,CAACV,mBAAmB;gBAACiJ,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAA9H,QAAA,eAC7CR,OAAA,CAACrB,QAAQ;kBAAC6J,IAAI,EAAExG,SAAS,CAACK,aAAc;kBAAA7B,QAAA,gBACtCR,OAAA,CAACd,aAAa;oBAACuJ,eAAe,EAAC;kBAAK;oBAAA9H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCd,OAAA,CAAChB,KAAK;oBAAC0J,OAAO,EAAC;kBAAO;oBAAA/H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBd,OAAA,CAACf,KAAK;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTd,OAAA,CAACZ,YAAY;oBACXuJ,SAAS,EAAEA,CAACvI,KAAK,EAAEyF,IAAI,KAAK;sBAC1B,IAAIA,IAAI,KAAK,aAAa,EAAE,OAAO,CAACQ,cAAc,CAACjG,KAAK,CAAC,EAAEyF,IAAI,CAAC;sBAChE,OAAO,CAACzF,KAAK,EAAEyF,IAAI,CAAC;oBACtB,CAAE;oBACF+C,UAAU,EAAE;sBAAEtI,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFd,OAAA,CAACX,MAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVd,OAAA,CAACpB,GAAG;oBAAC8J,OAAO,EAAC,WAAW;oBAACM,IAAI,EAAC,SAAS;oBAACnD,IAAI,EAAC;kBAAiB;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPd,OAAA,CAACrC,IAAI;MAAC6J,EAAE,EAAE;QAAExG,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eAClBR,OAAA,CAACpC,WAAW;QAAA4C,QAAA,gBACVR,OAAA,CAACtC,UAAU;UAACgD,OAAO,EAAC,IAAI;UAACD,YAAY;UAAAD,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbd,OAAA,CAACnC,IAAI;UAAC4J,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAlH,QAAA,gBACzBR,OAAA,CAACnC,IAAI;YAAC6G,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAtH,QAAA,eAC9BR,OAAA,CAACvC,GAAG;cAAC+L,SAAS,EAAC,QAAQ;cAAAhJ,QAAA,gBACrBR,OAAA,CAACtC,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,SAAS;gBAAAE,QAAA,EACrCoG;cAAY;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACbd,OAAA,CAACtC,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPd,OAAA,CAACnC,IAAI;YAAC6G,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAtH,QAAA,eAC9BR,OAAA,CAACvC,GAAG;cAAC+L,SAAS,EAAC,QAAQ;cAAAhJ,QAAA,gBACrBR,OAAA,CAACtC,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,cAAc;gBAAAE,QAAA,EAC1CqG;cAAa;gBAAAlG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbd,OAAA,CAACtC,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPd,OAAA,CAACnC,IAAI;YAAC6G,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAtH,QAAA,eAC9BR,OAAA,CAACvC,GAAG;cAAC+L,SAAS,EAAC,QAAQ;cAAAhJ,QAAA,gBACrBR,OAAA,CAACtC,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,cAAc;gBAAAE,QAAA,EAC1C6F,cAAc,CAACL,SAAS,IAAIY,YAAY,GAAGC,aAAa,CAAC,IAAI,CAAC;cAAC;gBAAAlG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACbd,OAAA,CAACtC,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPd,OAAA,CAACnC,IAAI;YAAC6G,IAAI;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAtH,QAAA,eAC9BR,OAAA,CAACvC,GAAG;cAAC+L,SAAS,EAAC,QAAQ;cAAAhJ,QAAA,gBACrBR,OAAA,CAACtC,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,WAAW;gBAAAE,QAAA,EACvCe,QAAQ,CAACkB;cAAM;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACbd,OAAA,CAACtC,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPd,OAAA,CAACvC,GAAG;MAACuD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTR,OAAA,CAACF,iBAAiB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,EAGLS,QAAQ,CAACkB,MAAM,GAAG,CAAC,iBAClBzC,OAAA,CAACvC,GAAG;MAACuD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTR,OAAA,CAACH,iBAAiB;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACQ,EAAA,CAxeID,UAAU;EAAA,QACsD9B,MAAM;AAAA;AAAAkK,GAAA,GADtEpI,UAAU;AA0ehB,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAAqI,GAAA;AAAAC,YAAA,CAAAtI,EAAA;AAAAsI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}