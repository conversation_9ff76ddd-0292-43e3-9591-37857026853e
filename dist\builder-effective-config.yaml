directories:
  output: dist
  buildResources: assets
files:
  - filter:
      - build/**/*
      - build/**/*
      - public/electron.js
      - public/preload.js
      - server/**/*
      - node_modules/**/*
extraMetadata:
  main: build/electron.js
appId: com.repostajes.manager
productName: Repostajes Manager
extraResources:
  - from: server
    to: server
win:
  target:
    target: portable
    arch:
      - x64
  icon: public/favicon.ico
portable:
  artifactName: ${productName}-${version}-portable.exe
publish:
  provider: github
  owner: repostajes-manager
  repo: app
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
extends: react-cra
electronVersion: 28.3.3
