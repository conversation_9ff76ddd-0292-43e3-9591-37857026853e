const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Iniciando construcción de aplicación portable...');

async function buildPortable() {
  try {
    // 1. Limpiar directorios anteriores
    console.log('🧹 Limpiando directorios...');

    // Limpiar build
    if (fs.existsSync('build')) {
      try {
        fs.rmSync('build', { recursive: true, force: true });
      } catch (error) {
        console.log('⚠️ No se pudo limpiar completamente el directorio build, continuando...');
      }
    }

    // Limpiar dist con reintentos
    if (fs.existsSync('dist')) {
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts) {
        try {
          fs.rmSync('dist', { recursive: true, force: true });
          break;
        } catch (error) {
          attempts++;
          if (attempts >= maxAttempts) {
            console.log('⚠️ No se pudo limpiar completamente el directorio dist.');
            console.log('💡 Cierra cualquier instancia de la aplicación y vuelve a intentar.');
            console.log('🔄 Continuando con la construcción...');
          } else {
            console.log(`⏳ Reintentando limpiar dist (${attempts}/${maxAttempts})...`);
            // Esperar un poco antes del siguiente intento
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      }
    }

    // 2. Construir la aplicación React
    console.log('⚛️ Construyendo aplicación React...');
    execSync('npm run build', { stdio: 'inherit' });

    // 3. Construir la aplicación Electron
    console.log('🖥️ Construyendo aplicación Electron...');
    execSync('npm run build-electron', { stdio: 'inherit' });

    console.log('✅ ¡Construcción completada!');
    console.log('📁 El ejecutable portable se encuentra en la carpeta "dist"');
    console.log('💡 Puedes ejecutar el archivo .exe desde cualquier ubicación');
    console.log('💾 La base de datos se creará automáticamente en la misma carpeta del ejecutable');

  } catch (error) {
    console.error('❌ Error durante la construcción:', error.message);
    process.exit(1);
  }
}

// Ejecutar la función
buildPortable();
