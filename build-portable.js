const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Iniciando construcción de aplicación portable...');

try {
  // 1. Limpiar directorios anteriores
  console.log('🧹 Limpiando directorios...');
  if (fs.existsSync('build')) {
    fs.rmSync('build', { recursive: true, force: true });
  }
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // 2. Construir la aplicación React
  console.log('⚛️ Construyendo aplicación React...');
  execSync('npm run build', { stdio: 'inherit' });

  // 3. Construir la aplicación Electron
  console.log('🖥️ Construyendo aplicación Electron...');
  execSync('npm run build-electron', { stdio: 'inherit' });

  console.log('✅ ¡Construcción completada!');
  console.log('📁 El ejecutable portable se encuentra en la carpeta "dist"');
  console.log('💡 Puedes ejecutar el archivo .exe desde cualquier ubicación');
  console.log('💾 La base de datos se creará automáticamente en la misma carpeta del ejecutable');

} catch (error) {
  console.error('❌ Error durante la construcción:', error.message);
  process.exit(1);
}
