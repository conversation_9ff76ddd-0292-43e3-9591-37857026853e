// Sistema de notificaciones para la aplicación

// Tipos de notificaciones
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// Función para mostrar notificación del sistema (si está disponible)
export function showSystemNotification(title, body, options = {}) {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      body,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      ...options
    });

    // Auto-cerrar después de 5 segundos
    setTimeout(() => {
      notification.close();
    }, 5000);

    return notification;
  }
  return null;
}

// Función para solicitar permisos de notificación
export async function requestNotificationPermission() {
  if ('Notification' in window) {
    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }
  return false;
}

// Función para verificar si las notificaciones están soportadas
export function areNotificationsSupported() {
  return 'Notification' in window;
}

// Función para verificar si las notificaciones están permitidas
export function areNotificationsAllowed() {
  return 'Notification' in window && Notification.permission === 'granted';
}

// Notificaciones específicas de la aplicación
export const AppNotifications = {
  // Notificaciones de vehículos
  vehicleAdded: (vehicleName) => {
    showSystemNotification(
      'Vehículo Agregado',
      `${vehicleName} ha sido agregado exitosamente.`,
      { tag: 'vehicle-added' }
    );
  },

  // Notificaciones de repostajes
  refuelAdded: (vehicleName, amount) => {
    showSystemNotification(
      'Repostaje Registrado',
      `Repostaje de ${amount}€ registrado para ${vehicleName}.`,
      { tag: 'refuel-added' }
    );
  },

  // Notificaciones de gastos
  expenseAdded: (vehicleName, amount) => {
    showSystemNotification(
      'Gasto Registrado',
      `Gasto de ${amount}€ registrado para ${vehicleName}.`,
      { tag: 'expense-added' }
    );
  },

  // Notificaciones de mantenimiento
  maintenanceReminder: (vehicleName, type) => {
    showSystemNotification(
      'Recordatorio de Mantenimiento',
      `${vehicleName} necesita ${type}.`,
      { 
        tag: 'maintenance-reminder',
        requireInteraction: true 
      }
    );
  },

  // Notificaciones de backup
  backupCreated: () => {
    showSystemNotification(
      'Backup Creado',
      'Se ha creado un backup de la base de datos exitosamente.',
      { tag: 'backup-created' }
    );
  },

  // Notificaciones de actualización
  updateAvailable: (version) => {
    showSystemNotification(
      'Actualización Disponible',
      `Nueva versión ${version} disponible para descarga.`,
      { 
        tag: 'update-available',
        requireInteraction: true 
      }
    );
  },

  updateDownloaded: () => {
    showSystemNotification(
      'Actualización Lista',
      'La actualización se ha descargado y está lista para instalar.',
      { 
        tag: 'update-downloaded',
        requireInteraction: true 
      }
    );
  },

  // Notificaciones de error
  databaseError: () => {
    showSystemNotification(
      'Error de Base de Datos',
      'Ha ocurrido un error con la base de datos. Revisa los logs.',
      { 
        tag: 'database-error',
        requireInteraction: true 
      }
    );
  },

  connectionError: () => {
    showSystemNotification(
      'Error de Conexión',
      'No se puede conectar al servidor. Verifica la aplicación.',
      { 
        tag: 'connection-error',
        requireInteraction: true 
      }
    );
  }
};

// Hook para inicializar notificaciones
export function initializeNotifications() {
  // Solicitar permisos al cargar la aplicación
  if (areNotificationsSupported() && Notification.permission === 'default') {
    requestNotificationPermission().then(granted => {
      if (granted) {
        console.log('Permisos de notificación concedidos');
      } else {
        console.log('Permisos de notificación denegados');
      }
    });
  }
}
