import * as React from 'react';
import { DesktopTimePickerProps } from "./DesktopTimePicker.types.js";
type DesktopTimePickerComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: DesktopTimePickerProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * Demos:
 *
 * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)
 * - [Validation](https://mui.com/x/react-date-pickers/validation/)
 *
 * API:
 *
 * - [DesktopTimePicker API](https://mui.com/x/api/date-pickers/desktop-time-picker/)
 */
declare const DesktopTimePicker: DesktopTimePickerComponent;
export { DesktopTimePicker };