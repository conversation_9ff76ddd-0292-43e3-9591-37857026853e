import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Divider,
  Paper,
} from '@mui/material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { useApp } from '../../context/AppContext';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import { es } from 'date-fns/locale';
import VehicleComparison from './VehicleComparison';
import AdvancedAnalytics from './AdvancedAnalytics';

// Colores para los gráficos
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

// Componente para métricas principales
const MetricCard = ({ title, value, subtitle, color = 'primary', trend }) => (
  <Card>
    <CardContent>
      <Typography color="textSecondary" gutterBottom variant="body2">
        {title}
      </Typography>
      <Typography variant="h4" component="div" color={color}>
        {value}
      </Typography>
      {subtitle && (
        <Typography variant="body2" color="textSecondary">
          {subtitle}
        </Typography>
      )}
      {trend && (
        <Box mt={1}>
          <Chip
            label={trend}
            size="small"
            color={trend.includes('+') ? 'success' : trend.includes('-') ? 'error' : 'default'}
          />
        </Box>
      )}
    </CardContent>
  </Card>
);

const Statistics = () => {
  const { vehicles, refuels, expenses, loadStatistics, statistics } = useApp();
  const [selectedVehicle, setSelectedVehicle] = useState('all');
  const [dateRange, setDateRange] = useState('6months');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [chartData, setChartData] = useState({
    consumption: [],
    costs: [],
    expensesByType: [],
    monthlyTrends: []
  });

  useEffect(() => {
    calculateStatistics();
  }, [refuels, expenses, selectedVehicle, dateRange]);

  const calculateStatistics = () => {
    console.log('🔄 Recalculating statistics with filters:', { selectedVehicle, dateRange });
    console.log('📊 Total data available:', { refuels: refuels.length, expenses: expenses.length });

    let filteredRefuels = [...refuels]; // Crear copia para evitar mutación
    let filteredExpenses = [...expenses];

    // Filtrar por vehículo
    if (selectedVehicle !== 'all') {
      const vehicleId = parseInt(selectedVehicle);
      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);
      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);
      console.log('🚗 Filtered by vehicle:', vehicleId, { refuels: filteredRefuels.length, expenses: filteredExpenses.length });
    }

    // Filtrar por fecha
    const now = new Date();
    let startDateFilter;

    if (startDate && endDate) {
      // Usar fechas personalizadas si están definidas
      startDateFilter = new Date(startDate);
      const endDateFilter = new Date(endDate);

      filteredRefuels = filteredRefuels.filter(r => {
        const refuelDate = new Date(r.fecha);
        return refuelDate >= startDateFilter && refuelDate <= endDateFilter;
      });

      filteredExpenses = filteredExpenses.filter(e => {
        const expenseDate = new Date(e.fecha);
        return expenseDate >= startDateFilter && expenseDate <= endDateFilter;
      });

      console.log('📅 Filtered by custom date range:', {
        startDate: startDateFilter,
        endDate: endDateFilter,
        refuels: filteredRefuels.length,
        expenses: filteredExpenses.length
      });
    } else {
      // Usar rangos predefinidos si no hay fechas personalizadas
      switch (dateRange) {
        case '3months':
          startDateFilter = subMonths(now, 3);
          break;
        case '6months':
          startDateFilter = subMonths(now, 6);
          break;
        case '1year':
          startDateFilter = subMonths(now, 12);
          break;
        case '2years':
          startDateFilter = subMonths(now, 24);
          break;
        default:
          startDateFilter = new Date(0); // Todos los datos
      }

      if (dateRange !== 'all') {
        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDateFilter);
        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDateFilter);
        console.log('📅 Filtered by date range:', dateRange, {
          refuels: filteredRefuels.length,
          expenses: filteredExpenses.length
        });
      }
    }

    // Calcular datos para gráficos
    calculateChartData(filteredRefuels, filteredExpenses);
  };

  const calculateChartData = (refuels, expenses) => {
    // 1. Datos de consumo por mes
    const consumptionByMonth = {};
    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));

    for (let i = 1; i < sortedRefuels.length; i++) {
      const current = sortedRefuels[i];
      const previous = sortedRefuels[i - 1];

      if (current.vehiculo_id === previous.vehiculo_id) {
        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;
        const liters = current.litros;

        if (kmDiff > 0 && liters > 0) {
          const consumption = (liters / kmDiff) * 100; // L/100km
          const month = format(new Date(current.fecha), 'yyyy-MM');

          if (!consumptionByMonth[month]) {
            consumptionByMonth[month] = { total: 0, count: 0, month };
          }
          consumptionByMonth[month].total += consumption;
          consumptionByMonth[month].count += 1;
        }
      }
    }

    const consumptionData = Object.values(consumptionByMonth)
      .map(item => ({
        month: format(new Date(item.month + '-01'), 'MMM yyyy', { locale: es }),
        consumption: (item.total / item.count).toFixed(1),
        date: item.month
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    // 2. Costes por mes
    const costsByMonth = {};

    [...refuels, ...expenses].forEach(item => {
      const month = format(new Date(item.fecha), 'yyyy-MM');
      const cost = item.coste_total || item.coste || 0;
      const type = item.litros ? 'Combustible' : 'Gastos';

      if (!costsByMonth[month]) {
        costsByMonth[month] = { month, Combustible: 0, Gastos: 0, date: month };
      }
      costsByMonth[month][type] += cost;
    });

    const costsData = Object.values(costsByMonth)
      .map(item => ({
        ...item,
        month: format(new Date(item.month + '-01'), 'MMM yyyy', { locale: es }),
        Total: item.Combustible + item.Gastos
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    // 3. Gastos por tipo (incluyendo combustible)
    const expensesByType = {};

    // Agregar gastos regulares
    expenses.forEach(expense => {
      const type = expense.tipo_gasto || 'Otros';
      expensesByType[type] = (expensesByType[type] || 0) + (expense.coste || 0);
    });

    // Agregar combustible como un tipo de gasto
    const totalFuelCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);
    if (totalFuelCost > 0) {
      expensesByType['Combustible'] = totalFuelCost;
    }

    const expensesData = Object.entries(expensesByType)
      .map(([name, value]) => ({ name, value: parseFloat(value.toFixed(2)) }))
      .sort((a, b) => b.value - a.value);

    // 4. Tendencias mensuales
    const monthlyTrends = costsData.map(item => ({
      month: item.month,
      date: item.date,
      totalCost: item.Total,
      fuelCost: item.Combustible,
      expenseCost: item.Gastos,
      consumption: consumptionData.find(c => c.date === item.date)?.consumption || 0
    }));

    setChartData({
      consumption: consumptionData,
      costs: costsData,
      expensesByType: expensesData,
      monthlyTrends
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  // Aplicar filtros para métricas principales
  const getFilteredData = () => {
    let filteredRefuels = [...refuels];
    let filteredExpenses = [...expenses];

    if (selectedVehicle !== 'all') {
      const vehicleId = parseInt(selectedVehicle);
      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);
      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);
    }

    if (dateRange !== 'all') {
      const now = new Date();
      let startDate;
      switch (dateRange) {
        case '3months': startDate = subMonths(now, 3); break;
        case '6months': startDate = subMonths(now, 6); break;
        case '1year': startDate = subMonths(now, 12); break;
        case '2years': startDate = subMonths(now, 24); break;
        default: startDate = new Date(0);
      }

      if (dateRange !== 'all') {
        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);
        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);
      }
    }

    return { filteredRefuels, filteredExpenses };
  };

  const { filteredRefuels, filteredExpenses } = getFilteredData();

  // Calcular métricas principales con datos filtrados
  const totalRefuels = filteredRefuels.length;
  const totalExpenses = filteredExpenses.length;
  const totalFuelCost = filteredRefuels.reduce((sum, r) => sum + (r.coste_total || 0), 0);
  const totalExpenseCost = filteredExpenses.reduce((sum, e) => sum + (e.coste || 0), 0);
  const totalCost = totalFuelCost + totalExpenseCost;
  const totalLiters = filteredRefuels.reduce((sum, r) => sum + (r.litros || 0), 0);

  // Calcular consumo promedio con datos filtrados
  let avgConsumption = 0;
  if (filteredRefuels.length >= 2) {
    const sortedRefuels = [...filteredRefuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));
    const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales -
      sortedRefuels[0].kilometros_actuales;
    if (totalKm > 0) {
      avgConsumption = (totalLiters / totalKm * 100);
    }
  }

  const avgFuelPrice = totalLiters > 0 ? totalFuelCost / totalLiters : 0;

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Estadísticas
      </Typography>

      <Box mb={3}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel id="vehicle-select-label">Vehículo</InputLabel>
              <Select
                labelId="vehicle-select-label"
                value={selectedVehicle}
                onChange={(e) => setSelectedVehicle(e.target.value)}
                label="Vehículo"
              >
                <MenuItem value="all">Todos los vehículos</MenuItem>
                {vehicles.map((vehicle) => (
                  <MenuItem key={vehicle.id} value={vehicle.id}>
                    {vehicle.nombre}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel id="date-range-label">Rango de fechas</InputLabel>
              <Select
                labelId="date-range-label"
                value={dateRange}
                onChange={(e) => {
                  setDateRange(e.target.value);
                  // Reset custom dates when selecting a predefined range
                  setStartDate('');
                  setEndDate('');
                }}
                label="Rango de fechas"
              >
                <MenuItem value="3months">Últimos 3 meses</MenuItem>
                <MenuItem value="6months">Últimos 6 meses</MenuItem>
                <MenuItem value="1year">Último año</MenuItem>
                <MenuItem value="2years">Últimos 2 años</MenuItem>
                <MenuItem value="all">Todo el historial</MenuItem>
                <MenuItem value="custom">Personalizado</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          {dateRange === 'custom' && (
            <>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Fecha de inicio"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Fecha de fin"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </Box>

      {/* Métricas principales */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Consumo Promedio"
            value={`${avgConsumption.toFixed(1)} L`}
            subtitle="Por 100 km"
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Precio Promedio"
            value={formatCurrency(avgFuelPrice)}
            subtitle="Por litro"
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Gasto Total"
            value={formatCurrency(totalCost)}
            subtitle={`${formatCurrency(totalFuelCost)} combustible + ${formatCurrency(totalExpenseCost)} gastos`}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Litros"
            value={`${totalLiters.toFixed(1)} L`}
            subtitle={`En ${totalRefuels} repostajes`}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Gráficos */}
      <Grid container spacing={3}>
        {/* Gráfico de consumo */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Evolución del Consumo
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData.consumption}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip
                      formatter={(value) => [`${value} L/100km`, 'Consumo']}
                      labelStyle={{ color: '#666' }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="consumption"
                      stroke="#8884d8"
                      strokeWidth={2}
                      name="L/100km"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Gráfico de costes mensuales */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Costes Mensuales
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={chartData.costs}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip
                      formatter={(value) => [formatCurrency(value)]}
                      labelStyle={{ color: '#666' }}
                    />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="Combustible"
                      stackId="1"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                    />
                    <Area
                      type="monotone"
                      dataKey="Gastos"
                      stackId="1"
                      stroke="#ffc658"
                      fill="#ffc658"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Gráfico de gastos por tipo */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Distribución de Gastos
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartData.expensesByType}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {chartData.expensesByType.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <ChartTooltip formatter={(value) => [formatCurrency(value)]} />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Tendencias combinadas */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Tendencias Generales
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={chartData.monthlyTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip
                      formatter={(value, name) => {
                        if (name === 'Coste Total') return [formatCurrency(value), name];
                        return [value, name];
                      }}
                      labelStyle={{ color: '#666' }}
                    />
                    <Legend />
                    <Bar dataKey="totalCost" fill="#8884d8" name="Coste Total (€)" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Resumen estadístico */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Resumen Estadístico
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="primary">
                  {totalRefuels}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Repostajes totales
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="success.main">
                  {totalExpenses}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Gastos registrados
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="warning.main">
                  {formatCurrency(totalCost / (totalRefuels + totalExpenses) || 0)}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Gasto promedio por registro
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="info.main">
                  {vehicles.length}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Vehículos activos
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Análisis avanzado */}
      <Box mt={4}>
        <AdvancedAnalytics />
      </Box>

      {/* Comparación de vehículos */}
      {vehicles.length > 1 && (
        <Box mt={4}>
          <VehicleComparison />
        </Box>
      )}
    </Box>
  );
};

export default Statistics;