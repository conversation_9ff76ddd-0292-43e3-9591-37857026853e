{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_DESKTOP_MODE_MEDIA_QUERY = void 0;\nexports.arrayIncludes = arrayIncludes;\nexports.getFocusedListItemIndex = exports.getActiveElement = exports.executeInTheNextEventLoopTick = void 0;\nexports.mergeSx = mergeSx;\nexports.onSpaceOrEnter = void 0;\nvar _ownerDocument = _interopRequireDefault(require(\"@mui/utils/ownerDocument\"));\n/* Use it instead of .includes method for IE support */\nfunction arrayIncludes(array, itemOrItems) {\n  if (Array.isArray(itemOrItems)) {\n    return itemOrItems.every(item => array.indexOf(item) !== -1);\n  }\n  return array.indexOf(itemOrItems) !== -1;\n}\nconst onSpaceOrEnter = (innerFn, externalEvent) => event => {\n  if (event.key === 'Enter' || event.key === ' ') {\n    innerFn(event);\n\n    // prevent any side effects\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  if (externalEvent) {\n    externalEvent(event);\n  }\n};\nexports.onSpaceOrEnter = onSpaceOrEnter;\nconst executeInTheNextEventLoopTick = fn => {\n  setTimeout(fn, 0);\n};\n\n// https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\nexports.executeInTheNextEventLoopTick = executeInTheNextEventLoopTick;\nconst getActiveElementInternal = (root = document) => {\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElementInternal(activeEl.shadowRoot);\n  }\n  return activeEl;\n};\n\n/**\n * Gets the currently active element within a given node's document.\n * This function traverses shadow DOM if necessary.\n * @param node - The node from which to get the active element.\n * @returns The currently active element, or null if none is found.\n */\nconst getActiveElement = node => {\n  return getActiveElementInternal((0, _ownerDocument.default)(node));\n};\n\n/**\n * Gets the index of the focused list item in a given ul list element.\n *\n * @param {HTMLUListElement} listElement - The list element to search within.\n * @returns {number} The index of the focused list item, or -1 if none is focused.\n */\nexports.getActiveElement = getActiveElement;\nconst getFocusedListItemIndex = listElement => {\n  const children = Array.from(listElement.children);\n  return children.indexOf(getActiveElement(listElement));\n};\nexports.getFocusedListItemIndex = getFocusedListItemIndex;\nconst DEFAULT_DESKTOP_MODE_MEDIA_QUERY = exports.DEFAULT_DESKTOP_MODE_MEDIA_QUERY = '@media (pointer: fine)';\nfunction mergeSx(...sxProps) {\n  return sxProps.reduce((acc, sxProp) => {\n    if (Array.isArray(sxProp)) {\n      acc.push(...sxProp);\n    } else if (sxProp != null) {\n      acc.push(sxProp);\n    }\n    return acc;\n  }, []);\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "DEFAULT_DESKTOP_MODE_MEDIA_QUERY", "arrayIncludes", "getFocusedListItemIndex", "getActiveElement", "executeInTheNextEventLoopTick", "mergeSx", "onSpaceOrEnter", "_ownerDocument", "array", "itemOrItems", "Array", "isArray", "every", "item", "indexOf", "innerFn", "externalEvent", "event", "key", "preventDefault", "stopPropagation", "fn", "setTimeout", "getActiveElementInternal", "root", "document", "activeEl", "activeElement", "shadowRoot", "node", "listElement", "children", "from", "sxProps", "reduce", "acc", "sxProp", "push"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/utils/utils.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_DESKTOP_MODE_MEDIA_QUERY = void 0;\nexports.arrayIncludes = arrayIncludes;\nexports.getFocusedListItemIndex = exports.getActiveElement = exports.executeInTheNextEventLoopTick = void 0;\nexports.mergeSx = mergeSx;\nexports.onSpaceOrEnter = void 0;\nvar _ownerDocument = _interopRequireDefault(require(\"@mui/utils/ownerDocument\"));\n/* Use it instead of .includes method for IE support */\nfunction arrayIncludes(array, itemOrItems) {\n  if (Array.isArray(itemOrItems)) {\n    return itemOrItems.every(item => array.indexOf(item) !== -1);\n  }\n  return array.indexOf(itemOrItems) !== -1;\n}\nconst onSpaceOrEnter = (innerFn, externalEvent) => event => {\n  if (event.key === 'Enter' || event.key === ' ') {\n    innerFn(event);\n\n    // prevent any side effects\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  if (externalEvent) {\n    externalEvent(event);\n  }\n};\nexports.onSpaceOrEnter = onSpaceOrEnter;\nconst executeInTheNextEventLoopTick = fn => {\n  setTimeout(fn, 0);\n};\n\n// https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\nexports.executeInTheNextEventLoopTick = executeInTheNextEventLoopTick;\nconst getActiveElementInternal = (root = document) => {\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElementInternal(activeEl.shadowRoot);\n  }\n  return activeEl;\n};\n\n/**\n * Gets the currently active element within a given node's document.\n * This function traverses shadow DOM if necessary.\n * @param node - The node from which to get the active element.\n * @returns The currently active element, or null if none is found.\n */\nconst getActiveElement = node => {\n  return getActiveElementInternal((0, _ownerDocument.default)(node));\n};\n\n/**\n * Gets the index of the focused list item in a given ul list element.\n *\n * @param {HTMLUListElement} listElement - The list element to search within.\n * @returns {number} The index of the focused list item, or -1 if none is focused.\n */\nexports.getActiveElement = getActiveElement;\nconst getFocusedListItemIndex = listElement => {\n  const children = Array.from(listElement.children);\n  return children.indexOf(getActiveElement(listElement));\n};\nexports.getFocusedListItemIndex = getFocusedListItemIndex;\nconst DEFAULT_DESKTOP_MODE_MEDIA_QUERY = exports.DEFAULT_DESKTOP_MODE_MEDIA_QUERY = '@media (pointer: fine)';\nfunction mergeSx(...sxProps) {\n  return sxProps.reduce((acc, sxProp) => {\n    if (Array.isArray(sxProp)) {\n      acc.push(...sxProp);\n    } else if (sxProp != null) {\n      acc.push(sxProp);\n    }\n    return acc;\n  }, []);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gCAAgC,GAAG,KAAK,CAAC;AACjDF,OAAO,CAACG,aAAa,GAAGA,aAAa;AACrCH,OAAO,CAACI,uBAAuB,GAAGJ,OAAO,CAACK,gBAAgB,GAAGL,OAAO,CAACM,6BAA6B,GAAG,KAAK,CAAC;AAC3GN,OAAO,CAACO,OAAO,GAAGA,OAAO;AACzBP,OAAO,CAACQ,cAAc,GAAG,KAAK,CAAC;AAC/B,IAAIC,cAAc,GAAGd,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF;AACA,SAASO,aAAaA,CAACO,KAAK,EAAEC,WAAW,EAAE;EACzC,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;IAC9B,OAAOA,WAAW,CAACG,KAAK,CAACC,IAAI,IAAIL,KAAK,CAACM,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9D;EACA,OAAOL,KAAK,CAACM,OAAO,CAACL,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1C;AACA,MAAMH,cAAc,GAAGA,CAACS,OAAO,EAAEC,aAAa,KAAKC,KAAK,IAAI;EAC1D,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAID,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;IAC9CH,OAAO,CAACE,KAAK,CAAC;;IAEd;IACAA,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;EACzB;EACA,IAAIJ,aAAa,EAAE;IACjBA,aAAa,CAACC,KAAK,CAAC;EACtB;AACF,CAAC;AACDnB,OAAO,CAACQ,cAAc,GAAGA,cAAc;AACvC,MAAMF,6BAA6B,GAAGiB,EAAE,IAAI;EAC1CC,UAAU,CAACD,EAAE,EAAE,CAAC,CAAC;AACnB,CAAC;;AAED;AACAvB,OAAO,CAACM,6BAA6B,GAAGA,6BAA6B;AACrE,MAAMmB,wBAAwB,GAAGA,CAACC,IAAI,GAAGC,QAAQ,KAAK;EACpD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,aAAa;EACnC,IAAI,CAACD,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,IAAIA,QAAQ,CAACE,UAAU,EAAE;IACvB,OAAOL,wBAAwB,CAACG,QAAQ,CAACE,UAAU,CAAC;EACtD;EACA,OAAOF,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMvB,gBAAgB,GAAG0B,IAAI,IAAI;EAC/B,OAAON,wBAAwB,CAAC,CAAC,CAAC,EAAEhB,cAAc,CAACZ,OAAO,EAAEkC,IAAI,CAAC,CAAC;AACpE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA/B,OAAO,CAACK,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMD,uBAAuB,GAAG4B,WAAW,IAAI;EAC7C,MAAMC,QAAQ,GAAGrB,KAAK,CAACsB,IAAI,CAACF,WAAW,CAACC,QAAQ,CAAC;EACjD,OAAOA,QAAQ,CAACjB,OAAO,CAACX,gBAAgB,CAAC2B,WAAW,CAAC,CAAC;AACxD,CAAC;AACDhC,OAAO,CAACI,uBAAuB,GAAGA,uBAAuB;AACzD,MAAMF,gCAAgC,GAAGF,OAAO,CAACE,gCAAgC,GAAG,wBAAwB;AAC5G,SAASK,OAAOA,CAAC,GAAG4B,OAAO,EAAE;EAC3B,OAAOA,OAAO,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;IACrC,IAAI1B,KAAK,CAACC,OAAO,CAACyB,MAAM,CAAC,EAAE;MACzBD,GAAG,CAACE,IAAI,CAAC,GAAGD,MAAM,CAAC;IACrB,CAAC,MAAM,IAAIA,MAAM,IAAI,IAAI,EAAE;MACzBD,GAAG,CAACE,IAAI,CAACD,MAAM,CAAC;IAClB;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}