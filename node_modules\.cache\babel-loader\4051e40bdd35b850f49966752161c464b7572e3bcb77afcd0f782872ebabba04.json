{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nfunction isHostComponent(element) {\n  return typeof element === 'string';\n}\nvar _default = exports.default = isHostComponent;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "isHostComponent", "element", "_default"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/isHostComponent/isHostComponent.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nfunction isHostComponent(element) {\n  return typeof element === 'string';\n}\nvar _default = exports.default = isHostComponent;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB;AACA;AACA;AACA,SAASC,eAAeA,CAACC,OAAO,EAAE;EAChC,OAAO,OAAOA,OAAO,KAAK,QAAQ;AACpC;AACA,IAAIC,QAAQ,GAAGL,OAAO,CAACE,OAAO,GAAGC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}