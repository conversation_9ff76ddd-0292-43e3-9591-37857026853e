const { execSync } = require('child_process');

console.log('🚀 Iniciando construcción simple de aplicación portable...');

try {
  // 1. Construir la aplicación React
  console.log('⚛️ Construyendo aplicación React...');
  execSync('npm run build', { stdio: 'inherit' });

  // 2. Construir la aplicación Electron directamente
  console.log('🖥️ Construyendo aplicación Electron...');
  execSync('npx electron-builder --publish=never', { stdio: 'inherit' });

  console.log('✅ ¡Construcción completada!');
  console.log('📁 El ejecutable portable se encuentra en la carpeta "dist"');
  console.log('💡 Puedes ejecutar el archivo .exe desde cualquier ubicación');
  console.log('💾 La base de datos se creará automáticamente en la misma carpeta del ejecutable');

} catch (error) {
  console.error('❌ Error durante la construcción:', error.message);
  console.log('💡 Sugerencias:');
  console.log('   - Cierra cualquier instancia de la aplicación');
  console.log('   - Elimina manualmente la carpeta "dist" si existe');
  console.log('   - Vuelve a ejecutar el comando');
  process.exit(1);
}
