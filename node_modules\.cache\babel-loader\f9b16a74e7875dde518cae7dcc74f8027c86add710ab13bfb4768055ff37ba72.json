{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"clearWarningsCache\", {\n  enumerable: true,\n  get: function () {\n    return _warning.clearWarningsCache;\n  }\n});\nObject.defineProperty(exports, \"warnOnce\", {\n  enumerable: true,\n  get: function () {\n    return _warning.warnOnce;\n  }\n});\nvar _warning = require(\"./warning\");", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "_warning", "clearWarningsCache", "warnOnce", "require"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-internals/warning/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"clearWarningsCache\", {\n  enumerable: true,\n  get: function () {\n    return _warning.clearWarningsCache;\n  }\n});\nObject.defineProperty(exports, \"warnOnce\", {\n  enumerable: true,\n  get: function () {\n    return _warning.warnOnce;\n  }\n});\nvar _warning = require(\"./warning\");"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFH,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,oBAAoB,EAAE;EACnDE,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOC,QAAQ,CAACC,kBAAkB;EACpC;AACF,CAAC,CAAC;AACFP,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,UAAU,EAAE;EACzCE,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOC,QAAQ,CAACE,QAAQ;EAC1B;AACF,CAAC,CAAC;AACF,IAAIF,QAAQ,GAAGG,OAAO,CAAC,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}