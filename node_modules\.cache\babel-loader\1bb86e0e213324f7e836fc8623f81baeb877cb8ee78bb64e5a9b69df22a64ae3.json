{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Timeout = void 0;\nexports.default = useTimeout;\nvar _useLazyRef = _interopRequireDefault(require(\"../useLazyRef/useLazyRef\"));\nvar _useOnMount = _interopRequireDefault(require(\"../useOnMount/useOnMount\"));\nclass Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexports.Timeout = Timeout;\nfunction useTimeout() {\n  const timeout = (0, _useLazyRef.default)(Timeout.create).current;\n  (0, _useOnMount.default)(timeout.disposeEffect);\n  return timeout;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "Timeout", "useTimeout", "_useLazyRef", "_useOnMount", "create", "currentId", "start", "delay", "fn", "clear", "setTimeout", "clearTimeout", "disposeEffect", "timeout", "current"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/useTimeout/useTimeout.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Timeout = void 0;\nexports.default = useTimeout;\nvar _useLazyRef = _interopRequireDefault(require(\"../useLazyRef/useLazyRef\"));\nvar _useOnMount = _interopRequireDefault(require(\"../useOnMount/useOnMount\"));\nclass Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexports.Timeout = Timeout;\nfunction useTimeout() {\n  const timeout = (0, _useLazyRef.default)(Timeout.create).current;\n  (0, _useOnMount.default)(timeout.disposeEffect);\n  return timeout;\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxBF,OAAO,CAACH,OAAO,GAAGM,UAAU;AAC5B,IAAIC,WAAW,GAAGT,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC7E,IAAIS,WAAW,GAAGV,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC7E,MAAMM,OAAO,CAAC;EACZ,OAAOI,MAAMA,CAAA,EAAG;IACd,OAAO,IAAIJ,OAAO,CAAC,CAAC;EACtB;EACAK,SAAS,GAAG,IAAI;;EAEhB;AACF;AACA;EACEC,KAAKA,CAACC,KAAK,EAAEC,EAAE,EAAE;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACJ,SAAS,GAAGK,UAAU,CAAC,MAAM;MAChC,IAAI,CAACL,SAAS,GAAG,IAAI;MACrBG,EAAE,CAAC,CAAC;IACN,CAAC,EAAED,KAAK,CAAC;EACX;EACAE,KAAK,GAAGA,CAAA,KAAM;IACZ,IAAI,IAAI,CAACJ,SAAS,KAAK,IAAI,EAAE;MAC3BM,YAAY,CAAC,IAAI,CAACN,SAAS,CAAC;MAC5B,IAAI,CAACA,SAAS,GAAG,IAAI;IACvB;EACF,CAAC;EACDO,aAAa,GAAGA,CAAA,KAAM;IACpB,OAAO,IAAI,CAACH,KAAK;EACnB,CAAC;AACH;AACAX,OAAO,CAACE,OAAO,GAAGA,OAAO;AACzB,SAASC,UAAUA,CAAA,EAAG;EACpB,MAAMY,OAAO,GAAG,CAAC,CAAC,EAAEX,WAAW,CAACP,OAAO,EAAEK,OAAO,CAACI,MAAM,CAAC,CAACU,OAAO;EAChE,CAAC,CAAC,EAAEX,WAAW,CAACR,OAAO,EAAEkB,OAAO,CAACD,aAAa,CAAC;EAC/C,OAAOC,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}