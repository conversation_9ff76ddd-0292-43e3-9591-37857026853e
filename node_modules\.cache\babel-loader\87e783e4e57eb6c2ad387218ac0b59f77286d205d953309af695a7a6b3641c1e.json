{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_STEP_NAVIGATION = void 0;\nexports.createStepNavigation = createStepNavigation;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nconst DEFAULT_STEP_NAVIGATION = exports.DEFAULT_STEP_NAVIGATION = {\n  hasNextStep: false,\n  hasSeveralSteps: false,\n  goToNextStep: () => {},\n  areViewsInSameStep: () => true\n};\n\n/**\n * Create an object that determines whether there is a next step and allows to go to the next step.\n * @param {CreateStepNavigationParameters<TStep>} parameters The parameters of the createStepNavigation function\n * @returns {CreateStepNavigationReturnValue} The return value of the createStepNavigation function\n */\nfunction createStepNavigation(parameters) {\n  const {\n    steps,\n    isViewMatchingStep,\n    onStepChange\n  } = parameters;\n  return parametersBis => {\n    if (steps == null) {\n      return DEFAULT_STEP_NAVIGATION;\n    }\n    const currentStepIndex = steps.findIndex(step => isViewMatchingStep(parametersBis.view, step));\n    const nextStep = currentStepIndex === -1 || currentStepIndex === steps.length - 1 ? null : steps[currentStepIndex + 1];\n    return {\n      hasNextStep: nextStep != null,\n      hasSeveralSteps: steps.length > 1,\n      goToNextStep: () => {\n        if (nextStep == null) {\n          return;\n        }\n        onStepChange((0, _extends2.default)({}, parametersBis, {\n          step: nextStep\n        }));\n      },\n      areViewsInSameStep: (viewA, viewB) => {\n        const stepA = steps.find(step => isViewMatchingStep(viewA, step));\n        const stepB = steps.find(step => isViewMatchingStep(viewB, step));\n        return stepA === stepB;\n      }\n    };\n  };\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "DEFAULT_STEP_NAVIGATION", "createStepNavigation", "_extends2", "hasNextStep", "hasSeveralSteps", "goToNextStep", "areViewsInSameStep", "parameters", "steps", "isViewMatchingStep", "onStepChange", "parametersBis", "currentStepIndex", "findIndex", "step", "view", "nextStep", "length", "viewA", "viewB", "stepA", "find", "stepB"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/utils/createStepNavigation.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_STEP_NAVIGATION = void 0;\nexports.createStepNavigation = createStepNavigation;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nconst DEFAULT_STEP_NAVIGATION = exports.DEFAULT_STEP_NAVIGATION = {\n  hasNextStep: false,\n  hasSeveralSteps: false,\n  goToNextStep: () => {},\n  areViewsInSameStep: () => true\n};\n\n/**\n * Create an object that determines whether there is a next step and allows to go to the next step.\n * @param {CreateStepNavigationParameters<TStep>} parameters The parameters of the createStepNavigation function\n * @returns {CreateStepNavigationReturnValue} The return value of the createStepNavigation function\n */\nfunction createStepNavigation(parameters) {\n  const {\n    steps,\n    isViewMatchingStep,\n    onStepChange\n  } = parameters;\n  return parametersBis => {\n    if (steps == null) {\n      return DEFAULT_STEP_NAVIGATION;\n    }\n    const currentStepIndex = steps.findIndex(step => isViewMatchingStep(parametersBis.view, step));\n    const nextStep = currentStepIndex === -1 || currentStepIndex === steps.length - 1 ? null : steps[currentStepIndex + 1];\n    return {\n      hasNextStep: nextStep != null,\n      hasSeveralSteps: steps.length > 1,\n      goToNextStep: () => {\n        if (nextStep == null) {\n          return;\n        }\n        onStepChange((0, _extends2.default)({}, parametersBis, {\n          step: nextStep\n        }));\n      },\n      areViewsInSameStep: (viewA, viewB) => {\n        const stepA = steps.find(step => isViewMatchingStep(viewA, step));\n        const stepB = steps.find(step => isViewMatchingStep(viewB, step));\n        return stepA === stepB;\n      }\n    };\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,uBAAuB,GAAG,KAAK,CAAC;AACxCF,OAAO,CAACG,oBAAoB,GAAGA,oBAAoB;AACnD,IAAIC,SAAS,GAAGT,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,MAAMM,uBAAuB,GAAGF,OAAO,CAACE,uBAAuB,GAAG;EAChEG,WAAW,EAAE,KAAK;EAClBC,eAAe,EAAE,KAAK;EACtBC,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC;EACtBC,kBAAkB,EAAEA,CAAA,KAAM;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASL,oBAAoBA,CAACM,UAAU,EAAE;EACxC,MAAM;IACJC,KAAK;IACLC,kBAAkB;IAClBC;EACF,CAAC,GAAGH,UAAU;EACd,OAAOI,aAAa,IAAI;IACtB,IAAIH,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOR,uBAAuB;IAChC;IACA,MAAMY,gBAAgB,GAAGJ,KAAK,CAACK,SAAS,CAACC,IAAI,IAAIL,kBAAkB,CAACE,aAAa,CAACI,IAAI,EAAED,IAAI,CAAC,CAAC;IAC9F,MAAME,QAAQ,GAAGJ,gBAAgB,KAAK,CAAC,CAAC,IAAIA,gBAAgB,KAAKJ,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,IAAI,GAAGT,KAAK,CAACI,gBAAgB,GAAG,CAAC,CAAC;IACtH,OAAO;MACLT,WAAW,EAAEa,QAAQ,IAAI,IAAI;MAC7BZ,eAAe,EAAEI,KAAK,CAACS,MAAM,GAAG,CAAC;MACjCZ,YAAY,EAAEA,CAAA,KAAM;QAClB,IAAIW,QAAQ,IAAI,IAAI,EAAE;UACpB;QACF;QACAN,YAAY,CAAC,CAAC,CAAC,EAAER,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEgB,aAAa,EAAE;UACrDG,IAAI,EAAEE;QACR,CAAC,CAAC,CAAC;MACL,CAAC;MACDV,kBAAkB,EAAEA,CAACY,KAAK,EAAEC,KAAK,KAAK;QACpC,MAAMC,KAAK,GAAGZ,KAAK,CAACa,IAAI,CAACP,IAAI,IAAIL,kBAAkB,CAACS,KAAK,EAAEJ,IAAI,CAAC,CAAC;QACjE,MAAMQ,KAAK,GAAGd,KAAK,CAACa,IAAI,CAACP,IAAI,IAAIL,kBAAkB,CAACU,KAAK,EAAEL,IAAI,CAAC,CAAC;QACjE,OAAOM,KAAK,KAAKE,KAAK;MACxB;IACF,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}