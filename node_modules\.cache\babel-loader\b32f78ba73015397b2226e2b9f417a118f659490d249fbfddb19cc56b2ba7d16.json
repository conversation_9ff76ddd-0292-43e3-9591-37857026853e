{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Refuels\\\\RefuelsList.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControlLabel, Switch, Tooltip, TablePagination } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, LocalGasStation as GasIcon } from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RefuelDialog = ({\n  open,\n  onClose,\n  refuel,\n  onSave,\n  vehicles\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    litros: '',\n    precio_litro: '',\n    coste_total: '',\n    gasolinera: '',\n    deposito_lleno: true,\n    notas: ''\n  });\n  useEffect(() => {\n    if (refuel) {\n      setFormData({\n        ...refuel,\n        fecha: refuel.fecha.split('T')[0],\n        // Formato para input date\n        deposito_lleno: !!refuel.deposito_lleno // Convert 0/1 to false/true\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        litros: '',\n        precio_litro: '',\n        coste_total: '',\n        gasolinera: '',\n        deposito_lleno: true,\n        notas: ''\n      });\n    }\n  }, [refuel, vehicles, open]);\n  const handleChange = field => event => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => {\n      const newData = {\n        ...prev,\n        [field]: value\n      };\n\n      // Cálculos automáticos para precios y costes.\n      if (field === 'coste_total') {\n        // Si el usuario modifica el coste total, calculamos el precio por litro.\n        const litros = parseFloat(newData.litros) || 0;\n        const coste = parseFloat(newData.coste_total) || 0;\n        if (litros > 0) {\n          newData.precio_litro = (coste / litros).toFixed(3);\n        }\n      } else if (field === 'litros' || field === 'precio_litro') {\n        // Si el usuario modifica los litros o el precio por litro, calculamos el coste total.\n        const litros = parseFloat(newData.litros) || 0;\n        const precio = parseFloat(newData.precio_litro) || 0;\n        newData.coste_total = (litros * precio).toFixed(2);\n      }\n      return newData;\n    });\n  };\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      litros: parseFloat(formData.litros),\n      precio_litro: parseFloat(formData.precio_litro),\n      coste_total: parseFloat(formData.coste_total),\n      kilometros_actuales: parseInt(formData.kilometros_actuales),\n      deposito_lleno: formData.deposito_lleno ? 1 : 0\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n  const isValid = formData.vehiculo_id && formData.fecha && formData.kilometros_actuales && formData.litros && formData.precio_litro;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: refuel ? 'Editar Repostaje' : 'Nuevo Repostaje'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: 2,\n        pt: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Veh\\xEDculo\",\n          select: true,\n          value: formData.vehiculo_id,\n          onChange: handleChange('vehiculo_id'),\n          fullWidth: true,\n          required: true,\n          children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: vehicle.id,\n            children: vehicle.nombre\n          }, vehicle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Fecha\",\n            type: \"date\",\n            value: formData.fecha,\n            onChange: handleChange('fecha'),\n            fullWidth: true,\n            required: true,\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Kilometraje actual\",\n            type: \"number\",\n            value: formData.kilometros_actuales,\n            onChange: handleChange('kilometros_actuales'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Litros\",\n            type: \"number\",\n            value: formData.litros,\n            onChange: handleChange('litros'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Precio por litro (\\u20AC)\",\n            type: \"number\",\n            value: formData.precio_litro,\n            onChange: handleChange('precio_litro'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.001\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Coste total (\\u20AC)\",\n            type: \"number\",\n            value: formData.coste_total,\n            onChange: handleChange('coste_total'),\n            fullWidth: true,\n            required: true,\n            inputProps: {\n              min: 0,\n              step: 0.01\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Gasolinera\",\n          value: formData.gasolinera,\n          onChange: handleChange('gasolinera'),\n          fullWidth: true,\n          placeholder: \"Nombre de la gasolinera\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: formData.deposito_lleno,\n            onChange: handleChange('deposito_lleno')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this),\n          label: \"Dep\\xF3sito lleno\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Notas\",\n          value: formData.notas,\n          onChange: handleChange('notas'),\n          fullWidth: true,\n          multiline: true,\n          rows: 2,\n          placeholder: \"Notas adicionales...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: !isValid,\n        children: refuel ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(RefuelDialog, \"GqcPthmOwROTs5BpcmUxIcsjgJI=\");\n_c = RefuelDialog;\nconst RefuelsList = () => {\n  _s2();\n  const {\n    vehicles,\n    refuels,\n    loadRefuels,\n    addRefuel\n  } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedRefuel, setSelectedRefuel] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n  useEffect(() => {\n    // Cargar todos los repostajes (sin límite)\n    loadRefuels(null, null);\n  }, []);\n  const handleAddRefuel = () => {\n    setSelectedRefuel(null);\n    setDialogOpen(true);\n  };\n  const handleEditRefuel = refuel => {\n    setSelectedRefuel(refuel);\n    setDialogOpen(true);\n  };\n  const handleDeleteRefuel = refuel => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete refuel:', refuel);\n  };\n  const handleSaveRefuel = async refuelData => {\n    try {\n      if (selectedRefuel) {\n        // TODO: Implementar actualización\n        console.log('Update refuel:', refuelData);\n      } else {\n        await addRefuel(refuelData);\n        // Recargar la lista\n        loadRefuels(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving refuel:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n  const formatDate = dateString => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', {\n        locale: es\n      });\n    } catch {\n      return dateString;\n    }\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  // Formatear números a máximo 3 decimales\n  const formatDecimal = (num, decimals = 3) => {\n    if (num === null || num === undefined || isNaN(num)) return '-';\n    const factor = Math.pow(10, decimals);\n    const rounded = Math.round(num * factor) / factor;\n    // Eliminar ceros decimales innecesarios\n    return rounded.toString().replace(/\\.?0+$/, '');\n  };\n\n  // Calcular kilómetros recorridos y consumo\n  const calculateTripInfo = (refuel, index) => {\n    if (!refuel.deposito_lleno) return {\n      kmTraveled: null,\n      consumption: null\n    };\n\n    // Encontrar el repostaje completo anterior\n    const previousFullRefuel = refuels.slice(index + 1) // Buscar en repostajes posteriores (la lista está ordenada por fecha descendente)\n    .find(r => r.deposito_lleno && r.vehiculo_id === refuel.vehiculo_id);\n    if (!previousFullRefuel) return {\n      kmTraveled: null,\n      consumption: null\n    };\n    const kmTraveled = refuel.kilometros_actuales - previousFullRefuel.kilometros_actuales;\n    const consumption = kmTraveled > 0 ? refuel.litros * 100 / kmTraveled : 0; // litros/100km\n\n    return {\n      kmTraveled: kmTraveled > 0 ? kmTraveled : null,\n      consumption: kmTraveled > 0 ? consumption : null\n    };\n  };\n\n  // Procesar todos los repostajes para obtener estadísticas\n  const processedRefuels = refuels.map((refuel, index) => {\n    const tripInfo = calculateTripInfo(refuel, index);\n    return {\n      ...refuel,\n      ...tripInfo\n    };\n  });\n\n  // Filtrar repostajes con datos de viaje válidos\n  const validTrips = processedRefuels.filter(r => r.kmTraveled > 0 && r.consumption > 0);\n\n  // Calcular estadísticas de distancia\n  const distances = validTrips.map(trip => trip.kmTraveled);\n  const minDistance = distances.length > 0 ? Math.min(...distances) : 0;\n  const maxDistance = distances.length > 0 ? Math.max(...distances) : 0;\n  const avgDistance = distances.length > 0 ? distances.reduce((a, b) => a + b, 0) / distances.length : 0;\n\n  // Calcular estadísticas rápidas\n  const totalRefuels = refuels.length;\n  const totalLiters = refuels.reduce((sum, refuel) => sum + (refuel.litros || 0), 0);\n  const totalCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);\n  const avgPricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0;\n\n  // Calcular consumo promedio (solo viajes válidos)\n  const avgConsumption = validTrips.length > 0 ? validTrips.reduce((sum, trip) => sum + trip.consumption, 0) / validTrips.length : 0;\n\n  // Paginación\n  const paginatedRefuels = processedRefuels.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        fontWeight: \"bold\",\n        children: \"Repostajes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 22\n        }, this),\n        onClick: handleAddRefuel,\n        disabled: vehicles.length === 0,\n        children: \"Nuevo Repostaje\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      mb: 3,\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: '1 1 180px'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary\",\n            children: formatNumber(totalRefuels)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Total Repostajes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: '1 1 180px'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"success.main\",\n            children: [formatDecimal(totalLiters, 1), \"L\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Total Litros\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: '1 1 180px'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"warning.main\",\n            children: formatDecimal(totalCost, 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Coste Total (\\u20AC)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: '1 1 180px'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"info.main\",\n            children: formatDecimal(avgPricePerLiter, 3)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Precio Promedio/L (\\u20AC)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: '1 1 180px'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"secondary\",\n            children: [validTrips.length > 0 ? formatDecimal(avgConsumption, 1) : '-', \"L\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Consumo Promedio/100km\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          flex: '1 1 180px'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"textSecondary\",\n              children: \"Distancia entre repostajes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              mt: 1,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"M\\xEDn:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"error.main\",\n                  children: minDistance ? formatDecimal(minDistance, 0) + ' km' : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"M\\xE1x:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"success.main\",\n                  children: maxDistance ? formatDecimal(maxDistance, 0) + ' km' : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"Prom:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: avgDistance ? formatDecimal(avgDistance, 0) + ' km' : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), refuels.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          py: 6,\n          children: [/*#__PURE__*/_jsxDEV(GasIcon, {\n            sx: {\n              fontSize: 64,\n              color: 'text.secondary',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"textSecondary\",\n            gutterBottom: true,\n            children: \"No hay repostajes registrados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            mb: 3,\n            children: \"Comienza registrando tu primer repostaje para hacer seguimiento del consumo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 28\n            }, this),\n            onClick: handleAddRefuel,\n            disabled: vehicles.length === 0,\n            children: vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Repostaje'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Fecha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Veh\\xEDculo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Gasolinera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Km\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Km Recorridos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Litros\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Consumo (L/100km)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"\\u20AC/L\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Lleno\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Acciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: paginatedRefuels.map(refuel => /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(refuel.fecha)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: refuel.vehiculo_nombre\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: refuel.gasolinera || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: formatNumber(refuel.kilometros_actuales)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: refuel.kmTraveled !== null ? formatNumber(Math.round(refuel.kmTraveled)) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: [formatDecimal(refuel.litros, 1), \"L\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: refuel.consumption !== null ? formatDecimal(refuel.consumption, 1) + 'L' : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: [formatDecimal(refuel.precio_litro, 3), \"\\u20AC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  children: formatCurrency(refuel.coste_total)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: refuel.deposito_lleno ? 'Sí' : 'No',\n                  color: refuel.deposito_lleno ? 'success' : 'default',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Editar\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleEditRefuel(refuel),\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Eliminar\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    color: \"error\",\n                    onClick: () => handleDeleteRefuel(refuel),\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 21\n              }, this)]\n            }, refuel.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n        component: \"div\",\n        count: refuels.length,\n        page: page,\n        onPageChange: (event, newPage) => setPage(newPage),\n        rowsPerPage: rowsPerPage,\n        onRowsPerPageChange: event => {\n          setRowsPerPage(parseInt(event.target.value, 10));\n          setPage(0);\n        },\n        rowsPerPageOptions: [10, 25, 50, 100],\n        labelRowsPerPage: \"Filas por p\\xE1gina:\",\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}-${to} de ${count}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(RefuelDialog, {\n      open: dialogOpen,\n      onClose: () => setDialogOpen(false),\n      refuel: selectedRefuel,\n      onSave: handleSaveRefuel,\n      vehicles: vehicles\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 352,\n    columnNumber: 5\n  }, this);\n};\n_s2(RefuelsList, \"QJ7L7C/FdPqXonlR76icZUScwUo=\", false, function () {\n  return [useApp];\n});\n_c2 = RefuelsList;\nexport default RefuelsList;\nvar _c, _c2;\n$RefreshReg$(_c, \"RefuelDialog\");\n$RefreshReg$(_c2, \"RefuelsList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "FormControlLabel", "Switch", "<PERSON><PERSON><PERSON>", "TablePagination", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "LocalGasStation", "GasIcon", "useApp", "format", "es", "jsxDEV", "_jsxDEV", "RefuelDialog", "open", "onClose", "refuel", "onSave", "vehicles", "_s", "formData", "setFormData", "vehiculo_id", "fecha", "Date", "toISOString", "split", "kilometros_actuales", "litros", "precio_litro", "coste_total", "gasolinera", "deposito_lleno", "notas", "length", "id", "handleChange", "field", "event", "value", "target", "type", "checked", "prev", "newData", "parseFloat", "coste", "toFixed", "precio", "handleSubmit", "dataToSave", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexDirection", "gap", "pt", "label", "select", "onChange", "required", "map", "vehicle", "nombre", "InputLabelProps", "shrink", "inputProps", "min", "step", "placeholder", "control", "multiline", "rows", "onClick", "variant", "disabled", "_c", "RefuelsList", "_s2", "refuels", "loadRefuels", "addRefuel", "dialogOpen", "setDialogOpen", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRefuel", "page", "setPage", "rowsPerPage", "setRowsPerPage", "handleAddRefuel", "handleEditRefuel", "handleDeleteRefuel", "console", "log", "handleSaveRefuel", "refuelData", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "formatDate", "dateString", "locale", "formatNumber", "num", "formatDecimal", "decimals", "undefined", "isNaN", "factor", "Math", "pow", "rounded", "round", "toString", "replace", "calculateTripInfo", "index", "kmTraveled", "consumption", "previousFullRefuel", "slice", "find", "r", "processedRefuels", "tripInfo", "validTrips", "filter", "distances", "trip", "minDistance", "maxDistance", "max", "avgDistance", "reduce", "a", "b", "totalRefuels", "totalLiters", "sum", "totalCost", "avgPricePerLiter", "avgConsumption", "paginatedRefuels", "justifyContent", "alignItems", "mb", "component", "fontWeight", "startIcon", "flexWrap", "sx", "flex", "textAlign", "color", "mt", "py", "fontSize", "gutterBottom", "align", "hover", "vehiculo_nombre", "size", "title", "count", "onPageChange", "newPage", "onRowsPerPageChange", "rowsPerPageOptions", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c2", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Refuels/RefuelsList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  FormControlLabel,\n  Switch,\n  Tooltip,\n  TablePagination,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  LocalGasStation as GasIcon,\n} from '@mui/icons-material';\nimport { useApp } from '../../context/AppContext';\nimport { format } from 'date-fns';\nimport { es } from 'date-fns/locale';\n\nconst RefuelDialog = ({ open, onClose, refuel, onSave, vehicles }) => {\n  const [formData, setFormData] = useState({\n    vehiculo_id: '',\n    fecha: new Date().toISOString().split('T')[0],\n    kilometros_actuales: '',\n    litros: '',\n    precio_litro: '',\n    coste_total: '',\n    gasolinera: '',\n    deposito_lleno: true,\n    notas: '',\n  });\n\n  useEffect(() => {\n    if (refuel) {\n      setFormData({\n        ...refuel,\n        fecha: refuel.fecha.split('T')[0], // Formato para input date\n        deposito_lleno: !!refuel.deposito_lleno, // Convert 0/1 to false/true\n      });\n    } else {\n      setFormData({\n        vehiculo_id: vehicles.length > 0 ? vehicles[0].id : '',\n        fecha: new Date().toISOString().split('T')[0],\n        kilometros_actuales: '',\n        litros: '',\n        precio_litro: '',\n        coste_total: '',\n        gasolinera: '',\n        deposito_lleno: true,\n        notas: '',\n      });\n    }\n  }, [refuel, vehicles, open]);\n\n  const handleChange = (field) => (event) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => {\n      const newData = { ...prev, [field]: value };\n      \n      // Cálculos automáticos para precios y costes.\n      if (field === 'coste_total') {\n        // Si el usuario modifica el coste total, calculamos el precio por litro.\n        const litros = parseFloat(newData.litros) || 0;\n        const coste = parseFloat(newData.coste_total) || 0;\n        if (litros > 0) {\n          newData.precio_litro = (coste / litros).toFixed(3);\n        }\n      } else if (field === 'litros' || field === 'precio_litro') {\n        // Si el usuario modifica los litros o el precio por litro, calculamos el coste total.\n        const litros = parseFloat(newData.litros) || 0;\n        const precio = parseFloat(newData.precio_litro) || 0;\n        newData.coste_total = (litros * precio).toFixed(2);\n      }\n      \n      return newData;\n    });\n  };\n\n  const handleSubmit = () => {\n    const dataToSave = {\n      ...formData,\n      litros: parseFloat(formData.litros),\n      precio_litro: parseFloat(formData.precio_litro),\n      coste_total: parseFloat(formData.coste_total),\n      kilometros_actuales: parseInt(formData.kilometros_actuales),\n      deposito_lleno: formData.deposito_lleno ? 1 : 0,\n    };\n    onSave(dataToSave);\n    onClose();\n  };\n\n  const isValid = formData.vehiculo_id && formData.fecha && formData.kilometros_actuales && \n                  formData.litros && formData.precio_litro;\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        {refuel ? 'Editar Repostaje' : 'Nuevo Repostaje'}\n      </DialogTitle>\n      <DialogContent>\n        <Box display=\"flex\" flexDirection=\"column\" gap={2} pt={1}>\n          <TextField\n            label=\"Vehículo\"\n            select\n            value={formData.vehiculo_id}\n            onChange={handleChange('vehiculo_id')}\n            fullWidth\n            required\n          >\n            {vehicles.map((vehicle) => (\n              <MenuItem key={vehicle.id} value={vehicle.id}>\n                {vehicle.nombre}\n              </MenuItem>\n            ))}\n          </TextField>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Fecha\"\n              type=\"date\"\n              value={formData.fecha}\n              onChange={handleChange('fecha')}\n              fullWidth\n              required\n              InputLabelProps={{ shrink: true }}\n            />\n            <TextField\n              label=\"Kilometraje actual\"\n              type=\"number\"\n              value={formData.kilometros_actuales}\n              onChange={handleChange('kilometros_actuales')}\n              fullWidth\n              required\n              inputProps={{ min: 0 }}\n            />\n          </Box>\n\n          <Box display=\"flex\" gap={2}>\n            <TextField\n              label=\"Litros\"\n              type=\"number\"\n              value={formData.litros}\n              onChange={handleChange('litros')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n            <TextField\n              label=\"Precio por litro (€)\"\n              type=\"number\"\n              value={formData.precio_litro}\n              onChange={handleChange('precio_litro')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.001 }}\n            />\n            <TextField\n              label=\"Coste total (€)\"\n              type=\"number\"\n              value={formData.coste_total}\n              onChange={handleChange('coste_total')}\n              fullWidth\n              required\n              inputProps={{ min: 0, step: 0.01 }}\n            />\n          </Box>\n\n          <TextField\n            label=\"Gasolinera\"\n            value={formData.gasolinera}\n            onChange={handleChange('gasolinera')}\n            fullWidth\n            placeholder=\"Nombre de la gasolinera\"\n          />\n\n          <FormControlLabel\n            control={\n              <Switch\n                checked={formData.deposito_lleno}\n                onChange={handleChange('deposito_lleno')}\n              />\n            }\n            label=\"Depósito lleno\"\n          />\n\n          <TextField\n            label=\"Notas\"\n            value={formData.notas}\n            onChange={handleChange('notas')}\n            fullWidth\n            multiline\n            rows={2}\n            placeholder=\"Notas adicionales...\"\n          />\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose}>Cancelar</Button>\n        <Button \n          onClick={handleSubmit} \n          variant=\"contained\"\n          disabled={!isValid}\n        >\n          {refuel ? 'Actualizar' : 'Guardar'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nconst RefuelsList = () => {\n  const { vehicles, refuels, loadRefuels, addRefuel } = useApp();\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [selectedRefuel, setSelectedRefuel] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(25);\n\n  useEffect(() => {\n    // Cargar todos los repostajes (sin límite)\n    loadRefuels(null, null);\n  }, []);\n\n  const handleAddRefuel = () => {\n    setSelectedRefuel(null);\n    setDialogOpen(true);\n  };\n\n  const handleEditRefuel = (refuel) => {\n    setSelectedRefuel(refuel);\n    setDialogOpen(true);\n  };\n\n  const handleDeleteRefuel = (refuel) => {\n    // TODO: Implementar eliminación con confirmación\n    console.log('Delete refuel:', refuel);\n  };\n\n  const handleSaveRefuel = async (refuelData) => {\n    try {\n      if (selectedRefuel) {\n        // TODO: Implementar actualización\n        console.log('Update refuel:', refuelData);\n      } else {\n        await addRefuel(refuelData);\n        // Recargar la lista\n        loadRefuels(null, null);\n      }\n    } catch (error) {\n      console.error('Error saving refuel:', error);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  const formatDate = (dateString) => {\n    try {\n      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });\n    } catch {\n      return dateString;\n    }\n  };\n\n  const formatNumber = (num) => {\n    return new Intl.NumberFormat('es-ES').format(num || 0);\n  };\n\n  // Formatear números a máximo 3 decimales\n  const formatDecimal = (num, decimals = 3) => {\n    if (num === null || num === undefined || isNaN(num)) return '-';\n    const factor = Math.pow(10, decimals);\n    const rounded = Math.round(num * factor) / factor;\n    // Eliminar ceros decimales innecesarios\n    return rounded.toString().replace(/\\.?0+$/, '');\n  };\n\n  // Calcular kilómetros recorridos y consumo\n  const calculateTripInfo = (refuel, index) => {\n    if (!refuel.deposito_lleno) return { kmTraveled: null, consumption: null };\n    \n    // Encontrar el repostaje completo anterior\n    const previousFullRefuel = refuels\n      .slice(index + 1) // Buscar en repostajes posteriores (la lista está ordenada por fecha descendente)\n      .find(r => r.deposito_lleno && r.vehiculo_id === refuel.vehiculo_id);\n    \n    if (!previousFullRefuel) return { kmTraveled: null, consumption: null };\n    \n    const kmTraveled = refuel.kilometros_actuales - previousFullRefuel.kilometros_actuales;\n    const consumption = kmTraveled > 0 ? (refuel.litros * 100) / kmTraveled : 0; // litros/100km\n    \n    return {\n      kmTraveled: kmTraveled > 0 ? kmTraveled : null,\n      consumption: kmTraveled > 0 ? consumption : null,\n    };\n  };\n\n  // Procesar todos los repostajes para obtener estadísticas\n  const processedRefuels = refuels.map((refuel, index) => {\n    const tripInfo = calculateTripInfo(refuel, index);\n    return {\n      ...refuel,\n      ...tripInfo\n    };\n  });\n\n  // Filtrar repostajes con datos de viaje válidos\n  const validTrips = processedRefuels.filter(r => r.kmTraveled > 0 && r.consumption > 0);\n  \n  // Calcular estadísticas de distancia\n  const distances = validTrips.map(trip => trip.kmTraveled);\n  const minDistance = distances.length > 0 ? Math.min(...distances) : 0;\n  const maxDistance = distances.length > 0 ? Math.max(...distances) : 0;\n  const avgDistance = distances.length > 0 ? \n    distances.reduce((a, b) => a + b, 0) / distances.length : 0;\n\n  // Calcular estadísticas rápidas\n  const totalRefuels = refuels.length;\n  const totalLiters = refuels.reduce((sum, refuel) => sum + (refuel.litros || 0), 0);\n  const totalCost = refuels.reduce((sum, refuel) => sum + (refuel.coste_total || 0), 0);\n  const avgPricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0;\n  \n  // Calcular consumo promedio (solo viajes válidos)\n  const avgConsumption = validTrips.length > 0 ? \n    validTrips.reduce((sum, trip) => sum + trip.consumption, 0) / validTrips.length : 0;\n\n  // Paginación\n  const paginatedRefuels = processedRefuels.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);\n\n  return (\n    <Box>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n        <Typography variant=\"h4\" component=\"h1\" fontWeight=\"bold\">\n          Repostajes\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleAddRefuel}\n          disabled={vehicles.length === 0}\n        >\n          Nuevo Repostaje\n        </Button>\n      </Box>\n\n      {/* Estadísticas rápidas */}\n      <Box display=\"flex\" gap={2} mb={3} flexWrap=\"wrap\">\n        <Card sx={{ flex: '1 1 180px' }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"primary\">{formatNumber(totalRefuels)}</Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Total Repostajes</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: '1 1 180px' }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"success.main\">\n              {formatDecimal(totalLiters, 1)}L\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Total Litros</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: '1 1 180px' }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"warning.main\">\n              {formatDecimal(totalCost, 2)}\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Coste Total (€)</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: '1 1 180px' }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"info.main\">\n              {formatDecimal(avgPricePerLiter, 3)}\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Precio Promedio/L (€)</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: '1 1 180px' }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Typography variant=\"h4\" color=\"secondary\">\n              {validTrips.length > 0 ? formatDecimal(avgConsumption, 1) : '-'}L\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">Consumo Promedio/100km</Typography>\n          </CardContent>\n        </Card>\n        <Card sx={{ flex: '1 1 180px' }}>\n          <CardContent sx={{ textAlign: 'center' }}>\n            <Box>\n              <Typography variant=\"subtitle2\" color=\"textSecondary\">Distancia entre repostajes</Typography>\n              <Box display=\"flex\" justifyContent=\"space-between\" mt={1}>\n                <Box>\n                  <Typography variant=\"caption\">Mín:</Typography>\n                  <Typography variant=\"h6\" color=\"error.main\">\n                    {minDistance ? formatDecimal(minDistance, 0) + ' km' : '-'}\n                  </Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"caption\">Máx:</Typography>\n                  <Typography variant=\"h6\" color=\"success.main\">\n                    {maxDistance ? formatDecimal(maxDistance, 0) + ' km' : '-'}\n                  </Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"caption\">Prom:</Typography>\n                  <Typography variant=\"h6\">\n                    {avgDistance ? formatDecimal(avgDistance, 0) + ' km' : '-'}\n                  </Typography>\n                </Box>\n              </Box>\n            </Box>\n          </CardContent>\n        </Card>\n      </Box>\n\n      {refuels.length === 0 ? (\n        <Card>\n          <CardContent>\n            <Box textAlign=\"center\" py={6}>\n              <GasIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"textSecondary\" gutterBottom>\n                No hay repostajes registrados\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\" mb={3}>\n                Comienza registrando tu primer repostaje para hacer seguimiento del consumo.\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={handleAddRefuel}\n                disabled={vehicles.length === 0}\n              >\n                {vehicles.length === 0 ? 'Primero agrega un vehículo' : 'Registrar Primer Repostaje'}\n              </Button>\n            </Box>\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <TableContainer component={Paper}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Fecha</TableCell>\n                  <TableCell>Vehículo</TableCell>\n                  <TableCell>Gasolinera</TableCell>\n                  <TableCell align=\"right\">Km</TableCell>\n                  <TableCell align=\"right\">Km Recorridos</TableCell>\n                  <TableCell align=\"right\">Litros</TableCell>\n                  <TableCell align=\"right\">Consumo (L/100km)</TableCell>\n                  <TableCell align=\"right\">€/L</TableCell>\n                  <TableCell align=\"right\">Total</TableCell>\n                  <TableCell align=\"center\">Lleno</TableCell>\n                  <TableCell align=\"center\">Acciones</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {paginatedRefuels.map((refuel) => (\n                  <TableRow key={refuel.id} hover>\n                    <TableCell>{formatDate(refuel.fecha)}</TableCell>\n                    <TableCell>{refuel.vehiculo_nombre}</TableCell>\n                    <TableCell>{refuel.gasolinera || '-'}</TableCell>\n                    <TableCell align=\"right\">{formatNumber(refuel.kilometros_actuales)}</TableCell>\n                    <TableCell align=\"right\">\n                      {refuel.kmTraveled !== null ? formatNumber(Math.round(refuel.kmTraveled)) : '-'}\n                    </TableCell>\n                    <TableCell align=\"right\">{formatDecimal(refuel.litros, 1)}L</TableCell>\n                    <TableCell align=\"right\">\n                      {refuel.consumption !== null ? formatDecimal(refuel.consumption, 1) + 'L' : '-'}\n                    </TableCell>\n                    <TableCell align=\"right\">{formatDecimal(refuel.precio_litro, 3)}€</TableCell>\n                    <TableCell align=\"right\">\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\n                        {formatCurrency(refuel.coste_total)}\n                      </Typography>\n                    </TableCell>\n                    <TableCell align=\"center\">\n                      <Chip \n                        label={refuel.deposito_lleno ? 'Sí' : 'No'} \n                        color={refuel.deposito_lleno ? 'success' : 'default'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell align=\"center\">\n                      <Tooltip title=\"Editar\">\n                        <IconButton size=\"small\" onClick={() => handleEditRefuel(refuel)}>\n                          <EditIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Eliminar\">\n                        <IconButton size=\"small\" color=\"error\" onClick={() => handleDeleteRefuel(refuel)}>\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n          \n          <TablePagination\n            component=\"div\"\n            count={refuels.length}\n            page={page}\n            onPageChange={(event, newPage) => setPage(newPage)}\n            rowsPerPage={rowsPerPage}\n            onRowsPerPageChange={(event) => {\n              setRowsPerPage(parseInt(event.target.value, 10));\n              setPage(0);\n            }}\n            rowsPerPageOptions={[10, 25, 50, 100]}\n            labelRowsPerPage=\"Filas por página:\"\n            labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}\n          />\n        </Card>\n      )}\n\n      <RefuelDialog\n        open={dialogOpen}\n        onClose={() => setDialogOpen(false)}\n        refuel={selectedRefuel}\n        onSave={handleSaveRefuel}\n        vehicles={vehicles}\n      />\n    </Box>\n  );\n};\n\nexport default RefuelsList;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,OAAO,EACPC,eAAe,QACV,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,eAAe,IAAIC,OAAO,QACrB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,MAAM;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC;IACvCgD,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,mBAAmB,EAAE,EAAE;IACvBC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,IAAI;IACpBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF1D,SAAS,CAAC,MAAM;IACd,IAAIyC,MAAM,EAAE;MACVK,WAAW,CAAC;QACV,GAAGL,MAAM;QACTO,KAAK,EAAEP,MAAM,CAACO,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAE;QACnCM,cAAc,EAAE,CAAC,CAAChB,MAAM,CAACgB,cAAc,CAAE;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,WAAW,CAAC;QACVC,WAAW,EAAEJ,QAAQ,CAACgB,MAAM,GAAG,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAACiB,EAAE,GAAG,EAAE;QACtDZ,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7CC,mBAAmB,EAAE,EAAE;QACvBC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,IAAI;QACpBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjB,MAAM,EAAEE,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EAE5B,MAAMsB,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,UAAU,GAAGH,KAAK,CAACE,MAAM,CAACE,OAAO,GAAGJ,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1FlB,WAAW,CAACsB,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG;QAAE,GAAGD,IAAI;QAAE,CAACN,KAAK,GAAGE;MAAM,CAAC;;MAE3C;MACA,IAAIF,KAAK,KAAK,aAAa,EAAE;QAC3B;QACA,MAAMT,MAAM,GAAGiB,UAAU,CAACD,OAAO,CAAChB,MAAM,CAAC,IAAI,CAAC;QAC9C,MAAMkB,KAAK,GAAGD,UAAU,CAACD,OAAO,CAACd,WAAW,CAAC,IAAI,CAAC;QAClD,IAAIF,MAAM,GAAG,CAAC,EAAE;UACdgB,OAAO,CAACf,YAAY,GAAG,CAACiB,KAAK,GAAGlB,MAAM,EAAEmB,OAAO,CAAC,CAAC,CAAC;QACpD;MACF,CAAC,MAAM,IAAIV,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,cAAc,EAAE;QACzD;QACA,MAAMT,MAAM,GAAGiB,UAAU,CAACD,OAAO,CAAChB,MAAM,CAAC,IAAI,CAAC;QAC9C,MAAMoB,MAAM,GAAGH,UAAU,CAACD,OAAO,CAACf,YAAY,CAAC,IAAI,CAAC;QACpDe,OAAO,CAACd,WAAW,GAAG,CAACF,MAAM,GAAGoB,MAAM,EAAED,OAAO,CAAC,CAAC,CAAC;MACpD;MAEA,OAAOH,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjB,GAAG9B,QAAQ;MACXQ,MAAM,EAAEiB,UAAU,CAACzB,QAAQ,CAACQ,MAAM,CAAC;MACnCC,YAAY,EAAEgB,UAAU,CAACzB,QAAQ,CAACS,YAAY,CAAC;MAC/CC,WAAW,EAAEe,UAAU,CAACzB,QAAQ,CAACU,WAAW,CAAC;MAC7CH,mBAAmB,EAAEwB,QAAQ,CAAC/B,QAAQ,CAACO,mBAAmB,CAAC;MAC3DK,cAAc,EAAEZ,QAAQ,CAACY,cAAc,GAAG,CAAC,GAAG;IAChD,CAAC;IACDf,MAAM,CAACiC,UAAU,CAAC;IAClBnC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMqC,OAAO,GAAGhC,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACO,mBAAmB,IACtEP,QAAQ,CAACQ,MAAM,IAAIR,QAAQ,CAACS,YAAY;EAExD,oBACEjB,OAAA,CAACtB,MAAM;IAACwB,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACsC,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3D3C,OAAA,CAACrB,WAAW;MAAAgE,QAAA,EACTvC,MAAM,GAAG,kBAAkB,GAAG;IAAiB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACd/C,OAAA,CAACpB,aAAa;MAAA+D,QAAA,eACZ3C,OAAA,CAACpC,GAAG;QAACoF,OAAO,EAAC,MAAM;QAACC,aAAa,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAR,QAAA,gBACvD3C,OAAA,CAAClB,SAAS;UACRsE,KAAK,EAAC,aAAU;UAChBC,MAAM;UACN1B,KAAK,EAAEnB,QAAQ,CAACE,WAAY;UAC5B4C,QAAQ,EAAE9B,YAAY,CAAC,aAAa,CAAE;UACtCkB,SAAS;UACTa,QAAQ;UAAAZ,QAAA,EAEPrC,QAAQ,CAACkD,GAAG,CAAEC,OAAO,iBACpBzD,OAAA,CAACjB,QAAQ;YAAkB4C,KAAK,EAAE8B,OAAO,CAAClC,EAAG;YAAAoB,QAAA,EAC1Cc,OAAO,CAACC;UAAM,GADFD,OAAO,CAAClC,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/C,OAAA,CAACpC,GAAG;UAACoF,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAAAP,QAAA,gBACzB3C,OAAA,CAAClB,SAAS;YACRsE,KAAK,EAAC,OAAO;YACbvB,IAAI,EAAC,MAAM;YACXF,KAAK,EAAEnB,QAAQ,CAACG,KAAM;YACtB2C,QAAQ,EAAE9B,YAAY,CAAC,OAAO,CAAE;YAChCkB,SAAS;YACTa,QAAQ;YACRI,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACF/C,OAAA,CAAClB,SAAS;YACRsE,KAAK,EAAC,oBAAoB;YAC1BvB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACO,mBAAoB;YACpCuC,QAAQ,EAAE9B,YAAY,CAAC,qBAAqB,CAAE;YAC9CkB,SAAS;YACTa,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE;YAAE;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/C,OAAA,CAACpC,GAAG;UAACoF,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAAAP,QAAA,gBACzB3C,OAAA,CAAClB,SAAS;YACRsE,KAAK,EAAC,QAAQ;YACdvB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACQ,MAAO;YACvBsC,QAAQ,EAAE9B,YAAY,CAAC,QAAQ,CAAE;YACjCkB,SAAS;YACTa,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAK;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACF/C,OAAA,CAAClB,SAAS;YACRsE,KAAK,EAAC,2BAAsB;YAC5BvB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACS,YAAa;YAC7BqC,QAAQ,EAAE9B,YAAY,CAAC,cAAc,CAAE;YACvCkB,SAAS;YACTa,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAM;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACF/C,OAAA,CAAClB,SAAS;YACRsE,KAAK,EAAC,sBAAiB;YACvBvB,IAAI,EAAC,QAAQ;YACbF,KAAK,EAAEnB,QAAQ,CAACU,WAAY;YAC5BoC,QAAQ,EAAE9B,YAAY,CAAC,aAAa,CAAE;YACtCkB,SAAS;YACTa,QAAQ;YACRM,UAAU,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAK;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/C,OAAA,CAAClB,SAAS;UACRsE,KAAK,EAAC,YAAY;UAClBzB,KAAK,EAAEnB,QAAQ,CAACW,UAAW;UAC3BmC,QAAQ,EAAE9B,YAAY,CAAC,YAAY,CAAE;UACrCkB,SAAS;UACTsB,WAAW,EAAC;QAAyB;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAEF/C,OAAA,CAAChB,gBAAgB;UACfiF,OAAO,eACLjE,OAAA,CAACf,MAAM;YACL6C,OAAO,EAAEtB,QAAQ,CAACY,cAAe;YACjCkC,QAAQ,EAAE9B,YAAY,CAAC,gBAAgB;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;UACDK,KAAK,EAAC;QAAgB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAEF/C,OAAA,CAAClB,SAAS;UACRsE,KAAK,EAAC,OAAO;UACbzB,KAAK,EAAEnB,QAAQ,CAACa,KAAM;UACtBiC,QAAQ,EAAE9B,YAAY,CAAC,OAAO,CAAE;UAChCkB,SAAS;UACTwB,SAAS;UACTC,IAAI,EAAE,CAAE;UACRH,WAAW,EAAC;QAAsB;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChB/C,OAAA,CAACnB,aAAa;MAAA8D,QAAA,gBACZ3C,OAAA,CAAChC,MAAM;QAACoG,OAAO,EAAEjE,OAAQ;QAAAwC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3C/C,OAAA,CAAChC,MAAM;QACLoG,OAAO,EAAE/B,YAAa;QACtBgC,OAAO,EAAC,WAAW;QACnBC,QAAQ,EAAE,CAAC9B,OAAQ;QAAAG,QAAA,EAElBvC,MAAM,GAAG,YAAY,GAAG;MAAS;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACxC,EAAA,CA7LIN,YAAY;AAAAsE,EAAA,GAAZtE,YAAY;AA+LlB,MAAMuE,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAM;IAAEnE,QAAQ;IAAEoE,OAAO;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGhF,MAAM,CAAC,CAAC;EAC9D,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGpH,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqH,cAAc,EAAEC,iBAAiB,CAAC,GAAGtH,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuH,IAAI,EAAEC,OAAO,CAAC,GAAGxH,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACyH,WAAW,EAAEC,cAAc,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd;IACAgH,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BL,iBAAiB,CAAC,IAAI,CAAC;IACvBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMQ,gBAAgB,GAAIlF,MAAM,IAAK;IACnC4E,iBAAiB,CAAC5E,MAAM,CAAC;IACzB0E,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMS,kBAAkB,GAAInF,MAAM,IAAK;IACrC;IACAoF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAErF,MAAM,CAAC;EACvC,CAAC;EAED,MAAMsF,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC7C,IAAI;MACF,IAAIZ,cAAc,EAAE;QAClB;QACAS,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,UAAU,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMf,SAAS,CAACe,UAAU,CAAC;QAC3B;QACAhB,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACrG,MAAM,CAACiG,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,OAAOvG,MAAM,CAAC,IAAIe,IAAI,CAACwF,UAAU,CAAC,EAAE,YAAY,EAAE;QAAEC,MAAM,EAAEvG;MAAG,CAAC,CAAC;IACnE,CAAC,CAAC,MAAM;MACN,OAAOsG,UAAU;IACnB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,GAAG,IAAK;IAC5B,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACnG,MAAM,CAAC0G,GAAG,IAAI,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACD,GAAG,EAAEE,QAAQ,GAAG,CAAC,KAAK;IAC3C,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,IAAIC,KAAK,CAACJ,GAAG,CAAC,EAAE,OAAO,GAAG;IAC/D,MAAMK,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEL,QAAQ,CAAC;IACrC,MAAMM,OAAO,GAAGF,IAAI,CAACG,KAAK,CAACT,GAAG,GAAGK,MAAM,CAAC,GAAGA,MAAM;IACjD;IACA,OAAOG,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;EACjD,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAC/G,MAAM,EAAEgH,KAAK,KAAK;IAC3C,IAAI,CAAChH,MAAM,CAACgB,cAAc,EAAE,OAAO;MAAEiG,UAAU,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAK,CAAC;;IAE1E;IACA,MAAMC,kBAAkB,GAAG7C,OAAO,CAC/B8C,KAAK,CAACJ,KAAK,GAAG,CAAC,CAAC,CAAC;IAAA,CACjBK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtG,cAAc,IAAIsG,CAAC,CAAChH,WAAW,KAAKN,MAAM,CAACM,WAAW,CAAC;IAEtE,IAAI,CAAC6G,kBAAkB,EAAE,OAAO;MAAEF,UAAU,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAK,CAAC;IAEvE,MAAMD,UAAU,GAAGjH,MAAM,CAACW,mBAAmB,GAAGwG,kBAAkB,CAACxG,mBAAmB;IACtF,MAAMuG,WAAW,GAAGD,UAAU,GAAG,CAAC,GAAIjH,MAAM,CAACY,MAAM,GAAG,GAAG,GAAIqG,UAAU,GAAG,CAAC,CAAC,CAAC;;IAE7E,OAAO;MACLA,UAAU,EAAEA,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,IAAI;MAC9CC,WAAW,EAAED,UAAU,GAAG,CAAC,GAAGC,WAAW,GAAG;IAC9C,CAAC;EACH,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAGjD,OAAO,CAAClB,GAAG,CAAC,CAACpD,MAAM,EAAEgH,KAAK,KAAK;IACtD,MAAMQ,QAAQ,GAAGT,iBAAiB,CAAC/G,MAAM,EAAEgH,KAAK,CAAC;IACjD,OAAO;MACL,GAAGhH,MAAM;MACT,GAAGwH;IACL,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGF,gBAAgB,CAACG,MAAM,CAACJ,CAAC,IAAIA,CAAC,CAACL,UAAU,GAAG,CAAC,IAAIK,CAAC,CAACJ,WAAW,GAAG,CAAC,CAAC;;EAEtF;EACA,MAAMS,SAAS,GAAGF,UAAU,CAACrE,GAAG,CAACwE,IAAI,IAAIA,IAAI,CAACX,UAAU,CAAC;EACzD,MAAMY,WAAW,GAAGF,SAAS,CAACzG,MAAM,GAAG,CAAC,GAAGuF,IAAI,CAAC/C,GAAG,CAAC,GAAGiE,SAAS,CAAC,GAAG,CAAC;EACrE,MAAMG,WAAW,GAAGH,SAAS,CAACzG,MAAM,GAAG,CAAC,GAAGuF,IAAI,CAACsB,GAAG,CAAC,GAAGJ,SAAS,CAAC,GAAG,CAAC;EACrE,MAAMK,WAAW,GAAGL,SAAS,CAACzG,MAAM,GAAG,CAAC,GACtCyG,SAAS,CAACM,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGR,SAAS,CAACzG,MAAM,GAAG,CAAC;;EAE7D;EACA,MAAMkH,YAAY,GAAG9D,OAAO,CAACpD,MAAM;EACnC,MAAMmH,WAAW,GAAG/D,OAAO,CAAC2D,MAAM,CAAC,CAACK,GAAG,EAAEtI,MAAM,KAAKsI,GAAG,IAAItI,MAAM,CAACY,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAM2H,SAAS,GAAGjE,OAAO,CAAC2D,MAAM,CAAC,CAACK,GAAG,EAAEtI,MAAM,KAAKsI,GAAG,IAAItI,MAAM,CAACc,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF,MAAM0H,gBAAgB,GAAGH,WAAW,GAAG,CAAC,GAAGE,SAAS,GAAGF,WAAW,GAAG,CAAC;;EAEtE;EACA,MAAMI,cAAc,GAAGhB,UAAU,CAACvG,MAAM,GAAG,CAAC,GAC1CuG,UAAU,CAACQ,MAAM,CAAC,CAACK,GAAG,EAAEV,IAAI,KAAKU,GAAG,GAAGV,IAAI,CAACV,WAAW,EAAE,CAAC,CAAC,GAAGO,UAAU,CAACvG,MAAM,GAAG,CAAC;;EAErF;EACA,MAAMwH,gBAAgB,GAAGnB,gBAAgB,CAACH,KAAK,CAACvC,IAAI,GAAGE,WAAW,EAAEF,IAAI,GAAGE,WAAW,GAAGA,WAAW,CAAC;EAErG,oBACEnF,OAAA,CAACpC,GAAG;IAAA+E,QAAA,gBACF3C,OAAA,CAACpC,GAAG;MAACoF,OAAO,EAAC,MAAM;MAAC+F,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAtG,QAAA,gBAC3E3C,OAAA,CAACnC,UAAU;QAACwG,OAAO,EAAC,IAAI;QAAC6E,SAAS,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAAAxG,QAAA,EAAC;MAE1D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/C,OAAA,CAAChC,MAAM;QACLqG,OAAO,EAAC,WAAW;QACnB+E,SAAS,eAAEpJ,OAAA,CAACX,OAAO;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBqB,OAAO,EAAEiB,eAAgB;QACzBf,QAAQ,EAAEhE,QAAQ,CAACgB,MAAM,KAAK,CAAE;QAAAqB,QAAA,EACjC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN/C,OAAA,CAACpC,GAAG;MAACoF,OAAO,EAAC,MAAM;MAACE,GAAG,EAAE,CAAE;MAAC+F,EAAE,EAAE,CAAE;MAACI,QAAQ,EAAC,MAAM;MAAA1G,QAAA,gBAChD3C,OAAA,CAAClC,IAAI;QAACwL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAY,CAAE;QAAA5G,QAAA,eAC9B3C,OAAA,CAACjC,WAAW;UAACuL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA7G,QAAA,gBACvC3C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACoF,KAAK,EAAC,SAAS;YAAA9G,QAAA,EAAE2D,YAAY,CAACkC,YAAY;UAAC;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClF/C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,OAAO;YAACoF,KAAK,EAAC,eAAe;YAAA9G,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACP/C,OAAA,CAAClC,IAAI;QAACwL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAY,CAAE;QAAA5G,QAAA,eAC9B3C,OAAA,CAACjC,WAAW;UAACuL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA7G,QAAA,gBACvC3C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACoF,KAAK,EAAC,cAAc;YAAA9G,QAAA,GAC1C6D,aAAa,CAACiC,WAAW,EAAE,CAAC,CAAC,EAAC,GACjC;UAAA;YAAA7F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,OAAO;YAACoF,KAAK,EAAC,eAAe;YAAA9G,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACP/C,OAAA,CAAClC,IAAI;QAACwL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAY,CAAE;QAAA5G,QAAA,eAC9B3C,OAAA,CAACjC,WAAW;UAACuL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA7G,QAAA,gBACvC3C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACoF,KAAK,EAAC,cAAc;YAAA9G,QAAA,EAC1C6D,aAAa,CAACmC,SAAS,EAAE,CAAC;UAAC;YAAA/F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACb/C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,OAAO;YAACoF,KAAK,EAAC,eAAe;YAAA9G,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACP/C,OAAA,CAAClC,IAAI;QAACwL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAY,CAAE;QAAA5G,QAAA,eAC9B3C,OAAA,CAACjC,WAAW;UAACuL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA7G,QAAA,gBACvC3C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACoF,KAAK,EAAC,WAAW;YAAA9G,QAAA,EACvC6D,aAAa,CAACoC,gBAAgB,EAAE,CAAC;UAAC;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACb/C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,OAAO;YAACoF,KAAK,EAAC,eAAe;YAAA9G,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACP/C,OAAA,CAAClC,IAAI;QAACwL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAY,CAAE;QAAA5G,QAAA,eAC9B3C,OAAA,CAACjC,WAAW;UAACuL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA7G,QAAA,gBACvC3C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACoF,KAAK,EAAC,WAAW;YAAA9G,QAAA,GACvCkF,UAAU,CAACvG,MAAM,GAAG,CAAC,GAAGkF,aAAa,CAACqC,cAAc,EAAE,CAAC,CAAC,GAAG,GAAG,EAAC,GAClE;UAAA;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,OAAO;YAACoF,KAAK,EAAC,eAAe;YAAA9G,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACP/C,OAAA,CAAClC,IAAI;QAACwL,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAY,CAAE;QAAA5G,QAAA,eAC9B3C,OAAA,CAACjC,WAAW;UAACuL,EAAE,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAA7G,QAAA,eACvC3C,OAAA,CAACpC,GAAG;YAAA+E,QAAA,gBACF3C,OAAA,CAACnC,UAAU;cAACwG,OAAO,EAAC,WAAW;cAACoF,KAAK,EAAC,eAAe;cAAA9G,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7F/C,OAAA,CAACpC,GAAG;cAACoF,OAAO,EAAC,MAAM;cAAC+F,cAAc,EAAC,eAAe;cAACW,EAAE,EAAE,CAAE;cAAA/G,QAAA,gBACvD3C,OAAA,CAACpC,GAAG;gBAAA+E,QAAA,gBACF3C,OAAA,CAACnC,UAAU;kBAACwG,OAAO,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/C/C,OAAA,CAACnC,UAAU;kBAACwG,OAAO,EAAC,IAAI;kBAACoF,KAAK,EAAC,YAAY;kBAAA9G,QAAA,EACxCsF,WAAW,GAAGzB,aAAa,CAACyB,WAAW,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG;gBAAG;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/C,OAAA,CAACpC,GAAG;gBAAA+E,QAAA,gBACF3C,OAAA,CAACnC,UAAU;kBAACwG,OAAO,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/C/C,OAAA,CAACnC,UAAU;kBAACwG,OAAO,EAAC,IAAI;kBAACoF,KAAK,EAAC,cAAc;kBAAA9G,QAAA,EAC1CuF,WAAW,GAAG1B,aAAa,CAAC0B,WAAW,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG;gBAAG;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/C,OAAA,CAACpC,GAAG;gBAAA+E,QAAA,gBACF3C,OAAA,CAACnC,UAAU;kBAACwG,OAAO,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChD/C,OAAA,CAACnC,UAAU;kBAACwG,OAAO,EAAC,IAAI;kBAAA1B,QAAA,EACrByF,WAAW,GAAG5B,aAAa,CAAC4B,WAAW,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG;gBAAG;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEL2B,OAAO,CAACpD,MAAM,KAAK,CAAC,gBACnBtB,OAAA,CAAClC,IAAI;MAAA6E,QAAA,eACH3C,OAAA,CAACjC,WAAW;QAAA4E,QAAA,eACV3C,OAAA,CAACpC,GAAG;UAAC4L,SAAS,EAAC,QAAQ;UAACG,EAAE,EAAE,CAAE;UAAAhH,QAAA,gBAC5B3C,OAAA,CAACL,OAAO;YAAC2J,EAAE,EAAE;cAAEM,QAAQ,EAAE,EAAE;cAAEH,KAAK,EAAE,gBAAgB;cAAER,EAAE,EAAE;YAAE;UAAE;YAAArG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjE/C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACoF,KAAK,EAAC,eAAe;YAACI,YAAY;YAAAlH,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACnC,UAAU;YAACwG,OAAO,EAAC,OAAO;YAACoF,KAAK,EAAC,eAAe;YAACR,EAAE,EAAE,CAAE;YAAAtG,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAAChC,MAAM;YACLqG,OAAO,EAAC,WAAW;YACnB+E,SAAS,eAAEpJ,OAAA,CAACX,OAAO;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBqB,OAAO,EAAEiB,eAAgB;YACzBf,QAAQ,EAAEhE,QAAQ,CAACgB,MAAM,KAAK,CAAE;YAAAqB,QAAA,EAE/BrC,QAAQ,CAACgB,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;UAA4B;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEP/C,OAAA,CAAClC,IAAI;MAAA6E,QAAA,gBACH3C,OAAA,CAAC5B,cAAc;QAAC8K,SAAS,EAAE3K,KAAM;QAAAoE,QAAA,eAC/B3C,OAAA,CAAC/B,KAAK;UAAA0E,QAAA,gBACJ3C,OAAA,CAAC3B,SAAS;YAAAsE,QAAA,eACR3C,OAAA,CAAC1B,QAAQ;cAAAqE,QAAA,gBACP3C,OAAA,CAAC7B,SAAS;gBAAAwE,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B/C,OAAA,CAAC7B,SAAS;gBAAAwE,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/C,OAAA,CAAC7B,SAAS;gBAAAwE,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACvC/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClD/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3C/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtD/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACxC/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1C/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,QAAQ;gBAAAnH,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3C/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,QAAQ;gBAAAnH,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/C,OAAA,CAAC9B,SAAS;YAAAyE,QAAA,EACPmG,gBAAgB,CAACtF,GAAG,CAAEpD,MAAM,iBAC3BJ,OAAA,CAAC1B,QAAQ;cAAiByL,KAAK;cAAApH,QAAA,gBAC7B3C,OAAA,CAAC7B,SAAS;gBAAAwE,QAAA,EAAEwD,UAAU,CAAC/F,MAAM,CAACO,KAAK;cAAC;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjD/C,OAAA,CAAC7B,SAAS;gBAAAwE,QAAA,EAAEvC,MAAM,CAAC4J;cAAe;gBAAApH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/C/C,OAAA,CAAC7B,SAAS;gBAAAwE,QAAA,EAAEvC,MAAM,CAACe,UAAU,IAAI;cAAG;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjD/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EAAE2D,YAAY,CAAClG,MAAM,CAACW,mBAAmB;cAAC;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/E/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EACrBvC,MAAM,CAACiH,UAAU,KAAK,IAAI,GAAGf,YAAY,CAACO,IAAI,CAACG,KAAK,CAAC5G,MAAM,CAACiH,UAAU,CAAC,CAAC,GAAG;cAAG;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACZ/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,GAAE6D,aAAa,CAACpG,MAAM,CAACY,MAAM,EAAE,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACvE/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,EACrBvC,MAAM,CAACkH,WAAW,KAAK,IAAI,GAAGd,aAAa,CAACpG,MAAM,CAACkH,WAAW,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG;cAAG;gBAAA1E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACZ/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,GAAE6D,aAAa,CAACpG,MAAM,CAACa,YAAY,EAAE,CAAC,CAAC,EAAC,QAAC;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7E/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,OAAO;gBAAAnH,QAAA,eACtB3C,OAAA,CAACnC,UAAU;kBAACwG,OAAO,EAAC,OAAO;kBAAC8E,UAAU,EAAC,MAAM;kBAAAxG,QAAA,EAC1CkD,cAAc,CAACzF,MAAM,CAACc,WAAW;gBAAC;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,QAAQ;gBAAAnH,QAAA,eACvB3C,OAAA,CAACxB,IAAI;kBACH4E,KAAK,EAAEhD,MAAM,CAACgB,cAAc,GAAG,IAAI,GAAG,IAAK;kBAC3CqI,KAAK,EAAErJ,MAAM,CAACgB,cAAc,GAAG,SAAS,GAAG,SAAU;kBACrD6I,IAAI,EAAC;gBAAO;kBAAArH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/C,OAAA,CAAC7B,SAAS;gBAAC2L,KAAK,EAAC,QAAQ;gBAAAnH,QAAA,gBACvB3C,OAAA,CAACd,OAAO;kBAACgL,KAAK,EAAC,QAAQ;kBAAAvH,QAAA,eACrB3C,OAAA,CAACvB,UAAU;oBAACwL,IAAI,EAAC,OAAO;oBAAC7F,OAAO,EAAEA,CAAA,KAAMkB,gBAAgB,CAAClF,MAAM,CAAE;oBAAAuC,QAAA,eAC/D3C,OAAA,CAACT,QAAQ;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACV/C,OAAA,CAACd,OAAO;kBAACgL,KAAK,EAAC,UAAU;kBAAAvH,QAAA,eACvB3C,OAAA,CAACvB,UAAU;oBAACwL,IAAI,EAAC,OAAO;oBAACR,KAAK,EAAC,OAAO;oBAACrF,OAAO,EAAEA,CAAA,KAAMmB,kBAAkB,CAACnF,MAAM,CAAE;oBAAAuC,QAAA,eAC/E3C,OAAA,CAACP,UAAU;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,GApCC3C,MAAM,CAACmB,EAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqCd,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEjB/C,OAAA,CAACb,eAAe;QACd+J,SAAS,EAAC,KAAK;QACfiB,KAAK,EAAEzF,OAAO,CAACpD,MAAO;QACtB2D,IAAI,EAAEA,IAAK;QACXmF,YAAY,EAAEA,CAAC1I,KAAK,EAAE2I,OAAO,KAAKnF,OAAO,CAACmF,OAAO,CAAE;QACnDlF,WAAW,EAAEA,WAAY;QACzBmF,mBAAmB,EAAG5I,KAAK,IAAK;UAC9B0D,cAAc,CAAC7C,QAAQ,CAACb,KAAK,CAACE,MAAM,CAACD,KAAK,EAAE,EAAE,CAAC,CAAC;UAChDuD,OAAO,CAAC,CAAC,CAAC;QACZ,CAAE;QACFqF,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;QACtCC,gBAAgB,EAAC,sBAAmB;QACpCC,kBAAkB,EAAEA,CAAC;UAAEC,IAAI;UAAEC,EAAE;UAAER;QAAM,CAAC,KAAK,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK;MAAG;QAAAvH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAED/C,OAAA,CAACC,YAAY;MACXC,IAAI,EAAE2E,UAAW;MACjB1E,OAAO,EAAEA,CAAA,KAAM2E,aAAa,CAAC,KAAK,CAAE;MACpC1E,MAAM,EAAE2E,cAAe;MACvB1E,MAAM,EAAEqF,gBAAiB;MACzBpF,QAAQ,EAAEA;IAAS;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC0B,GAAA,CA/TID,WAAW;EAAA,QACuC5E,MAAM;AAAA;AAAAgL,GAAA,GADxDpG,WAAW;AAiUjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAqG,GAAA;AAAAC,YAAA,CAAAtG,EAAA;AAAAsG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}