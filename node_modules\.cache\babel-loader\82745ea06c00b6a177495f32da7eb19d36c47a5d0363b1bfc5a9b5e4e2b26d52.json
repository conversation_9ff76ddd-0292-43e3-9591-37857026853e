{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useOrientation = useOrientation;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEnhancedEffect = _interopRequireDefault(require(\"@mui/utils/useEnhancedEffect\"));\nvar _utils = require(\"../../../utils/utils\");\nfunction getOrientation() {\n  if (typeof window === 'undefined') {\n    return 'portrait';\n  }\n  if (window.screen && window.screen.orientation && window.screen.orientation.angle) {\n    return Math.abs(window.screen.orientation.angle) === 90 ? 'landscape' : 'portrait';\n  }\n\n  // Support IOS safari\n  if (window.orientation) {\n    return Math.abs(Number(window.orientation)) === 90 ? 'landscape' : 'portrait';\n  }\n  return 'portrait';\n}\nfunction useOrientation(views, customOrientation) {\n  const [orientation, setOrientation] = React.useState(getOrientation);\n  (0, _useEnhancedEffect.default)(() => {\n    const eventHandler = () => {\n      setOrientation(getOrientation());\n    };\n    window.addEventListener('orientationchange', eventHandler);\n    return () => {\n      window.removeEventListener('orientationchange', eventHandler);\n    };\n  }, []);\n  if ((0, _utils.arrayIncludes)(views, ['hours', 'minutes', 'seconds'])) {\n    // could not display 13:34:44 in landscape mode\n    return 'portrait';\n  }\n  return customOrientation ?? orientation;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "useOrientation", "React", "_useEnhancedEffect", "_utils", "getOrientation", "window", "screen", "orientation", "angle", "Math", "abs", "Number", "views", "customOrientation", "setOrientation", "useState", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "arrayIncludes"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/hooks/useOrientation.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useOrientation = useOrientation;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEnhancedEffect = _interopRequireDefault(require(\"@mui/utils/useEnhancedEffect\"));\nvar _utils = require(\"../../../utils/utils\");\nfunction getOrientation() {\n  if (typeof window === 'undefined') {\n    return 'portrait';\n  }\n  if (window.screen && window.screen.orientation && window.screen.orientation.angle) {\n    return Math.abs(window.screen.orientation.angle) === 90 ? 'landscape' : 'portrait';\n  }\n\n  // Support IOS safari\n  if (window.orientation) {\n    return Math.abs(Number(window.orientation)) === 90 ? 'landscape' : 'portrait';\n  }\n  return 'portrait';\n}\nfunction useOrientation(views, customOrientation) {\n  const [orientation, setOrientation] = React.useState(getOrientation);\n  (0, _useEnhancedEffect.default)(() => {\n    const eventHandler = () => {\n      setOrientation(getOrientation());\n    };\n    window.addEventListener('orientationchange', eventHandler);\n    return () => {\n      window.removeEventListener('orientationchange', eventHandler);\n    };\n  }, []);\n  if ((0, _utils.arrayIncludes)(views, ['hours', 'minutes', 'seconds'])) {\n    // could not display 13:34:44 in landscape mode\n    return 'portrait';\n  }\n  return customOrientation ?? orientation;\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,cAAc,GAAGA,cAAc;AACvC,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,kBAAkB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACxF,IAAIU,MAAM,GAAGV,OAAO,CAAC,sBAAsB,CAAC;AAC5C,SAASW,cAAcA,CAAA,EAAG;EACxB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,UAAU;EACnB;EACA,IAAIA,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,WAAW,IAAIF,MAAM,CAACC,MAAM,CAACC,WAAW,CAACC,KAAK,EAAE;IACjF,OAAOC,IAAI,CAACC,GAAG,CAACL,MAAM,CAACC,MAAM,CAACC,WAAW,CAACC,KAAK,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,UAAU;EACpF;;EAEA;EACA,IAAIH,MAAM,CAACE,WAAW,EAAE;IACtB,OAAOE,IAAI,CAACC,GAAG,CAACC,MAAM,CAACN,MAAM,CAACE,WAAW,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,UAAU;EAC/E;EACA,OAAO,UAAU;AACnB;AACA,SAASP,cAAcA,CAACY,KAAK,EAAEC,iBAAiB,EAAE;EAChD,MAAM,CAACN,WAAW,EAAEO,cAAc,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAACX,cAAc,CAAC;EACpE,CAAC,CAAC,EAAEF,kBAAkB,CAACR,OAAO,EAAE,MAAM;IACpC,MAAMsB,YAAY,GAAGA,CAAA,KAAM;MACzBF,cAAc,CAACV,cAAc,CAAC,CAAC,CAAC;IAClC,CAAC;IACDC,MAAM,CAACY,gBAAgB,CAAC,mBAAmB,EAAED,YAAY,CAAC;IAC1D,OAAO,MAAM;MACXX,MAAM,CAACa,mBAAmB,CAAC,mBAAmB,EAAEF,YAAY,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAAC,CAAC,EAAEb,MAAM,CAACgB,aAAa,EAAEP,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;IACrE;IACA,OAAO,UAAU;EACnB;EACA,OAAOC,iBAAiB,IAAIN,WAAW;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}