{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.IsValidValueContext = void 0;\nexports.useIsValidValue = useIsValidValue;\nvar React = _interopRequireWildcard(require(\"react\"));\nconst IsValidValueContext = exports.IsValidValueContext = /*#__PURE__*/React.createContext(() => true);\n\n/**\n * Returns a function to check if a value is valid according to the validation props passed to the parent Picker.\n */\nif (process.env.NODE_ENV !== \"production\") IsValidValueContext.displayName = \"IsValidValueContext\";\nfunction useIsValidValue() {\n  return React.useContext(IsValidValueContext);\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "Object", "defineProperty", "exports", "value", "IsValidValueContext", "useIsValidValue", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "useContext"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/hooks/useIsValidValue.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.IsValidValueContext = void 0;\nexports.useIsValidValue = useIsValidValue;\nvar React = _interopRequireWildcard(require(\"react\"));\nconst IsValidValueContext = exports.IsValidValueContext = /*#__PURE__*/React.createContext(() => true);\n\n/**\n * Returns a function to check if a value is valid according to the validation props passed to the parent Picker.\n */\nif (process.env.NODE_ENV !== \"production\") IsValidValueContext.displayName = \"IsValidValueContext\";\nfunction useIsValidValue() {\n  return React.useContext(IsValidValueContext);\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,mBAAmB,GAAG,KAAK,CAAC;AACpCF,OAAO,CAACG,eAAe,GAAGA,eAAe;AACzC,IAAIC,KAAK,GAAGT,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,MAAMM,mBAAmB,GAAGF,OAAO,CAACE,mBAAmB,GAAG,aAAaE,KAAK,CAACC,aAAa,CAAC,MAAM,IAAI,CAAC;;AAEtG;AACA;AACA;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEN,mBAAmB,CAACO,WAAW,GAAG,qBAAqB;AAClG,SAASN,eAAeA,CAAA,EAAG;EACzB,OAAOC,KAAK,CAACM,UAAU,CAACR,mBAAmB,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}