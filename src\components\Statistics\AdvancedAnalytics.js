import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Speed as SpeedIcon,
  LocalGasStation as GasIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  ScatterChart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';
import { useApp } from '../../context/AppContext';
import { format, differenceInDays, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';

const AdvancedAnalytics = () => {
  const { vehicles, refuels, expenses } = useApp();
  const [analytics, setAnalytics] = useState({
    mileageEvolution: [],
    consumptionTrends: [],
    fuelPriceTrends: [],
    efficiencyMetrics: {},
    periodicAnalysis: {},
    extremeValues: {},
    predictions: {}
  });

  useEffect(() => {
    calculateAdvancedAnalytics();
  }, [refuels, expenses, vehicles]);

  const calculateAdvancedAnalytics = () => {
    if (refuels.length === 0) return;

    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));

    // 1. Evolución del kilometraje
    const mileageEvolution = sortedRefuels.map((refuel, index) => {
      const prevRefuel = index > 0 ? sortedRefuels[index - 1] : null;
      const kmDiff = prevRefuel ? refuel.kilometros_actuales - prevRefuel.kilometros_actuales : 0;
      const daysDiff = prevRefuel ? differenceInDays(new Date(refuel.fecha), new Date(prevRefuel.fecha)) : 0;
      const dailyKm = daysDiff > 0 ? kmDiff / daysDiff : 0;

      return {
        fecha: refuel.fecha,
        kilometraje: refuel.kilometros_actuales,
        kmRecorridos: kmDiff,
        diasTranscurridos: daysDiff,
        kmDiarios: dailyKm,
        vehiculo: refuel.vehiculo_nombre,
        month: format(new Date(refuel.fecha), 'MMM yyyy', { locale: es })
      };
    });

    // 2. Tendencias de consumo
    const consumptionTrends = [];
    for (let i = 1; i < sortedRefuels.length; i++) {
      const current = sortedRefuels[i];
      const previous = sortedRefuels[i - 1];

      if (current.vehiculo_id === previous.vehiculo_id) {
        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;
        const consumption = kmDiff > 0 ? (current.litros / kmDiff) * 100 : 0;
        const efficiency = kmDiff > 0 ? kmDiff / current.litros : 0;

        consumptionTrends.push({
          fecha: current.fecha,
          consumo: consumption,
          eficiencia: efficiency,
          litros: current.litros,
          kilometros: kmDiff,
          vehiculo: current.vehiculo_nombre,
          month: format(new Date(current.fecha), 'MMM yyyy', { locale: es })
        });
      }
    }

    // 3. Tendencias de precios de combustible
    const fuelPriceTrends = sortedRefuels.map(refuel => ({
      fecha: refuel.fecha,
      precio: refuel.precio_litro,
      costeTotal: refuel.coste_total,
      litros: refuel.litros,
      gasolinera: refuel.gasolinera,
      month: format(new Date(refuel.fecha), 'MMM yyyy', { locale: es })
    }));

    // 4. Métricas de eficiencia
    const efficiencyMetrics = calculateEfficiencyMetrics(consumptionTrends, mileageEvolution);

    // 5. Análisis periódico
    const periodicAnalysis = calculatePeriodicAnalysis(sortedRefuels, consumptionTrends);

    // 6. Valores extremos
    const extremeValues = calculateExtremeValues(consumptionTrends, fuelPriceTrends, mileageEvolution);

    // 7. Predicciones simples
    const predictions = calculatePredictions(consumptionTrends, mileageEvolution);

    setAnalytics({
      mileageEvolution,
      consumptionTrends,
      fuelPriceTrends,
      efficiencyMetrics,
      periodicAnalysis,
      extremeValues,
      predictions
    });
  };

  const calculateEfficiencyMetrics = (consumptionTrends, mileageEvolution) => {
    if (consumptionTrends.length === 0) return {};

    const consumptions = consumptionTrends.map(t => t.consumo).filter(c => c > 0);
    const efficiencies = consumptionTrends.map(t => t.eficiencia).filter(e => e > 0);
    const dailyKms = mileageEvolution.map(m => m.kmDiarios).filter(k => k > 0);

    return {
      avgConsumption: consumptions.reduce((a, b) => a + b, 0) / consumptions.length,
      minConsumption: Math.min(...consumptions),
      maxConsumption: Math.max(...consumptions),
      avgEfficiency: efficiencies.reduce((a, b) => a + b, 0) / efficiencies.length,
      minEfficiency: Math.min(...efficiencies),
      maxEfficiency: Math.max(...efficiencies),
      avgDailyKm: dailyKms.reduce((a, b) => a + b, 0) / dailyKms.length,
      minDailyKm: Math.min(...dailyKms),
      maxDailyKm: Math.max(...dailyKms),
      totalKm: mileageEvolution.length > 0 ?
        mileageEvolution[mileageEvolution.length - 1].kilometraje - mileageEvolution[0].kilometraje : 0
    };
  };

  const calculatePeriodicAnalysis = (refuels, consumptionTrends) => {
    // Análisis por mes
    const monthlyData = {};

    refuels.forEach(refuel => {
      const month = format(new Date(refuel.fecha), 'yyyy-MM');
      if (!monthlyData[month]) {
        monthlyData[month] = {
          refuels: 0,
          totalLiters: 0,
          totalCost: 0,
          avgPrice: 0,
          consumption: []
        };
      }
      monthlyData[month].refuels++;
      monthlyData[month].totalLiters += refuel.litros;
      monthlyData[month].totalCost += refuel.coste_total;
    });

    consumptionTrends.forEach(trend => {
      const month = format(new Date(trend.fecha), 'yyyy-MM');
      if (monthlyData[month]) {
        monthlyData[month].consumption.push(trend.consumo);
      }
    });

    // Calcular promedios mensuales
    Object.keys(monthlyData).forEach(month => {
      const data = monthlyData[month];
      data.avgPrice = data.totalLiters > 0 ? data.totalCost / data.totalLiters : 0;
      data.avgConsumption = data.consumption.length > 0 ?
        data.consumption.reduce((a, b) => a + b, 0) / data.consumption.length : 0;
    });

    return { monthlyData };
  };

  const calculateExtremeValues = (consumptionTrends, fuelPriceTrends, mileageEvolution) => {
    if (consumptionTrends.length === 0 || fuelPriceTrends.length === 0) return {};

    const consumptions = consumptionTrends.filter(t => t.consumo > 0);
    const prices = fuelPriceTrends.filter(p => p.precio > 0);
    const dailyKms = mileageEvolution.filter(m => m.kmDiarios > 0);

    const bestConsumption = consumptions.reduce((min, current) =>
      current.consumo < min.consumo ? current : min, consumptions[0]);
    const worstConsumption = consumptions.reduce((max, current) =>
      current.consumo > max.consumo ? current : max, consumptions[0]);

    const cheapestFuel = prices.reduce((min, current) =>
      current.precio < min.precio ? current : min, prices[0]);
    const expensiveFuel = prices.reduce((max, current) =>
      current.precio > max.precio ? current : max, prices[0]);

    const maxDailyKm = dailyKms.reduce((max, current) =>
      current.kmDiarios > max.kmDiarios ? current : max, dailyKms[0] || {});
    const minDailyKm = dailyKms.reduce((min, current) =>
      current.kmDiarios < min.kmDiarios ? current : min, dailyKms[0] || {});

    return {
      bestConsumption,
      worstConsumption,
      cheapestFuel,
      expensiveFuel,
      maxDailyKm,
      minDailyKm
    };
  };

  const calculatePredictions = (consumptionTrends, mileageEvolution) => {
    if (consumptionTrends.length < 3) return {};

    // Tendencia de consumo (últimos 6 registros)
    const recentConsumption = consumptionTrends.slice(-6);
    const consumptionTrend = recentConsumption.length > 1 ?
      (recentConsumption[recentConsumption.length - 1].consumo - recentConsumption[0].consumo) / recentConsumption.length : 0;

    // Tendencia de kilometraje diario
    const recentMileage = mileageEvolution.slice(-6).filter(m => m.kmDiarios > 0);
    const mileageTrend = recentMileage.length > 1 ?
      (recentMileage[recentMileage.length - 1].kmDiarios - recentMileage[0].kmDiarios) / recentMileage.length : 0;

    return {
      consumptionTrend,
      mileageTrend,
      consumptionDirection: consumptionTrend > 0.1 ? 'increasing' : consumptionTrend < -0.1 ? 'decreasing' : 'stable',
      mileageDirection: mileageTrend > 1 ? 'increasing' : mileageTrend < -1 ? 'decreasing' : 'stable'
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });
    } catch {
      return dateString;
    }
  };

  const getTrendIcon = (direction) => {
    switch (direction) {
      case 'increasing': return <TrendingUpIcon color="error" />;
      case 'decreasing': return <TrendingDownIcon color="success" />;
      default: return <SpeedIcon color="info" />;
    }
  };

  const getTrendColor = (direction, isGood) => {
    if (direction === 'stable') return 'info';
    return (direction === 'increasing') === isGood ? 'success' : 'warning';
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Análisis Avanzado
      </Typography>

      {/* Métricas de eficiencia */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom>
            Métricas de Rendimiento
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Box textAlign="center">
                <Typography variant="h5" color="primary">
                  {analytics.efficiencyMetrics.avgConsumption?.toFixed(1) || 'N/A'} L
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Consumo Promedio/100km
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Min: {analytics.efficiencyMetrics.minConsumption?.toFixed(1) || 'N/A'} |
                  Max: {analytics.efficiencyMetrics.maxConsumption?.toFixed(1) || 'N/A'}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box textAlign="center">
                <Typography variant="h5" color="success.main">
                  {analytics.efficiencyMetrics.avgEfficiency?.toFixed(1) || 'N/A'} km/L
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Eficiencia Promedio
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Min: {analytics.efficiencyMetrics.minEfficiency?.toFixed(1) || 'N/A'} |
                  Max: {analytics.efficiencyMetrics.maxEfficiency?.toFixed(1) || 'N/A'}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box textAlign="center">
                <Typography variant="h5" color="info.main">
                  {analytics.efficiencyMetrics.avgDailyKm?.toFixed(0) || 'N/A'} km
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Kilometraje Diario Promedio
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Min: {analytics.efficiencyMetrics.minDailyKm?.toFixed(0) || 'N/A'} |
                  Max: {analytics.efficiencyMetrics.maxDailyKm?.toFixed(0) || 'N/A'}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box textAlign="center">
                <Typography variant="h5" color="warning.main">
                  {analytics.efficiencyMetrics.totalKm?.toLocaleString() || 'N/A'} km
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Total Recorrido
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tendencias y predicciones */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom>
            Tendencias y Predicciones
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Alert
                severity={getTrendColor(analytics.predictions.consumptionDirection, false)}
                icon={getTrendIcon(analytics.predictions.consumptionDirection)}
              >
                <Typography variant="body2">
                  <strong>Tendencia de Consumo:</strong> {
                    analytics.predictions.consumptionDirection === 'increasing' ? 'Aumentando' :
                      analytics.predictions.consumptionDirection === 'decreasing' ? 'Disminuyendo' :
                        'Estable'
                  }
                </Typography>
                <Typography variant="caption">
                  {analytics.predictions.consumptionTrend > 0 ? '+' : ''}
                  {analytics.predictions.consumptionTrend?.toFixed(2) || 0} L/100km por repostaje
                </Typography>
              </Alert>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Alert
                severity={getTrendColor(analytics.predictions.mileageDirection, true)}
                icon={getTrendIcon(analytics.predictions.mileageDirection)}
              >
                <Typography variant="body2">
                  <strong>Tendencia de Uso:</strong> {
                    analytics.predictions.mileageDirection === 'increasing' ? 'Aumentando' :
                      analytics.predictions.mileageDirection === 'decreasing' ? 'Disminuyendo' :
                        'Estable'
                  }
                </Typography>
                <Typography variant="caption">
                  {analytics.predictions.mileageTrend > 0 ? '+' : ''}
                  {analytics.predictions.mileageTrend?.toFixed(1) || 0} km/día promedio
                </Typography>
              </Alert>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Gráficos avanzados */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Evolución del kilometraje */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Evolución del Kilometraje
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={analytics.mileageEvolution}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <ChartTooltip
                      formatter={(value, name) => {
                        if (name === 'Kilometraje Total') {
                          return [`${value.toLocaleString()} km`, 'Kilometraje Total'];
                        } else if (name === 'Km Diarios') {
                          return [`${value.toFixed(1)} km/día`, 'Km Diarios'];
                        }
                        return [value, name];
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="kilometraje"
                      stroke="#8884d8"
                      strokeWidth={2}
                      name="Kilometraje Total"
                      yAxisId="left"
                    />
                    <Line
                      type="monotone"
                      dataKey="kmDiarios"
                      stroke="#82ca9d"
                      strokeWidth={2}
                      name="Km Diarios"
                      yAxisId="right"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Tendencia de precios */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Evolución de Precios de Combustible
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={analytics.fuelPriceTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis />
                    <ChartTooltip
                      formatter={(value) => [formatCurrency(value), 'Precio/Litro']}
                    />
                    <Area
                      type="monotone"
                      dataKey="precio"
                      stroke="#ffc658"
                      fill="#ffc658"
                      fillOpacity={0.6}
                    />
                    <ReferenceLine
                      y={analytics.fuelPriceTrends.reduce((sum, item) => sum + item.precio, 0) / analytics.fuelPriceTrends.length}
                      stroke="red"
                      strokeDasharray="5 5"
                      label="Promedio"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Análisis de consumo vs eficiencia */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Consumo vs Eficiencia
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <ScatterChart data={analytics.consumptionTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="consumo"
                      name="Consumo L/100km"
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => value.toFixed(1)}
                      label={{
                        value: 'Consumo (L/100km)',
                        position: 'insideBottom',
                        offset: -5,
                        style: { textAnchor: 'middle', fontSize: '12px', fill: '#666' }
                      }}
                    />
                    <YAxis
                      dataKey="eficiencia"
                      name="Eficiencia km/L"
                      tickFormatter={(value) => value.toFixed(1)}
                      label={{
                        value: 'Eficiencia (km/L)',
                        angle: -90,
                        position: 'insideLeft',
                        style: { textAnchor: 'middle', fontSize: '12px', fill: '#666' }
                      }}
                    />
                    <ChartTooltip
                      formatter={(value, name, props) => {
                        if (name === 'eficiencia') {
                          return [
                            `${value.toFixed(2)} km/L`,
                            'Eficiencia'
                          ];
                        }
                        return [value, name];
                      }}
                      labelFormatter={(label) => `Consumo: ${parseFloat(label).toFixed(2)} L/100km`}
                    />
                    <Scatter
                      dataKey="eficiencia"
                      fill="#8884d8"
                    />
                  </ScatterChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Distribución de consumo */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Distribución de Consumo
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={analytics.consumptionTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis />
                    <ChartTooltip
                      formatter={(value) => [`${value.toFixed(1)} L/100km`, 'Consumo']}
                    />
                    <Bar
                      dataKey="consumo"
                      fill="#82ca9d"
                    />
                    <ReferenceLine
                      y={analytics.efficiencyMetrics.avgConsumption}
                      stroke="red"
                      strokeDasharray="5 5"
                      label="Promedio"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Valores extremos */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1">
            Registros Destacados (Máximos y Mínimos)
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom color="success.main">
                Mejores Registros
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableBody>
                    <TableRow>
                      <TableCell><strong>Mejor Consumo</strong></TableCell>
                      <TableCell>
                        {analytics.extremeValues.bestConsumption ?
                          `${analytics.extremeValues.bestConsumption.consumo.toFixed(1)} L/100km` : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {analytics.extremeValues.bestConsumption ?
                          formatDate(analytics.extremeValues.bestConsumption.fecha) : ''}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Combustible Más Barato</strong></TableCell>
                      <TableCell>
                        {analytics.extremeValues.cheapestFuel ?
                          formatCurrency(analytics.extremeValues.cheapestFuel.precio) : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {analytics.extremeValues.cheapestFuel ?
                          formatDate(analytics.extremeValues.cheapestFuel.fecha) : ''}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Mayor Uso Diario</strong></TableCell>
                      <TableCell>
                        {analytics.extremeValues.maxDailyKm ?
                          `${analytics.extremeValues.maxDailyKm.kmDiarios.toFixed(0)} km/día` : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {analytics.extremeValues.maxDailyKm ?
                          formatDate(analytics.extremeValues.maxDailyKm.fecha) : ''}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom color="warning.main">
                Registros a Mejorar
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableBody>
                    <TableRow>
                      <TableCell><strong>Peor Consumo</strong></TableCell>
                      <TableCell>
                        {analytics.extremeValues.worstConsumption ?
                          `${analytics.extremeValues.worstConsumption.consumo.toFixed(1)} L/100km` : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {analytics.extremeValues.worstConsumption ?
                          formatDate(analytics.extremeValues.worstConsumption.fecha) : ''}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Combustible Más Caro</strong></TableCell>
                      <TableCell>
                        {analytics.extremeValues.expensiveFuel ?
                          formatCurrency(analytics.extremeValues.expensiveFuel.precio) : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {analytics.extremeValues.expensiveFuel ?
                          formatDate(analytics.extremeValues.expensiveFuel.fecha) : ''}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Menor Uso Diario</strong></TableCell>
                      <TableCell>
                        {analytics.extremeValues.minDailyKm ?
                          `${analytics.extremeValues.minDailyKm.kmDiarios.toFixed(0)} km/día` : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {analytics.extremeValues.minDailyKm ?
                          formatDate(analytics.extremeValues.minDailyKm.fecha) : ''}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default AdvancedAnalytics;