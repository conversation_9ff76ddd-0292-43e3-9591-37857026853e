const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const { spawn } = require('child_process');

// Mantener una referencia global del objeto ventana
let mainWindow;
let serverProcess;

// Función para obtener la ruta de la aplicación (portable)
function getAppPath() {
  if (isDev) {
    return __dirname;
  }
  // En producción, usar la ruta donde está el ejecutable
  return path.dirname(process.execPath);
}

// Función para iniciar el servidor backend
function startBackendServer() {
  const appPath = getAppPath();
  const serverPath = isDev 
    ? path.join(__dirname, '..', 'server', 'server.js')
    : path.join(appPath, 'server', 'server.js');
  
  console.log('Iniciando servidor desde:', serverPath);
  
  serverProcess = spawn('node', [serverPath], {
    cwd: appPath,
    env: { 
      ...process.env, 
      APP_PATH: appPath,
      NODE_ENV: isDev ? 'development' : 'production'
    }
  });

  serverProcess.stdout.on('data', (data) => {
    console.log(`Servidor: ${data}`);
  });

  serverProcess.stderr.on('data', (data) => {
    console.error(`Error del servidor: ${data}`);
  });

  serverProcess.on('close', (code) => {
    console.log(`Servidor cerrado con código ${code}`);
  });
}

function createWindow() {
  // Crear la ventana del navegador
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'favicon.ico'),
    show: false
  });

  // Cargar la aplicación
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Mostrar ventana cuando esté lista
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Abrir DevTools en desarrollo
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Este método será llamado cuando Electron haya terminado la inicialización
app.whenReady().then(() => {
  // Iniciar el servidor backend primero
  startBackendServer();
  
  // Esperar un poco para que el servidor se inicie
  setTimeout(() => {
    createWindow();
  }, 2000);

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Salir cuando todas las ventanas estén cerradas
app.on('window-all-closed', () => {
  // Cerrar el servidor backend
  if (serverProcess) {
    serverProcess.kill();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  // Cerrar el servidor backend antes de salir
  if (serverProcess) {
    serverProcess.kill();
  }
});
