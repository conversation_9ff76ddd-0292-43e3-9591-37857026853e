{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Repostaje\\\\src\\\\components\\\\Statistics\\\\Statistics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, FormControl, InputLabel, Select, MenuItem, TextField, Chip, Divider, Paper } from '@mui/material';\nimport { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip as ChartTooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { useApp } from '../../context/AppContext';\nimport { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport VehicleComparison from './VehicleComparison';\nimport AdvancedAnalytics from './AdvancedAnalytics';\n\n// Colores para los gráficos\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\n\n// Componente para métricas principales\nconst MetricCard = ({\n  title,\n  value,\n  subtitle,\n  color = 'primary',\n  trend\n}) => /*#__PURE__*/_jsxDEV(Card, {\n  children: /*#__PURE__*/_jsxDEV(CardContent, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      color: \"textSecondary\",\n      gutterBottom: true,\n      variant: \"body2\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"div\",\n      color: color,\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 9\n    }, this), trend && /*#__PURE__*/_jsxDEV(Box, {\n      mt: 1,\n      children: /*#__PURE__*/_jsxDEV(Chip, {\n        label: trend,\n        size: \"small\",\n        color: trend.includes('+') ? 'success' : trend.includes('-') ? 'error' : 'default'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 45,\n  columnNumber: 3\n}, this);\n_c = MetricCard;\nconst Statistics = () => {\n  _s();\n  const {\n    vehicles,\n    refuels,\n    expenses,\n    loadStatistics,\n    statistics\n  } = useApp();\n  const [selectedVehicle, setSelectedVehicle] = useState('all');\n  const [dateRange, setDateRange] = useState('6months');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [chartData, setChartData] = useState({\n    consumption: [],\n    costs: [],\n    expensesByType: [],\n    monthlyTrends: []\n  });\n  useEffect(() => {\n    calculateStatistics();\n  }, [refuels, expenses, selectedVehicle, dateRange]);\n  const calculateStatistics = () => {\n    console.log('🔄 Recalculating statistics with filters:', {\n      selectedVehicle,\n      dateRange\n    });\n    console.log('📊 Total data available:', {\n      refuels: refuels.length,\n      expenses: expenses.length\n    });\n    let filteredRefuels = [...refuels]; // Crear copia para evitar mutación\n    let filteredExpenses = [...expenses];\n\n    // Filtrar por vehículo\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n      console.log('🚗 Filtered by vehicle:', vehicleId, {\n        refuels: filteredRefuels.length,\n        expenses: filteredExpenses.length\n      });\n    }\n\n    // Filtrar por fecha\n    const now = new Date();\n    let startDateFilter;\n    if (startDate && endDate) {\n      // Usar fechas personalizadas si están definidas\n      startDateFilter = new Date(startDate);\n      const endDateFilter = new Date(endDate);\n      filteredRefuels = filteredRefuels.filter(r => {\n        const refuelDate = new Date(r.fecha);\n        return refuelDate >= startDateFilter && refuelDate <= endDateFilter;\n      });\n      filteredExpenses = filteredExpenses.filter(e => {\n        const expenseDate = new Date(e.fecha);\n        return expenseDate >= startDateFilter && expenseDate <= endDateFilter;\n      });\n      console.log('📅 Filtered by custom date range:', {\n        startDate: startDateFilter,\n        endDate: endDateFilter,\n        refuels: filteredRefuels.length,\n        expenses: filteredExpenses.length\n      });\n    } else {\n      // Usar rangos predefinidos si no hay fechas personalizadas\n      switch (dateRange) {\n        case '3months':\n          startDateFilter = subMonths(now, 3);\n          break;\n        case '6months':\n          startDateFilter = subMonths(now, 6);\n          break;\n        case '1year':\n          startDateFilter = subMonths(now, 12);\n          break;\n        case '2years':\n          startDateFilter = subMonths(now, 24);\n          break;\n        default:\n          startDateFilter = new Date(0);\n        // Todos los datos\n      }\n      if (dateRange !== 'all') {\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDateFilter);\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDateFilter);\n        console.log('📅 Filtered by date range:', dateRange, {\n          refuels: filteredRefuels.length,\n          expenses: filteredExpenses.length\n        });\n      }\n    }\n\n    // Calcular datos para gráficos\n    calculateChartData(filteredRefuels, filteredExpenses);\n  };\n  const calculateChartData = (refuels, expenses) => {\n    // 1. Datos de consumo por mes\n    const consumptionByMonth = {};\n    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    for (let i = 1; i < sortedRefuels.length; i++) {\n      const current = sortedRefuels[i];\n      const previous = sortedRefuels[i - 1];\n      if (current.vehiculo_id === previous.vehiculo_id) {\n        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;\n        const liters = current.litros;\n        if (kmDiff > 0 && liters > 0) {\n          const consumption = liters / kmDiff * 100; // L/100km\n          const month = format(new Date(current.fecha), 'yyyy-MM');\n          if (!consumptionByMonth[month]) {\n            consumptionByMonth[month] = {\n              total: 0,\n              count: 0,\n              month\n            };\n          }\n          consumptionByMonth[month].total += consumption;\n          consumptionByMonth[month].count += 1;\n        }\n      }\n    }\n    const consumptionData = Object.values(consumptionByMonth).map(item => ({\n      month: format(new Date(item.month + '-01'), 'MMM yyyy', {\n        locale: es\n      }),\n      consumption: (item.total / item.count).toFixed(1),\n      date: item.month\n    })).sort((a, b) => a.date.localeCompare(b.date));\n\n    // 2. Costes por mes\n    const costsByMonth = {};\n    [...refuels, ...expenses].forEach(item => {\n      const month = format(new Date(item.fecha), 'yyyy-MM');\n      const cost = item.coste_total || item.coste || 0;\n      const type = item.litros ? 'Combustible' : 'Gastos';\n      if (!costsByMonth[month]) {\n        costsByMonth[month] = {\n          month,\n          Combustible: 0,\n          Gastos: 0,\n          date: month\n        };\n      }\n      costsByMonth[month][type] += cost;\n    });\n    const costsData = Object.values(costsByMonth).map(item => ({\n      ...item,\n      month: format(new Date(item.month + '-01'), 'MMM yyyy', {\n        locale: es\n      }),\n      Total: item.Combustible + item.Gastos\n    })).sort((a, b) => a.date.localeCompare(b.date));\n\n    // 3. Gastos por tipo\n    const expensesByType = {};\n    expenses.forEach(expense => {\n      const type = expense.tipo_gasto || 'Otros';\n      expensesByType[type] = (expensesByType[type] || 0) + (expense.coste || 0);\n    });\n    const expensesData = Object.entries(expensesByType).map(([name, value]) => ({\n      name,\n      value: parseFloat(value.toFixed(2))\n    })).sort((a, b) => b.value - a.value);\n\n    // 4. Tendencias mensuales\n    const monthlyTrends = costsData.map(item => {\n      var _consumptionData$find;\n      return {\n        month: item.month,\n        date: item.date,\n        totalCost: item.Total,\n        fuelCost: item.Combustible,\n        expenseCost: item.Gastos,\n        consumption: ((_consumptionData$find = consumptionData.find(c => c.date === item.date)) === null || _consumptionData$find === void 0 ? void 0 : _consumptionData$find.consumption) || 0\n      };\n    });\n    setChartData({\n      consumption: consumptionData,\n      costs: costsData,\n      expensesByType: expensesData,\n      monthlyTrends\n    });\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  // Aplicar filtros para métricas principales\n  const getFilteredData = () => {\n    let filteredRefuels = [...refuels];\n    let filteredExpenses = [...expenses];\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n    }\n    if (dateRange !== 'all') {\n      const now = new Date();\n      let startDate;\n      switch (dateRange) {\n        case '3months':\n          startDate = subMonths(now, 3);\n          break;\n        case '6months':\n          startDate = subMonths(now, 6);\n          break;\n        case '1year':\n          startDate = subMonths(now, 12);\n          break;\n        case '2years':\n          startDate = subMonths(now, 24);\n          break;\n        default:\n          startDate = new Date(0);\n      }\n      if (dateRange !== 'all') {\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);\n      }\n    }\n    return {\n      filteredRefuels,\n      filteredExpenses\n    };\n  };\n  const {\n    filteredRefuels,\n    filteredExpenses\n  } = getFilteredData();\n\n  // Calcular métricas principales con datos filtrados\n  const totalRefuels = filteredRefuels.length;\n  const totalExpenses = filteredExpenses.length;\n  const totalFuelCost = filteredRefuels.reduce((sum, r) => sum + (r.coste_total || 0), 0);\n  const totalExpenseCost = filteredExpenses.reduce((sum, e) => sum + (e.coste || 0), 0);\n  const totalCost = totalFuelCost + totalExpenseCost;\n  const totalLiters = filteredRefuels.reduce((sum, r) => sum + (r.litros || 0), 0);\n\n  // Calcular consumo promedio con datos filtrados\n  let avgConsumption = 0;\n  if (filteredRefuels.length >= 2) {\n    const sortedRefuels = [...filteredRefuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales - sortedRefuels[0].kilometros_actuales;\n    if (totalKm > 0) {\n      avgConsumption = totalLiters / totalKm * 100;\n    }\n  }\n  const avgFuelPrice = totalLiters > 0 ? totalFuelCost / totalLiters : 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Estad\\xEDsticas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"vehicle-select-label\",\n              children: \"Veh\\xEDculo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"vehicle-select-label\",\n              value: selectedVehicle,\n              onChange: e => setSelectedVehicle(e.target.value),\n              label: \"Veh\\xEDculo\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"Todos los veh\\xEDculos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: vehicle.id,\n                children: vehicle.nombre\n              }, vehicle.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"date-range-label\",\n              children: \"Rango de fechas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"date-range-label\",\n              value: dateRange,\n              onChange: e => {\n                setDateRange(e.target.value);\n                // Reset custom dates when selecting a predefined range\n                setStartDate('');\n                setEndDate('');\n              },\n              label: \"Rango de fechas\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"3months\",\n                children: \"\\xDAltimos 3 meses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"6months\",\n                children: \"\\xDAltimos 6 meses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"1year\",\n                children: \"\\xDAltimo a\\xF1o\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"2years\",\n                children: \"\\xDAltimos 2 a\\xF1os\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"Todo el historial\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"custom\",\n                children: \"Personalizado\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), dateRange === 'custom' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Fecha de inicio\",\n              type: \"date\",\n              value: startDate,\n              onChange: e => setStartDate(e.target.value),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Fecha de fin\",\n              type: \"date\",\n              value: endDate,\n              onChange: e => setEndDate(e.target.value),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Consumo Promedio\",\n          value: `${avgConsumption.toFixed(1)} L`,\n          subtitle: \"Por 100 km\",\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Precio Promedio\",\n          value: formatCurrency(avgFuelPrice),\n          subtitle: \"Por litro\",\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Gasto Total\",\n          value: formatCurrency(totalCost),\n          subtitle: `${formatCurrency(totalFuelCost)} combustible + ${formatCurrency(totalExpenseCost)} gastos`,\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Total Litros\",\n          value: `${totalLiters.toFixed(1)} L`,\n          subtitle: `En ${totalRefuels} repostajes`,\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Evoluci\\xF3n del Consumo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: chartData.consumption,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [`${value} L/100km`, 'Consumo'],\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"consumption\",\n                    stroke: \"#8884d8\",\n                    strokeWidth: 2,\n                    name: \"L/100km\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Costes Mensuales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                  data: chartData.costs,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [formatCurrency(value)],\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"Combustible\",\n                    stackId: \"1\",\n                    stroke: \"#82ca9d\",\n                    fill: \"#82ca9d\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"Gastos\",\n                    stackId: \"1\",\n                    stroke: \"#ffc658\",\n                    fill: \"#ffc658\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Distribuci\\xF3n de Gastos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(PieChart, {\n                  children: [/*#__PURE__*/_jsxDEV(Pie, {\n                    data: chartData.expensesByType,\n                    cx: \"50%\",\n                    cy: \"50%\",\n                    labelLine: false,\n                    label: ({\n                      name,\n                      percent\n                    }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                    outerRadius: 80,\n                    fill: \"#8884d8\",\n                    dataKey: \"value\",\n                    children: chartData.expensesByType.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                      fill: COLORS[index % COLORS.length]\n                    }, `cell-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: value => [formatCurrency(value)]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Tendencias Generales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              height: 300,\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(BarChart, {\n                  data: chartData.monthlyTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChartTooltip, {\n                    formatter: (value, name) => {\n                      if (name === 'Coste Total') return [formatCurrency(value), name];\n                      return [value, name];\n                    },\n                    labelStyle: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                    dataKey: \"totalCost\",\n                    fill: \"#8884d8\",\n                    name: \"Coste Total (\\u20AC)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Resumen Estad\\xEDstico\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: totalRefuels\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Repostajes totales\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"success.main\",\n                children: totalExpenses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Gastos registrados\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"warning.main\",\n                children: formatCurrency(totalCost / (totalRefuels + totalExpenses) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Gasto promedio por registro\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"info.main\",\n                children: vehicles.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Veh\\xEDculos activos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mt: 4,\n      children: /*#__PURE__*/_jsxDEV(AdvancedAnalytics, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 7\n    }, this), vehicles.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      mt: 4,\n      children: /*#__PURE__*/_jsxDEV(VehicleComparison, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 5\n  }, this);\n};\n_s(Statistics, \"oG7XeVzgnSHDB5hIm9Lx7dYtJbU=\", false, function () {\n  return [useApp];\n});\n_c2 = Statistics;\nexport default Statistics;\nvar _c, _c2;\n$RefreshReg$(_c, \"MetricCard\");\n$RefreshReg$(_c2, \"Statistics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Chip", "Divider", "Paper", "Line<PERSON>hart", "Line", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ChartTooltip", "Legend", "ResponsiveContainer", "useApp", "format", "subMonths", "startOfMonth", "endOfMonth", "es", "VehicleComparison", "AdvancedAnalytics", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "COLORS", "MetricCard", "title", "value", "subtitle", "color", "trend", "children", "gutterBottom", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "mt", "label", "size", "includes", "_c", "Statistics", "_s", "vehicles", "refuels", "expenses", "loadStatistics", "statistics", "selectedVehicle", "setSelectedVehicle", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "setStartDate", "endDate", "setEndDate", "chartData", "setChartData", "consumption", "costs", "expensesByType", "monthlyTrends", "calculateStatistics", "console", "log", "length", "filteredRefuels", "filteredExpenses", "vehicleId", "parseInt", "filter", "r", "vehiculo_id", "e", "now", "Date", "startDateFilter", "endDateFilter", "refuelDate", "fecha", "expenseDate", "calculateChartData", "consumptionByMonth", "sortedRefuels", "sort", "a", "b", "i", "current", "previous", "kmDiff", "kilometros_actuales", "liters", "litros", "month", "total", "count", "consumptionData", "Object", "values", "map", "item", "locale", "toFixed", "date", "localeCompare", "costsByMonth", "for<PERSON>ach", "cost", "coste_total", "coste", "type", "Combustible", "Gastos", "costsData", "Total", "expense", "tipo_gasto", "expensesData", "entries", "name", "parseFloat", "_consumptionData$find", "totalCost", "fuelCost", "expenseCost", "find", "c", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "getFilteredData", "totalRefuels", "totalExpenses", "totalFuelCost", "reduce", "sum", "totalExpenseCost", "totalLiters", "avgConsumption", "totalKm", "avgFuelPrice", "p", "mb", "container", "spacing", "alignItems", "xs", "md", "fullWidth", "id", "labelId", "onChange", "target", "vehicle", "nombre", "InputLabelProps", "shrink", "sm", "lg", "height", "width", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "formatter", "labelStyle", "stroke", "strokeWidth", "stackId", "fill", "cx", "cy", "labelLine", "percent", "outerRadius", "entry", "index", "sx", "textAlign", "_c2", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Repostaje/src/components/Statistics/Statistics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Chip,\n  Divider,\n  Paper,\n} from '@mui/material';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip as ChartTooltip,\n  Legend,\n  ResponsiveContainer,\n} from 'recharts';\nimport { useApp } from '../../context/AppContext';\nimport { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';\nimport { es } from 'date-fns/locale';\nimport VehicleComparison from './VehicleComparison';\nimport AdvancedAnalytics from './AdvancedAnalytics';\n\n// Colores para los gráficos\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\n\n// Componente para métricas principales\nconst MetricCard = ({ title, value, subtitle, color = 'primary', trend }) => (\n  <Card>\n    <CardContent>\n      <Typography color=\"textSecondary\" gutterBottom variant=\"body2\">\n        {title}\n      </Typography>\n      <Typography variant=\"h4\" component=\"div\" color={color}>\n        {value}\n      </Typography>\n      {subtitle && (\n        <Typography variant=\"body2\" color=\"textSecondary\">\n          {subtitle}\n        </Typography>\n      )}\n      {trend && (\n        <Box mt={1}>\n          <Chip \n            label={trend} \n            size=\"small\" \n            color={trend.includes('+') ? 'success' : trend.includes('-') ? 'error' : 'default'}\n          />\n        </Box>\n      )}\n    </CardContent>\n  </Card>\n);\n\nconst Statistics = () => {\n  const { vehicles, refuels, expenses, loadStatistics, statistics } = useApp();\n  const [selectedVehicle, setSelectedVehicle] = useState('all');\n  const [dateRange, setDateRange] = useState('6months');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [chartData, setChartData] = useState({\n    consumption: [],\n    costs: [],\n    expensesByType: [],\n    monthlyTrends: []\n  });\n\n  useEffect(() => {\n    calculateStatistics();\n  }, [refuels, expenses, selectedVehicle, dateRange]);\n\n  const calculateStatistics = () => {\n    console.log('🔄 Recalculating statistics with filters:', { selectedVehicle, dateRange });\n    console.log('📊 Total data available:', { refuels: refuels.length, expenses: expenses.length });\n    \n    let filteredRefuels = [...refuels]; // Crear copia para evitar mutación\n    let filteredExpenses = [...expenses];\n\n    // Filtrar por vehículo\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n      console.log('🚗 Filtered by vehicle:', vehicleId, { refuels: filteredRefuels.length, expenses: filteredExpenses.length });\n    }\n\n    // Filtrar por fecha\n    const now = new Date();\n    let startDateFilter;\n    \n    if (startDate && endDate) {\n      // Usar fechas personalizadas si están definidas\n      startDateFilter = new Date(startDate);\n      const endDateFilter = new Date(endDate);\n      \n      filteredRefuels = filteredRefuels.filter(r => {\n        const refuelDate = new Date(r.fecha);\n        return refuelDate >= startDateFilter && refuelDate <= endDateFilter;\n      });\n      \n      filteredExpenses = filteredExpenses.filter(e => {\n        const expenseDate = new Date(e.fecha);\n        return expenseDate >= startDateFilter && expenseDate <= endDateFilter;\n      });\n      \n      console.log('📅 Filtered by custom date range:', { \n        startDate: startDateFilter, \n        endDate: endDateFilter,\n        refuels: filteredRefuels.length, \n        expenses: filteredExpenses.length \n      });\n    } else {\n      // Usar rangos predefinidos si no hay fechas personalizadas\n      switch (dateRange) {\n        case '3months':\n          startDateFilter = subMonths(now, 3);\n          break;\n        case '6months':\n          startDateFilter = subMonths(now, 6);\n          break;\n        case '1year':\n          startDateFilter = subMonths(now, 12);\n          break;\n        case '2years':\n          startDateFilter = subMonths(now, 24);\n          break;\n        default:\n          startDateFilter = new Date(0); // Todos los datos\n      }\n      \n      if (dateRange !== 'all') {\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDateFilter);\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDateFilter);\n        console.log('📅 Filtered by date range:', dateRange, { \n          refuels: filteredRefuels.length, \n          expenses: filteredExpenses.length \n        });\n      }\n    }\n\n    // Calcular datos para gráficos\n    calculateChartData(filteredRefuels, filteredExpenses);\n  };\n\n  const calculateChartData = (refuels, expenses) => {\n    // 1. Datos de consumo por mes\n    const consumptionByMonth = {};\n    const sortedRefuels = [...refuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    \n    for (let i = 1; i < sortedRefuels.length; i++) {\n      const current = sortedRefuels[i];\n      const previous = sortedRefuels[i - 1];\n      \n      if (current.vehiculo_id === previous.vehiculo_id) {\n        const kmDiff = current.kilometros_actuales - previous.kilometros_actuales;\n        const liters = current.litros;\n        \n        if (kmDiff > 0 && liters > 0) {\n          const consumption = (liters / kmDiff) * 100; // L/100km\n          const month = format(new Date(current.fecha), 'yyyy-MM');\n          \n          if (!consumptionByMonth[month]) {\n            consumptionByMonth[month] = { total: 0, count: 0, month };\n          }\n          consumptionByMonth[month].total += consumption;\n          consumptionByMonth[month].count += 1;\n        }\n      }\n    }\n\n    const consumptionData = Object.values(consumptionByMonth)\n      .map(item => ({\n        month: format(new Date(item.month + '-01'), 'MMM yyyy', { locale: es }),\n        consumption: (item.total / item.count).toFixed(1),\n        date: item.month\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n\n    // 2. Costes por mes\n    const costsByMonth = {};\n    \n    [...refuels, ...expenses].forEach(item => {\n      const month = format(new Date(item.fecha), 'yyyy-MM');\n      const cost = item.coste_total || item.coste || 0;\n      const type = item.litros ? 'Combustible' : 'Gastos';\n      \n      if (!costsByMonth[month]) {\n        costsByMonth[month] = { month, Combustible: 0, Gastos: 0, date: month };\n      }\n      costsByMonth[month][type] += cost;\n    });\n\n    const costsData = Object.values(costsByMonth)\n      .map(item => ({\n        ...item,\n        month: format(new Date(item.month + '-01'), 'MMM yyyy', { locale: es }),\n        Total: item.Combustible + item.Gastos\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n\n    // 3. Gastos por tipo\n    const expensesByType = {};\n    expenses.forEach(expense => {\n      const type = expense.tipo_gasto || 'Otros';\n      expensesByType[type] = (expensesByType[type] || 0) + (expense.coste || 0);\n    });\n\n    const expensesData = Object.entries(expensesByType)\n      .map(([name, value]) => ({ name, value: parseFloat(value.toFixed(2)) }))\n      .sort((a, b) => b.value - a.value);\n\n    // 4. Tendencias mensuales\n    const monthlyTrends = costsData.map(item => ({\n      month: item.month,\n      date: item.date,\n      totalCost: item.Total,\n      fuelCost: item.Combustible,\n      expenseCost: item.Gastos,\n      consumption: consumptionData.find(c => c.date === item.date)?.consumption || 0\n    }));\n\n    setChartData({\n      consumption: consumptionData,\n      costs: costsData,\n      expensesByType: expensesData,\n      monthlyTrends\n    });\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('es-ES', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount || 0);\n  };\n\n  // Aplicar filtros para métricas principales\n  const getFilteredData = () => {\n    let filteredRefuels = [...refuels];\n    let filteredExpenses = [...expenses];\n\n    if (selectedVehicle !== 'all') {\n      const vehicleId = parseInt(selectedVehicle);\n      filteredRefuels = filteredRefuels.filter(r => r.vehiculo_id === vehicleId);\n      filteredExpenses = filteredExpenses.filter(e => e.vehiculo_id === vehicleId);\n    }\n\n    if (dateRange !== 'all') {\n      const now = new Date();\n      let startDate;\n      switch (dateRange) {\n        case '3months': startDate = subMonths(now, 3); break;\n        case '6months': startDate = subMonths(now, 6); break;\n        case '1year': startDate = subMonths(now, 12); break;\n        case '2years': startDate = subMonths(now, 24); break;\n        default: startDate = new Date(0);\n      }\n      \n      if (dateRange !== 'all') {\n        filteredRefuels = filteredRefuels.filter(r => new Date(r.fecha) >= startDate);\n        filteredExpenses = filteredExpenses.filter(e => new Date(e.fecha) >= startDate);\n      }\n    }\n\n    return { filteredRefuels, filteredExpenses };\n  };\n\n  const { filteredRefuels, filteredExpenses } = getFilteredData();\n\n  // Calcular métricas principales con datos filtrados\n  const totalRefuels = filteredRefuels.length;\n  const totalExpenses = filteredExpenses.length;\n  const totalFuelCost = filteredRefuels.reduce((sum, r) => sum + (r.coste_total || 0), 0);\n  const totalExpenseCost = filteredExpenses.reduce((sum, e) => sum + (e.coste || 0), 0);\n  const totalCost = totalFuelCost + totalExpenseCost;\n  const totalLiters = filteredRefuels.reduce((sum, r) => sum + (r.litros || 0), 0);\n  \n  // Calcular consumo promedio con datos filtrados\n  let avgConsumption = 0;\n  if (filteredRefuels.length >= 2) {\n    const sortedRefuels = [...filteredRefuels].sort((a, b) => new Date(a.fecha) - new Date(b.fecha));\n    const totalKm = sortedRefuels[sortedRefuels.length - 1].kilometros_actuales - \n                   sortedRefuels[0].kilometros_actuales;\n    if (totalKm > 0) {\n      avgConsumption = (totalLiters / totalKm * 100);\n    }\n  }\n\n  const avgFuelPrice = totalLiters > 0 ? totalFuelCost / totalLiters : 0;\n\n  return (\n    <Box p={3}>\n      <Typography variant=\"h4\" gutterBottom>\n        Estadísticas\n      </Typography>\n      \n      <Box mb={3}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={12} md={3}>\n            <FormControl fullWidth>\n              <InputLabel id=\"vehicle-select-label\">Vehículo</InputLabel>\n              <Select\n                labelId=\"vehicle-select-label\"\n                value={selectedVehicle}\n                onChange={(e) => setSelectedVehicle(e.target.value)}\n                label=\"Vehículo\"\n              >\n                <MenuItem value=\"all\">Todos los vehículos</MenuItem>\n                {vehicles.map((vehicle) => (\n                  <MenuItem key={vehicle.id} value={vehicle.id}>\n                    {vehicle.nombre}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <FormControl fullWidth>\n              <InputLabel id=\"date-range-label\">Rango de fechas</InputLabel>\n              <Select\n                labelId=\"date-range-label\"\n                value={dateRange}\n                onChange={(e) => {\n                  setDateRange(e.target.value);\n                  // Reset custom dates when selecting a predefined range\n                  setStartDate('');\n                  setEndDate('');\n                }}\n                label=\"Rango de fechas\"\n              >\n                <MenuItem value=\"3months\">Últimos 3 meses</MenuItem>\n                <MenuItem value=\"6months\">Últimos 6 meses</MenuItem>\n                <MenuItem value=\"1year\">Último año</MenuItem>\n                <MenuItem value=\"2years\">Últimos 2 años</MenuItem>\n                <MenuItem value=\"all\">Todo el historial</MenuItem>\n                <MenuItem value=\"custom\">Personalizado</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n          {dateRange === 'custom' && (\n            <>\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  label=\"Fecha de inicio\"\n                  type=\"date\"\n                  value={startDate}\n                  onChange={(e) => setStartDate(e.target.value)}\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  label=\"Fecha de fin\"\n                  type=\"date\"\n                  value={endDate}\n                  onChange={(e) => setEndDate(e.target.value)}\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </Box>\n\n      {/* Métricas principales */}\n      <Grid container spacing={3} mb={4}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Consumo Promedio\"\n            value={`${avgConsumption.toFixed(1)} L`}\n            subtitle=\"Por 100 km\"\n            color=\"primary\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Precio Promedio\"\n            value={formatCurrency(avgFuelPrice)}\n            subtitle=\"Por litro\"\n            color=\"success\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Gasto Total\"\n            value={formatCurrency(totalCost)}\n            subtitle={`${formatCurrency(totalFuelCost)} combustible + ${formatCurrency(totalExpenseCost)} gastos`}\n            color=\"warning\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Total Litros\"\n            value={`${totalLiters.toFixed(1)} L`}\n            subtitle={`En ${totalRefuels} repostajes`}\n            color=\"info\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* Gráficos */}\n      <Grid container spacing={3}>\n        {/* Gráfico de consumo */}\n        <Grid item xs={12} lg={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Evolución del Consumo\n              </Typography>\n              <Box height={300}>\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <LineChart data={chartData.consumption}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis dataKey=\"month\" />\n                    <YAxis />\n                    <ChartTooltip \n                      formatter={(value) => [`${value} L/100km`, 'Consumo']}\n                      labelStyle={{ color: '#666' }}\n                    />\n                    <Legend />\n                    <Line \n                      type=\"monotone\" \n                      dataKey=\"consumption\" \n                      stroke=\"#8884d8\" \n                      strokeWidth={2}\n                      name=\"L/100km\"\n                    />\n                  </LineChart>\n                </ResponsiveContainer>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Gráfico de costes mensuales */}\n        <Grid item xs={12} lg={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Costes Mensuales\n              </Typography>\n              <Box height={300}>\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <AreaChart data={chartData.costs}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis dataKey=\"month\" />\n                    <YAxis />\n                    <ChartTooltip \n                      formatter={(value) => [formatCurrency(value)]}\n                      labelStyle={{ color: '#666' }}\n                    />\n                    <Legend />\n                    <Area \n                      type=\"monotone\" \n                      dataKey=\"Combustible\" \n                      stackId=\"1\"\n                      stroke=\"#82ca9d\" \n                      fill=\"#82ca9d\" \n                    />\n                    <Area \n                      type=\"monotone\" \n                      dataKey=\"Gastos\" \n                      stackId=\"1\"\n                      stroke=\"#ffc658\" \n                      fill=\"#ffc658\" \n                    />\n                  </AreaChart>\n                </ResponsiveContainer>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Gráfico de gastos por tipo */}\n        <Grid item xs={12} lg={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Distribución de Gastos\n              </Typography>\n              <Box height={300}>\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <PieChart>\n                    <Pie\n                      data={chartData.expensesByType}\n                      cx=\"50%\"\n                      cy=\"50%\"\n                      labelLine={false}\n                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                      outerRadius={80}\n                      fill=\"#8884d8\"\n                      dataKey=\"value\"\n                    >\n                      {chartData.expensesByType.map((entry, index) => (\n                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                      ))}\n                    </Pie>\n                    <ChartTooltip formatter={(value) => [formatCurrency(value)]} />\n                  </PieChart>\n                </ResponsiveContainer>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Tendencias combinadas */}\n        <Grid item xs={12} lg={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Tendencias Generales\n              </Typography>\n              <Box height={300}>\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <BarChart data={chartData.monthlyTrends}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis dataKey=\"month\" />\n                    <YAxis />\n                    <ChartTooltip \n                      formatter={(value, name) => {\n                        if (name === 'Coste Total') return [formatCurrency(value), name];\n                        return [value, name];\n                      }}\n                      labelStyle={{ color: '#666' }}\n                    />\n                    <Legend />\n                    <Bar dataKey=\"totalCost\" fill=\"#8884d8\" name=\"Coste Total (€)\" />\n                  </BarChart>\n                </ResponsiveContainer>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Resumen estadístico */}\n      <Card sx={{ mt: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Resumen Estadístico\n          </Typography>\n          <Grid container spacing={2}>\n            <Grid item xs={12} sm={6} md={3}>\n              <Box textAlign=\"center\">\n                <Typography variant=\"h4\" color=\"primary\">\n                  {totalRefuels}\n                </Typography>\n                <Typography variant=\"body2\" color=\"textSecondary\">\n                  Repostajes totales\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} sm={6} md={3}>\n              <Box textAlign=\"center\">\n                <Typography variant=\"h4\" color=\"success.main\">\n                  {totalExpenses}\n                </Typography>\n                <Typography variant=\"body2\" color=\"textSecondary\">\n                  Gastos registrados\n                </Typography>\n              </Box>\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"warning.main\">\r\n                  {formatCurrency(totalCost / (totalRefuels + totalExpenses) || 0)}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Gasto promedio por registro\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Box textAlign=\"center\">\r\n                <Typography variant=\"h4\" color=\"info.main\">\r\n                  {vehicles.length}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"textSecondary\">\r\n                  Vehículos activos\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Análisis avanzado */}\r\n      <Box mt={4}>\r\n        <AdvancedAnalytics />\r\n      </Box>\r\n\r\n      {/* Comparación de vehículos */}\r\n      {vehicles.length > 1 && (\r\n        <Box mt={4}>\r\n          <VehicleComparison />\r\n        </Box>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default Statistics;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,OAAO,EACPC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,EACTC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,IAAIC,YAAY,EACvBC,MAAM,EACNC,mBAAmB,QACd,UAAU;AACjB,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,UAAU,QAAQ,UAAU;AACtE,SAASC,EAAE,QAAQ,iBAAiB;AACpC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,iBAAiB,MAAM,qBAAqB;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;;AAEjF;AACA,MAAMC,UAAU,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,KAAK,GAAG,SAAS;EAAEC;AAAM,CAAC,kBACtET,OAAA,CAACpC,IAAI;EAAA8C,QAAA,eACHV,OAAA,CAACnC,WAAW;IAAA6C,QAAA,gBACVV,OAAA,CAACrC,UAAU;MAAC6C,KAAK,EAAC,eAAe;MAACG,YAAY;MAACC,OAAO,EAAC,OAAO;MAAAF,QAAA,EAC3DL;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACbhB,OAAA,CAACrC,UAAU;MAACiD,OAAO,EAAC,IAAI;MAACK,SAAS,EAAC,KAAK;MAACT,KAAK,EAAEA,KAAM;MAAAE,QAAA,EACnDJ;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EACZT,QAAQ,iBACPP,OAAA,CAACrC,UAAU;MAACiD,OAAO,EAAC,OAAO;MAACJ,KAAK,EAAC,eAAe;MAAAE,QAAA,EAC9CH;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACb,EACAP,KAAK,iBACJT,OAAA,CAACtC,GAAG;MAACwD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTV,OAAA,CAAC5B,IAAI;QACH+C,KAAK,EAAEV,KAAM;QACbW,IAAI,EAAC,OAAO;QACZZ,KAAK,EAAEC,KAAK,CAACY,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGZ,KAAK,CAACY,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG;MAAU;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACP;AAACM,EAAA,GAzBIlB,UAAU;AA2BhB,MAAMmB,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,cAAc;IAAEC;EAAW,CAAC,GAAGtC,MAAM,CAAC,CAAC;EAC5E,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwE,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC0E,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4E,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8E,SAAS,EAAEC,YAAY,CAAC,GAAG/E,QAAQ,CAAC;IACzCgF,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEFlF,SAAS,CAAC,MAAM;IACdmF,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAClB,OAAO,EAAEC,QAAQ,EAAEG,eAAe,EAAEE,SAAS,CAAC,CAAC;EAEnD,MAAMY,mBAAmB,GAAGA,CAAA,KAAM;IAChCC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MAAEhB,eAAe;MAAEE;IAAU,CAAC,CAAC;IACxFa,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MAAEpB,OAAO,EAAEA,OAAO,CAACqB,MAAM;MAAEpB,QAAQ,EAAEA,QAAQ,CAACoB;IAAO,CAAC,CAAC;IAE/F,IAAIC,eAAe,GAAG,CAAC,GAAGtB,OAAO,CAAC,CAAC,CAAC;IACpC,IAAIuB,gBAAgB,GAAG,CAAC,GAAGtB,QAAQ,CAAC;;IAEpC;IACA,IAAIG,eAAe,KAAK,KAAK,EAAE;MAC7B,MAAMoB,SAAS,GAAGC,QAAQ,CAACrB,eAAe,CAAC;MAC3CkB,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,SAAS,CAAC;MAC1ED,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACD,WAAW,KAAKJ,SAAS,CAAC;MAC5EL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,SAAS,EAAE;QAAExB,OAAO,EAAEsB,eAAe,CAACD,MAAM;QAAEpB,QAAQ,EAAEsB,gBAAgB,CAACF;MAAO,CAAC,CAAC;IAC3H;;IAEA;IACA,MAAMS,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAIC,eAAe;IAEnB,IAAIxB,SAAS,IAAIE,OAAO,EAAE;MACxB;MACAsB,eAAe,GAAG,IAAID,IAAI,CAACvB,SAAS,CAAC;MACrC,MAAMyB,aAAa,GAAG,IAAIF,IAAI,CAACrB,OAAO,CAAC;MAEvCY,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAI;QAC5C,MAAMO,UAAU,GAAG,IAAIH,IAAI,CAACJ,CAAC,CAACQ,KAAK,CAAC;QACpC,OAAOD,UAAU,IAAIF,eAAe,IAAIE,UAAU,IAAID,aAAa;MACrE,CAAC,CAAC;MAEFV,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAI;QAC9C,MAAMO,WAAW,GAAG,IAAIL,IAAI,CAACF,CAAC,CAACM,KAAK,CAAC;QACrC,OAAOC,WAAW,IAAIJ,eAAe,IAAII,WAAW,IAAIH,aAAa;MACvE,CAAC,CAAC;MAEFd,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAC/CZ,SAAS,EAAEwB,eAAe;QAC1BtB,OAAO,EAAEuB,aAAa;QACtBjC,OAAO,EAAEsB,eAAe,CAACD,MAAM;QAC/BpB,QAAQ,EAAEsB,gBAAgB,CAACF;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,QAAQf,SAAS;QACf,KAAK,SAAS;UACZ0B,eAAe,GAAGjE,SAAS,CAAC+D,GAAG,EAAE,CAAC,CAAC;UACnC;QACF,KAAK,SAAS;UACZE,eAAe,GAAGjE,SAAS,CAAC+D,GAAG,EAAE,CAAC,CAAC;UACnC;QACF,KAAK,OAAO;UACVE,eAAe,GAAGjE,SAAS,CAAC+D,GAAG,EAAE,EAAE,CAAC;UACpC;QACF,KAAK,QAAQ;UACXE,eAAe,GAAGjE,SAAS,CAAC+D,GAAG,EAAE,EAAE,CAAC;UACpC;QACF;UACEE,eAAe,GAAG,IAAID,IAAI,CAAC,CAAC,CAAC;QAAE;MACnC;MAEA,IAAIzB,SAAS,KAAK,KAAK,EAAE;QACvBgB,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAI,IAAII,IAAI,CAACJ,CAAC,CAACQ,KAAK,CAAC,IAAIH,eAAe,CAAC;QACnFT,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAI,IAAIE,IAAI,CAACF,CAAC,CAACM,KAAK,CAAC,IAAIH,eAAe,CAAC;QACrFb,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEd,SAAS,EAAE;UACnDN,OAAO,EAAEsB,eAAe,CAACD,MAAM;UAC/BpB,QAAQ,EAAEsB,gBAAgB,CAACF;QAC7B,CAAC,CAAC;MACJ;IACF;;IAEA;IACAgB,kBAAkB,CAACf,eAAe,EAAEC,gBAAgB,CAAC;EACvD,CAAC;EAED,MAAMc,kBAAkB,GAAGA,CAACrC,OAAO,EAAEC,QAAQ,KAAK;IAChD;IACA,MAAMqC,kBAAkB,GAAG,CAAC,CAAC;IAC7B,MAAMC,aAAa,GAAG,CAAC,GAAGvC,OAAO,CAAC,CAACwC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIX,IAAI,CAACU,CAAC,CAACN,KAAK,CAAC,GAAG,IAAIJ,IAAI,CAACW,CAAC,CAACP,KAAK,CAAC,CAAC;IAExF,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,aAAa,CAAClB,MAAM,EAAEsB,CAAC,EAAE,EAAE;MAC7C,MAAMC,OAAO,GAAGL,aAAa,CAACI,CAAC,CAAC;MAChC,MAAME,QAAQ,GAAGN,aAAa,CAACI,CAAC,GAAG,CAAC,CAAC;MAErC,IAAIC,OAAO,CAAChB,WAAW,KAAKiB,QAAQ,CAACjB,WAAW,EAAE;QAChD,MAAMkB,MAAM,GAAGF,OAAO,CAACG,mBAAmB,GAAGF,QAAQ,CAACE,mBAAmB;QACzE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,MAAM;QAE7B,IAAIH,MAAM,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAMlC,WAAW,GAAIkC,MAAM,GAAGF,MAAM,GAAI,GAAG,CAAC,CAAC;UAC7C,MAAMI,KAAK,GAAGpF,MAAM,CAAC,IAAIiE,IAAI,CAACa,OAAO,CAACT,KAAK,CAAC,EAAE,SAAS,CAAC;UAExD,IAAI,CAACG,kBAAkB,CAACY,KAAK,CAAC,EAAE;YAC9BZ,kBAAkB,CAACY,KAAK,CAAC,GAAG;cAAEC,KAAK,EAAE,CAAC;cAAEC,KAAK,EAAE,CAAC;cAAEF;YAAM,CAAC;UAC3D;UACAZ,kBAAkB,CAACY,KAAK,CAAC,CAACC,KAAK,IAAIrC,WAAW;UAC9CwB,kBAAkB,CAACY,KAAK,CAAC,CAACE,KAAK,IAAI,CAAC;QACtC;MACF;IACF;IAEA,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAACjB,kBAAkB,CAAC,CACtDkB,GAAG,CAACC,IAAI,KAAK;MACZP,KAAK,EAAEpF,MAAM,CAAC,IAAIiE,IAAI,CAAC0B,IAAI,CAACP,KAAK,GAAG,KAAK,CAAC,EAAE,UAAU,EAAE;QAAEQ,MAAM,EAAExF;MAAG,CAAC,CAAC;MACvE4C,WAAW,EAAE,CAAC2C,IAAI,CAACN,KAAK,GAAGM,IAAI,CAACL,KAAK,EAAEO,OAAO,CAAC,CAAC,CAAC;MACjDC,IAAI,EAAEH,IAAI,CAACP;IACb,CAAC,CAAC,CAAC,CACFV,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACmB,IAAI,CAACC,aAAa,CAACnB,CAAC,CAACkB,IAAI,CAAC,CAAC;;IAE/C;IACA,MAAME,YAAY,GAAG,CAAC,CAAC;IAEvB,CAAC,GAAG9D,OAAO,EAAE,GAAGC,QAAQ,CAAC,CAAC8D,OAAO,CAACN,IAAI,IAAI;MACxC,MAAMP,KAAK,GAAGpF,MAAM,CAAC,IAAIiE,IAAI,CAAC0B,IAAI,CAACtB,KAAK,CAAC,EAAE,SAAS,CAAC;MACrD,MAAM6B,IAAI,GAAGP,IAAI,CAACQ,WAAW,IAAIR,IAAI,CAACS,KAAK,IAAI,CAAC;MAChD,MAAMC,IAAI,GAAGV,IAAI,CAACR,MAAM,GAAG,aAAa,GAAG,QAAQ;MAEnD,IAAI,CAACa,YAAY,CAACZ,KAAK,CAAC,EAAE;QACxBY,YAAY,CAACZ,KAAK,CAAC,GAAG;UAAEA,KAAK;UAAEkB,WAAW,EAAE,CAAC;UAAEC,MAAM,EAAE,CAAC;UAAET,IAAI,EAAEV;QAAM,CAAC;MACzE;MACAY,YAAY,CAACZ,KAAK,CAAC,CAACiB,IAAI,CAAC,IAAIH,IAAI;IACnC,CAAC,CAAC;IAEF,MAAMM,SAAS,GAAGhB,MAAM,CAACC,MAAM,CAACO,YAAY,CAAC,CAC1CN,GAAG,CAACC,IAAI,KAAK;MACZ,GAAGA,IAAI;MACPP,KAAK,EAAEpF,MAAM,CAAC,IAAIiE,IAAI,CAAC0B,IAAI,CAACP,KAAK,GAAG,KAAK,CAAC,EAAE,UAAU,EAAE;QAAEQ,MAAM,EAAExF;MAAG,CAAC,CAAC;MACvEqG,KAAK,EAAEd,IAAI,CAACW,WAAW,GAAGX,IAAI,CAACY;IACjC,CAAC,CAAC,CAAC,CACF7B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACmB,IAAI,CAACC,aAAa,CAACnB,CAAC,CAACkB,IAAI,CAAC,CAAC;;IAE/C;IACA,MAAM5C,cAAc,GAAG,CAAC,CAAC;IACzBf,QAAQ,CAAC8D,OAAO,CAACS,OAAO,IAAI;MAC1B,MAAML,IAAI,GAAGK,OAAO,CAACC,UAAU,IAAI,OAAO;MAC1CzD,cAAc,CAACmD,IAAI,CAAC,GAAG,CAACnD,cAAc,CAACmD,IAAI,CAAC,IAAI,CAAC,KAAKK,OAAO,CAACN,KAAK,IAAI,CAAC,CAAC;IAC3E,CAAC,CAAC;IAEF,MAAMQ,YAAY,GAAGpB,MAAM,CAACqB,OAAO,CAAC3D,cAAc,CAAC,CAChDwC,GAAG,CAAC,CAAC,CAACoB,IAAI,EAAEhG,KAAK,CAAC,MAAM;MAAEgG,IAAI;MAAEhG,KAAK,EAAEiG,UAAU,CAACjG,KAAK,CAAC+E,OAAO,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC,CACvEnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC9D,KAAK,GAAG6D,CAAC,CAAC7D,KAAK,CAAC;;IAEpC;IACA,MAAMqC,aAAa,GAAGqD,SAAS,CAACd,GAAG,CAACC,IAAI;MAAA,IAAAqB,qBAAA;MAAA,OAAK;QAC3C5B,KAAK,EAAEO,IAAI,CAACP,KAAK;QACjBU,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfmB,SAAS,EAAEtB,IAAI,CAACc,KAAK;QACrBS,QAAQ,EAAEvB,IAAI,CAACW,WAAW;QAC1Ba,WAAW,EAAExB,IAAI,CAACY,MAAM;QACxBvD,WAAW,EAAE,EAAAgE,qBAAA,GAAAzB,eAAe,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,KAAKH,IAAI,CAACG,IAAI,CAAC,cAAAkB,qBAAA,uBAA/CA,qBAAA,CAAiDhE,WAAW,KAAI;MAC/E,CAAC;IAAA,CAAC,CAAC;IAEHD,YAAY,CAAC;MACXC,WAAW,EAAEuC,eAAe;MAC5BtC,KAAK,EAAEuD,SAAS;MAChBtD,cAAc,EAAE0D,YAAY;MAC5BzD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmE,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC3H,MAAM,CAACuH,MAAM,IAAI,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIpE,eAAe,GAAG,CAAC,GAAGtB,OAAO,CAAC;IAClC,IAAIuB,gBAAgB,GAAG,CAAC,GAAGtB,QAAQ,CAAC;IAEpC,IAAIG,eAAe,KAAK,KAAK,EAAE;MAC7B,MAAMoB,SAAS,GAAGC,QAAQ,CAACrB,eAAe,CAAC;MAC3CkB,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,SAAS,CAAC;MAC1ED,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACD,WAAW,KAAKJ,SAAS,CAAC;IAC9E;IAEA,IAAIlB,SAAS,KAAK,KAAK,EAAE;MACvB,MAAMwB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtB,IAAIvB,SAAS;MACb,QAAQF,SAAS;QACf,KAAK,SAAS;UAAEE,SAAS,GAAGzC,SAAS,CAAC+D,GAAG,EAAE,CAAC,CAAC;UAAE;QAC/C,KAAK,SAAS;UAAEtB,SAAS,GAAGzC,SAAS,CAAC+D,GAAG,EAAE,CAAC,CAAC;UAAE;QAC/C,KAAK,OAAO;UAAEtB,SAAS,GAAGzC,SAAS,CAAC+D,GAAG,EAAE,EAAE,CAAC;UAAE;QAC9C,KAAK,QAAQ;UAAEtB,SAAS,GAAGzC,SAAS,CAAC+D,GAAG,EAAE,EAAE,CAAC;UAAE;QAC/C;UAAStB,SAAS,GAAG,IAAIuB,IAAI,CAAC,CAAC,CAAC;MAClC;MAEA,IAAIzB,SAAS,KAAK,KAAK,EAAE;QACvBgB,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACC,CAAC,IAAI,IAAII,IAAI,CAACJ,CAAC,CAACQ,KAAK,CAAC,IAAI3B,SAAS,CAAC;QAC7Ee,gBAAgB,GAAGA,gBAAgB,CAACG,MAAM,CAACG,CAAC,IAAI,IAAIE,IAAI,CAACF,CAAC,CAACM,KAAK,CAAC,IAAI3B,SAAS,CAAC;MACjF;IACF;IAEA,OAAO;MAAEc,eAAe;MAAEC;IAAiB,CAAC;EAC9C,CAAC;EAED,MAAM;IAAED,eAAe;IAAEC;EAAiB,CAAC,GAAGmE,eAAe,CAAC,CAAC;;EAE/D;EACA,MAAMC,YAAY,GAAGrE,eAAe,CAACD,MAAM;EAC3C,MAAMuE,aAAa,GAAGrE,gBAAgB,CAACF,MAAM;EAC7C,MAAMwE,aAAa,GAAGvE,eAAe,CAACwE,MAAM,CAAC,CAACC,GAAG,EAAEpE,CAAC,KAAKoE,GAAG,IAAIpE,CAAC,CAACsC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACvF,MAAM+B,gBAAgB,GAAGzE,gBAAgB,CAACuE,MAAM,CAAC,CAACC,GAAG,EAAElE,CAAC,KAAKkE,GAAG,IAAIlE,CAAC,CAACqC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF,MAAMa,SAAS,GAAGc,aAAa,GAAGG,gBAAgB;EAClD,MAAMC,WAAW,GAAG3E,eAAe,CAACwE,MAAM,CAAC,CAACC,GAAG,EAAEpE,CAAC,KAAKoE,GAAG,IAAIpE,CAAC,CAACsB,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEhF;EACA,IAAIiD,cAAc,GAAG,CAAC;EACtB,IAAI5E,eAAe,CAACD,MAAM,IAAI,CAAC,EAAE;IAC/B,MAAMkB,aAAa,GAAG,CAAC,GAAGjB,eAAe,CAAC,CAACkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIX,IAAI,CAACU,CAAC,CAACN,KAAK,CAAC,GAAG,IAAIJ,IAAI,CAACW,CAAC,CAACP,KAAK,CAAC,CAAC;IAChG,MAAMgE,OAAO,GAAG5D,aAAa,CAACA,aAAa,CAAClB,MAAM,GAAG,CAAC,CAAC,CAAC0B,mBAAmB,GAC5DR,aAAa,CAAC,CAAC,CAAC,CAACQ,mBAAmB;IACnD,IAAIoD,OAAO,GAAG,CAAC,EAAE;MACfD,cAAc,GAAID,WAAW,GAAGE,OAAO,GAAG,GAAI;IAChD;EACF;EAEA,MAAMC,YAAY,GAAGH,WAAW,GAAG,CAAC,GAAGJ,aAAa,GAAGI,WAAW,GAAG,CAAC;EAEtE,oBACE3H,OAAA,CAACtC,GAAG;IAACqK,CAAC,EAAE,CAAE;IAAArH,QAAA,gBACRV,OAAA,CAACrC,UAAU;MAACiD,OAAO,EAAC,IAAI;MAACD,YAAY;MAAAD,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbhB,OAAA,CAACtC,GAAG;MAACsK,EAAE,EAAE,CAAE;MAAAtH,QAAA,eACTV,OAAA,CAAClC,IAAI;QAACmK,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAzH,QAAA,gBAC7CV,OAAA,CAAClC,IAAI;UAACqH,IAAI;UAACiD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3H,QAAA,eACvBV,OAAA,CAACjC,WAAW;YAACuK,SAAS;YAAA5H,QAAA,gBACpBV,OAAA,CAAChC,UAAU;cAACuK,EAAE,EAAC,sBAAsB;cAAA7H,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3DhB,OAAA,CAAC/B,MAAM;cACLuK,OAAO,EAAC,sBAAsB;cAC9BlI,KAAK,EAAEwB,eAAgB;cACvB2G,QAAQ,EAAGlF,CAAC,IAAKxB,kBAAkB,CAACwB,CAAC,CAACmF,MAAM,CAACpI,KAAK,CAAE;cACpDa,KAAK,EAAC,aAAU;cAAAT,QAAA,gBAEhBV,OAAA,CAAC9B,QAAQ;gBAACoC,KAAK,EAAC,KAAK;gBAAAI,QAAA,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACnDS,QAAQ,CAACyD,GAAG,CAAEyD,OAAO,iBACpB3I,OAAA,CAAC9B,QAAQ;gBAAkBoC,KAAK,EAAEqI,OAAO,CAACJ,EAAG;gBAAA7H,QAAA,EAC1CiI,OAAO,CAACC;cAAM,GADFD,OAAO,CAACJ,EAAE;gBAAA1H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPhB,OAAA,CAAClC,IAAI;UAACqH,IAAI;UAACiD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3H,QAAA,eACvBV,OAAA,CAACjC,WAAW;YAACuK,SAAS;YAAA5H,QAAA,gBACpBV,OAAA,CAAChC,UAAU;cAACuK,EAAE,EAAC,kBAAkB;cAAA7H,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9DhB,OAAA,CAAC/B,MAAM;cACLuK,OAAO,EAAC,kBAAkB;cAC1BlI,KAAK,EAAE0B,SAAU;cACjByG,QAAQ,EAAGlF,CAAC,IAAK;gBACftB,YAAY,CAACsB,CAAC,CAACmF,MAAM,CAACpI,KAAK,CAAC;gBAC5B;gBACA6B,YAAY,CAAC,EAAE,CAAC;gBAChBE,UAAU,CAAC,EAAE,CAAC;cAChB,CAAE;cACFlB,KAAK,EAAC,iBAAiB;cAAAT,QAAA,gBAEvBV,OAAA,CAAC9B,QAAQ;gBAACoC,KAAK,EAAC,SAAS;gBAAAI,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpDhB,OAAA,CAAC9B,QAAQ;gBAACoC,KAAK,EAAC,SAAS;gBAAAI,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpDhB,OAAA,CAAC9B,QAAQ;gBAACoC,KAAK,EAAC,OAAO;gBAAAI,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7ChB,OAAA,CAAC9B,QAAQ;gBAACoC,KAAK,EAAC,QAAQ;gBAAAI,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClDhB,OAAA,CAAC9B,QAAQ;gBAACoC,KAAK,EAAC,KAAK;gBAAAI,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClDhB,OAAA,CAAC9B,QAAQ;gBAACoC,KAAK,EAAC,QAAQ;gBAAAI,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EACNgB,SAAS,KAAK,QAAQ,iBACrBhC,OAAA,CAAAE,SAAA;UAAAQ,QAAA,gBACEV,OAAA,CAAClC,IAAI;YAACqH,IAAI;YAACiD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3H,QAAA,eACvBV,OAAA,CAAC7B,SAAS;cACRmK,SAAS;cACTnH,KAAK,EAAC,iBAAiB;cACvB0E,IAAI,EAAC,MAAM;cACXvF,KAAK,EAAE4B,SAAU;cACjBuG,QAAQ,EAAGlF,CAAC,IAAKpB,YAAY,CAACoB,CAAC,CAACmF,MAAM,CAACpI,KAAK,CAAE;cAC9CuI,eAAe,EAAE;gBACfC,MAAM,EAAE;cACV;YAAE;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhB,OAAA,CAAClC,IAAI;YAACqH,IAAI;YAACiD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3H,QAAA,eACvBV,OAAA,CAAC7B,SAAS;cACRmK,SAAS;cACTnH,KAAK,EAAC,cAAc;cACpB0E,IAAI,EAAC,MAAM;cACXvF,KAAK,EAAE8B,OAAQ;cACfqG,QAAQ,EAAGlF,CAAC,IAAKlB,UAAU,CAACkB,CAAC,CAACmF,MAAM,CAACpI,KAAK,CAAE;cAC5CuI,eAAe,EAAE;gBACfC,MAAM,EAAE;cACV;YAAE;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNhB,OAAA,CAAClC,IAAI;MAACmK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACF,EAAE,EAAE,CAAE;MAAAtH,QAAA,gBAChCV,OAAA,CAAClC,IAAI;QAACqH,IAAI;QAACiD,EAAE,EAAE,EAAG;QAACW,EAAE,EAAE,CAAE;QAACV,EAAE,EAAE,CAAE;QAAA3H,QAAA,eAC9BV,OAAA,CAACI,UAAU;UACTC,KAAK,EAAC,kBAAkB;UACxBC,KAAK,EAAE,GAAGsH,cAAc,CAACvC,OAAO,CAAC,CAAC,CAAC,IAAK;UACxC9E,QAAQ,EAAC,YAAY;UACrBC,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPhB,OAAA,CAAClC,IAAI;QAACqH,IAAI;QAACiD,EAAE,EAAE,EAAG;QAACW,EAAE,EAAE,CAAE;QAACV,EAAE,EAAE,CAAE;QAAA3H,QAAA,eAC9BV,OAAA,CAACI,UAAU;UACTC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEwG,cAAc,CAACgB,YAAY,CAAE;UACpCvH,QAAQ,EAAC,WAAW;UACpBC,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPhB,OAAA,CAAClC,IAAI;QAACqH,IAAI;QAACiD,EAAE,EAAE,EAAG;QAACW,EAAE,EAAE,CAAE;QAACV,EAAE,EAAE,CAAE;QAAA3H,QAAA,eAC9BV,OAAA,CAACI,UAAU;UACTC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAEwG,cAAc,CAACL,SAAS,CAAE;UACjClG,QAAQ,EAAE,GAAGuG,cAAc,CAACS,aAAa,CAAC,kBAAkBT,cAAc,CAACY,gBAAgB,CAAC,SAAU;UACtGlH,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPhB,OAAA,CAAClC,IAAI;QAACqH,IAAI;QAACiD,EAAE,EAAE,EAAG;QAACW,EAAE,EAAE,CAAE;QAACV,EAAE,EAAE,CAAE;QAAA3H,QAAA,eAC9BV,OAAA,CAACI,UAAU;UACTC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAE,GAAGqH,WAAW,CAACtC,OAAO,CAAC,CAAC,CAAC,IAAK;UACrC9E,QAAQ,EAAE,MAAM8G,YAAY,aAAc;UAC1C7G,KAAK,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPhB,OAAA,CAAClC,IAAI;MAACmK,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAxH,QAAA,gBAEzBV,OAAA,CAAClC,IAAI;QAACqH,IAAI;QAACiD,EAAE,EAAE,EAAG;QAACY,EAAE,EAAE,CAAE;QAAAtI,QAAA,eACvBV,OAAA,CAACpC,IAAI;UAAA8C,QAAA,eACHV,OAAA,CAACnC,WAAW;YAAA6C,QAAA,gBACVV,OAAA,CAACrC,UAAU;cAACiD,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAACtC,GAAG;cAACuL,MAAM,EAAE,GAAI;cAAAvI,QAAA,eACfV,OAAA,CAACV,mBAAmB;gBAAC4J,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAAvI,QAAA,eAC7CV,OAAA,CAACzB,SAAS;kBAAC4K,IAAI,EAAE7G,SAAS,CAACE,WAAY;kBAAA9B,QAAA,gBACrCV,OAAA,CAACd,aAAa;oBAACkK,eAAe,EAAC;kBAAK;oBAAAvI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvChB,OAAA,CAAChB,KAAK;oBAACqK,OAAO,EAAC;kBAAO;oBAAAxI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBhB,OAAA,CAACf,KAAK;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACThB,OAAA,CAACZ,YAAY;oBACXkK,SAAS,EAAGhJ,KAAK,IAAK,CAAC,GAAGA,KAAK,UAAU,EAAE,SAAS,CAAE;oBACtDiJ,UAAU,EAAE;sBAAE/I,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFhB,OAAA,CAACX,MAAM;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVhB,OAAA,CAACxB,IAAI;oBACHqH,IAAI,EAAC,UAAU;oBACfwD,OAAO,EAAC,aAAa;oBACrBG,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfnD,IAAI,EAAC;kBAAS;oBAAAzF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPhB,OAAA,CAAClC,IAAI;QAACqH,IAAI;QAACiD,EAAE,EAAE,EAAG;QAACY,EAAE,EAAE,CAAE;QAAAtI,QAAA,eACvBV,OAAA,CAACpC,IAAI;UAAA8C,QAAA,eACHV,OAAA,CAACnC,WAAW;YAAA6C,QAAA,gBACVV,OAAA,CAACrC,UAAU;cAACiD,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAACtC,GAAG;cAACuL,MAAM,EAAE,GAAI;cAAAvI,QAAA,eACfV,OAAA,CAACV,mBAAmB;gBAAC4J,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAAvI,QAAA,eAC7CV,OAAA,CAACvB,SAAS;kBAAC0K,IAAI,EAAE7G,SAAS,CAACG,KAAM;kBAAA/B,QAAA,gBAC/BV,OAAA,CAACd,aAAa;oBAACkK,eAAe,EAAC;kBAAK;oBAAAvI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvChB,OAAA,CAAChB,KAAK;oBAACqK,OAAO,EAAC;kBAAO;oBAAAxI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBhB,OAAA,CAACf,KAAK;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACThB,OAAA,CAACZ,YAAY;oBACXkK,SAAS,EAAGhJ,KAAK,IAAK,CAACwG,cAAc,CAACxG,KAAK,CAAC,CAAE;oBAC9CiJ,UAAU,EAAE;sBAAE/I,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFhB,OAAA,CAACX,MAAM;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVhB,OAAA,CAACtB,IAAI;oBACHmH,IAAI,EAAC,UAAU;oBACfwD,OAAO,EAAC,aAAa;oBACrBK,OAAO,EAAC,GAAG;oBACXF,MAAM,EAAC,SAAS;oBAChBG,IAAI,EAAC;kBAAS;oBAAA9I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFhB,OAAA,CAACtB,IAAI;oBACHmH,IAAI,EAAC,UAAU;oBACfwD,OAAO,EAAC,QAAQ;oBAChBK,OAAO,EAAC,GAAG;oBACXF,MAAM,EAAC,SAAS;oBAChBG,IAAI,EAAC;kBAAS;oBAAA9I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPhB,OAAA,CAAClC,IAAI;QAACqH,IAAI;QAACiD,EAAE,EAAE,EAAG;QAACY,EAAE,EAAE,CAAE;QAAAtI,QAAA,eACvBV,OAAA,CAACpC,IAAI;UAAA8C,QAAA,eACHV,OAAA,CAACnC,WAAW;YAAA6C,QAAA,gBACVV,OAAA,CAACrC,UAAU;cAACiD,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAACtC,GAAG;cAACuL,MAAM,EAAE,GAAI;cAAAvI,QAAA,eACfV,OAAA,CAACV,mBAAmB;gBAAC4J,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAAvI,QAAA,eAC7CV,OAAA,CAACnB,QAAQ;kBAAA6B,QAAA,gBACPV,OAAA,CAAClB,GAAG;oBACFqK,IAAI,EAAE7G,SAAS,CAACI,cAAe;oBAC/BkH,EAAE,EAAC,KAAK;oBACRC,EAAE,EAAC,KAAK;oBACRC,SAAS,EAAE,KAAM;oBACjB3I,KAAK,EAAEA,CAAC;sBAAEmF,IAAI;sBAAEyD;oBAAQ,CAAC,KAAK,GAAGzD,IAAI,IAAI,CAACyD,OAAO,GAAG,GAAG,EAAE1E,OAAO,CAAC,CAAC,CAAC,GAAI;oBACvE2E,WAAW,EAAE,EAAG;oBAChBL,IAAI,EAAC,SAAS;oBACdN,OAAO,EAAC,OAAO;oBAAA3I,QAAA,EAEd4B,SAAS,CAACI,cAAc,CAACwC,GAAG,CAAC,CAAC+E,KAAK,EAAEC,KAAK,kBACzClK,OAAA,CAACjB,IAAI;sBAAuB4K,IAAI,EAAExJ,MAAM,CAAC+J,KAAK,GAAG/J,MAAM,CAAC4C,MAAM;oBAAE,GAArD,QAAQmH,KAAK,EAAE;sBAAArJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAwC,CACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNhB,OAAA,CAACZ,YAAY;oBAACkK,SAAS,EAAGhJ,KAAK,IAAK,CAACwG,cAAc,CAACxG,KAAK,CAAC;kBAAE;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPhB,OAAA,CAAClC,IAAI;QAACqH,IAAI;QAACiD,EAAE,EAAE,EAAG;QAACY,EAAE,EAAE,CAAE;QAAAtI,QAAA,eACvBV,OAAA,CAACpC,IAAI;UAAA8C,QAAA,eACHV,OAAA,CAACnC,WAAW;YAAA6C,QAAA,gBACVV,OAAA,CAACrC,UAAU;cAACiD,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAD,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAACtC,GAAG;cAACuL,MAAM,EAAE,GAAI;cAAAvI,QAAA,eACfV,OAAA,CAACV,mBAAmB;gBAAC4J,KAAK,EAAC,MAAM;gBAACD,MAAM,EAAC,MAAM;gBAAAvI,QAAA,eAC7CV,OAAA,CAACrB,QAAQ;kBAACwK,IAAI,EAAE7G,SAAS,CAACK,aAAc;kBAAAjC,QAAA,gBACtCV,OAAA,CAACd,aAAa;oBAACkK,eAAe,EAAC;kBAAK;oBAAAvI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvChB,OAAA,CAAChB,KAAK;oBAACqK,OAAO,EAAC;kBAAO;oBAAAxI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzBhB,OAAA,CAACf,KAAK;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACThB,OAAA,CAACZ,YAAY;oBACXkK,SAAS,EAAEA,CAAChJ,KAAK,EAAEgG,IAAI,KAAK;sBAC1B,IAAIA,IAAI,KAAK,aAAa,EAAE,OAAO,CAACQ,cAAc,CAACxG,KAAK,CAAC,EAAEgG,IAAI,CAAC;sBAChE,OAAO,CAAChG,KAAK,EAAEgG,IAAI,CAAC;oBACtB,CAAE;oBACFiD,UAAU,EAAE;sBAAE/I,KAAK,EAAE;oBAAO;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACFhB,OAAA,CAACX,MAAM;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACVhB,OAAA,CAACpB,GAAG;oBAACyK,OAAO,EAAC,WAAW;oBAACM,IAAI,EAAC,SAAS;oBAACrD,IAAI,EAAC;kBAAiB;oBAAAzF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPhB,OAAA,CAACpC,IAAI;MAACuM,EAAE,EAAE;QAAEjJ,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eAClBV,OAAA,CAACnC,WAAW;QAAA6C,QAAA,gBACVV,OAAA,CAACrC,UAAU;UAACiD,OAAO,EAAC,IAAI;UAACD,YAAY;UAAAD,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhB,OAAA,CAAClC,IAAI;UAACmK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAxH,QAAA,gBACzBV,OAAA,CAAClC,IAAI;YAACqH,IAAI;YAACiD,EAAE,EAAE,EAAG;YAACW,EAAE,EAAE,CAAE;YAACV,EAAE,EAAE,CAAE;YAAA3H,QAAA,eAC9BV,OAAA,CAACtC,GAAG;cAAC0M,SAAS,EAAC,QAAQ;cAAA1J,QAAA,gBACrBV,OAAA,CAACrC,UAAU;gBAACiD,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,SAAS;gBAAAE,QAAA,EACrC2G;cAAY;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACbhB,OAAA,CAACrC,UAAU;gBAACiD,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPhB,OAAA,CAAClC,IAAI;YAACqH,IAAI;YAACiD,EAAE,EAAE,EAAG;YAACW,EAAE,EAAE,CAAE;YAACV,EAAE,EAAE,CAAE;YAAA3H,QAAA,eAC9BV,OAAA,CAACtC,GAAG;cAAC0M,SAAS,EAAC,QAAQ;cAAA1J,QAAA,gBACrBV,OAAA,CAACrC,UAAU;gBAACiD,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,cAAc;gBAAAE,QAAA,EAC1C4G;cAAa;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbhB,OAAA,CAACrC,UAAU;gBAACiD,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPhB,OAAA,CAAClC,IAAI;YAACqH,IAAI;YAACiD,EAAE,EAAE,EAAG;YAACW,EAAE,EAAE,CAAE;YAACV,EAAE,EAAE,CAAE;YAAA3H,QAAA,eAC9BV,OAAA,CAACtC,GAAG;cAAC0M,SAAS,EAAC,QAAQ;cAAA1J,QAAA,gBACrBV,OAAA,CAACrC,UAAU;gBAACiD,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,cAAc;gBAAAE,QAAA,EAC1CoG,cAAc,CAACL,SAAS,IAAIY,YAAY,GAAGC,aAAa,CAAC,IAAI,CAAC;cAAC;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACbhB,OAAA,CAACrC,UAAU;gBAACiD,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPhB,OAAA,CAAClC,IAAI;YAACqH,IAAI;YAACiD,EAAE,EAAE,EAAG;YAACW,EAAE,EAAE,CAAE;YAACV,EAAE,EAAE,CAAE;YAAA3H,QAAA,eAC9BV,OAAA,CAACtC,GAAG;cAAC0M,SAAS,EAAC,QAAQ;cAAA1J,QAAA,gBACrBV,OAAA,CAACrC,UAAU;gBAACiD,OAAO,EAAC,IAAI;gBAACJ,KAAK,EAAC,WAAW;gBAAAE,QAAA,EACvCe,QAAQ,CAACsB;cAAM;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACbhB,OAAA,CAACrC,UAAU;gBAACiD,OAAO,EAAC,OAAO;gBAACJ,KAAK,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAElD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhB,OAAA,CAACtC,GAAG;MAACwD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTV,OAAA,CAACF,iBAAiB;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,EAGLS,QAAQ,CAACsB,MAAM,GAAG,CAAC,iBAClB/C,OAAA,CAACtC,GAAG;MAACwD,EAAE,EAAE,CAAE;MAAAR,QAAA,eACTV,OAAA,CAACH,iBAAiB;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACQ,EAAA,CAviBID,UAAU;EAAA,QACsDhC,MAAM;AAAA;AAAA8K,GAAA,GADtE9I,UAAU;AAyiBhB,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAA+I,GAAA;AAAAC,YAAA,CAAAhJ,EAAA;AAAAgJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}