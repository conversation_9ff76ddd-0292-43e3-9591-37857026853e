// Configuración de la API
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'http://localhost:3001/api' 
  : 'http://localhost:3001/api';

// Función helper para hacer peticiones
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

// Servicios de Vehículos
export const vehicleService = {
  getAll: () => apiRequest('/vehicles'),
  
  create: (vehicleData) => apiRequest('/vehicles', {
    method: 'POST',
    body: JSON.stringify(vehicleData),
  }),
  
  update: (id, vehicleData) => apiRequest(`/vehicles/${id}`, {
    method: 'PUT',
    body: JSON.stringify(vehicleData),
  }),
  
  delete: (id) => apiRequest(`/vehicles/${id}`, {
    method: 'DELETE',
  }),
};

// Servicios de Repostajes
export const refuelService = {
  getAll: () => apiRequest('/refuels'),
  
  create: (refuelData) => apiRequest('/refuels', {
    method: 'POST',
    body: JSON.stringify(refuelData),
  }),
  
  update: (id, refuelData) => apiRequest(`/refuels/${id}`, {
    method: 'PUT',
    body: JSON.stringify(refuelData),
  }),
  
  delete: (id) => apiRequest(`/refuels/${id}`, {
    method: 'DELETE',
  }),
};

// Servicios de Gastos
export const expenseService = {
  getAll: () => apiRequest('/expenses'),
  
  create: (expenseData) => apiRequest('/expenses', {
    method: 'POST',
    body: JSON.stringify(expenseData),
  }),
  
  update: (id, expenseData) => apiRequest(`/expenses/${id}`, {
    method: 'PUT',
    body: JSON.stringify(expenseData),
  }),
  
  delete: (id) => apiRequest(`/expenses/${id}`, {
    method: 'DELETE',
  }),
};

// Servicio de Estadísticas
export const statisticsService = {
  get: (vehicleId = null, startDate = null, endDate = null) => {
    const params = new URLSearchParams();
    if (vehicleId) params.append('vehicleId', vehicleId);
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const queryString = params.toString();
    return apiRequest(`/statistics${queryString ? `?${queryString}` : ''}`);
  },
};
