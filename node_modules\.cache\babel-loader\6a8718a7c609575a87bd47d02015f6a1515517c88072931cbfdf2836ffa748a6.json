{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ownerDocument;\nfunction ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ownerDocument", "node", "document"], "sources": ["D:/Proyectos Python/Repostaje/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/ownerDocument/ownerDocument.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ownerDocument;\nfunction ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,aAAa;AAC/B,SAASA,aAAaA,CAACC,IAAI,EAAE;EAC3B,OAAOA,IAAI,IAAIA,IAAI,CAACD,aAAa,IAAIE,QAAQ;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}